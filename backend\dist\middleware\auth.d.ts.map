{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,QAAQ,EAAE,YAAY,EAAC,MAAM,SAAS,CAAC;AAE/C,OAAO,EAAC,QAAQ,EAAC,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAC,oBAAoB,EAAY,UAAU,EAAC,MAAM,eAAe,CAAC;AAYzE,eAAO,MAAM,mBAAmB,GAC/B,SAAS,UAAU,EACnB,SAAQ,MAAmB,EAC3B,YAAW,MAAuB,EAClC,SAAS,MAAM,EACf,WAAW,MAAM,KACf,MAeF,CAAC;AAGF,eAAO,MAAM,oBAAoB,GAChC,SAAS,UAAU,EACnB,SAAQ,MAA2B,EACnC,YAAW,MAA+B,EAC1C,SAAS,MAAM,EACf,WAAW,MAAM,KACf,MAeF,CAAC;AAGF,eAAO,MAAM,iBAAiB,GAAI,OAAO,MAAM,KAAG,UAWjD,CAAC;AAGF,eAAO,MAAM,kBAAkB,GAAI,OAAO,MAAM,KAAG,UAWlD,CAAC;AAGF,eAAO,MAAM,YAAY,GACxB,KAAK,oBAAoB,EACzB,KAAK,QAAQ,EACb,MAAM,YAAY,KAChB,OAAO,CAAC,IAAI,CAkEd,CAAC;AAGF,eAAO,MAAM,oBAAoB,GAChC,KAAK,oBAAoB,EACzB,KAAK,QAAQ,EACb,MAAM,YAAY,KAChB,OAAO,CAAC,IAAI,CAiDd,CAAC;AAGF,eAAO,MAAM,SAAS,GAAI,GAAG,OAAO,QAAQ,EAAE,MAE5C,KAAK,oBAAoB,EACzB,KAAK,QAAQ,EACb,MAAM,YAAY,KAChB,IAmDH,CAAC;AAGF,eAAO,MAAM,wBAAwB,GACpC,KAAK,oBAAoB,EACzB,KAAK,QAAQ,EACb,MAAM,YAAY,KAChB,IA+CF,CAAC;AAGF,eAAO,MAAM,uBAAuB,GAAI,cAAa,MAAiB,MAEpE,KAAK,oBAAoB,EACzB,KAAK,QAAQ,EACb,MAAM,YAAY,KAChB,IAkEH,CAAC;AAGF,eAAO,MAAM,aAAa,GACzB,cAAa,MAAU,EACvB,WAAU,MAAuB,MAKhC,KAAK,oBAAoB,EACzB,KAAK,QAAQ,EACb,MAAM,YAAY,KAChB,IA4DH,CAAC;;mCA7cQ,UAAU,WACX,MAAM,cACH,MAAM,WACR,MAAM,aACJ,MAAM,KACf,MAAM;oCAmBC,UAAU,WACX,MAAM,cACH,MAAM,WACR,MAAM,aACJ,MAAM,KACf,MAAM;+BAkBgC,MAAM,KAAG,UAAU;gCAclB,MAAM,KAAG,UAAU;wBAevD,oBAAoB,OACpB,QAAQ,QACP,YAAY,KAChB,OAAO,CAAC,IAAI,CAAC;gCAsEV,oBAAoB,OACpB,QAAQ,QACP,YAAY,KAChB,OAAO,CAAC,IAAI,CAAC;0BAoDoB,QAAQ,EAAE,MAE5C,KAAK,oBAAoB,EACzB,KAAK,QAAQ,EACb,MAAM,YAAY,KAChB,IAAI;oCAuDF,oBAAoB,OACpB,QAAQ,QACP,YAAY,KAChB,IAAI;4CAkD8C,MAAM,MAEzD,KAAK,oBAAoB,EACzB,KAAK,QAAQ,EACb,MAAM,YAAY,KAChB,IAAI;kCAsEM,MAAM,aACT,MAAM,MAKf,KAAK,oBAAoB,EACzB,KAAK,QAAQ,EACb,MAAM,YAAY,KAChB,IAAI;;AA8DR,wBAWE"}