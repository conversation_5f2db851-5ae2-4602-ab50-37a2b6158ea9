{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAEjD,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC1C,OAAO,EAAE,oBAAoB,EAAY,UAAU,EAAE,MAAM,eAAe,CAAC;AAU3E,eAAO,MAAM,mBAAmB,GAAI,SAAS,UAAU,KAAG,MAQzD,CAAC;AAGF,eAAO,MAAM,oBAAoB,GAAI,SAAS,UAAU,KAAG,MAQ1D,CAAC;AAGF,eAAO,MAAM,iBAAiB,GAAI,OAAO,MAAM,KAAG,UAWjD,CAAC;AAGF,eAAO,MAAM,kBAAkB,GAAI,OAAO,MAAM,KAAG,UAWlD,CAAC;AAGF,eAAO,MAAM,YAAY,GACvB,KAAK,oBAAoB,EACzB,KAAK,QAAQ,EACb,MAAM,YAAY,KACjB,OAAO,CAAC,IAAI,CAuBd,CAAC;AAGF,eAAO,MAAM,oBAAoB,GAC/B,KAAK,oBAAoB,EACzB,KAAK,QAAQ,EACb,MAAM,YAAY,KACjB,OAAO,CAAC,IAAI,CAkBd,CAAC;AAGF,eAAO,MAAM,SAAS,GAAI,GAAG,OAAO,QAAQ,EAAE,MACpC,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,IAWxE,CAAC;AAGF,eAAO,MAAM,wBAAwB,GACnC,KAAK,oBAAoB,EACzB,KAAK,QAAQ,EACb,MAAM,YAAY,KACjB,IAUF,CAAC;AAGF,eAAO,MAAM,uBAAuB,GAAI,cAAa,MAAiB,MAC5D,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,IAmBxE,CAAC;AAGF,eAAO,MAAM,aAAa,GAAI,cAAa,MAAU,EAAE,WAAU,MAAuB,MAG9E,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,IA+BxE,CAAC;;mCAlM2C,UAAU,KAAG,MAAM;oCAWlB,UAAU,KAAG,MAAM;+BAWxB,MAAM,KAAG,UAAU;gCAclB,MAAM,KAAG,UAAU;wBAetD,oBAAoB,OACpB,QAAQ,QACP,YAAY,KACjB,OAAO,CAAC,IAAI,CAAC;gCA2BT,oBAAoB,OACpB,QAAQ,QACP,YAAY,KACjB,OAAO,CAAC,IAAI,CAAC;0BAqBoB,QAAQ,EAAE,MACpC,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,IAAI;oCAetE,oBAAoB,OACpB,QAAQ,QACP,YAAY,KACjB,IAAI;4CAa8C,MAAM,MACjD,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,IAAI;kCAsBlC,MAAM,aAAgB,MAAM,MAG7D,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,IAAI;;AAiC7E,wBAWE"}