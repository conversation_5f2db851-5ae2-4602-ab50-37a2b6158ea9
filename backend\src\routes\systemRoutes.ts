/**
 * System Routes (Health, API Info, etc.)
 * Separated to follow SRP - handles only system-level endpoints
 */

import { Router, Request, Response } from 'express';
import { config } from '../config';

const router = Router();

// Utility functions to follow DRY principle
const getAppVersion = (): string => process.env.npm_package_version || '1.0.0';
const getCurrentTimestamp = (): string => new Date().toISOString();

/**
 * Root Endpoint - API Information
 */
router.get('/', (req: Request, res: Response) => {
  const serverConfig = config.getServerConfig();

  res.status(200).json({
    message: 'PrintWedittV1 API',
    version: getAppVersion(),
    environment: serverConfig.nodeEnv,
    timestamp: getCurrentTimestamp(),
    endpoints: {
      health: '/health',
      api: '/api',
      auth: '/api/auth',
    },
    documentation: 'API documentation coming soon',
    links: {
      health: `${serverConfig.apiBaseUrl}/health`,
      api: `${serverConfig.apiBaseUrl}/api`,
      auth: `${serverConfig.apiBaseUrl}/api/auth`,
    },
  });
});

/**
 * Health Check Endpoint
 */
router.get('/health', (req: Request, res: Response) => {
  const serverConfig = config.getServerConfig();

  res.status(200).json({
    status: 'OK',
    timestamp: getCurrentTimestamp(),
    uptime: process.uptime(),
    environment: serverConfig.nodeEnv,
    version: getAppVersion(),
    apiBaseUrl: serverConfig.apiBaseUrl,
    frontendUrl: serverConfig.frontendUrl,
  });
});

/**
 * API Base Endpoint
 */
router.get('/api', (req: Request, res: Response) => {
  res.status(200).json({
    message: 'PrintWedittV1 API Endpoints',
    version: getAppVersion(),
    availableEndpoints: {
      auth: '/api/auth',
      users: '/api/users',
      // Add more endpoints as they are implemented
      // services: '/api/services',
      // orders: '/api/orders',
      // files: '/api/files',
    },
    documentation: 'API documentation coming soon',
  });
});

export default router;