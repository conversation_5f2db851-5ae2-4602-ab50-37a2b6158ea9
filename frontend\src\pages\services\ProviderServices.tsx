import React, { useState } from 'react';
import { 
  <PERSON>, 
  Palette, 
  Printer, 
  CheckCircle, 
  ArrowRight, 
  Mail, 
  Phone, 
  MapPin,
  Star,
  Award,
  Clock,
  DollarSign,
  Briefcase,
  Globe,
  Heart,
  Target,
  X,
  Building,
  User
} from 'lucide-react';

const ProviderServices: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'designer' | 'printer'>('designer');
  const [showRegistrationForm, setShowRegistrationForm] = useState(false);
  const [showDesignerForm, setShowDesignerForm] = useState(false);
  const [formData, setFormData] = useState({
    businessName: '',
    ownerName: '',
    email: '',
    phone: '',
    website: '',
    description: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    googleReviewsUrl: ''
  });
  const [designerFormData, setDesignerFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    website: '',
    portfolio: '',
    description: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isDesignerSubmitting, setIsDesignerSubmitting] = useState(false);
  const [isDesignerSubmitted, setIsDesignerSubmitted] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDesignerInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setDesignerFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));

    setIsSubmitting(false);
    setIsSubmitted(true);

    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false);
      setShowRegistrationForm(false);
      setFormData({
        businessName: '',
        ownerName: '',
        email: '',
        phone: '',
        website: '',
        description: '',
        address: '',
        city: '',
        state: '',
        zipCode: '',
        googleReviewsUrl: ''
      });
    }, 3000);
  };

  const handleDesignerSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsDesignerSubmitting(true);

    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));

    setIsDesignerSubmitting(false);
    setIsDesignerSubmitted(true);

    // Reset form after 3 seconds
    setTimeout(() => {
      setIsDesignerSubmitted(false);
      setShowDesignerForm(false);
      setDesignerFormData({
        fullName: '',
        email: '',
        phone: '',
        website: '',
        portfolio: '',
        description: ''
      });
    }, 3000);
  };
  const designerBenefits = [
    {
      icon: DollarSign,
      title: "Competitive Rates",
      description: "Earn $25-75 per hour based on project complexity and your experience level"
    },
    {
      icon: Clock,
      title: "Flexible Schedule",
      description: "Work on your own time with projects that fit your availability"
    },
    {
      icon: Briefcase,
      title: "Diverse Projects",
      description: "Work on business cards, brochures, logos, and marketing materials"
    },
    {
      icon: Globe,
      title: "Remote Work",
      description: "Work from anywhere with our fully remote collaboration tools"
    },
    {
      icon: Award,
      title: "Portfolio Growth",
      description: "Build your portfolio with real client projects and testimonials"
    },
    {
      icon: Users,
      title: "Supportive Team",
      description: "Join a community of designers with mentorship and feedback"
    }
  ];

  const printerBenefits = [
    {
      icon: Target,
      title: "Steady Work Flow",
      description: "Consistent orders from our established client base"
    },
    {
      icon: DollarSign,
      title: "Fair Pricing",
      description: "Competitive wholesale rates with prompt payment terms"
    },
    {
      icon: Award,
      title: "Quality Standards",
      description: "Work with clients who value quality and professionalism"
    },
    {
      icon: Clock,
      title: "Reliable Timelines",
      description: "Clear deadlines and realistic turnaround expectations"
    },
    {
      icon: Globe,
      title: "Market Expansion",
      description: "Access to new markets and customer segments"
    },
    {
      icon: Heart,
      title: "Partnership Focus",
      description: "Long-term relationships built on mutual success"
    }
  ];

  const designerRequirements = [
    "2+ years of graphic design experience",
    "Proficiency in Adobe Creative Suite (Illustrator, Photoshop, InDesign)",
    "Strong portfolio showcasing print design work",
    "Understanding of print production processes",
    "Excellent communication and time management skills",
    "Ability to work with brand guidelines and client feedback"
  ];

  const printerRequirements = [
    "Commercial printing facility with modern equipment",
    "Minimum 5 years in commercial printing business",
    "Quality certifications and insurance coverage",
    "Capacity for various print formats and materials",
    "Reliable delivery and logistics capabilities",
    "Commitment to quality standards and deadlines"
  ];

  const testimonials = [
    {
      name: "Sarah Johnson",
      role: "Freelance Designer",
      image: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75",
      quote: "Working with Weditt has been amazing. The projects are diverse and the team is incredibly supportive. I've grown my portfolio significantly!",
      rating: 5
    },
    {
      name: "Mike Chen",
      role: "Print Shop Owner",
      image: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75",
      quote: "Weditt has become one of our most reliable partners. The consistent work flow has helped us grow our business substantially.",
      rating: 5
    }
  ];

  return (
    <div className="min-h-screen bg-[#f5fbff]">
      {/* Hero Section */}
      <section className="bg-gary-900 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl text-gray-500 md:text-6xl font-bold mb-6">
              Join Our Provider Network
            </h1>
            <p className="text-xl mb-8 text-gray-500 max-w-3xl mx-auto">
              Partner with Weditt to grow your business. Whether you're a talented graphic designer or an established printing facility, we have opportunities for you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => setActiveTab('designer')}
                className={`px-8 py-3 rounded-lg font-semibold transition-colors inline-flex items-center justify-center ${
                  activeTab === 'designer'
                    ? 'bg-orange-500 text-white'
                    : 'border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-orange-600'
                }`}
              >
                <Palette className="mr-2 h-5 w-5" />
                For Designers
              </button>
              <button
                onClick={() => setActiveTab('printer')}
                className={`px-8 py-3 rounded-lg font-semibold transition-colors inline-flex items-center justify-center ${
                  activeTab === 'printer'
                    ? 'bg-orange-500 text-white'
                    : 'border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-orange-600'
                }`}
              >
                <Printer className="mr-2 h-5 w-5" />
                For Printers
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content Sections */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Designer Section */}
          {activeTab === 'designer' && (
            <div className="space-y-16">
              {/* Designer Header */}
              <div className="text-center">
                <div className="bg-purple-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Palette className="h-10 w-10 text-purple-600" />
                </div>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                  Join Our Design Team
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Work with established businesses and exciting startups to create stunning print materials. 
                  Grow your portfolio while earning competitive rates in a supportive environment.
                </p>
              </div>

              {/* Designer Benefits */}
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Why Work With Us?</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {designerBenefits.map((benefit, index) => (
                    <div key={index} className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                      <div className="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                        <benefit.icon className="h-6 w-6 text-purple-600" />
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">{benefit.title}</h4>
                      <p className="text-gray-600">{benefit.description}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Designer Requirements */}
              <div className="bg-white rounded-lg shadow-lg p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Requirements</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {designerRequirements.map((requirement, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-1 flex-shrink-0" />
                      <span className="text-gray-700">{requirement}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Designer Application Form */}
              <div className="bg-[#a7bed3] rounded-lg p-8">
                <div className="max-w-2xl mx-auto text-center">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to Join Our Team?</h3>
                  <p className="text-gray-600 mb-6">
                    Send us your portfolio and let's discuss how we can work together to create amazing designs.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <button
                      onClick={() => setShowDesignerForm(true)}
                      className="bg-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors inline-flex items-center justify-center"
                    >
                      <Mail className="mr-2 h-5 w-5" />
                      Apply Now
                    </button>
                    <a
                      href="mailto:<EMAIL>?subject=Portfolio Submission"
                      className="border-2 border-purple-600 text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-purple-600 hover:text-white transition-colors inline-flex items-center justify-center"
                    >
                      Submit Portfolio
                    </a>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Printer Section */}
          {activeTab === 'printer' && (
            <div className="space-y-16">
              {/* Printer Header */}
              <div className="text-center">
                <div className="bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Printer className="h-10 w-10 text-blue-600" />
                </div>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                  Become a Printing Partner
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Join our network of trusted printing facilities. Expand your business with consistent orders 
                  from our growing client base while maintaining your quality standards.
                </p>
              </div>

              {/* Printer Benefits */}
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Partnership Benefits</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {printerBenefits.map((benefit, index) => (
                    <div key={index} className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                      <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                        <benefit.icon className="h-6 w-6 text-blue-600" />
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">{benefit.title}</h4>
                      <p className="text-gray-600">{benefit.description}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Printer Requirements */}
              <div className="bg-white rounded-lg shadow-lg p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Partnership Requirements</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {printerRequirements.map((requirement, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-1 flex-shrink-0" />
                      <span className="text-gray-700">{requirement}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Printer Application Form */}
              <div className="bg-gray-250 rounded-lg p-8">
                <div className="max-w-2xl mx-auto text-center">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to Partner With Us?</h3>
                  <p className="text-gray-600 mb-6">
                    Let's discuss how we can work together to serve our clients and grow both our businesses.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <button
                      onClick={() => setShowRegistrationForm(true)}
                      className="bg-orange-500 text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-500 transition-colors inline-flex items-center justify-center"
                    >
                      <Mail className="mr-2 h-5 w-5" />
                      Partner With Us
                    </button>
                    <a
                      href="tel:******-123-4567"
                      className="border-2 border-orange-500 text-orange-500 px-8 py-3 rounded-lg font-semibold hover:bg-orange-500 hover:text-white transition-colors inline-flex items-center justify-center"
                    >
                      <Phone className="mr-2 h-5 w-5" />
                      Call Us
                    </a>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-gray-250 text-gray-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Have Questions?
            </h2>
            <p className="text-lg text-gray-500 max-w-2xl mx-auto">
              We're here to help you understand our partnership opportunities and answer any questions you may have.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="bg-orange-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Email Us</h3>
              <p className="text-orange-500 mb-2">For Designers:</p>
              <a href="mailto:<EMAIL>" className="text-orange-400 hover:text-orange-250">
                <EMAIL>
              </a>
              <p className="text-gray-600 mb-2 mt-4">For Printers:</p>
              <a href="mailto:<EMAIL>" className="text-orange-400 hover:text-orange-250">
                <EMAIL>
              </a>
            </div>

            <div>
              <div className="bg-orange-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Phone className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Call Us</h3>
              <p className="text-gray-300 mb-2">Partnership Hotline:</p>
              <a href="tel:******-123-4567" className="text-blue-400 hover:text-blue-300 text-lg">
                +1 (*************
              </a>
              <p className="text-gray-300 text-sm mt-2">Mon-Fri, 9AM-6PM EST</p>
            </div>

            <div>
              <div className="bg-orange-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Visit Us</h3>
              <p className="text-gray-300">
                123 Print Street<br />
                Business District<br />
                City, State 12345
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Registration Form Modal */}
      {showRegistrationForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Header */}
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Printer Shop Registration</h2>
                <button
                  onClick={() => setShowRegistrationForm(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              {isSubmitted ? (
                <div className="text-center py-12">
                  <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Registration Submitted!</h3>
                  <p className="text-gray-600">
                    Thank you for your interest in partnering with us. We'll review your application and contact you within 2-3 business days.
                  </p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Business Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <Building className="h-5 w-5 mr-2" />
                      Business Information
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="businessName" className="block text-sm font-medium text-gray-700 mb-2">
                          Business Name <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          id="businessName"
                          name="businessName"
                          value={formData.businessName}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          placeholder="Your printing business name"
                        />
                      </div>

                      <div>
                        <label htmlFor="ownerName" className="block text-sm font-medium text-gray-700 mb-2">
                          Owner/Manager Name <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          id="ownerName"
                          name="ownerName"
                          value={formData.ownerName}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          placeholder="Full name of owner or manager"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Contact Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <User className="h-5 w-5 mr-2" />
                      Contact Information
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                          Email Address <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          placeholder="<EMAIL>"
                        />
                      </div>

                      <div>
                        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                          Phone Number <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          placeholder="(*************"
                        />
                      </div>
                    </div>

                    <div className="mt-4">
                      <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-2">
                        Website (Optional)
                      </label>
                      <input
                        type="url"
                        id="website"
                        name="website"
                        value={formData.website}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        placeholder="https://yourprintshop.com"
                      />
                    </div>
                  </div>

                  {/* Location Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <MapPin className="h-5 w-5 mr-2" />
                      Location Information
                    </h3>
                    
                    <div className="space-y-4">
                      <div>
                        <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                          Street Address <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          id="address"
                          name="address"
                          value={formData.address}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          placeholder="123 Main Street"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-2">
                            City <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            id="city"
                            name="city"
                            value={formData.city}
                            onChange={handleInputChange}
                            required
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                            placeholder="City"
                          />
                        </div>

                        <div>
                          <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-2">
                            State <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            id="state"
                            name="state"
                            value={formData.state}
                            onChange={handleInputChange}
                            required
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                            placeholder="State"
                          />
                        </div>

                        <div>
                          <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 mb-2">
                            ZIP Code <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            id="zipCode"
                            name="zipCode"
                            value={formData.zipCode}
                            onChange={handleInputChange}
                            required
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                            placeholder="12345"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Business Description */}
                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                      About Your Print Shop <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      required
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="Tell us about your printing business, services offered, years in business, equipment, and what makes you unique..."
                    />
                  </div>

                  {/* Google Reviews */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <Star className="h-5 w-5 mr-2" />
                      Google Reviews (Optional)
                    </h3>
                    
                    <div>
                      <label htmlFor="googleReviewsUrl" className="block text-sm font-medium text-gray-700 mb-2">
                        Google Reviews URL
                      </label>
                      <input
                        type="url"
                        id="googleReviewsUrl"
                        name="googleReviewsUrl"
                        value={formData.googleReviewsUrl}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        placeholder="https://g.page/your-business/review"
                      />
                      <p className="text-sm text-gray-500 mt-1">
                        Link to your Google Business Profile reviews page (helps us verify your reputation)
                      </p>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="pt-6 border-t border-gray-200">
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full bg-orange-500 text-white py-3 px-6 rounded-lg hover:bg-orange-600 transition-colors font-semibold flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                          <span>Submitting Application...</span>
                        </>
                      ) : (
                        <>
                          <CheckCircle className="h-5 w-5" />
                          <span>Submit Registration</span>
                        </>
                      )}
                    </button>
                    
                    <p className="text-sm text-gray-500 text-center mt-3">
                      By submitting this form, you agree to our partnership terms and conditions.
                    </p>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Designer Registration Form Modal */}
      {showDesignerForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Header */}
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Designer Registration</h2>
                <button
                  onClick={() => setShowDesignerForm(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              {isDesignerSubmitted ? (
                <div className="text-center py-12">
                  <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Application Submitted!</h3>
                  <p className="text-gray-600">
                    Thank you for your interest in joining our design team. We'll review your application and portfolio, then contact you within 2-3 business days.
                  </p>
                </div>
              ) : (
                <form onSubmit={handleDesignerSubmit} className="space-y-6">
                  {/* Personal Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <User className="h-5 w-5 mr-2" />
                      Personal Information
                    </h3>
                    
                    <div>
                      <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-2">
                        Full Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="fullName"
                        name="fullName"
                        value={designerFormData.fullName}
                        onChange={handleDesignerInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        placeholder="Your full name"
                      />
                    </div>
                  </div>

                  {/* Contact Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <Mail className="h-5 w-5 mr-2" />
                      Contact Information
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="designerEmail" className="block text-sm font-medium text-gray-700 mb-2">
                          Email Address <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="email"
                          id="designerEmail"
                          name="email"
                          value={designerFormData.email}
                          onChange={handleDesignerInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          placeholder="<EMAIL>"
                        />
                      </div>

                      <div>
                        <label htmlFor="designerPhone" className="block text-sm font-medium text-gray-700 mb-2">
                          Phone Number <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="tel"
                          id="designerPhone"
                          name="phone"
                          value={designerFormData.phone}
                          onChange={handleDesignerInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          placeholder="(*************"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Professional Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <Palette className="h-5 w-5 mr-2" />
                      Professional Information
                    </h3>
                    
                    <div className="space-y-4">
                      <div>
                        <label htmlFor="designerWebsite" className="block text-sm font-medium text-gray-700 mb-2">
                          Website (Optional)
                        </label>
                        <input
                          type="url"
                          id="designerWebsite"
                          name="website"
                          value={designerFormData.website}
                          onChange={handleDesignerInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          placeholder="https://yourwebsite.com"
                        />
                      </div>

                      <div>
                        <label htmlFor="portfolio" className="block text-sm font-medium text-gray-700 mb-2">
                          Portfolio URL <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="url"
                          id="portfolio"
                          name="portfolio"
                          value={designerFormData.portfolio}
                          onChange={handleDesignerInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          placeholder="https://behance.net/yourportfolio or https://dribbble.com/yourwork"
                        />
                        <p className="text-sm text-gray-500 mt-1">
                          Link to your online portfolio (Behance, Dribbble, personal website, etc.)
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* About Designer */}
                  <div>
                    <label htmlFor="designerDescription" className="block text-sm font-medium text-gray-700 mb-2">
                      About You <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      id="designerDescription"
                      name="description"
                      value={designerFormData.description}
                      onChange={handleDesignerInputChange}
                      required
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder="Tell us about your design experience, specialties, years in the field, favorite types of projects, and what makes you unique as a designer..."
                    />
                  </div>

                  {/* Submit Button */}
                  <div className="pt-6 border-t border-gray-200">
                    <button
                      type="submit"
                      disabled={isDesignerSubmitting}
                      className="w-full bg-purple-600 text-white py-3 px-6 rounded-lg hover:bg-purple-700 transition-colors font-semibold flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isDesignerSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                          <span>Submitting Application...</span>
                        </>
                      ) : (
                        <>
                          <CheckCircle className="h-5 w-5" />
                          <span>Submit Application</span>
                        </>
                      )}
                    </button>
                    
                    <p className="text-sm text-gray-500 text-center mt-3">
                      By submitting this form, you agree to our freelancer terms and conditions.
                    </p>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProviderServices;