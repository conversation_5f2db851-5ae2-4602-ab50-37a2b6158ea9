import { PrismaClient, Service, ServicePricingType } from '@prisma/client';
import {
  CreateServiceData,
  UpdateServiceData,
  ServiceListQuery,
  ServiceListResponse,
  ServiceSummary,
  ServiceDetail,
  ServiceSearchQuery,
  ServiceSearchResult,
} from '../types/service';
import { ServiceMapper } from '../utils/ServiceMapper';

// Repository interface for dependency inversion - Core service operations only
export interface ICoreServiceRepository {
  // Service CRUD operations
  findServiceById(id: string, includeFormFields?: boolean): Promise<ServiceDetail | null>;
  createService(serviceData: CreateServiceData): Promise<Service>;
  updateService(id: string, data: UpdateServiceData): Promise<Service>;
  deleteService(id: string): Promise<boolean>;

  // Service list and search operations
  getServices(query: ServiceListQuery): Promise<ServiceListResponse>;
  searchServices(query: ServiceSearchQuery): Promise<ServiceSearchResult>;
  getServicesByCategory(categoryId: string): Promise<ServiceSummary[]>;

  // Utility operations
  serviceExists(id: string): Promise<boolean>;
}

// Prisma implementation of the repository - Core service operations only
export class CoreServiceRepository implements ICoreServiceRepository {
  constructor(private prisma: PrismaClient) {}

  async findServiceById(id: string, includeFormFields: boolean = true): Promise<ServiceDetail | null> {
    const service = await this.prisma.service.findUnique({
      where: { id },
      include: {
        service_categories: true,
        ...(includeFormFields && {
          formFields: {
            where: { isActive: true },
            include: {
              service_field_options: {
                where: { isActive: true },
                orderBy: { sortOrder: 'asc' },
              },
            },
            orderBy: { sortOrder: 'asc' },
          }
        }),
        providers: {
          where: { isActive: true },
          include: {
            provider: {
              select: {
                id: true,
                businessName: true,
                isVerified: true,
                isActive: true,
              },
            },
          },
        },
      },
    });

    if (!service) return null;
    return ServiceMapper.toServiceDetail(service);
  }

  async createService(serviceData: CreateServiceData): Promise<Service> {
    return this.prisma.service.create({
      data: {
        id: serviceData.id,
        name: serviceData.name,
        description: serviceData.description,
        detailedDesc: serviceData.detailedDesc,
        features: serviceData.features,
        notes: serviceData.notes,
        categoryId: serviceData.categoryId,
        image: serviceData.image,
        basePrice: serviceData.basePrice,
        pricingType: serviceData.pricingType,
        isActive: serviceData.isActive,
        sortOrder: serviceData.sortOrder,
      },
    });
  }

  async updateService(id: string, data: UpdateServiceData): Promise<Service> {
    return this.prisma.service.update({
      where: { id },
      data,
    });
  }

  async deleteService(id: string): Promise<boolean> {
    try {
      await this.prisma.service.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  async getServices(query: ServiceListQuery): Promise<ServiceListResponse> {
    const {
      page = 1,
      limit = 20,
      search,
      categoryId,
      pricingType,
      isActive = true,
      sortBy = 'sortOrder',
      sortOrder = 'asc',
      minPrice,
      maxPrice,
    } = query;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { features: { has: search } },
      ];
    }

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (pricingType) {
      where.pricingType = pricingType;
    }

    if (typeof isActive === 'boolean') {
      where.isActive = isActive;
    }

    if (minPrice !== undefined || maxPrice !== undefined) {
      where.basePrice = {};
      if (minPrice !== undefined) where.basePrice.gte = minPrice;
      if (maxPrice !== undefined) where.basePrice.lte = maxPrice;
    }

    // Get total count
    const total = await this.prisma.service.count({ where });

    // Get services with pagination
    const services = await this.prisma.service.findMany({
      where,
      include: {
        service_categories: {
          select: {
            name: true,
          },
        },
        providers: {
          where: { isActive: true },
          select: { id: true },
        },
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      services: ServiceMapper.toServiceSummaryArray(services),
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  async searchServices(query: ServiceSearchQuery): Promise<ServiceSearchResult> {
    const {
      q,
      category,
      minPrice,
      maxPrice,
      pricingType,
      features,
      sortBy = 'name',
      sortOrder = 'asc',
    } = query;

    // Build where clause
    const where: any = { isActive: true };

    if (q) {
      where.OR = [
        { name: { contains: q, mode: 'insensitive' } },
        { description: { contains: q, mode: 'insensitive' } },
        { detailedDesc: { contains: q, mode: 'insensitive' } },
      ];
    }

    if (category) {
      where.service_categories = {
        route: category,
      };
    }

    if (pricingType) {
      where.pricingType = pricingType;
    }

    if (features && features.length > 0) {
      where.features = {
        hasEvery: features,
      };
    }

    if (minPrice !== undefined || maxPrice !== undefined) {
      where.basePrice = {};
      if (minPrice !== undefined) where.basePrice.gte = minPrice;
      if (maxPrice !== undefined) where.basePrice.lte = maxPrice;
    }

    // Get services
    const services = await this.prisma.service.findMany({
      where,
      include: {
        service_categories: true,
        formFields: {
          where: { isActive: true },
          include: {
            service_field_options: {
              where: { isActive: true },
              orderBy: { sortOrder: 'asc' },
            },
          },
          orderBy: { sortOrder: 'asc' },
        },
        providers: {
          where: { isActive: true },
          include: {
            provider: {
              select: {
                id: true,
                businessName: true,
                isVerified: true,
                isActive: true,
              },
            },
          },
        },
      },
      orderBy: { [sortBy]: sortOrder },
    });

    // Get filters
    const [categories, priceRange] = await Promise.all([
      this.prisma.service_categories.findMany({
        where: { isActive: true },
        include: {
          _count: {
            select: { services: true },
          },
        },
      }),
      this.prisma.service.aggregate({
        where: { isActive: true },
        _min: { basePrice: true },
        _max: { basePrice: true },
      }),
    ]);

    // Extract features for filtering
    const allFeatures = services.reduce((acc, service) => {
      service.features.forEach(feature => {
        if (!acc[feature]) acc[feature] = 0;
        acc[feature]++;
      });
      return acc;
    }, {} as Record<string, number>);

    return {
      services: ServiceMapper.toServiceDetailArray(services),
      total: services.length,
      query,
      filters: {
        categories: categories.map(cat => ServiceMapper.toCategoryFilter(cat)),
        priceRange: ServiceMapper.toPriceRangeFilter(priceRange),
        features: Object.entries(allFeatures).map(([feature, count]) => ServiceMapper.toFeatureFilter(feature, count)),
      },
    };
  }

  async getServicesByCategory(categoryId: string): Promise<ServiceSummary[]> {
    const services = await this.prisma.service.findMany({
      where: {
        categoryId,
        isActive: true,
      },
      include: {
        service_categories: {
          select: {
            name: true,
          },
        },
        providers: {
          where: { isActive: true },
          select: { id: true },
        },
      },
      orderBy: { sortOrder: 'asc' },
    });

    return ServiceMapper.toServiceSummaryArray(services);
  }

  async serviceExists(id: string): Promise<boolean> {
    const count = await this.prisma.service.count({
      where: { id },
    });
    return count > 0;
  }
}