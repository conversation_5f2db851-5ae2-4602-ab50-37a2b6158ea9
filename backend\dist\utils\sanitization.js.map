{"version": 3, "file": "sanitization.js", "sourceRoot": "", "sources": ["../../src/utils/sanitization.ts"], "names": [], "mappings": ";;AAmIA,kDAMC;AAMD,oCAkBC;AAMD,0CAqBC;AAMD,sCAMC;AAMD,wCAiBC;AAvND,MAAM,wBAAwB,GAAG;IAE/B,WAAW;IACX,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;IACP,aAAa;IAGb,MAAM;IACN,mBAAmB;IACnB,eAAe;IACf,eAAe;IACf,MAAM;IACN,MAAM;IAGN,kBAAkB;IAClB,kBAAkB;IAClB,OAAO;IACP,QAAQ;IAGR,QAAQ;IACR,UAAU;CACX,CAAC;AAGF,MAAM,gBAAgB,GAAG;IACvB,UAAU;IACV,iBAAiB;IACjB,aAAa;IACb,iBAAiB;IACjB,OAAO;IACP,cAAc;IACd,aAAa;IACb,QAAQ;IACR,QAAQ;IACR,YAAY;IACZ,WAAW;IACX,KAAK;IACL,WAAW;IACX,cAAc;IACd,eAAe;IACf,QAAQ;IACR,KAAK;IACL,sBAAsB;IACtB,YAAY;IACZ,YAAY;IACZ,KAAK;IACL,KAAK;IACL,YAAY;IACZ,eAAe;IACf,eAAe;IACf,MAAM;IACN,OAAO;CACR,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;AAKpC,SAAS,gBAAgB,CAAC,SAAiB;IACzC,MAAM,cAAc,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;IAG/C,IAAI,gBAAgB,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;QAC9C,OAAO,IAAI,CAAC;IACd,CAAC;IAGD,OAAO,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;AAChF,CAAC;AAKD,SAAS,YAAY,CAAC,GAAQ,EAAE,WAAmB,EAAE,EAAE,eAAuB,CAAC;IAE7E,IAAI,YAAY,IAAI,QAAQ,EAAE,CAAC;QAC7B,OAAO,qBAAqB,CAAC;IAC/B,CAAC;IAGD,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;QACtC,OAAO,GAAG,CAAC;IACb,CAAC;IAGD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,OAAO,GAAG,CAAC;IACb,CAAC;IAGD,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,OAAO,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;IAGD,IAAI,GAAG,YAAY,IAAI,EAAE,CAAC;QACxB,OAAO,GAAG,CAAC;IACb,CAAC;IAGD,MAAM,SAAS,GAAQ,EAAE,CAAC;IAE1B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QAC/C,IAAI,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1B,SAAS,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;QAChC,CAAC;aAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACvD,SAAS,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;QACnE,CAAC;aAAM,CAAC;YACN,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACzB,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAMD,SAAgB,mBAAmB,CAAC,IAAS;IAC3C,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;AAC5B,CAAC;AAMD,SAAgB,YAAY,CAAC,IAAS;IACpC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,EACJ,QAAQ,EACR,kBAAkB,EAClB,yBAAyB,EACzB,sBAAsB,EACtB,6BAA6B,EAC7B,aAAa,EACb,WAAW,EACX,QAAQ,EACR,GAAG,aAAa,EACjB,GAAG,IAAI,CAAC;IAET,OAAO,aAAa,CAAC;AACvB,CAAC;AAMD,SAAgB,eAAe,CAAC,OAAY;IAC1C,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAC5C,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,MAAM,uBAAuB,GAAG;QAC9B,gBAAgB;QAChB,SAAS;QACT,YAAY;QACZ,SAAS;QACT,SAAS;KACV,CAAC;IAEF,MAAM,SAAS,GAAQ,EAAE,CAAC;IAE1B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QACnD,MAAM,WAAW,GAAG,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/E,SAAS,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC;IACtD,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAMD,SAAgB,aAAa,CAAC,KAAU;IACtC,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QACxC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC;AAC7B,CAAC;AAMD,SAAgB,cAAc,CAAC,GAAQ,EAAE,UAGrC,EAAE;IACJ,MAAM,EAAE,QAAQ,GAAG,EAAE,EAAE,qBAAqB,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;IAG9D,MAAM,cAAc,GAAG,CAAC,GAAG,gBAAgB,CAAC,CAAC;IAC7C,gBAAgB,CAAC,IAAI,CAAC,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAE1E,IAAI,CAAC;QACH,OAAO,YAAY,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IACrC,CAAC;YAAS,CAAC;QAET,gBAAgB,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;QAChD,gBAAgB,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;IAC3C,CAAC;AACH,CAAC;AAED,kBAAe;IACb,mBAAmB;IACnB,YAAY;IACZ,eAAe;IACf,aAAa;IACb,cAAc;IACd,gBAAgB;CACjB,CAAC"}