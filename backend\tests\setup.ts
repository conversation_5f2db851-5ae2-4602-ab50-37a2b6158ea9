/**
 * Jest Test Setup
 *
 * Global test configuration and setup for the PrintWeditt backend tests.
 * This file is executed before each test file.
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// Jest globals
declare global {
  var __PRISMA__: PrismaClient;
  var __TEST_USER_EMAIL__: string;
  var __TEST_USER_ID__: string;
}

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Global test configuration
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-that-is-at-least-32-characters-long-for-testing-only';
process.env.JWT_REFRESH_SECRET = 'test-refresh-secret-key-that-is-at-least-32-characters-long-for-testing-only';
process.env.DATABASE_URL =
  process.env.DATABASE_URL || 'postgresql://test_user:test_pass@localhost:5432/printweditt_test';

// Global test variables
declare global {
  var __PRISMA__: PrismaClient;
  var __TEST_USER_EMAIL__: string;
  var __TEST_USER_ID__: string;
}

// Test database instance
global.__PRISMA__ = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

// Test user data
global.__TEST_USER_EMAIL__ = '<EMAIL>';
global.__TEST_USER_ID__ = '';

// Setup before all tests
beforeAll(async () => {
  console.log('🔧 Setting up test environment...');

  try {
    // Connect to test database
    await global.__PRISMA__.$connect();
    console.log('✅ Connected to test database');

    // Clean test database
    await cleanTestDatabase();
    console.log('✅ Test database cleaned');
  } catch (error) {
    console.error('❌ Test setup failed:', error);
    process.exit(1);
  }
});

// Cleanup after all tests
afterAll(async () => {
  console.log('🧹 Cleaning up test environment...');

  try {
    // Clean test database
    await cleanTestDatabase();
    console.log('✅ Test database cleaned');

    // Disconnect from database
    await global.__PRISMA__.$disconnect();
    console.log('✅ Disconnected from test database');
  } catch (error) {
    console.error('❌ Test cleanup failed:', error);
  }
});

// Clean database between test suites
beforeEach(async () => {
  // Clean user-related data for fresh test state
  await cleanUserData();
});

/**
 * Clean the entire test database
 */
async function cleanTestDatabase(): Promise<void> {
  const tablenames = await global.__PRISMA__.$queryRaw<Array<{ tablename: string }>>`
    SELECT tablename FROM pg_tables WHERE schemaname='public'
  `;

  const tables = tablenames
    .map(({ tablename }) => tablename)
    .filter(name => name !== '_prisma_migrations')
    .map(name => `"public"."${name}"`)
    .join(', ');

  if (tables) {
    try {
      await global.__PRISMA__.$executeRawUnsafe(`TRUNCATE TABLE ${tables} RESTART IDENTITY CASCADE;`);
    } catch (error) {
      console.log('Note: Some tables may not exist yet, this is normal during initial setup');
    }
  }
}

/**
 * Clean user-related data for fresh test state
 */
async function cleanUserData(): Promise<void> {
  try {
    // Delete in correct order to respect foreign key constraints
    await global.__PRISMA__.user_sessions.deleteMany({});
    await global.__PRISMA__.user_addresses.deleteMany({});
    await global.__PRISMA__.admins.deleteMany({});
    await global.__PRISMA__.user.deleteMany({});

    // Also clean any other related tables
    await global.__PRISMA__.provider.deleteMany({});
    await global.__PRISMA__.order.deleteMany({});
  } catch (error) {
    // Ignore errors during cleanup as tables might not exist
    console.log('Cleanup warning:', error);
  }
}

// Export helper functions for tests
export { cleanTestDatabase, cleanUserData };

// Mock console methods for cleaner test output
const originalConsoleError = console.error;
console.error = (...args: any[]) => {
  // Only show errors if they're not expected test errors
  if (!args[0]?.toString().includes('Test setup') && !args[0]?.toString().includes('Note:')) {
    originalConsoleError(...args);
  }
};

// Extend Jest timeout for integration tests
jest.setTimeout(30000);
