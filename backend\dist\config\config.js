"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigurationManager = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const zod_1 = require("zod");
const result = dotenv_1.default.config();
if (result.error) {
    console.error('❌ Error loading .env file:', result.error.message);
    process.exit(1);
}
const DatabaseConfigSchema = zod_1.z.object({
    url: zod_1.z.string().url('Invalid database URL'),
    maxConnections: zod_1.z.number().int().positive().default(10),
    idleTimeout: zod_1.z.number().int().positive().default(30000),
    connectionTimeout: zod_1.z.number().int().positive().default(2000),
});
const JWTConfigSchema = zod_1.z.object({
    secret: zod_1.z.string().min(32, 'JWT secret must be at least 32 characters'),
    refreshSecret: zod_1.z
        .string()
        .min(32, 'JWT refresh secret must be at least 32 characters'),
    accessExpiresIn: zod_1.z.string().default('15m'),
    refreshExpiresIn: zod_1.z.string().default('7d'),
    issuer: zod_1.z.string().default('printweditt'),
    audience: zod_1.z.string().default('printweditt-users'),
});
const ServerConfigSchema = zod_1.z.object({
    port: zod_1.z.number().int().positive().default(3000),
    host: zod_1.z.string().default('localhost'),
    nodeEnv: zod_1.z.enum(['development', 'production', 'test']).default('development'),
    apiBaseUrl: zod_1.z.string().url('Invalid API base URL'),
    frontendUrl: zod_1.z.string().url('Invalid frontend URL'),
    corsOrigin: zod_1.z.string().url('Invalid CORS origin'),
});
const RedisConfigSchema = zod_1.z.object({
    url: zod_1.z.string().url('Invalid Redis URL'),
    password: zod_1.z.string().optional(),
    db: zod_1.z.number().int().min(0).max(15).default(0),
    keyPrefix: zod_1.z.string().default('printweditt:'),
    retryDelayOnFailover: zod_1.z.number().int().positive().default(100),
    maxRetriesPerRequest: zod_1.z.number().int().positive().default(3),
});
const AWSConfigSchema = zod_1.z.object({
    accessKeyId: zod_1.z.string().min(1, 'AWS access key ID is required'),
    secretAccessKey: zod_1.z.string().min(1, 'AWS secret access key is required'),
    region: zod_1.z.string().default('us-east-1'),
    s3Bucket: zod_1.z.string().min(1, 'S3 bucket name is required'),
    cloudfrontUrl: zod_1.z.string().url('Invalid CloudFront URL').optional(),
});
const EmailConfigSchema = zod_1.z.object({
    service: zod_1.z.string().default('gmail'),
    host: zod_1.z.string().default('smtp.gmail.com'),
    port: zod_1.z.number().int().positive().default(587),
    secure: zod_1.z.boolean().default(false),
    user: zod_1.z.string().email('Invalid email address'),
    password: zod_1.z.string().min(1, 'Email password is required'),
    from: zod_1.z.string().email('Invalid from email address'),
});
const LoggingConfigSchema = zod_1.z.object({
    level: zod_1.z.enum(['error', 'warn', 'info', 'debug']).default('info'),
    file: zod_1.z.string().optional(),
    enableConsole: zod_1.z.boolean().default(true),
    enableFile: zod_1.z.boolean().default(false),
    maxFileSize: zod_1.z.number().int().positive().default(10485760),
    maxFiles: zod_1.z.number().int().positive().default(5),
    format: zod_1.z.enum(['json', 'simple']).default('json'),
});
const SecurityConfigSchema = zod_1.z.object({
    bcryptRounds: zod_1.z.number().int().min(10).max(14).default(12),
    cookieSecret: zod_1.z
        .string()
        .min(32, 'Cookie secret must be at least 32 characters'),
    rateLimitWindowMs: zod_1.z.number().int().positive().default(900000),
    rateLimitMaxRequests: zod_1.z.number().int().positive().default(100),
    rateLimitAuthMax: zod_1.z.number().int().positive().default(5),
    maxFileSize: zod_1.z.number().int().positive().default(52428800),
    allowedFileTypes: zod_1.z
        .array(zod_1.z.string())
        .default(['jpg', 'jpeg', 'png', 'webp', 'pdf', 'ai', 'psd']),
});
const GoogleOAuthConfigSchema = zod_1.z.object({
    clientId: zod_1.z.string().min(1, 'Google client ID is required'),
    clientSecret: zod_1.z.string().min(1, 'Google client secret is required'),
    callbackUrl: zod_1.z.string().url('Invalid Google callback URL'),
});
const AppConfigSchema = zod_1.z.object({
    database: DatabaseConfigSchema,
    jwt: JWTConfigSchema,
    server: ServerConfigSchema,
    redis: RedisConfigSchema,
    aws: AWSConfigSchema,
    email: EmailConfigSchema,
    logging: LoggingConfigSchema,
    security: SecurityConfigSchema,
    googleOAuth: GoogleOAuthConfigSchema,
    enableSwagger: zod_1.z.boolean().default(true),
    enableLogging: zod_1.z.boolean().default(true),
    enableCors: zod_1.z.boolean().default(true),
});
class ConfigurationManager {
    config;
    isInitialized = false;
    constructor() {
        this.config = this.loadConfiguration();
    }
    loadConfiguration() {
        try {
            const rawConfig = {
                database: {
                    url: process.env.DATABASE_URL,
                    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '10'),
                    idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
                    connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '2000'),
                },
                jwt: {
                    secret: process.env.JWT_SECRET,
                    refreshSecret: process.env.JWT_REFRESH_SECRET,
                    accessExpiresIn: process.env.JWT_ACCESS_EXPIRES_IN || '15m',
                    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
                    issuer: process.env.JWT_ISSUER || 'printweditt',
                    audience: process.env.JWT_AUDIENCE || 'printweditt-users',
                },
                server: {
                    port: parseInt(process.env.PORT || '3000'),
                    host: process.env.HOST || 'localhost',
                    nodeEnv: process.env.NODE_ENV ||
                        'development',
                    apiBaseUrl: process.env.API_BASE_URL,
                    frontendUrl: process.env.FRONTEND_URL,
                    corsOrigin: process.env.CORS_ORIGIN,
                },
                redis: {
                    url: process.env.REDIS_URL,
                    password: process.env.REDIS_PASSWORD,
                    db: parseInt(process.env.REDIS_DB || '0'),
                    keyPrefix: process.env.REDIS_KEY_PREFIX || 'printweditt:',
                    retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY || '100'),
                    maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES || '3'),
                },
                aws: {
                    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
                    region: process.env.AWS_REGION || 'us-east-1',
                    s3Bucket: process.env.AWS_S3_BUCKET,
                    cloudfrontUrl: process.env.AWS_CLOUDFRONT_URL,
                },
                email: {
                    service: process.env.EMAIL_SERVICE || 'gmail',
                    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
                    port: parseInt(process.env.EMAIL_PORT || '587'),
                    secure: process.env.EMAIL_SECURE === 'true',
                    user: process.env.EMAIL_USER,
                    password: process.env.EMAIL_PASSWORD,
                    from: process.env.EMAIL_FROM,
                },
                logging: {
                    level: process.env.LOG_LEVEL ||
                        'info',
                    file: process.env.LOG_FILE,
                    enableConsole: process.env.LOG_ENABLE_CONSOLE !== 'false',
                    enableFile: process.env.LOG_ENABLE_FILE === 'true',
                    maxFileSize: parseInt(process.env.LOG_MAX_FILE_SIZE || '10485760'),
                    maxFiles: parseInt(process.env.LOG_MAX_FILES || '5'),
                    format: process.env.LOG_FORMAT || 'json',
                },
                security: {
                    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),
                    cookieSecret: process.env.COOKIE_SECRET,
                    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'),
                    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
                    rateLimitAuthMax: parseInt(process.env.RATE_LIMIT_AUTH_MAX || '5'),
                    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '52428800'),
                    allowedFileTypes: process.env.ALLOWED_FILE_TYPES?.split(',') || [
                        'jpg',
                        'jpeg',
                        'png',
                        'webp',
                        'pdf',
                        'ai',
                        'psd',
                    ],
                },
                googleOAuth: {
                    clientId: process.env.GOOGLE_CLIENT_ID,
                    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
                    callbackUrl: process.env.GOOGLE_CALLBACK_URL,
                },
                enableSwagger: process.env.ENABLE_SWAGGER !== 'false',
                enableLogging: process.env.ENABLE_LOGGING !== 'false',
                enableCors: process.env.ENABLE_CORS !== 'false',
            };
            const validatedConfig = AppConfigSchema.parse(rawConfig);
            console.log('✅ Configuration loaded and validated successfully');
            return validatedConfig;
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                console.error('❌ Configuration validation failed:');
                console.error('Validation errors:', error.format());
            }
            else {
                console.error('❌ Failed to load configuration:', error);
            }
            process.exit(1);
        }
    }
    getConfig() {
        if (!this.isInitialized) {
            this.isInitialized = true;
        }
        return this.config;
    }
    getDatabaseConfig() {
        return this.config.database;
    }
    getJWTConfig() {
        return this.config.jwt;
    }
    getServerConfig() {
        return this.config.server;
    }
    getRedisConfig() {
        return this.config.redis;
    }
    getAWSConfig() {
        return this.config.aws;
    }
    getEmailConfig() {
        return this.config.email;
    }
    getLoggingConfig() {
        return this.config.logging;
    }
    getSecurityConfig() {
        return this.config.security;
    }
    getGoogleOAuthConfig() {
        return this.config.googleOAuth;
    }
    isDevelopment() {
        return this.config.server.nodeEnv === 'development';
    }
    isProduction() {
        return this.config.server.nodeEnv === 'production';
    }
    isTest() {
        return this.config.server.nodeEnv === 'test';
    }
    reload() {
        console.log('🔄 Reloading configuration...');
        this.config = this.loadConfiguration();
    }
}
exports.ConfigurationManager = ConfigurationManager;
//# sourceMappingURL=config.js.map