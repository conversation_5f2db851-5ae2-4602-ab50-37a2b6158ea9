import { ProviderDetail, ProviderServiceSummary } from '../types/provider';

type WeeklyHours = {
  monday: { open: string; close: string; closed: boolean };
  tuesday: { open: string; close: string; closed: boolean };
  wednesday: { open: string; close: string; closed: boolean };
  thursday: { open: string; close: string; closed: boolean };
  friday: { open: string; close: string; closed: boolean };
  saturday: { open: string; close: string; closed: boolean };
  sunday: { open: string; close: string; closed: boolean };
};

const dayIndexToKey = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'] as const;

function buildWeeklyHours(hours: ProviderDetail['operatingHours']): WeeklyHours {
  const base: WeeklyHours = {
    monday: { open: '00:00', close: '00:00', closed: true },
    tuesday: { open: '00:00', close: '00:00', closed: true },
    wednesday: { open: '00:00', close: '00:00', closed: true },
    thursday: { open: '00:00', close: '00:00', closed: true },
    friday: { open: '00:00', close: '00:00', closed: true },
    saturday: { open: '00:00', close: '00:00', closed: true },
    sunday: { open: '00:00', close: '00:00', closed: true },
  };
  for (const h of hours || []) {
    const key = dayIndexToKey[h.dayOfWeek] || 'sunday';
    const closed = h.openTime === h.closeTime || (h.openTime === '00:00' && h.closeTime === '00:00');
    // @ts-ignore index by dynamic key
    base[key] = { open: h.openTime, close: h.closeTime, closed };
  }
  return base;
}

function mapServices(services?: ProviderServiceSummary[]) {
  return (services || []).map(s => ({
    serviceId: s.serviceId,
    isActive: s.isActive,
    agreedToPrice: false,
    agreedDate: s.createdAt.toISOString(),
    notes: s.description || undefined,
  }));
}

export function mapProviderDetailToFrontend(p: ProviderDetail) {
  return {
    id: p.id,
    businessName: p.businessName,
    logo: null as string | null,
    contactName: null as string | null,
    email: p.email || null,
    phone: p.phone || null,
    address: null as string | null,
    city: null as string | null,
    state: null as string | null,
    zipCode: null as string | null,
    coordinates: null as { lat: number; lng: number } | null,
    website: p.website || null,
    description: p.description || null,
    serviceAreas: (p.serviceAreas || []).map(a => a.zipCode),
    googleReviewsId: null as string | null,
    averageRating: p.averageRating || 0,
    reviewCount: p.reviewCount || 0,
    operatingHours: buildWeeklyHours(p.operatingHours || []),
    isVerified: p.isVerified,
    isActive: p.isActive,
    joinedDate: p.createdAt.toISOString(),
    services: mapServices(p.services),
    totalServicesOffered: (p.services || []).filter(s => s.isActive).length,
  };
}
