import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '../types/auth';
export interface TimedRequest extends Request {
    startTime?: number;
}
export declare const requestLogger: (req: TimedRequest & AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const logEvent: (eventName: string, getData?: (req: Request) => any) => (req: Request, res: Response, next: NextFunction) => void;
export declare const logAuthEvent: (eventType: "LOGIN" | "LOGOUT" | "REGISTER" | "PASSWORD_CHANGE" | "TOKEN_REFRESH" | "PASSWORD_RESET_REQUEST" | "PASSWORD_RESET_CONFIRM" | "EMAIL_VERIFICATION_SEND" | "EMAIL_VERIFICATION_CONFIRM") => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const logError: (err: Error, req: Request, res: Response, next: NextFunction) => void;
export declare const performanceLogger: (req: TimedRequest, res: Response, next: NextFunction) => void;
declare const _default: {
    requestLogger: (req: TimedRequest & AuthenticatedRequest, res: Response, next: NextFunction) => void;
    logEvent: (eventName: string, getData?: (req: Request) => any) => (req: Request, res: Response, next: NextFunction) => void;
    logAuthEvent: (eventType: "LOGIN" | "LOGOUT" | "REGISTER" | "PASSWORD_CHANGE" | "TOKEN_REFRESH" | "PASSWORD_RESET_REQUEST" | "PASSWORD_RESET_CONFIRM" | "EMAIL_VERIFICATION_SEND" | "EMAIL_VERIFICATION_CONFIRM") => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    logError: (err: Error, req: Request, res: Response, next: NextFunction) => void;
    performanceLogger: (req: TimedRequest, res: Response, next: NextFunction) => void;
};
export default _default;
//# sourceMappingURL=requestLogger.d.ts.map