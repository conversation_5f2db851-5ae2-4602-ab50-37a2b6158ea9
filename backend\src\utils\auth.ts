import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import {
	TokenPair,
	JWTPayload,
	SecurityEvent,
	SecurityEventType,
} from '../types/auth';
import {generateAccessToken, generateRefreshToken} from '../middleware/auth';
import {databaseService} from '../services';
import {sanitizeUser as sanitizeUserData} from '../utils/sanitization';

// Lazy database client access to avoid initialization issues
const getPrismaClient = () => databaseService.getClient();

// Hash password using bcrypt
export const hashPassword = async (
	password: string,
	bcryptRounds: number = 12
): Promise<string> => {
	return bcrypt.hash(password, bcryptRounds);
};

// Verify password against hash
export const verifyPassword = async (
	password: string,
	hash: string
): Promise<boolean> => {
	return bcrypt.compare(password, hash);
};

// Generate secure random token
export const generateSecureToken = (length: number = 32): string => {
	return crypto.randomBytes(length).toString('hex');
};

// Generate verification token with expiration
export const generateVerificationToken = (): {token: string; expires: Date} => {
	const token = generateSecureToken(32);
	const expires = new Date();
	expires.setHours(expires.getHours() + 24); // 24 hours expiration

	return {token, expires};
};

// Generate password reset token with expiration
export const generatePasswordResetToken = (): {
	token: string;
	expires: Date;
} => {
	const token = generateSecureToken(32);
	const expires = new Date();
	expires.setHours(expires.getHours() + 1); // 1 hour expiration

	return {token, expires};
};

// Generate token pair for authentication
export const generateTokenPair = async (
	userId: string,
	email: string,
	role: any,
	ipAddress: string,
	userAgent: string
): Promise<TokenPair> => {
	const payload: JWTPayload = {
		userId,
		email,
		role,
	};

	const accessToken = generateAccessToken(payload);
	const refreshToken = generateRefreshToken(payload);

	// Store refresh token in database
	const expiresAt = new Date();
	expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

	await getPrismaClient().user_sessions.create({
		data: {
			id: generateSecureToken(16),
			userId,
			refreshToken,
			expiresAt,
			ipAddress: ipAddress.substring(0, 45), // Ensure it fits in VARCHAR(45)
			userAgent: userAgent.substring(0, 500), // Ensure it fits in VARCHAR(500)
			isActive: true,
		},
	});

	return {accessToken, refreshToken};
};

// Revoke refresh token
export const revokeRefreshToken = async (
	refreshToken: string
): Promise<void> => {
	await getPrismaClient().user_sessions.updateMany({
		where: {refreshToken},
		data: {
			isActive: false,
			revokedAt: new Date(),
		},
	});
};

// Revoke all user sessions
export const revokeAllUserSessions = async (userId: string): Promise<void> => {
	await getPrismaClient().user_sessions.updateMany({
		where: {userId},
		data: {
			isActive: false,
			revokedAt: new Date(),
		},
	});
};

// Check if refresh token is valid
export const isRefreshTokenValid = async (
	refreshToken: string
): Promise<boolean> => {
	const session = await getPrismaClient().user_sessions.findUnique({
		where: {refreshToken},
	});

	if (!session) return false;
	if (!session.isActive) return false;
	if (session.expiresAt < new Date()) return false;

	return true;
};

// Clean up expired sessions
export const cleanupExpiredSessions = async (): Promise<number> => {
	const result = await getPrismaClient().user_sessions.deleteMany({
		where: {
			OR: [
				{expiresAt: {lt: new Date()}},
				{
					isActive: false,
					revokedAt: {lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)},
				}, // 30 days old
			],
		},
	});

	return result.count;
};

// Account locking utilities
export const incrementLoginAttempts = async (userId: string): Promise<void> => {
	const user = await getPrismaClient().user.findUnique({
		where: {id: userId},
		select: {loginAttempts: true},
	});

	if (!user) return;

	const newAttempts = user.loginAttempts + 1;
	const maxAttempts = 5;

	const updateData: any = {
		loginAttempts: newAttempts,
	};

	// Lock account if max attempts reached
	if (newAttempts >= maxAttempts) {
		const lockUntil = new Date();
		lockUntil.setMinutes(lockUntil.getMinutes() + 30); // Lock for 30 minutes
		updateData.lockedUntil = lockUntil;
	}

	await getPrismaClient().user.update({
		where: {id: userId},
		data: updateData,
	});
};

// Reset login attempts on successful login
export const resetLoginAttempts = async (userId: string): Promise<void> => {
	await getPrismaClient().user.update({
		where: {id: userId},
		data: {
			loginAttempts: 0,
			lockedUntil: null,
			lastLoginAt: new Date(),
		},
	});
};

// Check if account is locked
export const isAccountLocked = async (userId: string): Promise<boolean> => {
	const user = await getPrismaClient().user.findUnique({
		where: {id: userId},
		select: {lockedUntil: true},
	});

	if (!user?.lockedUntil) return false;

	if (user.lockedUntil > new Date()) {
		return true;
	} else {
		// Auto-unlock expired locks
		await getPrismaClient().user.update({
			where: {id: userId},
			data: {
				lockedUntil: null,
				loginAttempts: 0,
			},
		});
		return false;
	}
};

// Get account lock information
export const getAccountLockInfo = async (userId: string) => {
	const user = await getPrismaClient().user.findUnique({
		where: {id: userId},
		select: {
			loginAttempts: true,
			lockedUntil: true,
		},
	});

	if (!user) return null;

	const isLocked = user.lockedUntil ? user.lockedUntil > new Date() : false;
	const maxAttempts = 5;

	return {
		isLocked,
		lockExpires: user.lockedUntil,
		attempts: user.loginAttempts,
		maxAttempts,
		remainingAttempts: Math.max(0, maxAttempts - user.loginAttempts),
	};
};

// Security event logging
export const logSecurityEvent = async (
	event: Omit<SecurityEvent, 'timestamp'>
): Promise<void> => {
	// In a production environment, you might want to store these in a separate audit log table
	// For now, we'll just log to console in development
	if (process.env.NODE_ENV !== 'production') {
		console.log('Security Event:', {
			...event,
			timestamp: new Date().toISOString(),
		});
	}

	// You could extend this to store in database, send to monitoring service, etc.
};

// Extract IP address from request
export const getClientIP = (req: any): string => {
	return (
		req.headers['x-forwarded-for']?.split(',')[0] ||
		req.headers['x-real-ip'] ||
		req.connection?.remoteAddress ||
		req.socket?.remoteAddress ||
		req.ip ||
		'unknown'
	);
};

// Extract user agent from request
export const getUserAgent = (req: any): string => {
	return req.headers['user-agent'] || 'unknown';
};

// Sanitize user data for response (remove sensitive fields)
export const sanitizeUser = (user: any) => {
	return sanitizeUserData(user);
};

// Generate API response
export const createApiResponse = (
	success: boolean,
	data?: any,
	message?: string
) => {
	return {
		success,
		data,
		message,
		timestamp: new Date().toISOString(),
	};
};

export default {
	hashPassword,
	verifyPassword,
	generateSecureToken,
	generateVerificationToken,
	generatePasswordResetToken,
	generateTokenPair,
	revokeRefreshToken,
	revokeAllUserSessions,
	isRefreshTokenValid,
	cleanupExpiredSessions,
	incrementLoginAttempts,
	resetLoginAttempts,
	isAccountLocked,
	getAccountLockInfo,
	logSecurityEvent,
	getClientIP,
	getUserAgent,
	sanitizeUser,
	createApiResponse,
};
