// Test the updated logger
import 'dotenv/config';

console.log('✅ Starting logger test...');

try {
  // Test logger creation
  const { createLogger } = require('./src/utils/logger');
  console.log('✅ Logger module imported successfully');
  
  const testLogger = createLogger('TEST');
  console.log('✅ Logger instance created successfully');
  
  console.log('✅ All logger tests passed');
  
} catch (error: any) {
  console.error('❌ Logger test failed:', error.message);
  console.error(error.stack);
}

console.log('✅ Logger test completed');
process.exit(0);