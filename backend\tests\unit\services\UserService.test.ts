// Mock the logger before any imports
jest.mock('../../../src/utils/logger');

import { UserRole } from '@prisma/client';
import { ConflictError, NotFoundError } from '../../../src/middleware/errorHandler';
import { IUserRepository } from '../../../src/repositories/UserRepository';
import { UserService } from '../../../src/services/UserService';
import { CreateUserRequest, UpdateUserRequest, UserListQuery, UserStats, UserSummary } from '../../../src/types/user';
import * as authUtils from '../../../src/utils/auth';

// Mock dependencies
jest.mock('../../../src/utils/auth', () => ({
  hashPassword: jest.fn(),
}));

jest.mock('../../../src/models/UserModels', () => ({
  UserManagementModel: {
    createUserData: jest.fn().mockReturnValue({
      getNormalizedEmail: jest.fn().mockReturnValue('<EMAIL>'),
      getStorageData: jest.fn().mockReturnValue({
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'CUSTOMER',
        password: 'hashedpassword123',
        isActive: true,
        isVerified: false,
      }),
      password: 'hashedpassword123',
    }),
    createUserUpdateData: jest.fn().mockReturnValue({
      email: '<EMAIL>',
      getNormalizedEmail: jest.fn().mockReturnValue('<EMAIL>'),
      getStorageData: jest.fn().mockReturnValue({
        email: '<EMAIL>',
        firstName: 'Jane',
      }),
    }),
    createUserListQuery: jest.fn().mockReturnValue({
      getQueryParams: jest.fn().mockReturnValue({}),
      hasFilters: jest.fn().mockReturnValue(false),
    }),
    createBulkOperationData: jest.fn().mockReturnValue({
      getOperationSize: jest.fn().mockReturnValue(3),
    }),
  },
  UserProfileModel: {
    fromUser: jest.fn().mockReturnValue({
      toUserDetail: jest.fn().mockReturnValue({
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'CUSTOMER',
        isActive: true,
        isVerified: false,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
      }),
    }),
  },
  UserStatisticsModel: jest.fn().mockImplementation(() => ({
    getGrowthRate: jest.fn().mockReturnValue(5.2),
    getVerificationRate: jest.fn().mockReturnValue(85.5),
    toUserStats: jest.fn().mockReturnValue({
      totalUsers: 100,
      activeUsers: 85,
      verifiedUsers: 75,
      newUsersThisMonth: 15,
    }),
  })),
}));

// Helper function to create complete mock user objects
const createMockUser = (overrides: Partial<any> = {}) => ({
  id: 'user-123',
  email: '<EMAIL>',
  password: null,
  firstName: 'John',
  lastName: 'Doe',
  role: 'CUSTOMER' as UserRole,
  isActive: true,
  isVerified: false,
  emailVerified: null,
  emailVerificationToken: null,
  emailVerificationTokenExpires: null,
  passwordResetToken: null,
  passwordResetTokenExpires: null,
  lastLoginAt: null,
  loginAttempts: 0,
  lockedUntil: null,
  avatar: null,
  phone: null,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  ...overrides,
});

const createMockUserSummary = (overrides: Partial<UserSummary> = {}): UserSummary => ({
  id: 'user-123',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  role: 'CUSTOMER' as UserRole,
  isActive: true,
  isVerified: false,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  ...overrides,
});

describe('UserService', () => {
  let userService: UserService;
  let mockUserRepository: jest.Mocked<IUserRepository>;

  beforeEach(() => {
    // Mock the logger
    const { createLogger } = require('../../../src/utils/logger');
    createLogger.mockReturnValue({
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    });

    // Mock the UserModels
    const { UserManagementModel, UserProfileModel, UserStatisticsModel } = require('../../../src/models/UserModels');

    UserManagementModel.createUserData.mockReturnValue({
      getNormalizedEmail: jest.fn().mockReturnValue('<EMAIL>'),
      getStorageData: jest.fn().mockReturnValue({
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'CUSTOMER',
        password: 'hashedpassword123',
        isActive: true,
        isVerified: false,
      }),
      password: 'hashedpassword123',
    });

    UserManagementModel.createUserUpdateData.mockReturnValue({
      email: '<EMAIL>',
      getNormalizedEmail: jest.fn().mockReturnValue('<EMAIL>'),
      getStorageData: jest.fn().mockReturnValue({
        email: '<EMAIL>',
        firstName: 'Jane',
      }),
    });

    UserManagementModel.createUserListQuery.mockReturnValue({
      getQueryParams: jest.fn().mockReturnValue({}),
      hasFilters: jest.fn().mockReturnValue(false),
    });

    UserManagementModel.createBulkOperationData.mockReturnValue({
      getOperationSize: jest.fn().mockReturnValue(3),
    });

    UserProfileModel.fromUser.mockReturnValue({
      toUserDetail: jest.fn().mockReturnValue({
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'CUSTOMER',
        isActive: true,
        isVerified: false,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
      }),
    });

    UserStatisticsModel.mockImplementation(() => ({
      getGrowthRate: jest.fn().mockReturnValue(5.2),
      getVerificationRate: jest.fn().mockReturnValue(85.5),
      toUserStats: jest.fn().mockReturnValue({
        totalUsers: 100,
        activeUsers: 85,
        verifiedUsers: 75,
        newUsersThisMonth: 15,
      }),
    }));

    mockUserRepository = {
      findUserById: jest.fn(),
      findUserByEmail: jest.fn(),
      createUser: jest.fn(),
      updateUser: jest.fn(),
      deleteUser: jest.fn(),
      getUsers: jest.fn(),
      searchUsers: jest.fn(),
      getUserStats: jest.fn(),
      getUsersByRole: jest.fn(),
      getActiveUsers: jest.fn(),
      getVerifiedUsers: jest.fn(),
      bulkUpdateUsers: jest.fn(),
      bulkDeleteUsers: jest.fn(),
      userExists: jest.fn(),
      countUsers: jest.fn(),
      countUsersByRole: jest.fn(),
    } as any;

    userService = new UserService(mockUserRepository);
    jest.clearAllMocks();
  });

  describe('getUserById', () => {
    it('should successfully get user by ID', async () => {
      const mockUser = createMockUser();

      mockUserRepository.findUserById.mockResolvedValue(mockUser);

      const result = await userService.getUserById('user-123');

      expect(mockUserRepository.findUserById).toHaveBeenCalledWith('user-123');
      expect(result).toEqual({
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'CUSTOMER',
        isActive: true,
        isVerified: false,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
      });
    });

    it('should throw NotFoundError when user does not exist', async () => {
      mockUserRepository.findUserById.mockResolvedValue(null);

      await expect(userService.getUserById('non-existent')).rejects.toThrow(NotFoundError);
    });
  });

  describe('createUser', () => {
    it('should successfully create a new user', async () => {
      const userData: CreateUserRequest = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: 'password123',
        role: 'CUSTOMER',
      };

      const mockCreatedUser = createMockUser({
        id: 'user-123',
        password: 'hashedpassword123',
      });

      mockUserRepository.findUserByEmail.mockResolvedValue(null);
      (authUtils.hashPassword as jest.Mock).mockResolvedValue('hashedpassword123');
      mockUserRepository.createUser.mockResolvedValue(mockCreatedUser);

      const result = await userService.createUser(userData);

      expect(mockUserRepository.findUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(authUtils.hashPassword).toHaveBeenCalledWith('hashedpassword123');
      expect(mockUserRepository.createUser).toHaveBeenCalledWith({
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'CUSTOMER',
        password: 'hashedpassword123',
        isActive: true,
        isVerified: false,
      });
      expect(result.id).toBe('user-123');
    });

    it('should throw ConflictError when user already exists', async () => {
      const userData: CreateUserRequest = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: 'password123',
        role: 'CUSTOMER',
      };

      const existingUser = createMockUser({ email: '<EMAIL>' });
      mockUserRepository.findUserByEmail.mockResolvedValue(existingUser);

      await expect(userService.createUser(userData)).rejects.toThrow(ConflictError);
    });
  });

  describe('updateUser', () => {
    it('should successfully update a user', async () => {
      const updateData: UpdateUserRequest = {
        firstName: 'Jane',
        lastName: 'Smith',
      };

      const existingUser = createMockUser();
      const updatedUser = createMockUser({
        ...existingUser,
        ...updateData,
      });

      mockUserRepository.findUserById.mockResolvedValue(existingUser);
      mockUserRepository.updateUser.mockResolvedValue(updatedUser);

      const result = await userService.updateUser('user-123', updateData);

      expect(mockUserRepository.findUserById).toHaveBeenCalledWith('user-123');
      expect(mockUserRepository.updateUser).toHaveBeenCalledWith('user-123', {
        email: '<EMAIL>',
        firstName: 'Jane',
      });
      expect(result.firstName).toBe('John'); // Mocked return from UserProfileModel
    });

    it('should throw NotFoundError when user does not exist', async () => {
      mockUserRepository.findUserById.mockResolvedValue(null);

      await expect(userService.updateUser('non-existent', { firstName: 'Jane' })).rejects.toThrow(NotFoundError);
    });

    it('should throw ConflictError when updating to existing email', async () => {
      const updateData: UpdateUserRequest = {
        email: '<EMAIL>',
      };

      const existingUser = createMockUser({ email: '<EMAIL>' });

      mockUserRepository.findUserById.mockResolvedValue(existingUser);
      mockUserRepository.userExists.mockResolvedValue(true);

      await expect(userService.updateUser('user-123', updateData)).rejects.toThrow(ConflictError);
    });
  });

  describe('deleteUser', () => {
    it('should successfully delete a user', async () => {
      const existingUser = createMockUser();

      mockUserRepository.findUserById.mockResolvedValue(existingUser);
      mockUserRepository.deleteUser.mockResolvedValue(true);

      const result = await userService.deleteUser('user-123');

      expect(mockUserRepository.findUserById).toHaveBeenCalledWith('user-123');
      expect(mockUserRepository.deleteUser).toHaveBeenCalledWith('user-123');
      expect(result).toBe(true);
    });

    it('should throw NotFoundError when user does not exist', async () => {
      mockUserRepository.findUserById.mockResolvedValue(null);

      await expect(userService.deleteUser('non-existent')).rejects.toThrow(NotFoundError);
    });

    it('should return false when deletion fails', async () => {
      const existingUser = createMockUser();
      mockUserRepository.findUserById.mockResolvedValue(existingUser);
      mockUserRepository.deleteUser.mockResolvedValue(false);

      const result = await userService.deleteUser('user-123');

      expect(result).toBe(false);
    });
  });

  describe('getUsers', () => {
    it('should successfully get users list', async () => {
      const query: UserListQuery = { page: 1, limit: 10 };
      const mockResult = {
        users: [
          createMockUserSummary({ id: 'user-1', email: '<EMAIL>' }),
          createMockUserSummary({ id: 'user-2', email: '<EMAIL>' }),
        ],
        pagination: { page: 1, limit: 10, total: 2, totalPages: 1, hasNext: false, hasPrev: false },
      };

      mockUserRepository.getUsers.mockResolvedValue(mockResult);

      const result = await userService.getUsers(query);

      expect(mockUserRepository.getUsers).toHaveBeenCalledWith({});
      expect(result).toEqual(mockResult);
    });
  });

  describe('searchUsers', () => {
    it('should successfully search users', async () => {
      const mockUsers = [
        createMockUserSummary({ id: 'user-1', email: '<EMAIL>', firstName: 'John' }),
        createMockUserSummary({ id: 'user-2', email: '<EMAIL>', firstName: 'Jane' }),
      ];

      mockUserRepository.searchUsers.mockResolvedValue(mockUsers);

      const result = await userService.searchUsers('john', 5);

      expect(mockUserRepository.searchUsers).toHaveBeenCalledWith('john', 5);
      expect(result).toEqual(mockUsers);
    });

    it('should use default limit when not provided', async () => {
      const mockUsers: UserSummary[] = [];
      mockUserRepository.searchUsers.mockResolvedValue(mockUsers);

      await userService.searchUsers('test');

      expect(mockUserRepository.searchUsers).toHaveBeenCalledWith('test', 10);
    });
  });

  describe('getUserStats', () => {
    it('should successfully get user statistics', async () => {
      const mockStats: UserStats = {
        total: 100,
        totalUsers: 100,
        active: 85,
        activeUsers: 85,
        inactive: 15,
        verified: 75,
        verifiedUsers: 75,
        unverified: 25,
        byRole: {
          CUSTOMER: 80,
          PROVIDER: 15,
          ADMIN: 5,
        },
        usersByRole: {
          CUSTOMER: 80,
          PROVIDER: 15,
          ADMIN: 5,
        },
        recentRegistrations: 15,
        averageRegistrationsPerDay: 2.5,
        newUsersThisMonth: 15,
      };

      mockUserRepository.getUserStats.mockResolvedValue(mockStats);

      const result = await userService.getUserStats();

      expect(mockUserRepository.getUserStats).toHaveBeenCalled();
      expect(result).toEqual({
        totalUsers: 100,
        activeUsers: 85,
        verifiedUsers: 75,
        newUsersThisMonth: 15,
      });
    });
  });

  describe('getUsersByRole', () => {
    it('should successfully get users by role', async () => {
      const mockUsers = [
        createMockUserSummary({ id: 'user-1', email: '<EMAIL>', role: 'ADMIN' }),
        createMockUserSummary({ id: 'user-2', email: '<EMAIL>', role: 'ADMIN' }),
      ];

      mockUserRepository.getUsersByRole.mockResolvedValue(mockUsers);

      const result = await userService.getUsersByRole('ADMIN' as UserRole);

      expect(mockUserRepository.getUsersByRole).toHaveBeenCalledWith('ADMIN');
      expect(result).toEqual(mockUsers);
    });
  });

  describe('getActiveUsers', () => {
    it('should successfully get active users', async () => {
      const mockUsers = [
        createMockUserSummary({ id: 'user-1', email: '<EMAIL>', isActive: true }),
        createMockUserSummary({ id: 'user-2', email: '<EMAIL>', isActive: true }),
      ];

      mockUserRepository.getActiveUsers.mockResolvedValue(mockUsers);

      const result = await userService.getActiveUsers();

      expect(mockUserRepository.getActiveUsers).toHaveBeenCalled();
      expect(result).toEqual(mockUsers);
    });
  });

  describe('getVerifiedUsers', () => {
    it('should successfully get verified users', async () => {
      const mockUsers = [
        createMockUserSummary({ id: 'user-1', email: '<EMAIL>', isVerified: true }),
        createMockUserSummary({ id: 'user-2', email: '<EMAIL>', isVerified: true }),
      ];

      mockUserRepository.getVerifiedUsers.mockResolvedValue(mockUsers);

      const result = await userService.getVerifiedUsers();

      expect(mockUserRepository.getVerifiedUsers).toHaveBeenCalled();
      expect(result).toEqual(mockUsers);
    });
  });

  describe('bulkUpdateUsers', () => {
    it('should successfully bulk update users', async () => {
      const userIds = ['user-1', 'user-2', 'user-3'];
      const updates = { isActive: false };

      mockUserRepository.bulkUpdateUsers.mockResolvedValue(3);

      const result = await userService.bulkUpdateUsers(userIds, updates);

      expect(mockUserRepository.bulkUpdateUsers).toHaveBeenCalledWith(userIds, updates);
      expect(result).toBe(3);
    });

    it('should throw ConflictError when updating to existing email', async () => {
      const userIds = ['user-1', 'user-2'];
      const updates = { email: '<EMAIL>' };

      mockUserRepository.userExists.mockResolvedValue(true);

      await expect(userService.bulkUpdateUsers(userIds, updates)).rejects.toThrow(ConflictError);
    });
  });

  describe('bulkDeleteUsers', () => {
    it('should successfully bulk delete users', async () => {
      const userIds = ['user-1', 'user-2'];

      mockUserRepository.bulkDeleteUsers.mockResolvedValue(2);

      const result = await userService.bulkDeleteUsers(userIds);

      expect(mockUserRepository.bulkDeleteUsers).toHaveBeenCalledWith(userIds);
      expect(result).toBe(2);
    });
  });

  describe('userExists', () => {
    it('should check if user exists', async () => {
      mockUserRepository.userExists.mockResolvedValue(true);

      const result = await userService.userExists('<EMAIL>');

      expect(mockUserRepository.userExists).toHaveBeenCalledWith('<EMAIL>');
      expect(result).toBe(true);
    });
  });

  describe('countUsers', () => {
    it('should count all users', async () => {
      mockUserRepository.countUsers.mockResolvedValue(150);

      const result = await userService.countUsers();

      expect(mockUserRepository.countUsers).toHaveBeenCalled();
      expect(result).toBe(150);
    });
  });

  describe('countUsersByRole', () => {
    it('should count users by role', async () => {
      mockUserRepository.countUsersByRole.mockResolvedValue(120);

      const result = await userService.countUsersByRole('CUSTOMER' as UserRole);

      expect(mockUserRepository.countUsersByRole).toHaveBeenCalledWith('CUSTOMER');
      expect(result).toBe(120);
    });
  });
});
