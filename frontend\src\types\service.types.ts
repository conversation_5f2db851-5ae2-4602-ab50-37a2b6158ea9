// Service Types
export interface Service {
  id: string;
  name: string;
  description: string;
  category: string;
  price?: number;
  image?: string;
  providerId?: string;
}

export interface ServiceCategory {
  id: string;
  name: string;
  description: string;
  icon?: string;
  route: string;
}

export interface Provider {
  id: string;
  name: string;
  email: string;
  description?: string;
  services: Service[];
  location?: string;
  rating?: number;
}

// Component Props Types
export interface ServiceCardProps {
  service: Service;
  className?: string;
  showProvider?: boolean;
}

export interface CategoryCardProps {
  category: ServiceCategory;
  className?: string;
}

export interface ProductCardProps {
  product: Service;
  className?: string;
}