import { ValidationError } from '../middleware/errorHandler';
import {
  CreateServiceRequest,
  UpdateServiceRequest,
  ServiceListQuery,
  ServiceSearchQuery,
  CreateServiceCategoryRequest,
  UpdateServiceCategoryRequest,
  CreateServiceFormFieldRequest,
  UpdateServiceFormFieldRequest,
  CreateServiceFormFieldOptionRequest,
  UpdateServiceFormFieldOptionRequest,
  PriceCalculationRequest,
} from '../types/service';

/**
 * ValidationUtils class
 * Centralizes common validation patterns across service layer
 * Eliminates validation duplication and provides consistent error messages
 */
export class ValidationUtils {
  /**
   * Service validation methods
   */
  static validateServiceData(data: CreateServiceRequest): void {
    this.validateRequiredString(data.name, 'Service name');
    this.validateRequiredString(data.description, 'Service description');
    this.validateRequiredString(data.categoryId, 'Service category');
    this.validateRequiredString(data.image, 'Service image');
    this.validateNonNegativeNumber(data.basePrice, 'Base price');
  }

  static validateServiceUpdateData(data: UpdateServiceRequest): void {
    if (data.name !== undefined) this.validateNonEmptyString(data.name, 'Service name');
    if (data.description !== undefined) this.validateNonEmptyString(data.description, 'Service description');
    if (data.image !== undefined) this.validateNonEmptyString(data.image, 'Service image');
    if (data.basePrice !== undefined) this.validateNonNegativeNumber(data.basePrice, 'Base price');
  }

  static validateServiceListQuery(query: ServiceListQuery): void {
    if (query.page !== undefined) this.validatePositiveNumber(query.page, 'Page number');
    if (query.limit !== undefined) this.validateNumberInRange(query.limit, 1, 100, 'Limit');
    if (query.minPrice !== undefined) this.validateNonNegativeNumber(query.minPrice, 'Minimum price');
    if (query.maxPrice !== undefined) this.validateNonNegativeNumber(query.maxPrice, 'Maximum price');
    
    if (query.minPrice !== undefined && query.maxPrice !== undefined && query.minPrice > query.maxPrice) {
      throw new ValidationError('Minimum price cannot be greater than maximum price');
    }
  }

  static validateServiceSearchQuery(query: ServiceSearchQuery): void {
    if (query.minPrice !== undefined) this.validateNonNegativeNumber(query.minPrice, 'Minimum price');
    if (query.maxPrice !== undefined) this.validateNonNegativeNumber(query.maxPrice, 'Maximum price');
    
    if (query.minPrice !== undefined && query.maxPrice !== undefined && query.minPrice > query.maxPrice) {
      throw new ValidationError('Minimum price cannot be greater than maximum price');
    }
  }

  /**
   * Service Category validation methods
   */
  static validateServiceCategoryData(data: CreateServiceCategoryRequest): void {
    this.validateRequiredString(data.name, 'Category name');
    this.validateRequiredString(data.route, 'Category route');
    this.validateRouteFormat(data.route);
  }

  static validateServiceCategoryUpdateData(data: UpdateServiceCategoryRequest): void {
    if (data.name !== undefined) this.validateNonEmptyString(data.name, 'Category name');
    if (data.route !== undefined) {
      this.validateNonEmptyString(data.route, 'Category route');
      this.validateRouteFormat(data.route);
    }
  }

  /**
   * Form Field validation methods
   */
  static validateServiceFormFieldData(data: CreateServiceFormFieldRequest): void {
    this.validateRequiredString(data.name, 'Field name');
    this.validateRequiredString(data.label, 'Field label');
    if (!data.type) {
      throw new ValidationError('Field type is required');
    }
  }

  static validateServiceFormFieldUpdateData(data: UpdateServiceFormFieldRequest): void {
    if (data.name !== undefined) this.validateNonEmptyString(data.name, 'Field name');
    if (data.label !== undefined) this.validateNonEmptyString(data.label, 'Field label');
  }

  /**
   * Form Field Option validation methods
   */
  static validateServiceFormFieldOptionData(data: CreateServiceFormFieldOptionRequest): void {
    this.validateRequiredString(data.value, 'Option value');
    this.validateRequiredString(data.label, 'Option label');
  }

  static validateServiceFormFieldOptionUpdateData(data: UpdateServiceFormFieldOptionRequest): void {
    if (data.value !== undefined) this.validateNonEmptyString(data.value, 'Option value');
    if (data.label !== undefined) this.validateNonEmptyString(data.label, 'Option label');
  }

  /**
   * Price Calculation validation methods
   */
  static validatePriceCalculationRequest(request: PriceCalculationRequest): void {
    if (!request.serviceId) {
      throw new ValidationError('Service ID is required for price calculation');
    }
    if (!request.formData || typeof request.formData !== 'object') {
      throw new ValidationError('Form data is required for price calculation');
    }
    if (request.quantity !== undefined) this.validatePositiveNumber(request.quantity, 'Quantity');
  }

  /**
   * Common validation utilities
   */
  static validatePopularServicesLimit(limit: number): void {
    this.validateNumberInRange(limit, 1, 50, 'Limit');
  }

  /**
   * Base validation methods
   */
  private static validateRequiredString(value: unknown, fieldName: string): void {
    if (!value || typeof value !== 'string' || value.trim().length === 0) {
      throw new ValidationError(`${fieldName} is required`);
    }
  }

  private static validateNonEmptyString(value: string, fieldName: string): void {
    if (value.trim().length === 0) {
      throw new ValidationError(`${fieldName} cannot be empty`);
    }
  }

  private static validatePositiveNumber(value: number, fieldName: string): void {
    if (value < 1) {
      throw new ValidationError(`${fieldName} must be positive`);
    }
  }

  private static validateNonNegativeNumber(value: number, fieldName: string): void {
    if (value < 0) {
      throw new ValidationError(`${fieldName} must be non-negative`);
    }
  }

  private static validateNumberInRange(value: number, min: number, max: number, fieldName: string): void {
    if (value < min || value > max) {
      throw new ValidationError(`${fieldName} must be between ${min} and ${max}`);
    }
  }

  private static validateRouteFormat(route: string): void {
    if (!/^[a-z0-9-]+$/.test(route)) {
      throw new ValidationError('Category route must contain only lowercase letters, numbers, and hyphens');
    }
  }
}