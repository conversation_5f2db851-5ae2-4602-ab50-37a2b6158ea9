import { PrismaClient, Service, ServicePricingType } from '@prisma/client';
import {
  CreateServiceCategoryData,
  CreateServiceData,
  CreateServiceFormFieldData,
  CreateServiceFormFieldOptionData,
  PriceCalculationRequest,
  PriceCalculationResponse,
  ServiceCategoryDetail,
  ServiceCategorySummary,
  ServiceDetail,
  ServiceFormFieldDetail,
  ServiceFormFieldOptionDetail,
  ServiceListQuery,
  ServiceListResponse,
  ServiceSearchQuery,
  ServiceSearchResult,
  ServiceStats,
  ServiceSummary,
  UpdateServiceCategoryData,
  UpdateServiceData,
  UpdateServiceFormFieldData,
  UpdateServiceFormFieldOptionData,
} from '../types/service';

// Import the specialized repositories
import { CoreServiceRepository, ICoreServiceRepository } from './CoreServiceRepository';
import { IServiceCategoryRepository, ServiceCategoryRepository } from './ServiceCategoryRepository';
import { IServiceFormFieldRepository, ServiceFormFieldRepository } from './ServiceFormFieldRepository';
import { IServicePricingRepository, ServicePricingRepository } from './ServicePricingRepository';

/**
 * CompositeServiceRepository
 * Combines all specialized repositories while maintaining the original interface
 * This allows gradual migration and maintains backward compatibility
 */
export interface IServiceRepository {
  // Service CRUD operations
  findServiceById(id: string, includeFormFields?: boolean): Promise<ServiceDetail | null>;
  createService(serviceData: CreateServiceData): Promise<Service>;
  updateService(id: string, data: UpdateServiceData): Promise<Service>;
  deleteService(id: string): Promise<boolean>;

  // Service list and search operations
  getServices(query: ServiceListQuery): Promise<ServiceListResponse>;
  searchServices(query: ServiceSearchQuery): Promise<ServiceSearchResult>;
  getServicesByCategory(categoryId: string): Promise<ServiceSummary[]>;

  // Service statistics and analytics
  getServiceStats(): Promise<ServiceStats>;
  getPopularServices(limit?: number): Promise<ServiceSummary[]>;
  getServicesByPricingType(pricingType: ServicePricingType): Promise<ServiceSummary[]>;

  // Service category operations
  getServiceCategories(): Promise<ServiceCategorySummary[]>;
  findServiceCategoryById(id: string): Promise<ServiceCategoryDetail | null>;
  findServiceCategoryByRoute(route: string): Promise<ServiceCategoryDetail | null>;
  createServiceCategory(categoryData: CreateServiceCategoryData): Promise<any>;
  updateServiceCategory(id: string, data: UpdateServiceCategoryData): Promise<any>;
  deleteServiceCategory(id: string): Promise<boolean>;

  // Service form field operations
  getServiceFormFields(serviceId: string): Promise<ServiceFormFieldDetail[]>;
  findServiceFormFieldById(id: string): Promise<ServiceFormFieldDetail | null>;
  createServiceFormField(fieldData: CreateServiceFormFieldData): Promise<any>;
  updateServiceFormField(id: string, data: UpdateServiceFormFieldData): Promise<any>;
  deleteServiceFormField(id: string): Promise<boolean>;

  // Service form field option operations
  findServiceFormFieldOptionById(id: string): Promise<ServiceFormFieldOptionDetail | null>;
  getServiceFormFieldOptions(fieldId: string): Promise<ServiceFormFieldOptionDetail[]>;
  createServiceFormFieldOption(optionData: CreateServiceFormFieldOptionData): Promise<any>;
  updateServiceFormFieldOption(id: string, data: UpdateServiceFormFieldOptionData): Promise<any>;
  deleteServiceFormFieldOption(id: string): Promise<boolean>;

  // Utility operations
  serviceExists(id: string): Promise<boolean>;
  serviceCategoryExists(id: string): Promise<boolean>;
  countServices(): Promise<number>;
  countServicesByCategory(categoryId: string): Promise<number>;

  // Price calculation
  calculateServicePrice(request: PriceCalculationRequest): Promise<PriceCalculationResponse>;
}

export class CompositeServiceRepository implements IServiceRepository {
  private coreRepository: ICoreServiceRepository;
  private categoryRepository: IServiceCategoryRepository;
  private formFieldRepository: IServiceFormFieldRepository;
  private pricingRepository: IServicePricingRepository;

  constructor(prisma: PrismaClient) {
    this.coreRepository = new CoreServiceRepository(prisma);
    this.categoryRepository = new ServiceCategoryRepository(prisma);
    this.formFieldRepository = new ServiceFormFieldRepository(prisma);
    this.pricingRepository = new ServicePricingRepository(prisma);
  }

  // Core service operations - delegate to CoreServiceRepository
  async findServiceById(id: string, includeFormFields?: boolean): Promise<ServiceDetail | null> {
    return this.coreRepository.findServiceById(id, includeFormFields);
  }

  async createService(serviceData: CreateServiceData): Promise<Service> {
    return this.coreRepository.createService(serviceData);
  }

  async updateService(id: string, data: UpdateServiceData): Promise<Service> {
    return this.coreRepository.updateService(id, data);
  }

  async deleteService(id: string): Promise<boolean> {
    return this.coreRepository.deleteService(id);
  }

  async getServices(query: ServiceListQuery): Promise<ServiceListResponse> {
    return this.coreRepository.getServices(query);
  }

  async searchServices(query: ServiceSearchQuery): Promise<ServiceSearchResult> {
    return this.coreRepository.searchServices(query);
  }

  async getServicesByCategory(categoryId: string): Promise<ServiceSummary[]> {
    return this.coreRepository.getServicesByCategory(categoryId);
  }

  async serviceExists(id: string): Promise<boolean> {
    return this.coreRepository.serviceExists(id);
  }

  // Pricing operations - delegate to ServicePricingRepository
  async getServiceStats(): Promise<ServiceStats> {
    return this.pricingRepository.getServiceStats();
  }

  async getPopularServices(limit?: number): Promise<ServiceSummary[]> {
    return this.pricingRepository.getPopularServices(limit);
  }

  async getServicesByPricingType(pricingType: ServicePricingType): Promise<ServiceSummary[]> {
    return this.pricingRepository.getServicesByPricingType(pricingType);
  }

  async calculateServicePrice(request: PriceCalculationRequest): Promise<PriceCalculationResponse> {
    return this.pricingRepository.calculateServicePrice(request);
  }

  async countServices(): Promise<number> {
    return this.pricingRepository.countServices();
  }

  // Category operations - delegate to ServiceCategoryRepository
  async getServiceCategories(): Promise<ServiceCategorySummary[]> {
    return this.categoryRepository.getServiceCategories();
  }

  async findServiceCategoryById(id: string): Promise<ServiceCategoryDetail | null> {
    return this.categoryRepository.findServiceCategoryById(id);
  }

  async findServiceCategoryByRoute(route: string): Promise<ServiceCategoryDetail | null> {
    return this.categoryRepository.findServiceCategoryByRoute(route);
  }

  async createServiceCategory(categoryData: CreateServiceCategoryData): Promise<any> {
    return this.categoryRepository.createServiceCategory(categoryData);
  }

  async updateServiceCategory(id: string, data: UpdateServiceCategoryData): Promise<any> {
    return this.categoryRepository.updateServiceCategory(id, data);
  }

  async deleteServiceCategory(id: string): Promise<boolean> {
    return this.categoryRepository.deleteServiceCategory(id);
  }

  async serviceCategoryExists(id: string): Promise<boolean> {
    return this.categoryRepository.serviceCategoryExists(id);
  }

  async countServicesByCategory(categoryId: string): Promise<number> {
    return this.categoryRepository.countServicesByCategory(categoryId);
  }

  // Form field operations - delegate to ServiceFormFieldRepository
  async getServiceFormFields(serviceId: string): Promise<ServiceFormFieldDetail[]> {
    return this.formFieldRepository.getServiceFormFields(serviceId);
  }

  async findServiceFormFieldById(id: string): Promise<ServiceFormFieldDetail | null> {
    return this.formFieldRepository.findServiceFormFieldById(id);
  }

  async createServiceFormField(fieldData: CreateServiceFormFieldData): Promise<any> {
    return this.formFieldRepository.createServiceFormField(fieldData);
  }

  async updateServiceFormField(id: string, data: UpdateServiceFormFieldData): Promise<any> {
    return this.formFieldRepository.updateServiceFormField(id, data);
  }

  async deleteServiceFormField(id: string): Promise<boolean> {
    return this.formFieldRepository.deleteServiceFormField(id);
  }

  async getServiceFormFieldOptions(fieldId: string): Promise<ServiceFormFieldOptionDetail[]> {
    return this.formFieldRepository.getServiceFormFieldOptions(fieldId);
  }

  async findServiceFormFieldOptionById(id: string): Promise<ServiceFormFieldOptionDetail | null> {
    return this.formFieldRepository.findServiceFormFieldOptionById(id);
  }

  async createServiceFormFieldOption(optionData: CreateServiceFormFieldOptionData): Promise<any> {
    return this.formFieldRepository.createServiceFormFieldOption(optionData);
  }

  async updateServiceFormFieldOption(id: string, data: UpdateServiceFormFieldOptionData): Promise<any> {
    return this.formFieldRepository.updateServiceFormFieldOption(id, data);
  }

  async deleteServiceFormFieldOption(id: string): Promise<boolean> {
    return this.formFieldRepository.deleteServiceFormFieldOption(id);
  }
}
