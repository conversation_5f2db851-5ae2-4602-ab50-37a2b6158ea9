import winston from 'winston';
declare const logger: winston.Logger;
export declare class Logger {
    private context;
    constructor(context?: string);
    private formatMessage;
    error(message: string, error?: Error | any, meta?: any): void;
    warn(message: string, meta?: any): void;
    info(message: string, meta?: any): void;
    http(message: string, meta?: any): void;
    debug(message: string, meta?: any): void;
    security(event: string, details: any): void;
    auth(event: string, userId?: string, details?: any): void;
    performance(operation: string, duration: number, meta?: any): void;
    database(operation: string, table?: string, meta?: any): void;
}
export declare const defaultLogger: Logger;
export declare const authLogger: Logger;
export declare const dbLogger: Logger;
export declare const httpLogger: Logger;
export declare const securityLogger: Logger;
export declare const morganStream: {
    write: (message: string) => void;
};
export declare const createLogger: (context: string) => Logger;
export { logger as winstonLogger };
export default defaultLogger;
//# sourceMappingURL=logger.d.ts.map