import winston from 'winston';
import { LoggingConfig } from '../config';
declare function createLogger(service: string): winston.Logger;
export declare const authLogger: winston.Logger;
export declare const dbLogger: winston.Logger;
export declare const apiLogger: winston.Logger;
export declare const securityLogger: winston.Logger;
export declare const emailLogger: winston.Logger;
export declare const fileLogger: winston.Logger;
export { createLogger };
export declare const getLoggerConfig: () => LoggingConfig;
//# sourceMappingURL=logger.d.ts.map