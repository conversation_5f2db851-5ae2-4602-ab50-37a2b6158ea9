import winston from 'winston';
import { LoggingConfig } from '../config';
declare class AppLogger {
    private logger;
    private service;
    constructor(logger: winston.Logger, service: string);
    error(message: string, error?: Error | any, meta?: any): void;
    warn(message: string, meta?: any): void;
    info(message: string, meta?: any): void;
    http(message: string, meta?: any): void;
    debug(message: string, meta?: any): void;
    verbose(message: string, meta?: any): void;
    silly(message: string, meta?: any): void;
    security(event: string, details: any): void;
    auth(event: string, userId?: string, details?: any): void;
    performance(operation: string, duration: number, meta?: any): void;
    database(operation: string, table?: string, meta?: any): void;
}
declare function createLogger(service: string): AppLogger;
export declare const morganStream: {
    write: (message: string) => void;
};
export declare const authLogger: AppLogger;
export declare const dbLogger: AppLogger;
export declare const apiLogger: AppLogger;
export declare const securityLogger: AppLogger;
export declare const emailLogger: AppLogger;
export declare const fileLogger: AppLogger;
export declare const httpLogger: AppLogger;
export { createLogger };
export declare const getLoggerConfig: () => LoggingConfig;
export declare const clearLoggerCache: () => void;
declare const _default: {
    createLogger: typeof createLogger;
    authLogger: AppLogger;
    dbLogger: AppLogger;
    apiLogger: AppLogger;
    securityLogger: AppLogger;
    emailLogger: AppLogger;
    fileLogger: AppLogger;
    httpLogger: AppLogger;
    morganStream: {
        write: (message: string) => void;
    };
    getLoggerConfig: () => LoggingConfig;
    clearLoggerCache: () => void;
};
export default _default;
//# sourceMappingURL=logger.d.ts.map