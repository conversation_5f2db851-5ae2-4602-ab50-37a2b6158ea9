import { PrismaClient, User, UserRole, user_sessions } from '@prisma/client';
import { AuthUser } from '../types/auth';

// Repository interface for dependency inversion
export interface IAuthRepository {
  // User operations
  findUserByEmail(email: string): Promise<User | null>;
  findUserById(id: string): Promise<User | null>;
  findUserByIdSafe(id: string): Promise<AuthUser | null>;
  createUser(userData: CreateUserData): Promise<User>;
  updateUser(id: string, data: UpdateUserData): Promise<User>;
  updateUserPassword(id: string, hashedPassword: string): Promise<void>;
  
  // Login attempt tracking
  incrementLoginAttempts(userId: string): Promise<void>;
  resetLoginAttempts(userId: string): Promise<void>;
  isAccountLocked(userId: string): Promise<boolean>;
  getAccountLockInfo(userId: string): Promise<AccountLockInfo | null>;
  
  // Password reset operations
  findUserByPasswordResetToken(token: string): Promise<User | null>;
  setPasswordResetToken(userId: string, token: string, expiresAt: Date): Promise<void>;
  clearPasswordResetToken(userId: string): Promise<void>;
  
  // Email verification operations
  findUserByEmailVerificationToken(token: string): Promise<User | null>;
  setEmailVerificationToken(userId: string, token: string, expiresAt: Date): Promise<void>;
  markEmailAsVerified(userId: string): Promise<void>;
  clearEmailVerificationToken(userId: string): Promise<void>;
  
  // Session management
  createSession(sessionData: CreateSessionData): Promise<user_sessions>;
  findSessionByRefreshToken(refreshToken: string): Promise<user_sessions | null>;
  revokeSession(refreshToken: string): Promise<void>;
  revokeAllUserSessions(userId: string): Promise<void>;
  getUserSessions(userId: string): Promise<SessionData[]>;
  revokeSpecificSession(sessionId: string, userId: string): Promise<boolean>;
  cleanupExpiredSessions(): Promise<number>;
}

// Data transfer objects
export interface CreateUserData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string | null;
  role?: UserRole;
  isActive?: boolean;
  isVerified?: boolean;
}

export interface UpdateUserData {
  firstName?: string;
  lastName?: string;
  phone?: string | null;
  avatar?: string | null;
  emailVerified?: Date | null;
  emailVerificationToken?: string | null;
  emailVerificationTokenExpires?: Date | null;
  passwordResetToken?: string | null;
  passwordResetTokenExpires?: Date | null;
  lastLoginAt?: Date;
  loginAttempts?: number;
  lockedUntil?: Date | null;
}

export interface CreateSessionData {
  id: string;
  userId: string;
  refreshToken: string;
  expiresAt: Date;
  ipAddress: string;
  userAgent: string;
}

export interface SessionData {
  id: string;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  expiresAt: Date;
  isActive: boolean;
}

export interface AccountLockInfo {
  isLocked: boolean;
  lockExpires?: Date | null;
  attempts: number;
  maxAttempts: number;
  remainingAttempts: number;
}

// Prisma implementation of the repository
export class AuthRepository implements IAuthRepository {
  constructor(private prisma: PrismaClient) {}

  // User operations
  async findUserByEmail(email: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    });
  }

  async findUserById(id: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { id }
    });
  }

  async findUserByIdSafe(id: string): Promise<AuthUser | null> {
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        isVerified: true,
        avatar: true,
        phone: true,
        createdAt: true,
        updatedAt: true,
        lastLoginAt: true
      }
    });
    
    return user as AuthUser | null;
  }

  async createUser(userData: CreateUserData): Promise<User> {
    return this.prisma.user.create({
      data: {
        email: userData.email.toLowerCase(),
        password: userData.password,
        firstName: userData.firstName.trim(),
        lastName: userData.lastName.trim(),
        phone: userData.phone?.trim() || null,
        role: userData.role || UserRole.CUSTOMER,
        isActive: userData.isActive ?? true,
        isVerified: userData.isVerified ?? false
      }
    });
  }

  async updateUser(id: string, data: UpdateUserData): Promise<User> {
    return this.prisma.user.update({
      where: { id },
      data: {
        ...(data.firstName && { firstName: data.firstName.trim() }),
        ...(data.lastName && { lastName: data.lastName.trim() }),
        ...(data.phone !== undefined && { phone: data.phone?.trim() || null }),
        ...(data.avatar !== undefined && { avatar: data.avatar?.trim() || null }),
        ...(data.emailVerified !== undefined && { emailVerified: data.emailVerified }),
        ...(data.emailVerificationToken !== undefined && { 
          emailVerificationToken: data.emailVerificationToken 
        }),
        ...(data.emailVerificationTokenExpires !== undefined && { 
          emailVerificationTokenExpires: data.emailVerificationTokenExpires 
        }),
        ...(data.passwordResetToken !== undefined && { 
          passwordResetToken: data.passwordResetToken 
        }),
        ...(data.passwordResetTokenExpires !== undefined && { 
          passwordResetTokenExpires: data.passwordResetTokenExpires 
        }),
        ...(data.lastLoginAt && { lastLoginAt: data.lastLoginAt }),
        ...(data.loginAttempts !== undefined && { loginAttempts: data.loginAttempts }),
        ...(data.lockedUntil !== undefined && { lockedUntil: data.lockedUntil })
      }
    });
  }

  async updateUserPassword(id: string, hashedPassword: string): Promise<void> {
    await this.prisma.user.update({
      where: { id },
      data: { password: hashedPassword }
    });
  }

  // Login attempt tracking
  async incrementLoginAttempts(userId: string): Promise<void> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { loginAttempts: true }
    });

    if (!user) return;

    const newAttempts = user.loginAttempts + 1;
    const maxAttempts = 5;

    const updateData: any = {
      loginAttempts: newAttempts
    };

    // Lock account if max attempts reached
    if (newAttempts >= maxAttempts) {
      const lockUntil = new Date();
      lockUntil.setMinutes(lockUntil.getMinutes() + 30); // Lock for 30 minutes
      updateData.lockedUntil = lockUntil;
    }

    await this.prisma.user.update({
      where: { id: userId },
      data: updateData
    });
  }

  async resetLoginAttempts(userId: string): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        loginAttempts: 0,
        lockedUntil: null,
        lastLoginAt: new Date()
      }
    });
  }

  async isAccountLocked(userId: string): Promise<boolean> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { lockedUntil: true }
    });

    if (!user?.lockedUntil) return false;
    
    if (user.lockedUntil > new Date()) {
      return true;
    } else {
      // Auto-unlock expired locks
      await this.prisma.user.update({
        where: { id: userId },
        data: {
          lockedUntil: null,
          loginAttempts: 0
        }
      });
      return false;
    }
  }

  async getAccountLockInfo(userId: string): Promise<AccountLockInfo | null> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { 
        loginAttempts: true, 
        lockedUntil: true 
      }
    });

    if (!user) return null;

    const isLocked = user.lockedUntil ? user.lockedUntil > new Date() : false;
    const maxAttempts = 5;

    return {
      isLocked,
      lockExpires: user.lockedUntil,
      attempts: user.loginAttempts,
      maxAttempts,
      remainingAttempts: Math.max(0, maxAttempts - user.loginAttempts)
    };
  }

  // Password reset operations
  async findUserByPasswordResetToken(token: string): Promise<User | null> {
    return this.prisma.user.findFirst({
      where: {
        passwordResetToken: token,
        passwordResetTokenExpires: { gt: new Date() }
      }
    });
  }

  async setPasswordResetToken(userId: string, token: string, expiresAt: Date): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        passwordResetToken: token,
        passwordResetTokenExpires: expiresAt
      }
    });
  }

  async clearPasswordResetToken(userId: string): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        passwordResetToken: null,
        passwordResetTokenExpires: null
      }
    });
  }

  // Email verification operations
  async findUserByEmailVerificationToken(token: string): Promise<User | null> {
    return this.prisma.user.findFirst({
      where: {
        emailVerificationToken: token,
        emailVerificationTokenExpires: { gt: new Date() }
      }
    });
  }

  async setEmailVerificationToken(userId: string, token: string, expiresAt: Date): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        emailVerificationToken: token,
        emailVerificationTokenExpires: expiresAt
      }
    });
  }

  async markEmailAsVerified(userId: string): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        isVerified: true,
        emailVerified: new Date(),
        emailVerificationToken: null,
        emailVerificationTokenExpires: null
      }
    });
  }

  async clearEmailVerificationToken(userId: string): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        emailVerificationToken: null,
        emailVerificationTokenExpires: null
      }
    });
  }

  // Session management
  async createSession(sessionData: CreateSessionData): Promise<user_sessions> {
    return this.prisma.user_sessions.create({
      data: {
        id: sessionData.id,
        userId: sessionData.userId,
        refreshToken: sessionData.refreshToken,
        expiresAt: sessionData.expiresAt,
        ipAddress: sessionData.ipAddress.substring(0, 45), // Ensure it fits in VARCHAR(45)
        userAgent: sessionData.userAgent.substring(0, 500), // Ensure it fits in VARCHAR(500)
        isActive: true
      }
    });
  }

  async findSessionByRefreshToken(refreshToken: string): Promise<user_sessions | null> {
    return this.prisma.user_sessions.findUnique({
      where: { refreshToken }
    });
  }

  async revokeSession(refreshToken: string): Promise<void> {
    await this.prisma.user_sessions.updateMany({
      where: { refreshToken },
      data: {
        isActive: false,
        revokedAt: new Date()
      }
    });
  }

  async revokeAllUserSessions(userId: string): Promise<void> {
    await this.prisma.user_sessions.updateMany({
      where: { userId },
      data: {
        isActive: false,
        revokedAt: new Date()
      }
    });
  }

  async getUserSessions(userId: string): Promise<SessionData[]> {
    const sessions = await this.prisma.user_sessions.findMany({
      where: { 
        userId,
        isActive: true,
        expiresAt: { gt: new Date() }
      },
      select: {
        id: true,
        ipAddress: true,
        userAgent: true,
        createdAt: true,
        expiresAt: true,
        isActive: true
      },
      orderBy: { createdAt: 'desc' }
    });

    return sessions as SessionData[];
  }

  async revokeSpecificSession(sessionId: string, userId: string): Promise<boolean> {
    // Find the session first to ensure it belongs to the user
    const session = await this.prisma.user_sessions.findFirst({
      where: { 
        id: sessionId,
        userId 
      }
    });

    if (!session) {
      return false;
    }

    await this.prisma.user_sessions.update({
      where: { id: sessionId },
      data: {
        isActive: false,
        revokedAt: new Date()
      }
    });

    return true;
  }

  async cleanupExpiredSessions(): Promise<number> {
    // First, deactivate expired sessions
    const expiredSessionsResult = await this.prisma.user_sessions.updateMany({
      where: {
        expiresAt: { lt: new Date() },
        isActive: true
      },
      data: {
        isActive: false,
        revokedAt: new Date()
      }
    });

    // Then, delete old revoked sessions (30 days old)
    await this.prisma.user_sessions.deleteMany({
      where: {
        isActive: false,
        revokedAt: { lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
      }
    });

    return expiredSessionsResult.count;
  }
}

export default AuthRepository;