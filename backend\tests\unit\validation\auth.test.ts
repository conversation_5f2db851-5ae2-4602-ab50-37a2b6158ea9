import {
  changePasswordSchema,
  loginSchema,
  passwordResetConfirmSchema,
  registerSchema,
  updateProfileSchema,
} from '@/validation/auth';

describe('Auth Validation Schemas', () => {
  describe('registerSchema', () => {
    it('should validate valid registration data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'John',
        lastName: 'Doe',
        role: 'CUSTOMER',
      };

      const { error, value } = registerSchema.validate(validData);

      expect(error).toBeUndefined();
      expect(value).toEqual(validData);
    });

    it('should require email', () => {
      const invalidData = {
        password: 'SecurePass123!',
        firstName: 'John',
        lastName: 'Doe',
        role: 'CUSTOMER',
      };

      const { error } = registerSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('email');
      expect(error?.details[0].message).toContain('required');
    });

    it('should validate email format', () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'SecurePass123!',
        firstName: 'John',
        lastName: 'Doe',
        role: 'CUSTOMER',
      };

      const { error } = registerSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('email');
      expect(error?.details[0].message).toContain('valid email');
    });

    it('should require password', () => {
      const invalidData = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'CUSTOMER',
      };

      const { error } = registerSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('password');
    });

    it('should validate password strength', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: '123', // Too weak
        firstName: 'John',
        lastName: 'Doe',
        role: 'CUSTOMER',
      };

      const { error } = registerSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('password');
    });

    it('should require firstName', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        lastName: 'Doe',
        role: 'CUSTOMER',
      };

      const { error } = registerSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('firstName');
    });

    it('should require lastName', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'John',
        role: 'CUSTOMER',
      };

      const { error } = registerSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('lastName');
    });

    it('should validate role enum', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'John',
        lastName: 'Doe',
        role: 'invalid-role',
      };

      const { error } = registerSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('role');
    });

    it('should allow valid roles', () => {
      const validRoles = ['CUSTOMER', 'PROVIDER', 'ADMIN'];

      validRoles.forEach(role => {
        const validData = {
          email: '<EMAIL>',
          password: 'SecurePass123!',
          firstName: 'John',
          lastName: 'Doe',
          role,
        };

        const { error } = registerSchema.validate(validData);
        expect(error).toBeUndefined();
      });
    });

    it('should allow optional phone number', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'John',
        lastName: 'Doe',
        role: 'CUSTOMER',
        phone: '+15551234567',
      };

      const { error } = registerSchema.validate(validData);
      expect(error).toBeUndefined();
    });

    it('should trim whitespace from strings', () => {
      const dataWithWhitespace = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: '  John  ',
        lastName: '  Doe  ',
        role: 'CUSTOMER',
      };

      const { error, value } = registerSchema.validate(dataWithWhitespace);

      expect(error).toBeUndefined();
      expect(value.email).toBe('<EMAIL>');
      expect(value.firstName).toBe('John');
      expect(value.lastName).toBe('Doe');
    });
  });

  describe('loginSchema', () => {
    it('should validate valid login data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
      };

      const { error, value } = loginSchema.validate(validData);

      expect(error).toBeUndefined();
      expect(value).toEqual(validData);
    });

    it('should require email', () => {
      const invalidData = {
        password: 'SecurePass123!',
      };

      const { error } = loginSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('email');
    });

    it('should require password', () => {
      const invalidData = {
        email: '<EMAIL>',
      };

      const { error } = loginSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('password');
    });

    it('should validate email format', () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'SecurePass123!',
      };

      const { error } = loginSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('email');
    });

    it('should allow optional rememberMe field', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        rememberMe: true,
      };

      const { error } = loginSchema.validate(validData);
      expect(error).toBeUndefined();
    });
  });

  describe('changePasswordSchema', () => {
    it('should validate valid password change data', () => {
      const validData = {
        currentPassword: 'OldPass123!',
        newPassword: 'NewSecurePass456!',
      };

      const { error, value } = changePasswordSchema.validate(validData);

      expect(error).toBeUndefined();
      expect(value).toEqual(validData);
    });

    it('should require currentPassword', () => {
      const invalidData = {
        newPassword: 'NewSecurePass456!',
      };

      const { error } = changePasswordSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('currentPassword');
    });

    it('should require newPassword', () => {
      const invalidData = {
        currentPassword: 'OldPass123!',
      };

      const { error } = changePasswordSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('newPassword');
    });

    it('should validate new password strength', () => {
      const invalidData = {
        currentPassword: 'OldPass123!',
        newPassword: 'weak',
      };

      const { error } = changePasswordSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('newPassword');
    });

    it('should ensure passwords are different', () => {
      const invalidData = {
        currentPassword: 'SamePass123!',
        newPassword: 'SamePass123!',
      };

      const { error } = changePasswordSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('different');
    });
  });

  describe('passwordResetConfirmSchema', () => {
    it('should validate valid reset password data', () => {
      const validData = {
        token: 'reset-token-123',
        newPassword: 'NewSecurePass123!',
      };

      const { error, value } = passwordResetConfirmSchema.validate(validData);

      expect(error).toBeUndefined();
      expect(value).toEqual(validData);
    });

    it('should require token', () => {
      const invalidData = {
        newPassword: 'NewSecurePass123!',
      };

      const { error } = passwordResetConfirmSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('token');
    });

    it('should require newPassword', () => {
      const invalidData = {
        token: 'reset-token-123',
      };

      const { error } = passwordResetConfirmSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('newPassword');
    });

    it('should validate new password strength', () => {
      const invalidData = {
        token: 'reset-token-123',
        newPassword: 'weak',
      };

      const { error } = passwordResetConfirmSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('newPassword');
    });

    it('should validate token format', () => {
      const invalidData = {
        token: 'ab', // Too short
        newPassword: 'NewSecurePass123!',
      };

      const { error } = passwordResetConfirmSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('token');
    });
  });

  describe('updateProfileSchema', () => {
    it('should validate valid profile update data', () => {
      const validData = {
        firstName: 'UpdatedJohn',
        lastName: 'UpdatedDoe',
        phone: '+15559876543',
      };

      const { error, value } = updateProfileSchema.validate(validData);

      expect(error).toBeUndefined();
      expect(value).toEqual(validData);
    });

    it('should allow partial updates', () => {
      const partialData = {
        firstName: 'UpdatedJohn',
      };

      const { error } = updateProfileSchema.validate(partialData);

      expect(error).toBeUndefined();
    });

    it('should validate email format if provided', () => {
      const invalidData = {
        email: 'invalid-email-format',
        firstName: 'John',
      };

      const { error } = updateProfileSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('email');
    });

    it('should allow valid email update', () => {
      const validData = {
        email: '<EMAIL>',
        firstName: 'John',
      };

      const { error } = updateProfileSchema.validate(validData);

      expect(error).toBeUndefined();
    });

    it('should validate phone format if provided', () => {
      const validPhoneFormats = ['+15551234567', '15551234567', '+12345678901'];

      validPhoneFormats.forEach(phone => {
        const data = { phone };
        const { error } = updateProfileSchema.validate(data);
        expect(error).toBeUndefined();
      });
    });

    it('should reject empty string updates', () => {
      const invalidData = {
        firstName: '',
        lastName: 'Doe',
      };

      const { error } = updateProfileSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('firstName');
    });

    it('should trim whitespace from strings', () => {
      const dataWithWhitespace = {
        firstName: '  UpdatedJohn  ',
        lastName: '  UpdatedDoe  ',
        email: '<EMAIL>',
      };

      const { error, value } = updateProfileSchema.validate(dataWithWhitespace);

      expect(error).toBeUndefined();
      expect(value.firstName).toBe('UpdatedJohn');
      expect(value.lastName).toBe('UpdatedDoe');
      expect(value.email).toBe('<EMAIL>');
    });

    it('should require at least one field for update', () => {
      const emptyData = {};

      const { error } = updateProfileSchema.validate(emptyData);

      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('At least one field must be provided');
    });
  });

  describe('common validation patterns', () => {
    it('should handle undefined values gracefully', () => {
      const schemas = [
        registerSchema,
        loginSchema,
        changePasswordSchema,
        passwordResetConfirmSchema,
        updateProfileSchema,
      ];

      schemas.forEach(schema => {
        const { error } = schema.validate(undefined);
        expect(error).toBeUndefined(); // Joi object schemas accept undefined by default
      });
    });

    it('should handle null values gracefully', () => {
      const schemas = [
        registerSchema,
        loginSchema,
        changePasswordSchema,
        passwordResetConfirmSchema,
        updateProfileSchema,
      ];

      schemas.forEach(schema => {
        const { error } = schema.validate(null);
        expect(error).toBeDefined(); // Should require at least some fields
      });
    });

    it('should reject extra unknown fields', () => {
      const dataWithExtra = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        unknownField: 'should be rejected',
      };

      const { error } = loginSchema.validate(dataWithExtra);

      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('not allowed');
    });
  });
});
