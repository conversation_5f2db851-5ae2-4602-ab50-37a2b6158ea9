import { PrismaClient, User, UserRole } from '@prisma/client';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { AuthenticatedRequest } from '../types/auth';

const prisma = new PrismaClient();

// Mock authentication middleware for testing
export const mockAuthenticate = (user: User) => {
  return (req: AuthenticatedRequest, res: any, next: any) => {
    req.user = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      isActive: user.isActive,
      isVerified: user.isVerified,
      avatar: user.avatar,
      phone: user.phone,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLoginAt: user.lastLoginAt,
    };
    req.userId = user.id;
    next();
  };
};

// Create test user with proper password hashing
export async function createTestUser(userData: {
  email: string;
  firstName?: string;
  lastName?: string;
  role?: UserRole;
  isActive?: boolean;
  isVerified?: boolean;
  phone?: string;
  avatar?: string;
}) {
  const hashedPassword = await bcrypt.hash('TestPass123!', 10);

  return await prisma.user.create({
    data: {
      email: userData.email,
      password: hashedPassword,
      firstName: userData.firstName || 'Test',
      lastName: userData.lastName || 'User',
      role: userData.role || UserRole.CUSTOMER,
      isActive: userData.isActive ?? true,
      isVerified: userData.isVerified ?? false,
      phone: userData.phone || null,
      avatar: userData.avatar || null,
    },
  });
}

// Generate auth token for testing
export function generateAuthToken(user: User): string {
  const payload = {
    userId: user.id,
    email: user.email,
    role: user.role,
  };

  return jwt.sign(payload, 'test-jwt-secret-key-for-testing-only', {
    expiresIn: '1h',
  });
}

// Clean up test data
export async function cleanupTestData(emails: string[]) {
  await prisma.user.deleteMany({
    where: {
      email: {
        in: emails,
      },
    },
  });
}

// Mock request object for testing
export function createMockRequest(user?: User): Partial<AuthenticatedRequest> {
  const mockReq: Partial<AuthenticatedRequest> = {
    user: user
      ? {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          isActive: user.isActive,
          isVerified: user.isVerified,
          avatar: user.avatar,
          phone: user.phone,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          lastLoginAt: user.lastLoginAt,
        }
      : undefined,
    userId: user?.id,
    body: {},
    params: {},
    query: {},
    headers: {},
    get: jest.fn(),
  };

  return mockReq;
}

// Mock response object for testing
export function createMockResponse() {
  const res: any = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
  };

  return res;
}

// Test data generators
export const testUsers = {
  admin: {
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    role: UserRole.ADMIN,
    isVerified: true,
  },
  customer: {
    email: '<EMAIL>',
    firstName: 'Customer',
    lastName: 'User',
    role: UserRole.CUSTOMER,
    isVerified: true,
    phone: '+**********',
  },
  provider: {
    email: '<EMAIL>',
    firstName: 'Provider',
    lastName: 'User',
    role: UserRole.PROVIDER,
    isVerified: true,
    phone: '+**********',
  },
  unverified: {
    email: '<EMAIL>',
    firstName: 'Unverified',
    lastName: 'User',
    role: UserRole.CUSTOMER,
    isVerified: false,
  },
  inactive: {
    email: '<EMAIL>',
    firstName: 'Inactive',
    lastName: 'User',
    role: UserRole.CUSTOMER,
    isActive: false,
  },
};

// Validation test data
export const validationTestData = {
  validUser: {
    email: '<EMAIL>',
    password: 'ValidPass123!',
    firstName: 'Valid',
    lastName: 'User',
    role: UserRole.CUSTOMER,
  },
  invalidEmail: {
    email: 'invalid-email',
    password: 'ValidPass123!',
    firstName: 'Invalid',
    lastName: 'Email',
  },
  weakPassword: {
    email: '<EMAIL>',
    password: 'weak',
    firstName: 'Weak',
    lastName: 'Password',
  },
  missingFields: {
    email: '<EMAIL>',
    password: 'ValidPass123!',
    // Missing firstName and lastName
  },
  longName: {
    email: '<EMAIL>',
    password: 'ValidPass123!',
    firstName: 'a'.repeat(151), // Exceeds 150 char limit
    lastName: 'User',
  },
};

// Performance test helpers
export async function createBulkTestUsers(count: number, prefix: string = 'bulk') {
  const users = [];

  for (let i = 0; i < count; i++) {
    const user = await createTestUser({
      email: `${prefix}${i}@test.com`,
      firstName: `${prefix}${i}`,
      lastName: 'User',
      role: UserRole.CUSTOMER,
    });
    users.push(user);
  }

  return users;
}

// Database helpers
export async function resetTestDatabase() {
  // This would typically reset the test database
  // For now, we'll just clean up users
  await prisma.user.deleteMany({
    where: {
      email: {
        contains: '@test.com',
      },
    },
  });
}

export async function getTestDatabaseStats() {
  const totalUsers = await prisma.user.count();
  const activeUsers = await prisma.user.count({ where: { isActive: true } });
  const verifiedUsers = await prisma.user.count({ where: { isVerified: true } });

  return {
    totalUsers,
    activeUsers,
    verifiedUsers,
    inactiveUsers: totalUsers - activeUsers,
    unverifiedUsers: totalUsers - verifiedUsers,
  };
}
