import { UserRole, User } from '@prisma/client';
import { AuthenticatedRequest } from '../types/auth';
export declare const mockAuthenticate: (user: User) => (req: AuthenticatedRequest, res: any, next: any) => void;
export declare function createTestUser(userData: {
    email: string;
    firstName?: string;
    lastName?: string;
    role?: UserRole;
    isActive?: boolean;
    isVerified?: boolean;
    phone?: string;
    avatar?: string;
}): Promise<{
    password: string | null;
    email: string;
    id: string;
    firstName: string;
    lastName: string;
    role: import(".prisma/client").$Enums.UserRole;
    isActive: boolean;
    isVerified: boolean;
    emailVerified: Date | null;
    emailVerificationToken: string | null;
    emailVerificationTokenExpires: Date | null;
    passwordResetToken: string | null;
    passwordResetTokenExpires: Date | null;
    lastLoginAt: Date | null;
    loginAttempts: number;
    lockedUntil: Date | null;
    avatar: string | null;
    phone: string | null;
    createdAt: Date;
    updatedAt: Date;
}>;
export declare function generateAuthToken(user: User): string;
export declare function cleanupTestData(emails: string[]): Promise<void>;
export declare function createMockRequest(user?: User): Partial<AuthenticatedRequest>;
export declare function createMockResponse(): any;
export declare const testUsers: {
    admin: {
        email: string;
        firstName: string;
        lastName: string;
        role: "ADMIN";
        isVerified: boolean;
    };
    customer: {
        email: string;
        firstName: string;
        lastName: string;
        role: "CUSTOMER";
        isVerified: boolean;
        phone: string;
    };
    provider: {
        email: string;
        firstName: string;
        lastName: string;
        role: "PROVIDER";
        isVerified: boolean;
        phone: string;
    };
    unverified: {
        email: string;
        firstName: string;
        lastName: string;
        role: "CUSTOMER";
        isVerified: boolean;
    };
    inactive: {
        email: string;
        firstName: string;
        lastName: string;
        role: "CUSTOMER";
        isActive: boolean;
    };
};
export declare const validationTestData: {
    validUser: {
        email: string;
        password: string;
        firstName: string;
        lastName: string;
        role: "CUSTOMER";
    };
    invalidEmail: {
        email: string;
        password: string;
        firstName: string;
        lastName: string;
    };
    weakPassword: {
        email: string;
        password: string;
        firstName: string;
        lastName: string;
    };
    missingFields: {
        email: string;
        password: string;
    };
    longName: {
        email: string;
        password: string;
        firstName: string;
        lastName: string;
    };
};
export declare function createBulkTestUsers(count: number, prefix?: string): Promise<{
    password: string | null;
    email: string;
    id: string;
    firstName: string;
    lastName: string;
    role: import(".prisma/client").$Enums.UserRole;
    isActive: boolean;
    isVerified: boolean;
    emailVerified: Date | null;
    emailVerificationToken: string | null;
    emailVerificationTokenExpires: Date | null;
    passwordResetToken: string | null;
    passwordResetTokenExpires: Date | null;
    lastLoginAt: Date | null;
    loginAttempts: number;
    lockedUntil: Date | null;
    avatar: string | null;
    phone: string | null;
    createdAt: Date;
    updatedAt: Date;
}[]>;
export declare function resetTestDatabase(): Promise<void>;
export declare function getTestDatabaseStats(): Promise<{
    totalUsers: number;
    activeUsers: number;
    verifiedUsers: number;
    inactiveUsers: number;
    unverifiedUsers: number;
}>;
//# sourceMappingURL=testUtils.d.ts.map