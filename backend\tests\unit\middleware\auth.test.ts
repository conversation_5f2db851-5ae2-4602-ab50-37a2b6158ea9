import { UserRole } from '@prisma/client';
import { NextFunction, Response } from 'express';
import jwt from 'jsonwebtoken';

// Mock crypto before importing auth middleware
jest.mock('crypto', () => ({
  randomBytes: jest.fn(() => ({
    toString: jest.fn(() => 'mock-jti-123'),
  })),
}));

import {
  authenticate,
  authorize,
  authRateLimit,
  generateAccessToken,
  generateRefreshToken,
  optionalAuthenticate,
  requireEmailVerification,
  requireOwnershipOrAdmin,
  verifyAccessToken,
  verifyRefreshToken,
} from '@/middleware/auth';
import { AuthenticationError, AuthorizationError } from '@/middleware/errorHandler';
import { AuthenticatedRequest, JWTPayload } from '@/types/auth';

// Mock jwt
jest.mock('jsonwebtoken');
const mockJwt = jwt as jest.Mocked<typeof jwt>;

// Mock logger
jest.mock('@/utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    security: jest.fn(),
    auth: jest.fn(),
    performance: jest.fn(),
    database: jest.fn(),
  })),
  authLogger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
  securityLogger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

describe('Auth Middleware', () => {
  let mockRequest: Partial<AuthenticatedRequest>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    // Reset modules and mock crypto for each test
    jest.resetModules();
    jest.doMock('crypto', () => ({
      randomBytes: jest.fn(() => ({
        toString: jest.fn(() => 'mock-jti-123'),
      })),
    }));

    mockRequest = {
      headers: {},
      ip: '***********',
      connection: {
        remoteAddress: '***********',
        destroySoon: jest.fn(),
        write: jest.fn(),
        connect: jest.fn(),
        setEncoding: jest.fn(),
      } as any,
      path: '/test',
      params: {},
      body: {},
    };

    mockResponse = {
      getHeader: jest.fn(),
    };

    mockNext = jest.fn();

    // Set environment variables for tests
    process.env.JWT_SECRET = 'test-jwt-secret-key-that-is-at-least-32-characters-long-for-testing-only';
    process.env.JWT_REFRESH_SECRET = 'test-refresh-secret-key-that-is-at-least-32-characters-long-for-testing-only';
    process.env.JWT_EXPIRES_IN = '15m';
    process.env.JWT_REFRESH_EXPIRES_IN = '7d';

    jest.clearAllMocks();
  });

  afterEach(() => {
    delete process.env.JWT_SECRET;
    delete process.env.JWT_REFRESH_SECRET;
    delete process.env.JWT_EXPIRES_IN;
    delete process.env.JWT_REFRESH_EXPIRES_IN;
  });

  describe('generateAccessToken', () => {
    it('should generate access token with correct payload', () => {
      const payload: JWTPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
        role: 'CUSTOMER' as UserRole,
      };

      mockJwt.sign.mockReturnValue('mock-access-token' as any);

      const token = generateAccessToken(payload);

      expect(mockJwt.sign).toHaveBeenCalledWith(
        {
          userId: 'user-123',
          email: '<EMAIL>',
          role: 'CUSTOMER',
          jti: 'mock-jti-123',
        },
        'test-jwt-secret-key-that-is-at-least-32-characters-long-for-testing-only',
        { expiresIn: '15m' }
      );
      expect(token).toBe('mock-access-token');
    });

    it('should generate access token with custom options', () => {
      const payload: JWTPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
        role: 'ADMIN' as UserRole,
      };

      mockJwt.sign.mockReturnValue('mock-custom-token' as any);

      const token = generateAccessToken(payload, 'custom-secret', '30m', 'test-issuer', 'test-audience');

      expect(mockJwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'user-123',
          email: '<EMAIL>',
          role: 'ADMIN',
        }),
        'custom-secret',
        {
          expiresIn: '30m',
          issuer: 'test-issuer',
          audience: 'test-audience',
        }
      );
    });
  });

  describe('generateRefreshToken', () => {
    it('should generate refresh token with correct payload', () => {
      const payload: JWTPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
        role: 'PROVIDER' as UserRole,
      };

      mockJwt.sign.mockReturnValue('mock-refresh-token' as any);

      const token = generateRefreshToken(payload);

      expect(mockJwt.sign).toHaveBeenCalledWith(
        {
          userId: 'user-123',
          email: '<EMAIL>',
          role: 'PROVIDER',
          jti: 'mock-jti-123',
        },
        'test-refresh-secret-key-that-is-at-least-32-characters-long-for-testing-only',
        { expiresIn: '7d' }
      );
      expect(token).toBe('mock-refresh-token');
    });
  });

  describe('verifyAccessToken', () => {
    it('should verify valid access token', () => {
      const mockPayload: JWTPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
        role: 'CUSTOMER' as UserRole,
      };

      mockJwt.verify.mockReturnValue(mockPayload as any);

      const result = verifyAccessToken('valid-token');

      expect(mockJwt.verify).toHaveBeenCalledWith(
        'valid-token',
        'test-jwt-secret-key-that-is-at-least-32-characters-long-for-testing-only'
      );
      expect(result).toEqual(mockPayload);
    });

    it('should throw AuthenticationError for expired token', () => {
      mockJwt.verify.mockImplementation(() => {
        throw new jwt.TokenExpiredError('Token expired', new Date());
      });

      expect(() => verifyAccessToken('expired-token')).toThrow(AuthenticationError);
      expect(() => verifyAccessToken('expired-token')).toThrow('Access token expired');
    });

    it('should throw AuthenticationError for invalid token', () => {
      mockJwt.verify.mockImplementation(() => {
        throw new jwt.JsonWebTokenError('Invalid token');
      });

      expect(() => verifyAccessToken('invalid-token')).toThrow(AuthenticationError);
      expect(() => verifyAccessToken('invalid-token')).toThrow('Invalid access token');
    });

    it('should throw AuthenticationError for unknown verification error', () => {
      mockJwt.verify.mockImplementation(() => {
        throw new Error('Unknown error');
      });

      expect(() => verifyAccessToken('token')).toThrow(AuthenticationError);
      expect(() => verifyAccessToken('token')).toThrow('Token verification failed');
    });
  });

  describe('verifyRefreshToken', () => {
    it('should verify valid refresh token', () => {
      const mockPayload: JWTPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
        role: 'CUSTOMER' as UserRole,
      };

      mockJwt.verify.mockReturnValue(mockPayload as any);

      const result = verifyRefreshToken('valid-refresh-token');

      expect(mockJwt.verify).toHaveBeenCalledWith(
        'valid-refresh-token',
        'test-refresh-secret-key-that-is-at-least-32-characters-long-for-testing-only'
      );
      expect(result).toEqual(mockPayload);
    });

    it('should throw AuthenticationError for expired refresh token', () => {
      mockJwt.verify.mockImplementation(() => {
        throw new jwt.TokenExpiredError('Token expired', new Date());
      });

      expect(() => verifyRefreshToken('expired-token')).toThrow(AuthenticationError);
      expect(() => verifyRefreshToken('expired-token')).toThrow('Refresh token expired');
    });
  });

  describe('authenticate middleware', () => {
    it('should authenticate valid bearer token', async () => {
      const mockPayload: JWTPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
        role: 'CUSTOMER' as UserRole,
      };

      mockRequest.headers = {
        authorization: 'Bearer valid-token',
      };

      mockJwt.verify.mockReturnValue(mockPayload as any);

      await authenticate(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockRequest.userId).toBe('user-123');
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should throw AuthenticationError when no authorization header', async () => {
      mockRequest.headers = {};

      await authenticate(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(AuthenticationError));
    });

    it('should throw AuthenticationError when authorization header is not Bearer', async () => {
      mockRequest.headers = {
        authorization: 'Basic invalid-format',
      };

      await authenticate(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(AuthenticationError));
    });

    it('should handle token verification errors', async () => {
      mockRequest.headers = {
        authorization: 'Bearer invalid-token',
      };

      mockJwt.verify.mockImplementation(() => {
        throw new jwt.JsonWebTokenError('Invalid token');
      });

      await authenticate(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  describe('optionalAuthenticate middleware', () => {
    it('should authenticate when valid token provided', async () => {
      const mockPayload: JWTPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
        role: 'CUSTOMER' as UserRole,
      };

      mockRequest.headers = {
        authorization: 'Bearer valid-token',
      };

      mockJwt.verify.mockReturnValue(mockPayload as any);

      await optionalAuthenticate(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockRequest.userId).toBe('user-123');
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should continue without authentication when no token provided', async () => {
      mockRequest.headers = {};

      await optionalAuthenticate(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockRequest.userId).toBeUndefined();
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should continue when token verification fails', async () => {
      mockRequest.headers = {
        authorization: 'Bearer invalid-token',
      };

      mockJwt.verify.mockImplementation(() => {
        throw new jwt.JsonWebTokenError('Invalid token');
      });

      await optionalAuthenticate(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });
  });

  describe('authorize middleware', () => {
    it('should authorize user with correct role', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'ADMIN' as UserRole,
      } as any;

      const middleware = authorize('ADMIN' as UserRole);
      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should authorize user with one of multiple allowed roles', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'PROVIDER' as UserRole,
      } as any;

      const middleware = authorize('ADMIN' as UserRole, 'PROVIDER' as UserRole);
      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should throw AuthenticationError when no user', () => {
      mockRequest.user = undefined;

      const middleware = authorize('ADMIN' as UserRole);

      expect(() => {
        middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);
      }).toThrow(AuthenticationError);
    });

    it('should throw AuthorizationError when user role not allowed', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'CUSTOMER' as UserRole,
      } as any;

      const middleware = authorize('ADMIN' as UserRole);

      expect(() => {
        middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);
      }).toThrow(AuthorizationError);
    });
  });

  describe('requireEmailVerification middleware', () => {
    it('should pass when user is verified', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>',
        isVerified: true,
      } as any;

      requireEmailVerification(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should throw AuthenticationError when no user', () => {
      mockRequest.user = undefined;

      expect(() => {
        requireEmailVerification(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);
      }).toThrow(AuthenticationError);
    });

    it('should throw AuthorizationError when user is not verified', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>',
        isVerified: false,
      } as any;

      expect(() => {
        requireEmailVerification(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);
      }).toThrow(AuthorizationError);
    });
  });

  describe('requireOwnershipOrAdmin middleware', () => {
    it('should allow admin access to any resource', () => {
      mockRequest.user = {
        id: 'admin-123',
        role: 'ADMIN' as UserRole,
      } as any;
      mockRequest.params = { userId: 'other-user-123' };

      const middleware = requireOwnershipOrAdmin();
      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should allow owner access to their own resource', () => {
      mockRequest.user = {
        id: 'user-123',
        role: 'CUSTOMER' as UserRole,
      } as any;
      mockRequest.params = { userId: 'user-123' };

      const middleware = requireOwnershipOrAdmin();
      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should allow access when target user ID is in request body', () => {
      mockRequest.user = {
        id: 'user-123',
        role: 'CUSTOMER' as UserRole,
      } as any;
      mockRequest.body = { userId: 'user-123' };

      const middleware = requireOwnershipOrAdmin();
      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should use custom parameter name', () => {
      mockRequest.user = {
        id: 'user-123',
        role: 'customer' as UserRole,
      } as any;
      mockRequest.params = { customId: 'user-123' };

      const middleware = requireOwnershipOrAdmin('customId');
      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should throw AuthenticationError when no user', () => {
      mockRequest.user = undefined;

      const middleware = requireOwnershipOrAdmin();

      expect(() => {
        middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);
      }).toThrow(AuthenticationError);
    });

    it('should throw AuthorizationError when access denied', () => {
      mockRequest.user = {
        id: 'user-123',
        role: 'CUSTOMER' as UserRole,
      } as any;
      mockRequest.params = { userId: 'other-user-123' };

      const middleware = requireOwnershipOrAdmin();

      expect(() => {
        middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);
      }).toThrow(AuthorizationError);
    });
  });

  describe('authRateLimit middleware', () => {
    beforeEach(() => {
      // Clear any previous test state
      process.env.NODE_ENV = 'test';
    });

    it('should allow requests within rate limit', () => {
      const middleware = authRateLimit(5, 60000);

      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should use test-friendly limits in test environment', () => {
      process.env.NODE_ENV = 'test';
      const middleware = authRateLimit(2, 60000);

      // Should allow many requests in test mode
      for (let i = 0; i < 10; i++) {
        middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);
      }

      expect(mockNext).toHaveBeenCalledTimes(10);
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should throw AuthenticationError when rate limit exceeded', () => {
      process.env.NODE_ENV = 'production';
      const middleware = authRateLimit(2, 60000);

      // First two requests should pass
      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);
      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      // Third request should be rate limited
      expect(() => {
        middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);
      }).toThrow(AuthenticationError);
    });

    it('should reset counter after window expires', done => {
      process.env.NODE_ENV = 'production';
      const middleware = authRateLimit(1, 50); // 50ms window

      // First request should pass
      middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);

      // Wait for window to expire
      setTimeout(() => {
        // This request should also pass after reset
        middleware(mockRequest as AuthenticatedRequest, mockResponse as Response, mockNext);
        expect(mockNext).toHaveBeenCalledTimes(2);
        done();
      }, 60);
    });

    afterEach(() => {
      delete process.env.NODE_ENV;
    });
  });
});
