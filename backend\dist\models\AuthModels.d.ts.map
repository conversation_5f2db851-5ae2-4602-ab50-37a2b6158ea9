{"version": 3, "file": "AuthModels.d.ts", "sourceRoot": "", "sources": ["../../src/models/AuthModels.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC1C,OAAO,EACL,eAAe,EACf,YAAY,EAEZ,qBAAqB,EACrB,oBAAoB,EACpB,QAAQ,EACR,SAAS,EACT,WAAW,EACZ,MAAM,eAAe,CAAC;AAGvB,qBAAa,mBAAmB;IAE9B,MAAM,CAAC,sBAAsB,CAAC,OAAO,EAAE,eAAe,GAAG,gBAAgB;IAWzE,MAAM,CAAC,eAAe,CAAC,OAAO,EAAE,YAAY,GAAG,SAAS;IAKxD,MAAM,CAAC,uBAAuB,CAAC,OAAO,EAAE,oBAAoB,GAAG,iBAAiB;IAUhF,MAAM,CAAC,wBAAwB,CAAC,OAAO,EAAE,qBAAqB,GAAG,kBAAkB;CAGpF;AAGD,qBAAa,gBAAgB;aAET,KAAK,EAAE,MAAM;aACb,QAAQ,EAAE,MAAM;aAChB,SAAS,EAAE,MAAM;aACjB,QAAQ,EAAE,MAAM;aAChB,KAAK,CAAC,EAAE,MAAM;gBAJd,KAAK,EAAE,MAAM,EACb,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,EACjB,QAAQ,EAAE,MAAM,EAChB,KAAK,CAAC,EAAE,MAAM,YAAA;IAKhC,OAAO,CAAC,QAAQ;IAsBhB,OAAO,CAAC,YAAY;IAKpB,OAAO,CAAC,eAAe;IAMvB,OAAO,CAAC,YAAY;IAMpB,cAAc;;;;;;CAQf;AAGD,qBAAa,SAAS;aAEF,KAAK,EAAE,MAAM;aACb,QAAQ,EAAE,MAAM;gBADhB,KAAK,EAAE,MAAM,EACb,QAAQ,EAAE,MAAM;IAKlC,OAAO,CAAC,QAAQ;IAehB,kBAAkB,IAAI,MAAM;CAG7B;AAGD,qBAAa,iBAAiB;aAEV,SAAS,CAAC,EAAE,MAAM;aAClB,QAAQ,CAAC,EAAE,MAAM;aACjB,KAAK,CAAC,EAAE,MAAM;aACd,MAAM,CAAC,EAAE,MAAM;gBAHf,SAAS,CAAC,EAAE,MAAM,YAAA,EAClB,QAAQ,CAAC,EAAE,MAAM,YAAA,EACjB,KAAK,CAAC,EAAE,MAAM,YAAA,EACd,MAAM,CAAC,EAAE,MAAM,YAAA;IAKjC,OAAO,CAAC,QAAQ;IAkBhB,OAAO,CAAC,WAAW;IAKnB,OAAO,CAAC,YAAY;IAKpB,OAAO,CAAC,UAAU;IAUlB,UAAU,IAAI,OAAO;IAQrB,cAAc;CAUf;AAGD,qBAAa,kBAAkB;aAEX,eAAe,EAAE,MAAM;aACvB,WAAW,EAAE,MAAM;gBADnB,eAAe,EAAE,MAAM,EACvB,WAAW,EAAE,MAAM;IAKrC,OAAO,CAAC,QAAQ;IAkBhB,OAAO,CAAC,eAAe;CAKxB;AAGD,qBAAa,WAAW;aAEJ,EAAE,EAAE,MAAM;aACV,KAAK,EAAE,MAAM;aACb,SAAS,EAAE,MAAM;aACjB,QAAQ,EAAE,MAAM;aAChB,IAAI,EAAE,QAAQ;aACd,QAAQ,EAAE,OAAO;aACjB,UAAU,EAAE,OAAO;aACnB,MAAM,EAAE,MAAM,GAAG,IAAI;aACrB,KAAK,EAAE,MAAM,GAAG,IAAI;aACpB,SAAS,EAAE,IAAI;aACf,SAAS,EAAE,IAAI;aACf,WAAW,EAAE,IAAI,GAAG,IAAI;gBAXxB,EAAE,EAAE,MAAM,EACV,KAAK,EAAE,MAAM,EACb,SAAS,EAAE,MAAM,EACjB,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,QAAQ,EACd,QAAQ,EAAE,OAAO,EACjB,UAAU,EAAE,OAAO,EACnB,MAAM,EAAE,MAAM,GAAG,IAAI,EACrB,KAAK,EAAE,MAAM,GAAG,IAAI,EACpB,SAAS,EAAE,IAAI,EACf,SAAS,EAAE,IAAI,EACf,WAAW,EAAE,IAAI,GAAG,IAAI;IAI1C,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,GAAG,WAAW;IAkBhD,cAAc,IAAI,MAAM;IAKxB,iBAAiB,IAAI,OAAO;IAK5B,uBAAuB,IAAI,MAAM;IAcjC,MAAM;;;;;;;;;;;;;;;;;CAmBP;AAGD,qBAAa,sBAAsB;aAEf,IAAI,EAAE,WAAW;aACjB,MAAM,EAAE,SAAS;gBADjB,IAAI,EAAE,WAAW,EACjB,MAAM,EAAE,SAAS;IAInC,aAAa,IAAI,WAAW,CAAC;QAAE,IAAI,EAAE,WAAW,CAAC;QAAC,MAAM,EAAE,SAAS,CAAA;KAAE,CAAC;CAUvE;AAGD,qBAAa,WAAW;aAEJ,EAAE,EAAE,MAAM;aACV,SAAS,EAAE,MAAM;aACjB,SAAS,EAAE,MAAM;aACjB,SAAS,EAAE,IAAI;aACf,SAAS,EAAE,IAAI;aACf,QAAQ,EAAE,OAAO;gBALjB,EAAE,EAAE,MAAM,EACV,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,IAAI,EACf,SAAS,EAAE,IAAI,EACf,QAAQ,EAAE,OAAO;IAInC,SAAS,CAAC,gBAAgB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,GAAG,OAAO;IAKtE,aAAa,IAAI;QAAE,OAAO,EAAE,MAAM,CAAC;QAAC,EAAE,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE;IAgChE,sBAAsB,IAAI,MAAM;IAKhC,SAAS,IAAI,OAAO;IAKpB,MAAM;;;;;;;;;qBA1CsB,MAAM;gBAAM,MAAM;oBAAU,MAAM;;;CAwD/D;;;;;;;;;;;AAED,wBASE"}