const request = require('supertest');

async function testAuthEndpoint() {
  try {
    // Import app
    const appModule = await import('./src/app.ts');
    const app = appModule.default;
    
    console.log('🧪 Testing auth endpoint...');
    
    const testUser = {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'User',
      phone: '+1234567890'
    };
    
    const response = await request(app)
      .post('/api/auth/register')
      .send(testUser);
    
    console.log('📊 Response Status:', response.status);
    console.log('📋 Response Body:', JSON.stringify(response.body, null, 2));
    console.log('📄 Response Text:', response.text);
    
  } catch (error) {
    console.error('❌ Test Error:', error.message);
    console.error('📜 Full Error:', error);
  }
}

testAuthEndpoint();