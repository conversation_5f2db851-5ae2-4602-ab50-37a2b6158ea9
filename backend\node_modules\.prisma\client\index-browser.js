
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.13.0
 * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
 */
Prisma.prismaVersion = {
  client: "6.13.0",
  engine: "361e86d0ea4987e9f53a565309b3eed797a6bcbd"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  firstName: 'firstName',
  lastName: 'lastName',
  role: 'role',
  isActive: 'isActive',
  isVerified: 'isVerified',
  emailVerified: 'emailVerified',
  emailVerificationToken: 'emailVerificationToken',
  emailVerificationTokenExpires: 'emailVerificationTokenExpires',
  passwordResetToken: 'passwordResetToken',
  passwordResetTokenExpires: 'passwordResetTokenExpires',
  lastLoginAt: 'lastLoginAt',
  loginAttempts: 'loginAttempts',
  lockedUntil: 'lockedUntil',
  avatar: 'avatar',
  phone: 'phone',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProviderScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  businessName: 'businessName',
  description: 'description',
  email: 'email',
  website: 'website',
  phone: 'phone',
  isVerified: 'isVerified',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProviderServiceScalarFieldEnum = {
  id: 'id',
  providerId: 'providerId',
  serviceId: 'serviceId',
  price: 'price',
  description: 'description',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ServiceScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  detailedDesc: 'detailedDesc',
  features: 'features',
  notes: 'notes',
  categoryId: 'categoryId',
  image: 'image',
  basePrice: 'basePrice',
  pricingType: 'pricingType',
  isActive: 'isActive',
  sortOrder: 'sortOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ServiceFormFieldScalarFieldEnum = {
  id: 'id',
  serviceId: 'serviceId',
  name: 'name',
  label: 'label',
  type: 'type',
  required: 'required',
  placeholder: 'placeholder',
  defaultValue: 'defaultValue',
  validation: 'validation',
  sortOrder: 'sortOrder',
  isActive: 'isActive',
  createdAt: 'createdAt'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  providerId: 'providerId',
  serviceId: 'serviceId',
  status: 'status',
  totalAmount: 'totalAmount',
  description: 'description',
  requirements: 'requirements',
  dueDate: 'dueDate',
  completedAt: 'completedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderItemScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  name: 'name',
  description: 'description',
  quantity: 'quantity',
  price: 'price',
  createdAt: 'createdAt'
};

exports.Prisma.OrderFileScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  filename: 'filename',
  originalName: 'originalName',
  mimeType: 'mimeType',
  size: 'size',
  url: 'url',
  type: 'type',
  isPublic: 'isPublic',
  virusScanned: 'virusScanned',
  accessToken: 'accessToken',
  uploadedBy: 'uploadedBy',
  createdAt: 'createdAt'
};

exports.Prisma.OrderStatusHistoryScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  status: 'status',
  comment: 'comment',
  changedBy: 'changedBy',
  createdAt: 'createdAt'
};

exports.Prisma.AdminsScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  role: 'role',
  permissions: 'permissions',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Gallery_imagesScalarFieldEnum = {
  id: 'id',
  filename: 'filename',
  originalName: 'originalName',
  mimeType: 'mimeType',
  size: 'size',
  url: 'url',
  title: 'title',
  description: 'description',
  category: 'category',
  tags: 'tags',
  isActive: 'isActive',
  sortOrder: 'sortOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Provider_operating_hoursScalarFieldEnum = {
  id: 'id',
  providerId: 'providerId',
  dayOfWeek: 'dayOfWeek',
  openTime: 'openTime',
  closeTime: 'closeTime',
  createdAt: 'createdAt'
};

exports.Prisma.Provider_rating_displaysScalarFieldEnum = {
  id: 'id',
  providerId: 'providerId',
  averageRating: 'averageRating',
  reviewCount: 'reviewCount',
  lastUpdated: 'lastUpdated',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Provider_service_areasScalarFieldEnum = {
  id: 'id',
  providerId: 'providerId',
  streetAddress: 'streetAddress',
  zipCode: 'zipCode',
  city: 'city',
  state: 'state',
  isActive: 'isActive',
  createdAt: 'createdAt'
};

exports.Prisma.Service_categoriesScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  icon: 'icon',
  route: 'route',
  isActive: 'isActive',
  sortOrder: 'sortOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Service_field_optionsScalarFieldEnum = {
  id: 'id',
  fieldId: 'fieldId',
  value: 'value',
  label: 'label',
  priceModifier: 'priceModifier',
  sortOrder: 'sortOrder',
  isActive: 'isActive',
  createdAt: 'createdAt'
};

exports.Prisma.User_addressesScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  streetAddress: 'streetAddress',
  city: 'city',
  state: 'state',
  zipCode: 'zipCode',
  country: 'country',
  isDefault: 'isDefault',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.User_sessionsScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  refreshToken: 'refreshToken',
  expiresAt: 'expiresAt',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  isActive: 'isActive',
  revokedAt: 'revokedAt',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserRole = exports.$Enums.UserRole = {
  CUSTOMER: 'CUSTOMER',
  PROVIDER: 'PROVIDER',
  ADMIN: 'ADMIN'
};

exports.ServicePricingType = exports.$Enums.ServicePricingType = {
  FIXED: 'FIXED',
  VARIABLE: 'VARIABLE',
  QUOTE: 'QUOTE'
};

exports.FormFieldType = exports.$Enums.FormFieldType = {
  TEXT: 'TEXT',
  NUMBER: 'NUMBER',
  SELECT: 'SELECT',
  CHECKBOX: 'CHECKBOX',
  RADIO: 'RADIO',
  FILE: 'FILE'
};

exports.OrderStatus = exports.$Enums.OrderStatus = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  IN_PROGRESS: 'IN_PROGRESS',
  REVIEW: 'REVIEW',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED'
};

exports.FileType = exports.$Enums.FileType = {
  DESIGN: 'DESIGN',
  REFERENCE: 'REFERENCE',
  PROOF: 'PROOF',
  FINAL: 'FINAL',
  OTHER: 'OTHER'
};

exports.AdminRole = exports.$Enums.AdminRole = {
  ADMIN: 'ADMIN',
  SUPER_ADMIN: 'SUPER_ADMIN'
};

exports.AddressType = exports.$Enums.AddressType = {
  BILLING: 'BILLING',
  SHIPPING: 'SHIPPING',
  BOTH: 'BOTH'
};

exports.Prisma.ModelName = {
  User: 'User',
  Provider: 'Provider',
  ProviderService: 'ProviderService',
  Service: 'Service',
  ServiceFormField: 'ServiceFormField',
  Order: 'Order',
  OrderItem: 'OrderItem',
  OrderFile: 'OrderFile',
  OrderStatusHistory: 'OrderStatusHistory',
  admins: 'admins',
  gallery_images: 'gallery_images',
  provider_operating_hours: 'provider_operating_hours',
  provider_rating_displays: 'provider_rating_displays',
  provider_service_areas: 'provider_service_areas',
  service_categories: 'service_categories',
  service_field_options: 'service_field_options',
  user_addresses: 'user_addresses',
  user_sessions: 'user_sessions'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
