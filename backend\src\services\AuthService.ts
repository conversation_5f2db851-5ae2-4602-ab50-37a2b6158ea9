import { UserRole } from '@prisma/client';
import { JWTConfig, SecurityConfig } from '../config';
import { generateAccessToken, generateRefreshToken, verifyRefreshToken } from '../middleware/auth';
import { AuthenticationError, ConflictError, NotFoundError } from '../middleware/errorHandler';
import {
  AccountLockInfo,
  CreateSessionData,
  CreateUserData,
  IAuthRepository,
  SessionData,
  UpdateUserData,
} from '../repositories/AuthRepository';
import {
  AuthUser,
  ChangePasswordRequest,
  LoginRequest,
  RegisterRequest,
  SecurityEvent,
  SecurityEventType,
  TokenPair,
  UpdateProfileRequest,
} from '../types/auth';
import { generateSecureToken, hashPassword, logSecurityEvent, sanitizeUser, verifyPassword } from '../utils/auth';
import { authLogger, dbLogger, securityLogger } from '../utils/logger';
import { EmailService } from './EmailService';

// Service interface for dependency inversion
export interface IAuthService {
  // Authentication operations
  register(data: RegisterRequest, context: RequestContext): Promise<AuthResult>;
  login(data: LoginRequest, context: RequestContext): Promise<AuthResult>;
  refreshTokens(refreshToken: string, context: RequestContext): Promise<TokenResult>;
  logout(refreshToken: string | undefined, user: AuthUser, context: RequestContext): Promise<void>;
  logoutAllDevices(user: AuthUser, context: RequestContext): Promise<void>;

  // Profile operations
  getProfile(userId: string): Promise<AuthUser>;
  updateProfile(userId: string, data: UpdateProfileRequest): Promise<AuthUser>;
  changePassword(userId: string, data: ChangePasswordRequest, context: RequestContext): Promise<void>;

  // Password reset operations
  requestPasswordReset(email: string, context: RequestContext): Promise<void>;
  resetPassword(token: string, newPassword: string, context: RequestContext): Promise<void>;

  // Email verification operations
  sendEmailVerification(userId: string, context: RequestContext): Promise<void>;
  verifyEmail(token: string, context: RequestContext): Promise<void>;

  // Session operations
  getUserSessions(userId: string): Promise<SessionData[]>;
  revokeSession(sessionId: string, userId: string): Promise<void>;

  // Account management
  isAccountLocked(userId: string): Promise<boolean>;
  getAccountLockInfo(userId: string): Promise<AccountLockInfo | null>;
}

// Data transfer objects
export interface RequestContext {
  ipAddress: string;
  userAgent: string;
}

export interface AuthResult {
  user: AuthUser;
  tokens: TokenPair;
}

export interface TokenResult {
  tokens: TokenPair;
}

// Service implementation
export class AuthService implements IAuthService {
  private jwtConfig: JWTConfig;
  private securityConfig: SecurityConfig;

  constructor(
    private authRepository: IAuthRepository,
    private emailService: EmailService,
    jwtConfig: JWTConfig,
    securityConfig: SecurityConfig
  ) {
    this.jwtConfig = jwtConfig;
    this.securityConfig = securityConfig;
    authLogger.info('AuthService initialized', {
      jwtIssuer: jwtConfig.issuer,
      jwtAudience: jwtConfig.audience,
      accessTokenExpiry: jwtConfig.accessExpiresIn,
      refreshTokenExpiry: jwtConfig.refreshExpiresIn,
      bcryptRounds: securityConfig.bcryptRounds,
    });
  }

  // Authentication operations
  async register(data: RegisterRequest, context: RequestContext): Promise<AuthResult> {
    const startTime = Date.now();
    const { email, password, firstName, lastName, phone } = data;

    authLogger.info('User registration attempt', {
      email: email.toLowerCase(),
      firstName,
      lastName,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
    });

    try {
      // Check if user already exists
      const existingUser = await this.authRepository.findUserByEmail(email);
      if (existingUser) {
        const duration = Date.now() - startTime;
        authLogger.warn('Registration failed: Email already exists', {
          email: email.toLowerCase(),
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          duration,
        });

        await this.logSecurityEvent({
          type: SecurityEventType.LOGIN_FAILURE,
          email: email.toLowerCase(),
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          metadata: { reason: 'Email already registered' },
        });
        throw new ConflictError('Email already registered');
      }

      // Hash password and create user
      const hashedPassword = await hashPassword(password, this.securityConfig.bcryptRounds);
      const userData: CreateUserData = {
        email,
        password: hashedPassword,
        firstName,
        lastName,
        phone,
        role: data.role || UserRole.CUSTOMER,
        isActive: true,
        isVerified: false,
      };

      dbLogger.info('Creating new user', {
        table: 'User',
        email: email.toLowerCase(),
        role: UserRole.CUSTOMER,
      });

      const user = await this.authRepository.createUser(userData);

      // Generate tokens
      const tokens = await this.generateTokenPair(user.id, user.email, user.role, context);

      const duration = Date.now() - startTime;
      authLogger.info('User registration successful', {
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });

      // Log successful registration
      await this.logSecurityEvent({
        type: SecurityEventType.LOGIN_SUCCESS,
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        metadata: { action: 'registration' },
      });

      return {
        user: sanitizeUser(user) as AuthUser,
        tokens,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      authLogger.error('User registration failed', error, {
        email: email.toLowerCase(),
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });
      throw error;
    }
  }

  async login(data: LoginRequest, context: RequestContext): Promise<AuthResult> {
    const startTime = Date.now();
    const { email, password } = data;

    authLogger.info('User login attempt', {
      email: email.toLowerCase(),
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
    });

    try {
      // Find user
      const user = await this.authRepository.findUserByEmail(email);
      if (!user) {
        const duration = Date.now() - startTime;
        authLogger.warn('Login failed: User not found', {
          email: email.toLowerCase(),
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          duration,
        });

        await this.logSecurityEvent({
          type: SecurityEventType.LOGIN_FAILURE,
          email: email.toLowerCase(),
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          metadata: { reason: 'User not found' },
        });
        throw new AuthenticationError('Invalid credentials');
      }

      // Check if account is locked
      const isLocked = await this.authRepository.isAccountLocked(user.id);
      if (isLocked) {
        const lockInfo = await this.authRepository.getAccountLockInfo(user.id);
        const duration = Date.now() - startTime;

        authLogger.warn('Login failed: Account locked', {
          userId: user.id,
          email: user.email,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          lockInfo,
          duration,
        });

        await this.logSecurityEvent({
          type: SecurityEventType.LOGIN_FAILURE,
          userId: user.id,
          email: user.email,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          metadata: { reason: 'Account locked', lockInfo },
        });
        throw new AuthenticationError('Account is locked');
      }

      // Check if user is active
      if (!user.isActive) {
        const duration = Date.now() - startTime;
        authLogger.warn('Login failed: Account deactivated', {
          userId: user.id,
          email: user.email,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          duration,
        });

        await this.logSecurityEvent({
          type: SecurityEventType.LOGIN_FAILURE,
          userId: user.id,
          email: user.email,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          metadata: { reason: 'Account deactivated' },
        });
        throw new AuthenticationError('Account is deactivated');
      }

      // Verify password
      if (!user.password) {
        const duration = Date.now() - startTime;
        authLogger.warn('Login failed: Password not set', {
          userId: user.id,
          email: user.email,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          duration,
        });
        throw new AuthenticationError('Password not set. Please use password reset.');
      }

      const isPasswordValid = await verifyPassword(password, user.password);
      if (!isPasswordValid) {
        await this.authRepository.incrementLoginAttempts(user.id);
        const duration = Date.now() - startTime;

        authLogger.warn('Login failed: Invalid password', {
          userId: user.id,
          email: user.email,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          duration,
        });

        await this.logSecurityEvent({
          type: SecurityEventType.LOGIN_FAILURE,
          userId: user.id,
          email: user.email,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          metadata: { reason: 'Invalid password' },
        });
        throw new AuthenticationError('Invalid credentials');
      }

      // Reset login attempts on successful login
      await this.authRepository.resetLoginAttempts(user.id);

      // Generate tokens
      const tokens = await this.generateTokenPair(user.id, user.email, user.role, context);

      const duration = Date.now() - startTime;
      authLogger.info('User login successful', {
        userId: user.id,
        email: user.email,
        role: user.role,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });

      // Log successful login
      await this.logSecurityEvent({
        type: SecurityEventType.LOGIN_SUCCESS,
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        metadata: { action: 'login' },
      });

      return {
        user: sanitizeUser(user) as AuthUser,
        tokens,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      authLogger.error('User login failed', error, {
        email: email.toLowerCase(),
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });
      throw error;
    }
  }

  async refreshTokens(refreshToken: string, context: RequestContext): Promise<TokenResult> {
    const startTime = Date.now();
    // Verify refresh token format
    const payload = verifyRefreshToken(refreshToken);

    // Check if refresh token exists and is valid
    const session = await this.authRepository.findSessionByRefreshToken(refreshToken);
    if (!session || !session.isActive || session.expiresAt < new Date()) {
      const duration = Date.now() - startTime;
      authLogger.warn('Refresh token failed: Invalid or expired', {
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });
      throw new AuthenticationError('Invalid or expired refresh token');
    }

    // Get user
    const user = await this.authRepository.findUserById(payload.userId);
    if (!user || !user.isActive) {
      const duration = Date.now() - startTime;
      authLogger.warn('Refresh token failed: User not found or inactive', {
        userId: payload.userId,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });
      throw new AuthenticationError('User not found or inactive');
    }

    // Revoke old refresh token
    await this.authRepository.revokeSession(refreshToken);

    // Generate new token pair
    const tokens = await this.generateTokenPair(user.id, user.email, user.role, context);

    const duration = Date.now() - startTime;
    authLogger.info('Refresh token successful', {
      userId: user.id,
      email: user.email,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      duration,
    });

    // Log token refresh
    await this.logSecurityEvent({
      type: SecurityEventType.TOKEN_REFRESH,
      userId: user.id,
      email: user.email,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
    });

    return { tokens };
  }

  async logout(refreshToken: string | undefined, user: AuthUser, context: RequestContext): Promise<void> {
    const startTime = Date.now();
    if (refreshToken) {
      await this.authRepository.revokeSession(refreshToken);
      const duration = Date.now() - startTime;
      authLogger.info('Logout successful (by refresh token)', {
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });
    }

    // Log logout
    await this.logSecurityEvent({
      type: SecurityEventType.LOGOUT,
      userId: user.id,
      email: user.email,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
    });
  }

  async logoutAllDevices(user: AuthUser, context: RequestContext): Promise<void> {
    const startTime = Date.now();
    await this.authRepository.revokeAllUserSessions(user.id);
    const duration = Date.now() - startTime;
    authLogger.info('Logout from all devices successful', {
      userId: user.id,
      email: user.email,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      duration,
    });

    // Log logout from all devices
    await this.logSecurityEvent({
      type: SecurityEventType.LOGOUT,
      userId: user.id,
      email: user.email,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      metadata: { action: 'logout_all_devices' },
    });
  }

  // Profile operations
  async getProfile(userId: string): Promise<AuthUser> {
    const startTime = Date.now();
    const user = await this.authRepository.findUserByIdSafe(userId);
    if (!user) {
      const duration = Date.now() - startTime;
      authLogger.warn('Get profile failed: User not found', {
        userId,
        duration,
      });
      throw new NotFoundError('User not found');
    }
    const duration = Date.now() - startTime;
    authLogger.info('Get profile successful', {
      userId: user.id,
      email: user.email,
      duration,
    });
    return user;
  }

  async updateProfile(userId: string, data: UpdateProfileRequest): Promise<AuthUser> {
    const startTime = Date.now();
    const updateData: UpdateUserData = {};

    if (data.firstName !== undefined) updateData.firstName = data.firstName;
    if (data.lastName !== undefined) updateData.lastName = data.lastName;
    if (data.phone !== undefined) updateData.phone = data.phone;
    if (data.avatar !== undefined) updateData.avatar = data.avatar;

    dbLogger.info('Updating user profile', {
      table: 'User',
      userId,
      updatedFields: Object.keys(updateData),
    });

    const updatedUser = await this.authRepository.updateUser(userId, updateData);
    const duration = Date.now() - startTime;
    authLogger.info('Profile updated successful', {
      userId: updatedUser.id,
      email: updatedUser.email,
      duration,
    });
    return sanitizeUser(updatedUser) as AuthUser;
  }

  async changePassword(userId: string, data: ChangePasswordRequest, context: RequestContext): Promise<void> {
    const startTime = Date.now();
    const { currentPassword, newPassword } = data;

    authLogger.info('User password change attempt', {
      userId,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
    });

    try {
      // Get user with password
      const user = await this.authRepository.findUserById(userId);
      if (!user || !user.password) {
        const duration = Date.now() - startTime;
        authLogger.warn('Password change failed: User not found or password not set', {
          userId,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          duration,
        });
        throw new AuthenticationError('Current password verification failed');
      }

      // Verify current password
      const isCurrentPasswordValid = await verifyPassword(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        const duration = Date.now() - startTime;
        authLogger.warn('Password change failed: Current password incorrect', {
          userId,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          duration,
        });
        throw new AuthenticationError('Current password is incorrect');
      }

      // Hash new password and update
      const hashedNewPassword = await hashPassword(newPassword, this.securityConfig.bcryptRounds);
      await this.authRepository.updateUserPassword(userId, hashedNewPassword);

      // Revoke all existing sessions for security
      await this.authRepository.revokeAllUserSessions(userId);

      const duration = Date.now() - startTime;
      authLogger.info('Password change successful', {
        userId,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });

      // Log password change
      await this.logSecurityEvent({
        type: SecurityEventType.PASSWORD_CHANGE,
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      authLogger.error('Password change failed', error, {
        userId,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });
      throw error;
    }
  }

  // Session operations
  async getUserSessions(userId: string): Promise<SessionData[]> {
    const startTime = Date.now();
    const sessions = await this.authRepository.getUserSessions(userId);
    const duration = Date.now() - startTime;
    authLogger.info('Get user sessions successful', {
      userId,
      sessionCount: sessions.length,
      duration,
    });
    return sessions;
  }

  async revokeSession(sessionId: string, userId: string): Promise<void> {
    const startTime = Date.now();
    const success = await this.authRepository.revokeSpecificSession(sessionId, userId);
    if (!success) {
      const duration = Date.now() - startTime;
      authLogger.warn('Revoke session failed: Session not found', {
        sessionId,
        userId,
        duration,
      });
      throw new NotFoundError('Session not found');
    }
    const duration = Date.now() - startTime;
    authLogger.info('Revoke session successful', {
      sessionId,
      userId,
      duration,
    });
  }

  // Account management
  async isAccountLocked(userId: string): Promise<boolean> {
    const startTime = Date.now();
    const isLocked = await this.authRepository.isAccountLocked(userId);
    const duration = Date.now() - startTime;
    authLogger.info('Check account lock status', {
      userId,
      isLocked,
      duration,
    });
    return isLocked;
  }

  async getAccountLockInfo(userId: string): Promise<AccountLockInfo | null> {
    const startTime = Date.now();
    const lockInfo = await this.authRepository.getAccountLockInfo(userId);
    const duration = Date.now() - startTime;
    authLogger.info('Get account lock info', {
      userId,
      lockInfo,
      duration,
    });
    return lockInfo;
  }

  // Password reset operations
  async requestPasswordReset(email: string, context: RequestContext): Promise<void> {
    const startTime = Date.now();

    authLogger.info('Password reset request', {
      email: email.toLowerCase(),
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
    });

    try {
      // Find user by email
      const user = await this.authRepository.findUserByEmail(email);
      if (!user) {
        const duration = Date.now() - startTime;
        authLogger.warn('Password reset request for non-existent user', {
          email: email.toLowerCase(),
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          duration,
        });
        // Don't reveal that user doesn't exist - return success anyway
        return;
      }

      // Check if user is active
      if (!user.isActive) {
        const duration = Date.now() - startTime;
        authLogger.warn('Password reset request for deactivated user', {
          userId: user.id,
          email: user.email,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          duration,
        });
        // Don't reveal that user is deactivated - return success anyway
        return;
      }

      // Generate secure reset token
      const resetToken = generateSecureToken(32);
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 1); // 1 hour expiry

      // Save reset token
      await this.authRepository.setPasswordResetToken(user.id, resetToken, expiresAt);

      // Send reset email
      await this.emailService.sendPasswordResetEmail(user.email, resetToken, user.firstName);

      const duration = Date.now() - startTime;
      authLogger.info('Password reset email sent', {
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });

      // Log security event
      await this.logSecurityEvent({
        type: SecurityEventType.PASSWORD_RESET_REQUEST,
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      authLogger.error('Password reset request failed', error, {
        email: email.toLowerCase(),
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });
      throw error;
    }
  }

  async resetPassword(token: string, newPassword: string, context: RequestContext): Promise<void> {
    const startTime = Date.now();

    authLogger.info('Password reset attempt', {
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
    });

    try {
      // Find user by reset token
      const user = await this.authRepository.findUserByPasswordResetToken(token);
      if (!user) {
        const duration = Date.now() - startTime;
        authLogger.warn('Password reset failed: Invalid or expired token', {
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          duration,
        });
        throw new AuthenticationError('Invalid or expired reset token');
      }

      // Hash new password
      const hashedPassword = await hashPassword(newPassword, this.securityConfig.bcryptRounds);

      // Update password and clear reset token
      await this.authRepository.updateUserPassword(user.id, hashedPassword);
      await this.authRepository.clearPasswordResetToken(user.id);

      // Revoke all existing sessions for security
      await this.authRepository.revokeAllUserSessions(user.id);

      const duration = Date.now() - startTime;
      authLogger.info('Password reset successful', {
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });

      // Log security event
      await this.logSecurityEvent({
        type: SecurityEventType.PASSWORD_RESET_COMPLETE,
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      authLogger.error('Password reset failed', error, {
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });
      throw error;
    }
  }

  // Email verification operations
  async sendEmailVerification(userId: string, context: RequestContext): Promise<void> {
    const startTime = Date.now();

    try {
      // Get user
      const user = await this.authRepository.findUserById(userId);
      if (!user) {
        const duration = Date.now() - startTime;
        authLogger.warn('Email verification request for non-existent user', {
          userId,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          duration,
        });
        throw new NotFoundError('User not found');
      }

      // Check if already verified
      if (user.isVerified) {
        const duration = Date.now() - startTime;
        authLogger.info('Email verification request for already verified user', {
          userId: user.id,
          email: user.email,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          duration,
        });
        return; // Already verified, nothing to do
      }

      // Generate secure verification token
      const verificationToken = generateSecureToken(32);
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24); // 24 hour expiry

      // Save verification token
      await this.authRepository.setEmailVerificationToken(user.id, verificationToken, expiresAt);

      // Send verification email
      await this.emailService.sendEmailVerification(user.email, verificationToken, user.firstName);

      const duration = Date.now() - startTime;
      authLogger.info('Email verification sent', {
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });

      // Log security event
      await this.logSecurityEvent({
        type: SecurityEventType.EMAIL_VERIFICATION_SENT,
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      authLogger.error('Email verification send failed', error, {
        userId,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });
      throw error;
    }
  }

  async verifyEmail(token: string, context: RequestContext): Promise<void> {
    const startTime = Date.now();

    authLogger.info('Email verification attempt', {
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
    });

    try {
      // Find user by verification token
      const user = await this.authRepository.findUserByEmailVerificationToken(token);
      if (!user) {
        const duration = Date.now() - startTime;
        authLogger.warn('Email verification failed: Invalid or expired token', {
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          duration,
        });
        throw new AuthenticationError('Invalid or expired verification token');
      }

      // Mark email as verified
      await this.authRepository.markEmailAsVerified(user.id);

      // Send welcome email
      await this.emailService.sendWelcomeEmail(user.email, user.firstName);

      const duration = Date.now() - startTime;
      authLogger.info('Email verification successful', {
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });

      // Log security event
      await this.logSecurityEvent({
        type: SecurityEventType.EMAIL_VERIFIED,
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      authLogger.error('Email verification failed', error, {
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        duration,
      });
      throw error;
    }
  }

  // Private helper methods
  private async generateTokenPair(
    userId: string,
    email: string,
    role: UserRole,
    context: RequestContext
  ): Promise<TokenPair> {
    const startTime = Date.now();

    try {
      // Generate tokens with configuration
      const accessToken = generateAccessToken(
        { userId, email, role },
        this.jwtConfig.secret,
        this.jwtConfig.accessExpiresIn,
        this.jwtConfig.issuer,
        this.jwtConfig.audience
      );
      const refreshToken = generateRefreshToken(
        { userId, email, role },
        this.jwtConfig.refreshSecret,
        this.jwtConfig.refreshExpiresIn,
        this.jwtConfig.issuer,
        this.jwtConfig.audience
      );

      // Create session with refresh token expiry
      const refreshTokenExpiry = new Date();
      refreshTokenExpiry.setTime(refreshTokenExpiry.getTime() + this.parseJWTExpiry(this.jwtConfig.refreshExpiresIn));

      const sessionData: CreateSessionData = {
        id: generateSecureToken(16),
        userId,
        refreshToken,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        expiresAt: refreshTokenExpiry,
      };

      dbLogger.info('Creating user session', {
        table: 'Session',
        userId,
        ipAddress: context.ipAddress,
        expiresAt: sessionData.expiresAt,
      });

      await this.authRepository.createSession(sessionData);

      const duration = Date.now() - startTime;
      authLogger.debug('Token pair generated successfully', {
        userId,
        email,
        role,
        duration,
        accessTokenExpiry: this.jwtConfig.accessExpiresIn,
        refreshTokenExpiry: this.jwtConfig.refreshExpiresIn,
      });

      return { accessToken, refreshToken };
    } catch (error) {
      const duration = Date.now() - startTime;
      authLogger.error('Token pair generation failed', error, {
        userId,
        email,
        role,
        duration,
      });
      throw error;
    }
  }

  private parseJWTExpiry(expiry: string): number {
    const unit = expiry.slice(-1);
    const value = parseInt(expiry.slice(0, -1));

    switch (unit) {
      case 's':
        return value * 1000;
      case 'm':
        return value * 60 * 1000;
      case 'h':
        return value * 60 * 60 * 1000;
      case 'd':
        return value * 24 * 60 * 60 * 1000;
      default:
        return 15 * 60 * 1000; // Default to 15 minutes
    }
  }

  private async logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): Promise<void> {
    try {
      await logSecurityEvent(event);

      securityLogger.info('Security event logged', {
        eventType: event.type,
        userId: event.userId,
        email: event.email,
        ipAddress: event.ipAddress,
      });
    } catch (error) {
      securityLogger.error('Failed to log security event', error, {
        eventType: event.type,
        userId: event.userId,
        email: event.email,
      });
      // Don't throw error to avoid breaking the main flow
    }
  }
}

export default AuthService;
