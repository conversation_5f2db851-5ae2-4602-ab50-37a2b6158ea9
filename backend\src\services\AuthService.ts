import {User, UserRole} from '@prisma/client';
import {
	IAuthRepository,
	CreateUserData,
	UpdateUserData,
	CreateSessionData,
	SessionData,
	AccountLockInfo,
} from '../repositories/AuthRepository';
import {
	AuthUser,
	TokenPair,
	JWTPayload,
	SecurityEvent,
	SecurityEventType,
	RegisterRequest,
	LoginRequest,
	ChangePasswordRequest,
	UpdateProfileRequest,
} from '../types/auth';
import {
	generateAccessToken,
	generateRefreshToken,
	verifyRefreshToken,
} from '../middleware/auth';
import {
	hashPassword,
	verifyPassword,
	generateSecureToken,
	logSecurityEvent,
	sanitizeUser,
} from '../utils/auth';
import {
	AuthenticationError,
	ConflictError,
	NotFoundError,
} from '../middleware/errorHandler';
import {authLogger, dbLogger, securityLogger} from '../utils/logger';

// Service interface for dependency inversion
export interface IAuthService {
	// Authentication operations
	register(data: RegisterRequest, context: RequestContext): Promise<AuthResult>;
	login(data: LoginRequest, context: RequestContext): Promise<AuthResult>;
	refreshTokens(
		refreshToken: string,
		context: RequestContext
	): Promise<TokenResult>;
	logout(
		refreshToken: string | undefined,
		user: AuthUser,
		context: RequestContext
	): Promise<void>;
	logoutAllDevices(user: AuthUser, context: RequestContext): Promise<void>;

	// Profile operations
	getProfile(userId: string): Promise<AuthUser>;
	updateProfile(userId: string, data: UpdateProfileRequest): Promise<AuthUser>;
	changePassword(
		userId: string,
		data: ChangePasswordRequest,
		context: RequestContext
	): Promise<void>;

	// Session operations
	getUserSessions(userId: string): Promise<SessionData[]>;
	revokeSession(sessionId: string, userId: string): Promise<void>;

	// Account management
	isAccountLocked(userId: string): Promise<boolean>;
	getAccountLockInfo(userId: string): Promise<AccountLockInfo | null>;
}

// Data transfer objects
export interface RequestContext {
	ipAddress: string;
	userAgent: string;
}

export interface AuthResult {
	user: AuthUser;
	tokens: TokenPair;
}

export interface TokenResult {
	tokens: TokenPair;
}

// Service implementation
export class AuthService implements IAuthService {
	constructor(private authRepository: IAuthRepository) {
		authLogger.info('AuthService initialized');
	}

	// Authentication operations
	async register(
		data: RegisterRequest,
		context: RequestContext
	): Promise<AuthResult> {
		const startTime = Date.now();
		const {email, password, firstName, lastName, phone} = data;

		authLogger.info('User registration attempt', {
			email: email.toLowerCase(),
			firstName,
			lastName,
			ipAddress: context.ipAddress,
			userAgent: context.userAgent,
		});

		try {
			// Check if user already exists
			const existingUser = await this.authRepository.findUserByEmail(email);
			if (existingUser) {
				const duration = Date.now() - startTime;
				authLogger.warn('Registration failed: Email already exists', {
					email: email.toLowerCase(),
					ipAddress: context.ipAddress,
					userAgent: context.userAgent,
					duration,
				});

				await this.logSecurityEvent({
					type: SecurityEventType.LOGIN_FAILURE,
					email: email.toLowerCase(),
					ipAddress: context.ipAddress,
					userAgent: context.userAgent,
					metadata: {reason: 'Email already registered'},
				});
				throw new ConflictError('Email address is already registered');
			}

			// Hash password and create user
			const hashedPassword = await hashPassword(password);
			const userData: CreateUserData = {
				email,
				password: hashedPassword,
				firstName,
				lastName,
				phone,
				role: UserRole.CUSTOMER,
				isActive: true,
				isVerified: false,
			};

			dbLogger.database('Creating new user', 'User', {
				email: email.toLowerCase(),
				role: UserRole.CUSTOMER,
			});

			const user = await this.authRepository.createUser(userData);

			// Generate tokens
			const tokens = await this.generateTokenPair(
				user.id,
				user.email,
				user.role,
				context
			);

			const duration = Date.now() - startTime;
			authLogger.info('User registration successful', {
				userId: user.id,
				email: user.email,
				ipAddress: context.ipAddress,
				userAgent: context.userAgent,
				duration,
			});

			// Log successful registration
			await this.logSecurityEvent({
				type: SecurityEventType.LOGIN_SUCCESS,
				userId: user.id,
				email: user.email,
				ipAddress: context.ipAddress,
				userAgent: context.userAgent,
				metadata: {action: 'registration'},
			});

			return {
				user: sanitizeUser(user) as AuthUser,
				tokens,
			};
		} catch (error) {
			const duration = Date.now() - startTime;
			authLogger.error('User registration failed', error, {
				email: email.toLowerCase(),
				ipAddress: context.ipAddress,
				userAgent: context.userAgent,
				duration,
			});
			throw error;
		}
	}

	async login(
		data: LoginRequest,
		context: RequestContext
	): Promise<AuthResult> {
		const startTime = Date.now();
		const {email, password} = data;

		authLogger.info('User login attempt', {
			email: email.toLowerCase(),
			ipAddress: context.ipAddress,
			userAgent: context.userAgent,
		});

		try {
			// Find user
			const user = await this.authRepository.findUserByEmail(email);
			if (!user) {
				const duration = Date.now() - startTime;
				authLogger.warn('Login failed: User not found', {
					email: email.toLowerCase(),
					ipAddress: context.ipAddress,
					userAgent: context.userAgent,
					duration,
				});

				await this.logSecurityEvent({
					type: SecurityEventType.LOGIN_FAILURE,
					email: email.toLowerCase(),
					ipAddress: context.ipAddress,
					userAgent: context.userAgent,
					metadata: {reason: 'User not found'},
				});
				throw new AuthenticationError('Invalid email or password');
			}

			// Check if account is locked
			const isLocked = await this.authRepository.isAccountLocked(user.id);
			if (isLocked) {
				const lockInfo = await this.authRepository.getAccountLockInfo(user.id);
				const duration = Date.now() - startTime;

				authLogger.warn('Login failed: Account locked', {
					userId: user.id,
					email: user.email,
					ipAddress: context.ipAddress,
					userAgent: context.userAgent,
					lockInfo,
					duration,
				});

				await this.logSecurityEvent({
					type: SecurityEventType.LOGIN_FAILURE,
					userId: user.id,
					email: user.email,
					ipAddress: context.ipAddress,
					userAgent: context.userAgent,
					metadata: {reason: 'Account locked', lockInfo},
				});
				throw new AuthenticationError(
					`Account is temporarily locked. Try again after ${lockInfo?.lockExpires?.toLocaleTimeString()}`
				);
			}

			// Check if user is active
			if (!user.isActive) {
				const duration = Date.now() - startTime;
				authLogger.warn('Login failed: Account deactivated', {
					userId: user.id,
					email: user.email,
					ipAddress: context.ipAddress,
					userAgent: context.userAgent,
					duration,
				});

				await this.logSecurityEvent({
					type: SecurityEventType.LOGIN_FAILURE,
					userId: user.id,
					email: user.email,
					ipAddress: context.ipAddress,
					userAgent: context.userAgent,
					metadata: {reason: 'Account deactivated'},
				});
				throw new AuthenticationError('Account is deactivated');
			}

			// Verify password
			if (!user.password) {
				const duration = Date.now() - startTime;
				authLogger.warn('Login failed: Password not set', {
					userId: user.id,
					email: user.email,
					ipAddress: context.ipAddress,
					userAgent: context.userAgent,
					duration,
				});
				throw new AuthenticationError(
					'Password not set. Please use password reset.'
				);
			}

			const isPasswordValid = await verifyPassword(password, user.password);
			if (!isPasswordValid) {
				await this.authRepository.incrementLoginAttempts(user.id);
				const duration = Date.now() - startTime;

				authLogger.warn('Login failed: Invalid password', {
					userId: user.id,
					email: user.email,
					ipAddress: context.ipAddress,
					userAgent: context.userAgent,
					duration,
				});

				await this.logSecurityEvent({
					type: SecurityEventType.LOGIN_FAILURE,
					userId: user.id,
					email: user.email,
					ipAddress: context.ipAddress,
					userAgent: context.userAgent,
					metadata: {reason: 'Invalid password'},
				});
				throw new AuthenticationError('Invalid email or password');
			}

			// Reset login attempts on successful login
			await this.authRepository.resetLoginAttempts(user.id);

			// Generate tokens
			const tokens = await this.generateTokenPair(
				user.id,
				user.email,
				user.role,
				context
			);

			const duration = Date.now() - startTime;
			authLogger.info('User login successful', {
				userId: user.id,
				email: user.email,
				role: user.role,
				ipAddress: context.ipAddress,
				userAgent: context.userAgent,
				duration,
			});

			// Log successful login
			await this.logSecurityEvent({
				type: SecurityEventType.LOGIN_SUCCESS,
				userId: user.id,
				email: user.email,
				ipAddress: context.ipAddress,
				userAgent: context.userAgent,
				metadata: {action: 'login'},
			});

			return {
				user: sanitizeUser(user) as AuthUser,
				tokens,
			};
		} catch (error) {
			const duration = Date.now() - startTime;
			authLogger.error('User login failed', error, {
				email: email.toLowerCase(),
				ipAddress: context.ipAddress,
				userAgent: context.userAgent,
				duration,
			});
			throw error;
		}
	}

	async refreshTokens(
		refreshToken: string,
		context: RequestContext
	): Promise<TokenResult> {
		const startTime = Date.now();
		// Verify refresh token format
		const payload = verifyRefreshToken(refreshToken);

		// Check if refresh token exists and is valid
		const session = await this.authRepository.findSessionByRefreshToken(
			refreshToken
		);
		if (!session || !session.isActive || session.expiresAt < new Date()) {
			const duration = Date.now() - startTime;
			authLogger.warn('Refresh token failed: Invalid or expired', {
				ipAddress: context.ipAddress,
				userAgent: context.userAgent,
				duration,
			});
			throw new AuthenticationError('Invalid or expired refresh token');
		}

		// Get user
		const user = await this.authRepository.findUserById(payload.userId);
		if (!user || !user.isActive) {
			const duration = Date.now() - startTime;
			authLogger.warn('Refresh token failed: User not found or inactive', {
				userId: payload.userId,
				ipAddress: context.ipAddress,
				userAgent: context.userAgent,
				duration,
			});
			throw new AuthenticationError('User not found or inactive');
		}

		// Revoke old refresh token
		await this.authRepository.revokeSession(refreshToken);

		// Generate new token pair
		const tokens = await this.generateTokenPair(
			user.id,
			user.email,
			user.role,
			context
		);

		const duration = Date.now() - startTime;
		authLogger.info('Refresh token successful', {
			userId: user.id,
			email: user.email,
			ipAddress: context.ipAddress,
			userAgent: context.userAgent,
			duration,
		});

		// Log token refresh
		await this.logSecurityEvent({
			type: SecurityEventType.TOKEN_REFRESH,
			userId: user.id,
			email: user.email,
			ipAddress: context.ipAddress,
			userAgent: context.userAgent,
		});

		return {tokens};
	}

	async logout(
		refreshToken: string | undefined,
		user: AuthUser,
		context: RequestContext
	): Promise<void> {
		const startTime = Date.now();
		if (refreshToken) {
			await this.authRepository.revokeSession(refreshToken);
			const duration = Date.now() - startTime;
			authLogger.info('Logout successful (by refresh token)', {
				userId: user.id,
				email: user.email,
				ipAddress: context.ipAddress,
				userAgent: context.userAgent,
				duration,
			});
		}

		// Log logout
		await this.logSecurityEvent({
			type: SecurityEventType.LOGOUT,
			userId: user.id,
			email: user.email,
			ipAddress: context.ipAddress,
			userAgent: context.userAgent,
		});
	}

	async logoutAllDevices(
		user: AuthUser,
		context: RequestContext
	): Promise<void> {
		const startTime = Date.now();
		await this.authRepository.revokeAllUserSessions(user.id);
		const duration = Date.now() - startTime;
		authLogger.info('Logout from all devices successful', {
			userId: user.id,
			email: user.email,
			ipAddress: context.ipAddress,
			userAgent: context.userAgent,
			duration,
		});

		// Log logout from all devices
		await this.logSecurityEvent({
			type: SecurityEventType.LOGOUT,
			userId: user.id,
			email: user.email,
			ipAddress: context.ipAddress,
			userAgent: context.userAgent,
			metadata: {action: 'logout_all_devices'},
		});
	}

	// Profile operations
	async getProfile(userId: string): Promise<AuthUser> {
		const startTime = Date.now();
		const user = await this.authRepository.findUserByIdSafe(userId);
		if (!user) {
			const duration = Date.now() - startTime;
			authLogger.warn('Get profile failed: User not found', {
				userId,
				duration,
			});
			throw new NotFoundError('User not found');
		}
		const duration = Date.now() - startTime;
		authLogger.info('Get profile successful', {
			userId: user.id,
			email: user.email,
			duration,
		});
		return user;
	}

	async updateProfile(
		userId: string,
		data: UpdateProfileRequest
	): Promise<AuthUser> {
		const startTime = Date.now();
		const updateData: UpdateUserData = {};

		if (data.firstName !== undefined) updateData.firstName = data.firstName;
		if (data.lastName !== undefined) updateData.lastName = data.lastName;
		if (data.phone !== undefined) updateData.phone = data.phone;
		if (data.avatar !== undefined) updateData.avatar = data.avatar;

		dbLogger.database('Updating user profile', 'User', {
			userId,
			updatedFields: Object.keys(updateData),
		});

		const updatedUser = await this.authRepository.updateUser(
			userId,
			updateData
		);
		const duration = Date.now() - startTime;
		authLogger.info('Profile updated successful', {
			userId: updatedUser.id,
			email: updatedUser.email,
			duration,
		});
		return sanitizeUser(updatedUser) as AuthUser;
	}

	async changePassword(
		userId: string,
		data: ChangePasswordRequest,
		context: RequestContext
	): Promise<void> {
		const startTime = Date.now();
		const {currentPassword, newPassword} = data;

		authLogger.info('User password change attempt', {
			userId,
			ipAddress: context.ipAddress,
			userAgent: context.userAgent,
		});

		try {
			// Get user with password
			const user = await this.authRepository.findUserById(userId);
			if (!user || !user.password) {
				const duration = Date.now() - startTime;
				authLogger.warn(
					'Password change failed: User not found or password not set',
					{
						userId,
						ipAddress: context.ipAddress,
						userAgent: context.userAgent,
						duration,
					}
				);
				throw new AuthenticationError('Current password verification failed');
			}

			// Verify current password
			const isCurrentPasswordValid = await verifyPassword(
				currentPassword,
				user.password
			);
			if (!isCurrentPasswordValid) {
				const duration = Date.now() - startTime;
				authLogger.warn('Password change failed: Current password incorrect', {
					userId,
					ipAddress: context.ipAddress,
					userAgent: context.userAgent,
					duration,
				});
				throw new AuthenticationError('Current password is incorrect');
			}

			// Hash new password and update
			const hashedNewPassword = await hashPassword(newPassword);
			await this.authRepository.updateUserPassword(userId, hashedNewPassword);

			// Revoke all existing sessions for security
			await this.authRepository.revokeAllUserSessions(userId);

			const duration = Date.now() - startTime;
			authLogger.info('Password change successful', {
				userId,
				email: user.email,
				ipAddress: context.ipAddress,
				userAgent: context.userAgent,
				duration,
			});

			// Log password change
			await this.logSecurityEvent({
				type: SecurityEventType.PASSWORD_CHANGE,
				userId: user.id,
				email: user.email,
				ipAddress: context.ipAddress,
				userAgent: context.userAgent,
			});
		} catch (error) {
			const duration = Date.now() - startTime;
			authLogger.error('Password change failed', error, {
				userId,
				ipAddress: context.ipAddress,
				userAgent: context.userAgent,
				duration,
			});
			throw error;
		}
	}

	// Session operations
	async getUserSessions(userId: string): Promise<SessionData[]> {
		const startTime = Date.now();
		const sessions = await this.authRepository.getUserSessions(userId);
		const duration = Date.now() - startTime;
		authLogger.info('Get user sessions successful', {
			userId,
			sessionCount: sessions.length,
			duration,
		});
		return sessions;
	}

	async revokeSession(sessionId: string, userId: string): Promise<void> {
		const startTime = Date.now();
		const success = await this.authRepository.revokeSpecificSession(
			sessionId,
			userId
		);
		if (!success) {
			const duration = Date.now() - startTime;
			authLogger.warn('Revoke session failed: Session not found', {
				sessionId,
				userId,
				duration,
			});
			throw new NotFoundError('Session not found');
		}
		const duration = Date.now() - startTime;
		authLogger.info('Revoke session successful', {
			sessionId,
			userId,
			duration,
		});
	}

	// Account management
	async isAccountLocked(userId: string): Promise<boolean> {
		const startTime = Date.now();
		const isLocked = await this.authRepository.isAccountLocked(userId);
		const duration = Date.now() - startTime;
		authLogger.info('Check account lock status', {
			userId,
			isLocked,
			duration,
		});
		return isLocked;
	}

	async getAccountLockInfo(userId: string): Promise<AccountLockInfo | null> {
		const startTime = Date.now();
		const lockInfo = await this.authRepository.getAccountLockInfo(userId);
		const duration = Date.now() - startTime;
		authLogger.info('Get account lock info', {
			userId,
			lockInfo,
			duration,
		});
		return lockInfo;
	}

	// Private helper methods
	private async generateTokenPair(
		userId: string,
		email: string,
		role: UserRole,
		context: RequestContext
	): Promise<TokenPair> {
		const startTime = Date.now();

		try {
			// Generate tokens
			const accessToken = generateAccessToken({userId, email, role});
			const refreshToken = generateRefreshToken({userId, email, role});

			// Create session
			const sessionData: CreateSessionData = {
				id: generateSecureToken(16),
				userId,
				refreshToken,
				ipAddress: context.ipAddress,
				userAgent: context.userAgent,
				expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
			};

			dbLogger.database('Creating user session', 'Session', {
				userId,
				ipAddress: context.ipAddress,
			});

			await this.authRepository.createSession(sessionData);

			const duration = Date.now() - startTime;
			authLogger.debug('Token pair generated successfully', {
				userId,
				email,
				role,
				duration,
			});

			return {accessToken, refreshToken};
		} catch (error) {
			const duration = Date.now() - startTime;
			authLogger.error('Token pair generation failed', error, {
				userId,
				email,
				role,
				duration,
			});
			throw error;
		}
	}

	private async logSecurityEvent(
		event: Omit<SecurityEvent, 'timestamp'>
	): Promise<void> {
		try {
			await logSecurityEvent(event);

			securityLogger.security('Security event logged', {
				eventType: event.type,
				userId: event.userId,
				email: event.email,
				ipAddress: event.ipAddress,
			});
		} catch (error) {
			securityLogger.error('Failed to log security event', error, {
				eventType: event.type,
				userId: event.userId,
				email: event.email,
			});
			// Don't throw error to avoid breaking the main flow
		}
	}
}

export default AuthService;
