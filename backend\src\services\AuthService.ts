import { User, UserRole } from '@prisma/client';
import { 
  IAuthRepository, 
  CreateUserData, 
  UpdateUserData, 
  CreateSessionData,
  SessionData,
  AccountLockInfo
} from '../repositories/AuthRepository';
import { 
  AuthUser, 
  TokenPair, 
  JWTPayload, 
  SecurityEvent, 
  SecurityEventType,
  RegisterRequest,
  LoginRequest,
  ChangePasswordRequest,
  UpdateProfileRequest
} from '../types/auth';
import { 
  generateAccessToken, 
  generateRefreshToken, 
  verifyRefreshToken 
} from '../middleware/auth';
import { 
  hashPassword, 
  verifyPassword, 
  generateSecureToken,
  logSecurityEvent,
  sanitizeUser 
} from '../utils/auth';
import { 
  AuthenticationError, 
  ConflictError, 
  NotFoundError 
} from '../middleware/errorHandler';

// Service interface for dependency inversion
export interface IAuthService {
  // Authentication operations
  register(data: RegisterRequest, context: RequestContext): Promise<AuthResult>;
  login(data: LoginRequest, context: RequestContext): Promise<AuthResult>;
  refreshTokens(refreshToken: string, context: RequestContext): Promise<TokenResult>;
  logout(refreshToken: string | undefined, user: AuthUser, context: RequestContext): Promise<void>;
  logoutAllDevices(user: AuthUser, context: RequestContext): Promise<void>;
  
  // Profile operations
  getProfile(userId: string): Promise<AuthUser>;
  updateProfile(userId: string, data: UpdateProfileRequest): Promise<AuthUser>;
  changePassword(userId: string, data: ChangePasswordRequest, context: RequestContext): Promise<void>;
  
  // Session operations
  getUserSessions(userId: string): Promise<SessionData[]>;
  revokeSession(sessionId: string, userId: string): Promise<void>;
  
  // Account management
  isAccountLocked(userId: string): Promise<boolean>;
  getAccountLockInfo(userId: string): Promise<AccountLockInfo | null>;
}

// Data transfer objects
export interface RequestContext {
  ipAddress: string;
  userAgent: string;
}

export interface AuthResult {
  user: AuthUser;
  tokens: TokenPair;
}

export interface TokenResult {
  tokens: TokenPair;
}

// Service implementation
export class AuthService implements IAuthService {
  constructor(private authRepository: IAuthRepository) {}

  // Authentication operations
  async register(data: RegisterRequest, context: RequestContext): Promise<AuthResult> {
    const { email, password, firstName, lastName, phone } = data;
    
    // Check if user already exists
    const existingUser = await this.authRepository.findUserByEmail(email);
    if (existingUser) {
      await this.logSecurityEvent({
        type: SecurityEventType.LOGIN_FAILURE,
        email: email.toLowerCase(),
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        metadata: { reason: 'Email already registered' }
      });
      throw new ConflictError('Email address is already registered');
    }

    // Hash password and create user
    const hashedPassword = await hashPassword(password);
    const userData: CreateUserData = {
      email,
      password: hashedPassword,
      firstName,
      lastName,
      phone,
      role: UserRole.CUSTOMER,
      isActive: true,
      isVerified: false
    };

    const user = await this.authRepository.createUser(userData);

    // Generate tokens
    const tokens = await this.generateTokenPair(
      user.id,
      user.email,
      user.role,
      context
    );

    // Log successful registration
    await this.logSecurityEvent({
      type: SecurityEventType.LOGIN_SUCCESS,
      userId: user.id,
      email: user.email,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      metadata: { action: 'registration' }
    });

    return {
      user: sanitizeUser(user) as AuthUser,
      tokens
    };
  }

  async login(data: LoginRequest, context: RequestContext): Promise<AuthResult> {
    const { email, password } = data;

    // Find user
    const user = await this.authRepository.findUserByEmail(email);
    if (!user) {
      await this.logSecurityEvent({
        type: SecurityEventType.LOGIN_FAILURE,
        email: email.toLowerCase(),
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        metadata: { reason: 'User not found' }
      });
      throw new AuthenticationError('Invalid email or password');
    }

    // Check if account is locked
    const isLocked = await this.authRepository.isAccountLocked(user.id);
    if (isLocked) {
      const lockInfo = await this.authRepository.getAccountLockInfo(user.id);
      await this.logSecurityEvent({
        type: SecurityEventType.LOGIN_FAILURE,
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        metadata: { reason: 'Account locked', lockInfo }
      });
      throw new AuthenticationError(
        `Account is temporarily locked. Try again after ${lockInfo?.lockExpires?.toLocaleTimeString()}`
      );
    }

    // Check if user is active
    if (!user.isActive) {
      await this.logSecurityEvent({
        type: SecurityEventType.LOGIN_FAILURE,
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        metadata: { reason: 'Account deactivated' }
      });
      throw new AuthenticationError('Account is deactivated');
    }

    // Verify password
    if (!user.password) {
      throw new AuthenticationError('Password not set. Please use password reset.');
    }

    const isPasswordValid = await verifyPassword(password, user.password);
    if (!isPasswordValid) {
      await this.authRepository.incrementLoginAttempts(user.id);
      await this.logSecurityEvent({
        type: SecurityEventType.LOGIN_FAILURE,
        userId: user.id,
        email: user.email,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        metadata: { reason: 'Invalid password' }
      });
      throw new AuthenticationError('Invalid email or password');
    }

    // Reset login attempts on successful login
    await this.authRepository.resetLoginAttempts(user.id);

    // Generate tokens
    const tokens = await this.generateTokenPair(
      user.id,
      user.email,
      user.role,
      context
    );

    // Log successful login
    await this.logSecurityEvent({
      type: SecurityEventType.LOGIN_SUCCESS,
      userId: user.id,
      email: user.email,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    });

    return {
      user: sanitizeUser(user) as AuthUser,
      tokens
    };
  }

  async refreshTokens(refreshToken: string, context: RequestContext): Promise<TokenResult> {
    // Verify refresh token format
    const payload = verifyRefreshToken(refreshToken);

    // Check if refresh token exists and is valid
    const session = await this.authRepository.findSessionByRefreshToken(refreshToken);
    if (!session || !session.isActive || session.expiresAt < new Date()) {
      throw new AuthenticationError('Invalid or expired refresh token');
    }

    // Get user
    const user = await this.authRepository.findUserById(payload.userId);
    if (!user || !user.isActive) {
      throw new AuthenticationError('User not found or inactive');
    }

    // Revoke old refresh token
    await this.authRepository.revokeSession(refreshToken);

    // Generate new token pair
    const tokens = await this.generateTokenPair(
      user.id,
      user.email,
      user.role,
      context
    );

    // Log token refresh
    await this.logSecurityEvent({
      type: SecurityEventType.TOKEN_REFRESH,
      userId: user.id,
      email: user.email,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    });

    return { tokens };
  }

  async logout(refreshToken: string | undefined, user: AuthUser, context: RequestContext): Promise<void> {
    if (refreshToken) {
      await this.authRepository.revokeSession(refreshToken);
    }

    // Log logout
    await this.logSecurityEvent({
      type: SecurityEventType.LOGOUT,
      userId: user.id,
      email: user.email,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    });
  }

  async logoutAllDevices(user: AuthUser, context: RequestContext): Promise<void> {
    await this.authRepository.revokeAllUserSessions(user.id);

    // Log logout from all devices
    await this.logSecurityEvent({
      type: SecurityEventType.LOGOUT,
      userId: user.id,
      email: user.email,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      metadata: { action: 'logout_all_devices' }
    });
  }

  // Profile operations
  async getProfile(userId: string): Promise<AuthUser> {
    const user = await this.authRepository.findUserByIdSafe(userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }
    return user;
  }

  async updateProfile(userId: string, data: UpdateProfileRequest): Promise<AuthUser> {
    const updateData: UpdateUserData = {};
    
    if (data.firstName !== undefined) updateData.firstName = data.firstName;
    if (data.lastName !== undefined) updateData.lastName = data.lastName;
    if (data.phone !== undefined) updateData.phone = data.phone;
    if (data.avatar !== undefined) updateData.avatar = data.avatar;

    const updatedUser = await this.authRepository.updateUser(userId, updateData);
    return sanitizeUser(updatedUser) as AuthUser;
  }

  async changePassword(
    userId: string, 
    data: ChangePasswordRequest, 
    context: RequestContext
  ): Promise<void> {
    const { currentPassword, newPassword } = data;

    // Get user with password
    const user = await this.authRepository.findUserById(userId);
    if (!user || !user.password) {
      throw new AuthenticationError('Current password verification failed');
    }

    // Verify current password
    const isCurrentPasswordValid = await verifyPassword(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new AuthenticationError('Current password is incorrect');
    }

    // Hash new password and update
    const hashedNewPassword = await hashPassword(newPassword);
    await this.authRepository.updateUserPassword(userId, hashedNewPassword);

    // Revoke all existing sessions for security
    await this.authRepository.revokeAllUserSessions(userId);

    // Log password change
    await this.logSecurityEvent({
      type: SecurityEventType.PASSWORD_CHANGE,
      userId: user.id,
      email: user.email,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    });
  }

  // Session operations
  async getUserSessions(userId: string): Promise<SessionData[]> {
    return this.authRepository.getUserSessions(userId);
  }

  async revokeSession(sessionId: string, userId: string): Promise<void> {
    const success = await this.authRepository.revokeSpecificSession(sessionId, userId);
    if (!success) {
      throw new NotFoundError('Session not found');
    }
  }

  // Account management
  async isAccountLocked(userId: string): Promise<boolean> {
    return this.authRepository.isAccountLocked(userId);
  }

  async getAccountLockInfo(userId: string): Promise<AccountLockInfo | null> {
    return this.authRepository.getAccountLockInfo(userId);
  }

  // Private helper methods
  private async generateTokenPair(
    userId: string,
    email: string,
    role: UserRole,
    context: RequestContext
  ): Promise<TokenPair> {
    const payload: JWTPayload = {
      userId,
      email,
      role
    };

    const accessToken = generateAccessToken(payload);
    const refreshToken = generateRefreshToken(payload);

    // Store refresh token in database
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

    const sessionData: CreateSessionData = {
      id: generateSecureToken(16),
      userId,
      refreshToken,
      expiresAt,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    };

    await this.authRepository.createSession(sessionData);

    return { accessToken, refreshToken };
  }

  private async logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): Promise<void> {
    await logSecurityEvent(event);
  }
}

export default AuthService;