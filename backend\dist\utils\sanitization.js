"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sanitizeRequestBody = sanitizeRequestBody;
exports.sanitizeUser = sanitizeUser;
exports.sanitizeHeaders = sanitizeHeaders;
exports.sanitizeQuery = sanitizeQuery;
exports.sanitizeObject = sanitizeObject;
const SENSITIVE_FIELD_PATTERNS = [
    /password/i,
    /secret/i,
    /token/i,
    /key/i,
    /auth/i,
    /credential/i,
    /ssn/i,
    /social.*security/i,
    /credit.*card/i,
    /card.*number/i,
    /cvv/i,
    /cvc/i,
    /account.*number/i,
    /routing.*number/i,
    /iban/i,
    /swift/i,
    /phone/i,
    /address/i,
];
const SENSITIVE_FIELDS = [
    'password',
    'currentPassword',
    'newPassword',
    'confirmPassword',
    'token',
    'refreshToken',
    'accessToken',
    'apiKey',
    'secret',
    'privateKey',
    'publicKey',
    'jwt',
    'sessionId',
    'sessionToken',
    'authorization',
    'bearer',
    'ssn',
    'socialSecurityNumber',
    'creditCard',
    'cardNumber',
    'cvv',
    'cvc',
    'expiryDate',
    'accountNumber',
    'routingNumber',
    'iban',
    'swift',
].map(field => field.toLowerCase());
function isSensitiveField(fieldName) {
    const lowerFieldName = fieldName.toLowerCase();
    if (SENSITIVE_FIELDS.includes(lowerFieldName)) {
        return true;
    }
    return SENSITIVE_FIELD_PATTERNS.some(pattern => pattern.test(lowerFieldName));
}
function deepSanitize(obj, maxDepth = 10, currentDepth = 0) {
    if (currentDepth >= maxDepth) {
        return '[MAX_DEPTH_REACHED]';
    }
    if (obj === null || obj === undefined) {
        return obj;
    }
    if (typeof obj !== 'object') {
        return obj;
    }
    if (Array.isArray(obj)) {
        return obj.map(item => deepSanitize(item, maxDepth, currentDepth + 1));
    }
    if (obj instanceof Date) {
        return obj;
    }
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
        if (isSensitiveField(key)) {
            sanitized[key] = '[REDACTED]';
        }
        else if (typeof value === 'object' && value !== null) {
            sanitized[key] = deepSanitize(value, maxDepth, currentDepth + 1);
        }
        else {
            sanitized[key] = value;
        }
    }
    return sanitized;
}
function sanitizeRequestBody(body) {
    if (!body) {
        return body;
    }
    return deepSanitize(body);
}
function sanitizeUser(user) {
    if (!user || typeof user !== 'object') {
        return user;
    }
    const { password, passwordResetToken, passwordResetTokenExpires, emailVerificationToken, emailVerificationTokenExpires, loginAttempts, lockedUntil, sessions, ...sanitizedUser } = user;
    return sanitizedUser;
}
function sanitizeHeaders(headers) {
    if (!headers || typeof headers !== 'object') {
        return headers;
    }
    const sensitiveHeaderPatterns = [
        /authorization/i,
        /cookie/i,
        /x-api-key/i,
        /x-auth/i,
        /bearer/i,
    ];
    const sanitized = {};
    for (const [key, value] of Object.entries(headers)) {
        const isSensitive = sensitiveHeaderPatterns.some(pattern => pattern.test(key));
        sanitized[key] = isSensitive ? '[REDACTED]' : value;
    }
    return sanitized;
}
function sanitizeQuery(query) {
    if (!query || typeof query !== 'object') {
        return query;
    }
    return deepSanitize(query);
}
function sanitizeObject(obj, options = {}) {
    const { maxDepth = 10, customSensitiveFields = [] } = options;
    const originalFields = [...SENSITIVE_FIELDS];
    SENSITIVE_FIELDS.push(...customSensitiveFields.map(f => f.toLowerCase()));
    try {
        return deepSanitize(obj, maxDepth);
    }
    finally {
        SENSITIVE_FIELDS.length = originalFields.length;
        SENSITIVE_FIELDS.push(...originalFields);
    }
}
exports.default = {
    sanitizeRequestBody,
    sanitizeUser,
    sanitizeHeaders,
    sanitizeQuery,
    sanitizeObject,
    isSensitiveField,
};
//# sourceMappingURL=sanitization.js.map