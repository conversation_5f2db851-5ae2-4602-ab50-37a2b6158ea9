{"version": 3, "file": "requestLogger.js", "sourceRoot": "", "sources": ["../../src/middleware/requestLogger.ts"], "names": [], "mappings": ";;;AACA,4CAA2D;AAqB3D,SAAS,iBAAiB;IACxB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AAC3E,CAAC;AAGD,SAAS,WAAW,CAAC,GAAY;IAC/B,OAAO,CACL,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAW;QACxC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAW;QAClC,GAAG,CAAC,UAAU,EAAE,aAAa;QAC7B,GAAG,CAAC,MAAM,EAAE,aAAa;QACzB,GAAG,CAAC,EAAE;QACN,SAAS,CACV,CAAC;AACJ,CAAC;AAGM,MAAM,aAAa,GAAG,CAAC,GAAwC,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACjH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC7B,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;IAE1B,MAAM,SAAS,GAAG,iBAAiB,EAAE,CAAC;IACtC,MAAM,OAAO,GAAmB;QAC9B,SAAS;QACT,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,GAAG,EAAE,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,GAAG;QAC/B,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;QACpC,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC;QACpB,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,SAAS,EAAG,GAAW,CAAC,IAAI,EAAE,KAAK;QACnC,SAAS;KACV,CAAC;IAGF,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IAGzC,mBAAU,CAAC,IAAI,CAAC,kBAAkB,EAAE;QAClC,SAAS,EAAE,OAAO,CAAC,SAAS;QAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,GAAG,EAAE,OAAO,CAAC,GAAG;QAChB,SAAS,EAAE,OAAO,CAAC,SAAS;QAC5B,EAAE,EAAE,OAAO,CAAC,EAAE;QACd,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,SAAS,EAAE,OAAO,CAAC,SAAS;QAC5B,OAAO,EAAE,GAAG,CAAC,OAAO;QACpB,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,IAAI,EAAE,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC;KACpC,CAAC,CAAC;IAGH,MAAM,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC;IAC5B,GAAG,CAAC,GAAG,GAAG,UAAS,KAAW,EAAE,QAAc;QAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QACxC,MAAM,YAAY,GAAG,GAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAGzF,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;QACzD,mBAAU,CAAC,QAAQ,CAAC,CAAC,mBAAmB,EAAE;YACxC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,QAAQ;YACR,YAAY;YACZ,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,SAAS,EAAE,OAAO,CAAC,SAAS;SAC7B,CAAC,CAAC;QAGH,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACpB,mBAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACvC,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,QAAQ;gBACR,UAAU,EAAE,GAAG,CAAC,UAAU;gBAC1B,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC,CAAC;QACL,CAAC;QAGD,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IACjD,CAAC,CAAC;IAEF,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAvEW,QAAA,aAAa,iBAuExB;AAGF,SAAS,mBAAmB,CAAC,IAAS;IACpC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,eAAe,GAAG;QACtB,UAAU;QACV,iBAAiB;QACjB,aAAa;QACb,iBAAiB;QACjB,OAAO;QACP,cAAc;QACd,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,KAAK;QACL,sBAAsB;KACvB,CAAC;IAEF,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;IAE9B,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,KAAK,IAAI,SAAS,EAAE,CAAC;YACvB,SAAS,CAAC,KAAK,CAAC,GAAG,YAAY,CAAC;QAClC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,SAAS,CAAC;AACnB,CAAC;AAGM,MAAM,QAAQ,GAAG,CAAC,SAAiB,EAAE,OAA+B,EAAE,EAAE;IAC7E,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC/D,MAAM,MAAM,GAAG,IAAA,qBAAY,EAAC,OAAO,CAAC,CAAC;QACrC,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAE9C,MAAM,CAAC,IAAI,CAAC,UAAU,SAAS,EAAE,EAAE;YACjC,KAAK,EAAE,SAAS;YAChB,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC;YACxC,MAAM,EAAG,GAA4B,CAAC,MAAM;YAC5C,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC;YACpB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;YACpC,GAAG,SAAS;SACb,CAAC,CAAC;QAEH,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAhBW,QAAA,QAAQ,YAgBnB;AAGK,MAAM,YAAY,GAAG,CAAC,SAAgF,EAAE,EAAE;IAC/G,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC5E,MAAM,MAAM,GAAG,IAAA,qBAAY,EAAC,WAAW,CAAC,CAAC;QAGzC,MAAM,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC;QAC5B,GAAG,CAAC,GAAG,GAAG,UAAS,KAAW,EAAE,QAAc;YAC5C,MAAM,SAAS,GAAG,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC;YAEhE,MAAM,CAAC,IAAI,CAAC,GAAG,SAAS,IAAI,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE,GAAG,CAAC,MAAM,EAAE;gBAC3E,SAAS;gBACT,OAAO,EAAE,SAAS;gBAClB,UAAU,EAAE,GAAG,CAAC,UAAU;gBAC1B,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC;gBACxC,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC;gBACpB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;gBACpC,KAAK,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,IAAK,GAAW,CAAC,IAAI,EAAE,KAAK;aACnD,CAAC,CAAC;YAEH,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACjD,CAAC,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAxBW,QAAA,YAAY,gBAwBvB;AAGK,MAAM,QAAQ,GAAG,CAAC,GAAU,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAC5F,MAAM,MAAM,GAAG,IAAA,qBAAY,EAAC,OAAO,CAAC,CAAC;IAErC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,GAAG,EAAE;QACjC,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC;QACxC,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,GAAG,EAAE,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,GAAG;QAC/B,UAAU,EAAE,GAAG,CAAC,UAAU;QAC1B,MAAM,EAAG,GAA4B,CAAC,MAAM;QAC5C,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC;QACpB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;QACpC,IAAI,EAAE,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC;QACnC,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,MAAM,EAAE,GAAG,CAAC,MAAM;KACnB,CAAC,CAAC;IAEH,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,CAAC,CAAC;AAjBW,QAAA,QAAQ,YAiBnB;AAGK,MAAM,iBAAiB,GAAG,CAAC,GAAiB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAC9F,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC7B,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;IAE1B,MAAM,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC;IAC5B,GAAG,CAAC,GAAG,GAAG,UAAS,KAAW,EAAE,QAAc;QAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QACxC,MAAM,iBAAiB,GAAG,IAAA,qBAAY,EAAC,aAAa,CAAC,CAAC;QAGtD,iBAAiB,CAAC,WAAW,CAAC,qBAAqB,EAAE,QAAQ,EAAE;YAC7D,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC;YACxC,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,GAAG;YAC/B,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAClF,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IACjD,CAAC,CAAC;IAEF,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAtBW,QAAA,iBAAiB,qBAsB5B;AAEF,kBAAe;IACb,aAAa,EAAb,qBAAa;IACb,QAAQ,EAAR,gBAAQ;IACR,YAAY,EAAZ,oBAAY;IACZ,QAAQ,EAAR,gBAAQ;IACR,iBAAiB,EAAjB,yBAAiB;CAClB,CAAC"}