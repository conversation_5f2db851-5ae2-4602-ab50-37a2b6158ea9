{"version": 3, "file": "requestLogger.js", "sourceRoot": "", "sources": ["../../src/middleware/requestLogger.ts"], "names": [], "mappings": ";;;AACA,4CAA6C;AAqB7C,SAAS,iBAAiB;IACzB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AAC1E,CAAC;AAGD,SAAS,WAAW,CAAC,GAAY;IAChC,OAAO,CACL,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAY;QACzC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAY;QACpC,GAAG,CAAC,UAAU,EAAE,aAAa;QAC7B,GAAG,CAAC,MAAM,EAAE,aAAa;QACzB,GAAG,CAAC,EAAE;QACN,SAAS,CACT,CAAC;AACH,CAAC;AAGM,MAAM,aAAa,GAAG,CAC5B,GAAwC,EACxC,GAAa,EACb,IAAkB,EACX,EAAE;IACT,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC7B,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;IAE1B,MAAM,SAAS,GAAG,iBAAiB,EAAE,CAAC;IACtC,MAAM,OAAO,GAAmB;QAC/B,SAAS;QACT,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,GAAG,EAAE,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,GAAG;QAC/B,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;QACpC,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC;QACpB,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,SAAS,EAAG,GAAW,CAAC,IAAI,EAAE,KAAK;QACnC,SAAS;KACT,CAAC;IAGF,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IAGzC,MAAM,UAAU,GAAG,IAAA,qBAAY,EAAC,MAAM,CAAC,CAAC;IACxC,UAAU,CAAC,IAAI,CAAC,kBAAkB,EAAE;QACnC,SAAS,EAAE,OAAO,CAAC,SAAS;QAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,GAAG,EAAE,OAAO,CAAC,GAAG;QAChB,SAAS,EAAE,OAAO,CAAC,SAAS;QAC5B,EAAE,EAAE,OAAO,CAAC,EAAE;QACd,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,SAAS,EAAE,OAAO,CAAC,SAAS;QAC5B,OAAO,EAAE,GAAG,CAAC,OAAO;QACpB,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,IAAI,EAAE,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC;KACnC,CAAC,CAAC;IAGH,MAAM,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC;IAC5B,GAAG,CAAC,GAAG,GAAG,UAAU,KAAW,EAAE,QAAc;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QACxC,MAAM,YAAY,GACjB,GAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAGrE,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;QACzD,MAAM,UAAU,GAAG,IAAA,qBAAY,EAAC,MAAM,CAAC,CAAC;QACxC,UAAU,CAAC,QAAQ,CAAC,CAAC,mBAAmB,EAAE;YACzC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,QAAQ;YACR,YAAY;YACZ,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,SAAS,EAAE,OAAO,CAAC,SAAS;SAC5B,CAAC,CAAC;QAGH,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACrB,MAAM,UAAU,GAAG,IAAA,qBAAY,EAAC,MAAM,CAAC,CAAC;YACxC,UAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACxC,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,QAAQ;gBACR,UAAU,EAAE,GAAG,CAAC,UAAU;gBAC1B,MAAM,EAAE,OAAO,CAAC,MAAM;aACtB,CAAC,CAAC;QACJ,CAAC;QAGD,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IAChD,CAAC,CAAC;IAEF,IAAI,EAAE,CAAC;AACR,CAAC,CAAC;AA/EW,QAAA,aAAa,iBA+ExB;AAGF,SAAS,mBAAmB,CAAC,IAAS;IACrC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QACvC,OAAO,IAAI,CAAC;IACb,CAAC;IAED,MAAM,eAAe,GAAG;QACvB,UAAU;QACV,iBAAiB;QACjB,aAAa;QACb,iBAAiB;QACjB,OAAO;QACP,cAAc;QACd,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,KAAK;QACL,sBAAsB;KACtB,CAAC;IAEF,MAAM,SAAS,GAAG,EAAC,GAAG,IAAI,EAAC,CAAC;IAE5B,eAAe,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;QACjC,IAAI,KAAK,IAAI,SAAS,EAAE,CAAC;YACxB,SAAS,CAAC,KAAK,CAAC,GAAG,YAAY,CAAC;QACjC,CAAC;IACF,CAAC,CAAC,CAAC;IAEH,OAAO,SAAS,CAAC;AAClB,CAAC;AAGM,MAAM,QAAQ,GAAG,CACvB,SAAiB,EACjB,OAA+B,EAC9B,EAAE;IACH,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAChE,MAAM,MAAM,GAAG,IAAA,qBAAY,EAAC,OAAO,CAAC,CAAC;QACrC,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAE9C,MAAM,CAAC,IAAI,CAAC,UAAU,SAAS,EAAE,EAAE;YAClC,KAAK,EAAE,SAAS;YAChB,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC;YACxC,MAAM,EAAG,GAA4B,CAAC,MAAM;YAC5C,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC;YACpB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;YACpC,GAAG,SAAS;SACZ,CAAC,CAAC;QAEH,IAAI,EAAE,CAAC;IACR,CAAC,CAAC;AACH,CAAC,CAAC;AAnBW,QAAA,QAAQ,YAmBnB;AAGK,MAAM,YAAY,GAAG,CAC3B,SAKkB,EACjB,EAAE;IACH,OAAO,CACN,GAAyB,EACzB,GAAa,EACb,IAAkB,EACX,EAAE;QACT,MAAM,MAAM,GAAG,IAAA,qBAAY,EAAC,WAAW,CAAC,CAAC;QAGzC,MAAM,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC;QAC5B,GAAG,CAAC,GAAG,GAAG,UAAU,KAAW,EAAE,QAAc;YAC9C,MAAM,SAAS,GAAG,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC;YAEhE,MAAM,CAAC,IAAI,CAAC,GAAG,SAAS,IAAI,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE;gBAChE,SAAS;gBACT,OAAO,EAAE,SAAS;gBAClB,UAAU,EAAE,GAAG,CAAC,UAAU;gBAC1B,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC;gBACxC,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC;gBACpB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;gBACpC,KAAK,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,IAAK,GAAW,CAAC,IAAI,EAAE,KAAK;gBAClD,MAAM,EAAE,GAAG,CAAC,MAAM;aAClB,CAAC,CAAC;YAEH,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QAChD,CAAC,CAAC;QAEF,IAAI,EAAE,CAAC;IACR,CAAC,CAAC;AACH,CAAC,CAAC;AApCW,QAAA,YAAY,gBAoCvB;AAGK,MAAM,QAAQ,GAAG,CACvB,GAAU,EACV,GAAY,EACZ,GAAa,EACb,IAAkB,EACX,EAAE;IACT,MAAM,MAAM,GAAG,IAAA,qBAAY,EAAC,OAAO,CAAC,CAAC;IAErC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,GAAG,EAAE;QAClC,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC;QACxC,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,GAAG,EAAE,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,GAAG;QAC/B,UAAU,EAAE,GAAG,CAAC,UAAU;QAC1B,MAAM,EAAG,GAA4B,CAAC,MAAM;QAC5C,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC;QACpB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;QACpC,IAAI,EAAE,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC;QACnC,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,MAAM,EAAE,GAAG,CAAC,MAAM;KAClB,CAAC,CAAC;IAEH,IAAI,CAAC,GAAG,CAAC,CAAC;AACX,CAAC,CAAC;AAtBW,QAAA,QAAQ,YAsBnB;AAGK,MAAM,iBAAiB,GAAG,CAChC,GAAiB,EACjB,GAAa,EACb,IAAkB,EACX,EAAE;IACT,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC7B,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;IAE1B,MAAM,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC;IAC5B,GAAG,CAAC,GAAG,GAAG,UAAU,KAAW,EAAE,QAAc;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QACxC,MAAM,iBAAiB,GAAG,IAAA,qBAAY,EAAC,aAAa,CAAC,CAAC;QAGtD,iBAAiB,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7C,QAAQ;YACR,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC;YACxC,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,GAAG;YAC/B,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,YAAY,EACX,GAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACpE,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IAChD,CAAC,CAAC;IAEF,IAAI,EAAE,CAAC;AACR,CAAC,CAAC;AA5BW,QAAA,iBAAiB,qBA4B5B;AAEF,kBAAe;IACd,aAAa,EAAb,qBAAa;IACb,QAAQ,EAAR,gBAAQ;IACR,YAAY,EAAZ,oBAAY;IACZ,QAAQ,EAAR,gBAAQ;IACR,iBAAiB,EAAjB,yBAAiB;CACjB,CAAC"}