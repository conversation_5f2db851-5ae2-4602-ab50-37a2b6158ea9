import { ConflictError, NotFoundError, ValidationError } from '../middleware/errorHandler';
import { IProviderRepository } from '../repositories/ProviderRepository';
import {
  AddRatingRequest,
  CreateProviderRequest,
  CreateProviderServiceRequest,
  CreateServiceAreaRequest,
  ProviderDetail,
  ProviderListQuery,
  ProviderListResponse,
  ProviderOperatingHour,
  ProviderRatingDisplay,
  ProviderServiceArea,
  ProviderServiceSummary,
  UpdateProviderRequest,
  UpdateProviderServiceRequest,
  UpsertOperatingHoursRequest,
} from '../types/provider';

export interface IProviderService {
  // Provider CRUD
  listProviders(query: ProviderListQuery): Promise<ProviderListResponse>;
  getProviderById(id: string): Promise<ProviderDetail>;
  createProvider(req: CreateProviderRequest): Promise<ProviderDetail>;
  updateProvider(id: string, req: UpdateProviderRequest): Promise<ProviderDetail>;
  deleteProvider(id: string): Promise<boolean>;

  // Provider services
  listProviderServices(providerId: string): Promise<ProviderServiceSummary[]>;
  addProviderService(providerId: string, req: CreateProviderServiceRequest): Promise<ProviderServiceSummary>;
  updateProviderService(
    providerId: string,
    serviceId: string,
    req: UpdateProviderServiceRequest
  ): Promise<ProviderServiceSummary>;
  removeProviderService(providerId: string, serviceId: string): Promise<boolean>;

  // Operating hours
  getOperatingHours(providerId: string): Promise<ProviderOperatingHour[]>;
  upsertOperatingHours(providerId: string, req: UpsertOperatingHoursRequest): Promise<ProviderOperatingHour[]>;

  // Service areas
  listServiceAreas(providerId: string): Promise<ProviderServiceArea[]>;
  addServiceArea(providerId: string, req: CreateServiceAreaRequest): Promise<ProviderServiceArea>;
  removeServiceArea(providerId: string, areaId: string): Promise<boolean>;

  // Ratings
  getRatingDisplay(providerId: string): Promise<ProviderRatingDisplay | null>;
  addRating(providerId: string, req: AddRatingRequest): Promise<ProviderRatingDisplay>;
}

export class ProviderService implements IProviderService {
  constructor(private repo: IProviderRepository) {}

  // Provider CRUD
  async listProviders(query: ProviderListQuery): Promise<ProviderListResponse> {
    // Basic validation
    if (query.page !== undefined && query.page < 1) throw new ValidationError('Page must be >= 1');
    if (query.limit !== undefined && (query.limit < 1 || query.limit > 100))
      throw new ValidationError('Limit must be 1..100');
    return this.repo.listProviders(query);
  }

  async getProviderById(id: string): Promise<ProviderDetail> {
    const p = await this.repo.findById(id);
    if (!p) throw new NotFoundError('Provider not found');
    return p;
  }

  async createProvider(req: CreateProviderRequest): Promise<ProviderDetail> {
    if (!req.userId) throw new ValidationError('userId is required');
    if (!req.businessName || !req.businessName.trim()) throw new ValidationError('businessName is required');

    // Ensure the user does not already have a provider profile
    const existing = await this.repo.findByUserId(req.userId);
    if (existing) throw new ConflictError('User already has a provider profile');
    return this.repo.createProvider(req);
  }

  async updateProvider(id: string, req: UpdateProviderRequest): Promise<ProviderDetail> {
    const existing = await this.repo.findById(id);
    if (!existing) throw new NotFoundError('Provider not found');
    return this.repo.updateProvider(id, req);
  }

  async deleteProvider(id: string): Promise<boolean> {
    const existing = await this.repo.findById(id);
    if (!existing) throw new NotFoundError('Provider not found');
    return this.repo.deleteProvider(id);
  }

  // Provider services
  async listProviderServices(providerId: string): Promise<ProviderServiceSummary[]> {
    const existing = await this.repo.findById(providerId);
    if (!existing) throw new NotFoundError('Provider not found');
    return this.repo.listProviderServices(providerId);
  }

  async addProviderService(providerId: string, req: CreateProviderServiceRequest): Promise<ProviderServiceSummary> {
    if (!req.serviceId) throw new ValidationError('serviceId is required');
    if (req.price == null || req.price < 0) throw new ValidationError('price must be non-negative');
    const existing = await this.repo.findById(providerId);
    if (!existing) throw new NotFoundError('Provider not found');
    return this.repo.addProviderService(providerId, req);
  }

  async updateProviderService(
    providerId: string,
    serviceId: string,
    req: UpdateProviderServiceRequest
  ): Promise<ProviderServiceSummary> {
    const existing = await this.repo.findById(providerId);
    if (!existing) throw new NotFoundError('Provider not found');
    return this.repo.updateProviderService(providerId, serviceId, req);
  }

  async removeProviderService(providerId: string, serviceId: string): Promise<boolean> {
    const existing = await this.repo.findById(providerId);
    if (!existing) throw new NotFoundError('Provider not found');
    return this.repo.removeProviderService(providerId, serviceId);
  }

  // Operating hours
  async getOperatingHours(providerId: string): Promise<ProviderOperatingHour[]> {
    const existing = await this.repo.findById(providerId);
    if (!existing) throw new NotFoundError('Provider not found');
    return this.repo.getOperatingHours(providerId);
  }

  async upsertOperatingHours(providerId: string, req: UpsertOperatingHoursRequest): Promise<ProviderOperatingHour[]> {
    const existing = await this.repo.findById(providerId);
    if (!existing) throw new NotFoundError('Provider not found');
    if (!req.hours || req.hours.length === 0) throw new ValidationError('hours is required');
    return this.repo.upsertOperatingHours(providerId, req);
  }

  // Service areas
  async listServiceAreas(providerId: string): Promise<ProviderServiceArea[]> {
    const existing = await this.repo.findById(providerId);
    if (!existing) throw new NotFoundError('Provider not found');
    return this.repo.listServiceAreas(providerId);
  }

  async addServiceArea(providerId: string, req: CreateServiceAreaRequest): Promise<ProviderServiceArea> {
    const existing = await this.repo.findById(providerId);
    if (!existing) throw new NotFoundError('Provider not found');
    return this.repo.addServiceArea(providerId, req);
  }

  async removeServiceArea(providerId: string, areaId: string): Promise<boolean> {
    const existing = await this.repo.findById(providerId);
    if (!existing) throw new NotFoundError('Provider not found');
    return this.repo.removeServiceArea(providerId, areaId);
  }

  // Ratings
  async getRatingDisplay(providerId: string): Promise<ProviderRatingDisplay | null> {
    const existing = await this.repo.findById(providerId);
    if (!existing) throw new NotFoundError('Provider not found');
    return this.repo.getRatingDisplay(providerId);
  }

  async addRating(providerId: string, req: AddRatingRequest): Promise<ProviderRatingDisplay> {
    const existing = await this.repo.findById(providerId);
    if (!existing) throw new NotFoundError('Provider not found');
    if (!req.rating || req.rating < 1 || req.rating > 5) throw new ValidationError('rating must be 1-5');
    return this.repo.incrementRating(providerId, req.rating);
  }
}

export default ProviderService;
