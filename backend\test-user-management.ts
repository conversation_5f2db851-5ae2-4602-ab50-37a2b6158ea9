#!/usr/bin/env ts-node

/**
 * User Management API Test Script
 * 
 * This script tests the comprehensive user management features including:
 * - User registration and authentication
 * - Profile management
 * - Password reset functionality
 * - Email verification
 * - Session management
 * 
 * Run with: npx ts-node test-user-management.ts
 */

import axios, { AxiosResponse } from 'axios';
import 'dotenv/config';

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
const BASE_URL = `${API_BASE_URL}/api`;

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  isActive: boolean;
  isVerified: boolean;
  avatar: string | null;
  phone: string | null;
}

interface AuthResponse {
  user: User;
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

class UserManagementTester {
  private testEmail = `test-${Date.now()}@example.com`;
  private testPassword = 'TestPassword123!';
  private accessToken = '';
  private refreshToken = '';
  private userId = '';

  constructor() {
    console.log('🧪 Starting User Management API Tests');
    console.log('=====================================');
    console.log(`📧 Test Email: ${this.testEmail}`);
    console.log(`🔑 Test Password: ${this.testPassword}`);
    console.log(`🌐 API Base URL: ${BASE_URL}`);
    console.log('');
  }

  async runTests(): Promise<void> {
    try {
      // Test user registration
      await this.testUserRegistration();
      
      // Test user login
      await this.testUserLogin();
      
      // Test get profile
      await this.testGetProfile();
      
      // Test profile update
      await this.testUpdateProfile();
      
      // Test change password
      await this.testChangePassword();
      
      // Test password reset request
      await this.testPasswordResetRequest();
      
      // Test session management
      await this.testSessionManagement();
      
      // Test logout
      await this.testLogout();

      console.log('');
      console.log('✅ All tests completed successfully!');
      console.log('=====================================');
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      process.exit(1);
    }
  }

  private async testUserRegistration(): Promise<void> {
    console.log('🔬 Testing User Registration...');
    
    try {
      const response: AxiosResponse<ApiResponse<AuthResponse>> = await axios.post(
        `${BASE_URL}/auth/register`,
        {
          email: this.testEmail,
          password: this.testPassword,
          firstName: 'Test',
          lastName: 'User',
          phone: '+1234567890'
        }
      );

      if (response.status === 201 && response.data.success) {
        this.accessToken = response.data.data!.tokens.accessToken;
        this.refreshToken = response.data.data!.tokens.refreshToken;
        this.userId = response.data.data!.user.id;
        
        console.log('  ✅ User registration successful');
        console.log(`  📝 User ID: ${this.userId}`);
        console.log(`  🎫 Access Token: ${this.accessToken.substring(0, 20)}...`);
        console.log(`  🔄 Refresh Token: ${this.refreshToken.substring(0, 20)}...`);
      } else {
        throw new Error(`Registration failed: ${response.data.message || 'Unknown error'}`);
      }
    } catch (error: any) {
      console.error('  ❌ Registration failed:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  private async testUserLogin(): Promise<void> {
    console.log('');
    console.log('🔬 Testing User Login...');
    
    try {
      const response: AxiosResponse<ApiResponse<AuthResponse>> = await axios.post(
        `${BASE_URL}/auth/login`,
        {
          email: this.testEmail,
          password: this.testPassword
        }
      );

      if (response.status === 200 && response.data.success) {
        // Update tokens with fresh ones from login
        this.accessToken = response.data.data!.tokens.accessToken;
        this.refreshToken = response.data.data!.tokens.refreshToken;
        
        console.log('  ✅ User login successful');
        console.log(`  🎫 New Access Token: ${this.accessToken.substring(0, 20)}...`);
      } else {
        throw new Error(`Login failed: ${response.data.message || 'Unknown error'}`);
      }
    } catch (error: any) {
      console.error('  ❌ Login failed:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  private async testGetProfile(): Promise<void> {
    console.log('');
    console.log('🔬 Testing Get User Profile...');
    
    try {
      const response: AxiosResponse<ApiResponse<{ user: User }>> = await axios.get(
        `${BASE_URL}/auth/me`,
        {
          headers: {
            Authorization: `Bearer ${this.accessToken}`
          }
        }
      );

      if (response.status === 200 && response.data.success) {
        const user = response.data.data!.user;
        console.log('  ✅ Profile retrieval successful');
        console.log(`  👤 User: ${user.firstName} ${user.lastName} (${user.email})`);
        console.log(`  📱 Phone: ${user.phone}`);
        console.log(`  🎭 Role: ${user.role}`);
        console.log(`  ✅ Active: ${user.isActive}`);
        console.log(`  📧 Verified: ${user.isVerified}`);
      } else {
        throw new Error(`Get profile failed: ${response.data.message || 'Unknown error'}`);
      }
    } catch (error: any) {
      console.error('  ❌ Get profile failed:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  private async testUpdateProfile(): Promise<void> {
    console.log('');
    console.log('🔬 Testing Update User Profile...');
    
    try {
      const response: AxiosResponse<ApiResponse<{ user: User }>> = await axios.patch(
        `${BASE_URL}/auth/profile`,
        {
          firstName: 'Updated',
          lastName: 'TestUser',
          phone: '+1987654321'
        },
        {
          headers: {
            Authorization: `Bearer ${this.accessToken}`
          }
        }
      );

      if (response.status === 200 && response.data.success) {
        const user = response.data.data!.user;
        console.log('  ✅ Profile update successful');
        console.log(`  👤 Updated User: ${user.firstName} ${user.lastName}`);
        console.log(`  📱 Updated Phone: ${user.phone}`);
      } else {
        throw new Error(`Profile update failed: ${response.data.message || 'Unknown error'}`);
      }
    } catch (error: any) {
      console.error('  ❌ Profile update failed:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  private async testChangePassword(): Promise<void> {
    console.log('');
    console.log('🔬 Testing Change Password...');
    
    const newPassword = 'NewTestPassword123!';
    
    try {
      const response: AxiosResponse<ApiResponse> = await axios.post(
        `${BASE_URL}/auth/change-password`,
        {
          currentPassword: this.testPassword,
          newPassword: newPassword
        },
        {
          headers: {
            Authorization: `Bearer ${this.accessToken}`
          }
        }
      );

      if (response.status === 200 && response.data.success) {
        console.log('  ✅ Password change successful');
        console.log('  ⚠️  All sessions should now be invalidated');
        
        // Update password for future tests
        this.testPassword = newPassword;
        
        // Need to login again since all sessions are invalidated
        await this.testUserLogin();
      } else {
        throw new Error(`Password change failed: ${response.data.message || 'Unknown error'}`);
      }
    } catch (error: any) {
      console.error('  ❌ Password change failed:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  private async testPasswordResetRequest(): Promise<void> {
    console.log('');
    console.log('🔬 Testing Password Reset Request...');
    
    try {
      const response: AxiosResponse<ApiResponse> = await axios.post(
        `${BASE_URL}/auth/password-reset/request`,
        {
          email: this.testEmail
        }
      );

      if (response.status === 200 && response.data.success) {
        console.log('  ✅ Password reset request successful');
        console.log('  📧 Reset email would be sent (check server logs for token)');
      } else {
        throw new Error(`Password reset request failed: ${response.data.message || 'Unknown error'}`);
      }
    } catch (error: any) {
      console.error('  ❌ Password reset request failed:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  private async testSessionManagement(): Promise<void> {
    console.log('');
    console.log('🔬 Testing Session Management...');
    
    try {
      const response: AxiosResponse<ApiResponse<{ sessions: any[] }>> = await axios.get(
        `${BASE_URL}/auth/sessions`,
        {
          headers: {
            Authorization: `Bearer ${this.accessToken}`
          }
        }
      );

      if (response.status === 200 && response.data.success) {
        const sessions = response.data.data!.sessions;
        console.log('  ✅ Session retrieval successful');
        console.log(`  📱 Active sessions: ${sessions.length}`);
        
        sessions.forEach((session, index) => {
          console.log(`    ${index + 1}. IP: ${session.ipAddress} | Current: ${session.isCurrent} | Created: ${new Date(session.createdAt).toLocaleString()}`);
        });
      } else {
        throw new Error(`Session management failed: ${response.data.message || 'Unknown error'}`);
      }
    } catch (error: any) {
      console.error('  ❌ Session management failed:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  private async testLogout(): Promise<void> {
    console.log('');
    console.log('🔬 Testing User Logout...');
    
    try {
      const response: AxiosResponse<ApiResponse> = await axios.post(
        `${BASE_URL}/auth/logout`,
        {
          refreshToken: this.refreshToken
        },
        {
          headers: {
            Authorization: `Bearer ${this.accessToken}`
          }
        }
      );

      if (response.status === 200 && response.data.success) {
        console.log('  ✅ User logout successful');
        console.log('  🔓 Session invalidated');
      } else {
        throw new Error(`Logout failed: ${response.data.message || 'Unknown error'}`);
      }
    } catch (error: any) {
      console.error('  ❌ Logout failed:', error.response?.data?.message || error.message);
      throw error;
    }
  }
}

// Main execution
async function main() {
  const tester = new UserManagementTester();
  await tester.runTests();
}

// Run the tests
if (require.main === module) {
  main().catch((error) => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}