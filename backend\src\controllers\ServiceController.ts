import { ServicePricingType } from '@prisma/client';
import { Response } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { IServiceService } from '../services/ServiceService';
import { AuthenticatedRequest } from '../types/auth';
import {
  CreateServiceCategoryRequest,
  CreateServiceFormFieldOptionRequest,
  CreateServiceFormFieldRequest,
  CreateServiceRequest,
  PriceCalculationRequest,
  ServiceListQuery,
  ServiceSearchQuery,
  UpdateServiceCategoryRequest,
  UpdateServiceFormFieldOptionRequest,
  UpdateServiceFormFieldRequest,
  UpdateServiceRequest,
} from '../types/service';
import { createLogger } from '../utils/logger';
import { RequestLogger } from '../utils/RequestLogger';
import { createPaginationMeta } from '../utils/responseWrapper';

// Controller interface for dependency inversion
export interface IServiceController {
  // Service CRUD operations
  getServiceById: any;
  createService: any;
  updateService: any;
  deleteService: any;

  // Service list and search operations
  getServices: any;
  searchServices: any;
  getServicesByCategory: any;
  getPopularServices: any;
  getServicesByPricingType: any;

  // Service statistics
  getServiceStats: any;

  // Service category operations
  getServiceCategories: any;
  getServiceCategoryById: any;
  getServiceCategoryByRoute: any;
  getServicesByCategoryRoute: any;
  createServiceCategory: any;
  updateServiceCategory: any;
  deleteServiceCategory: any;

  // Service form field operations
  getServiceFormFields: any;
  getServiceFormFieldById: any;
  createServiceFormField: any;
  updateServiceFormField: any;
  deleteServiceFormField: any;

  // Service form field option operations
  getServiceFormFieldOptions: any;
  createServiceFormFieldOption: any;
  updateServiceFormFieldOption: any;
  deleteServiceFormFieldOption: any;

  // Price calculation
  calculateServicePrice: any;

  // Utility operations
  serviceExists: any;
  serviceCategoryExists: any;
  countServices: any;
  countServicesByCategory: any;
}

// HTTP Controller implementation - handles only HTTP concerns
export class ServiceController implements IServiceController {
  private logger =
    (createLogger && createLogger('ServiceController')) ||
    ({
      info: () => undefined,
      error: () => undefined,
      warn: () => undefined,
      debug: () => undefined,
    } as unknown as ReturnType<typeof createLogger>);

  constructor(private serviceService: IServiceService) {
    this.logger.info('ServiceController initialized');
  }

  // Service CRUD operations
  getServiceById = asyncHandler(
    RequestLogger.withLogging(
      this.logger,
      'Get service by ID',
      async (req: AuthenticatedRequest, res: Response): Promise<void> => {
        const { id } = req.params;
        const service = await this.serviceService.getServiceById(id);
        res.success({ service });
      }
    )
  );

  createService = asyncHandler(
    RequestLogger.withLogging(
      this.logger,
      'Create service',
      async (req: AuthenticatedRequest, res: Response): Promise<void> => {
        const serviceData: CreateServiceRequest = req.body;
        const service = await this.serviceService.createService(serviceData);
        res.created({ service }, 'Service created successfully');
      }
    )
  );

  updateService = asyncHandler(
    RequestLogger.withLogging(
      this.logger,
      'Update service',
      async (req: AuthenticatedRequest, res: Response): Promise<void> => {
        const { id } = req.params;
        const updateData: UpdateServiceRequest = req.body;
        const service = await this.serviceService.updateService(id, updateData);
        res.success({ service }, 'Service updated successfully');
      }
    )
  );

  deleteService = asyncHandler(
    RequestLogger.withLogging(
      this.logger,
      'Delete service',
      async (req: AuthenticatedRequest, res: Response): Promise<void> => {
        const { id } = req.params;
        const deleted = await this.serviceService.deleteService(id);
        res.success({ deleted }, 'Service deleted successfully');
      }
    )
  );

  // Service list and search operations
  getServices = asyncHandler(
    RequestLogger.withLogging(
      this.logger,
      'Get services list',
      async (req: AuthenticatedRequest, res: Response): Promise<void> => {
        const query: ServiceListQuery = req.query;
        const result = await this.serviceService.getServices(query);

        // Extract pagination data
        const { services, pagination: paginationData, ...otherData } = result;
        const pagination = paginationData
          ? createPaginationMeta(paginationData.page, paginationData.limit, paginationData.total)
          : undefined;

        res.success({ services, ...otherData }, undefined, undefined, pagination);
      }
    )
  );

  searchServices = asyncHandler(
    RequestLogger.withLogging(
      this.logger,
      'Search services',
      async (req: AuthenticatedRequest, res: Response): Promise<void> => {
        const query: ServiceSearchQuery = req.query;
        const result = await this.serviceService.searchServices(query);
        res.success(result);
      }
    )
  );

  getServicesByCategory = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { categoryId } = req.params;

    this.logger.info('Get services by category request received', {
      requestId,
      categoryId,
      requestingUserId: req.userId,
    });

    try {
      const services = await this.serviceService.getServicesByCategory(categoryId);

      const duration = Date.now() - startTime;
      this.logger.info('Get services by category completed successfully', {
        requestId,
        categoryId,
        count: services.length,
        duration,
      });

      res.success({ services });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get services by category failed', error, {
        requestId,
        categoryId,
        duration,
      });
      throw error;
    }
  });

  getPopularServices = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { limit } = req.query;

    this.logger.info('Get popular services request received', {
      requestId,
      limit,
      requestingUserId: req.userId,
    });

    try {
      const services = await this.serviceService.getPopularServices(limit ? parseInt(limit as string) : 10);

      const duration = Date.now() - startTime;
      this.logger.info('Get popular services completed successfully', {
        requestId,
        count: services.length,
        duration,
      });

      res.success({ services });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get popular services failed', error, {
        requestId,
        duration,
      });
      throw error;
    }
  });

  getServicesByPricingType = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { pricingType } = req.params;

    this.logger.info('Get services by pricing type request received', {
      requestId,
      pricingType,
      requestingUserId: req.userId,
    });

    try {
      const services = await this.serviceService.getServicesByPricingType(pricingType as ServicePricingType);

      const duration = Date.now() - startTime;
      this.logger.info('Get services by pricing type completed successfully', {
        requestId,
        pricingType,
        count: services.length,
        duration,
      });

      res.success({ services });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get services by pricing type failed', error, {
        requestId,
        pricingType,
        duration,
      });
      throw error;
    }
  });

  // Service statistics
  getServiceStats = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Get service stats request received', {
      requestId,
      requestingUserId: req.userId,
    });

    try {
      const stats = await this.serviceService.getServiceStats();

      const duration = Date.now() - startTime;
      this.logger.info('Get service stats completed successfully', {
        requestId,
        totalServices: stats.total,
        activeServices: stats.active,
        duration,
      });

      res.success({ stats });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get service stats failed', error, {
        requestId,
        duration,
      });
      throw error;
    }
  });

  // Service category operations
  getServiceCategories = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Get service categories request received', {
      requestId,
      requestingUserId: req.userId,
    });

    try {
      const categories = await this.serviceService.getServiceCategories();

      const duration = Date.now() - startTime;
      this.logger.info('Get service categories completed successfully', {
        requestId,
        count: categories.length,
        duration,
      });

      res.success({ categories });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get service categories failed', error, {
        requestId,
        duration,
      });
      throw error;
    }
  });

  getServiceCategoryById = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { id } = req.params;

    this.logger.info('Get service category by ID request received', {
      requestId,
      categoryId: id,
      requestingUserId: req.userId,
    });

    try {
      const category = await this.serviceService.getServiceCategoryById(id);

      const duration = Date.now() - startTime;
      this.logger.info('Get service category by ID completed successfully', {
        requestId,
        categoryId: id,
        categoryName: category.name,
        duration,
      });

      res.success({ category });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get service category by ID failed', error, {
        requestId,
        categoryId: id,
        duration,
      });
      throw error;
    }
  });

  getServiceCategoryByRoute = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { route } = req.params;

    this.logger.info('Get service category by route request received', {
      requestId,
      route,
      requestingUserId: req.userId,
    });

    try {
      const category = await this.serviceService.getServiceCategoryByRoute(route);

      const duration = Date.now() - startTime;
      this.logger.info('Get service category by route completed successfully', {
        requestId,
        route,
        categoryName: category.name,
        duration,
      });

      res.success({ category });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get service category by route failed', error, {
        requestId,
        route,
        duration,
      });
      throw error;
    }
  });

  // Convenience: get services by category route (returns only services array)
  getServicesByCategoryRoute = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { route } = req.params as { route: string };

    this.logger.info('Get services by category route request received', {
      requestId,
      route,
      requestingUserId: req.userId,
    });

    try {
      const category = await this.serviceService.getServiceCategoryByRoute(route);
      const services = category.services || [];

      const duration = Date.now() - startTime;
      this.logger.info('Get services by category route completed successfully', {
        requestId,
        route,
        count: services.length,
        duration,
      });

      res.success({ services });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get services by category route failed', error, {
        requestId,
        route,
        duration,
      });
      throw error;
    }
  });

  createServiceCategory = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const categoryData: CreateServiceCategoryRequest = req.body;

    this.logger.info('Create service category request received', {
      requestId,
      categoryName: categoryData.name,
      route: categoryData.route,
      requestingUserId: req.userId,
    });

    try {
      const category = await this.serviceService.createServiceCategory(categoryData);

      const duration = Date.now() - startTime;
      this.logger.info('Create service category completed successfully', {
        requestId,
        categoryId: category.id,
        categoryName: category.name,
        duration,
      });

      res.created({ category }, 'Service category created successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Create service category failed', error, {
        requestId,
        categoryName: categoryData.name,
        duration,
      });
      throw error;
    }
  });

  updateServiceCategory = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { id } = req.params;
    const updateData: UpdateServiceCategoryRequest = req.body;

    this.logger.info('Update service category request received', {
      requestId,
      categoryId: id,
      updatedFields: Object.keys(updateData),
      requestingUserId: req.userId,
    });

    try {
      const category = await this.serviceService.updateServiceCategory(id, updateData);

      const duration = Date.now() - startTime;
      this.logger.info('Update service category completed successfully', {
        requestId,
        categoryId: id,
        categoryName: category.name,
        duration,
      });

      res.success({ category }, 'Service category updated successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Update service category failed', error, {
        requestId,
        categoryId: id,
        duration,
      });
      throw error;
    }
  });

  deleteServiceCategory = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { id } = req.params;

    this.logger.info('Delete service category request received', {
      requestId,
      categoryId: id,
      requestingUserId: req.userId,
    });

    try {
      const deleted = await this.serviceService.deleteServiceCategory(id);

      const duration = Date.now() - startTime;
      this.logger.info('Delete service category completed successfully', {
        requestId,
        categoryId: id,
        deleted,
        duration,
      });

      res.success({ deleted }, 'Service category deleted successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Delete service category failed', error, {
        requestId,
        categoryId: id,
        duration,
      });
      throw error;
    }
  });

  // Service form field operations
  getServiceFormFields = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { serviceId } = req.params;

    this.logger.info('Get service form fields request received', {
      requestId,
      serviceId,
      requestingUserId: req.userId,
    });

    try {
      const formFields = await this.serviceService.getServiceFormFields(serviceId);

      const duration = Date.now() - startTime;
      this.logger.info('Get service form fields completed successfully', {
        requestId,
        serviceId,
        count: formFields.length,
        duration,
      });

      res.success({ formFields });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get service form fields failed', error, {
        requestId,
        serviceId,
        duration,
      });
      throw error;
    }
  });

  getServiceFormFieldById = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { id } = req.params;

    this.logger.info('Get service form field by ID request received', {
      requestId,
      fieldId: id,
      requestingUserId: req.userId,
    });

    try {
      const formField = await this.serviceService.getServiceFormFieldById(id);

      const duration = Date.now() - startTime;
      this.logger.info('Get service form field by ID completed successfully', {
        requestId,
        fieldId: id,
        fieldName: formField.name,
        duration,
      });

      res.success({ formField });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get service form field by ID failed', error, {
        requestId,
        fieldId: id,
        duration,
      });
      throw error;
    }
  });

  createServiceFormField = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const fieldData: CreateServiceFormFieldRequest = req.body;

    this.logger.info('Create service form field request received', {
      requestId,
      serviceId: fieldData.serviceId,
      fieldName: fieldData.name,
      requestingUserId: req.userId,
    });

    try {
      const formField = await this.serviceService.createServiceFormField(fieldData);

      const duration = Date.now() - startTime;
      this.logger.info('Create service form field completed successfully', {
        requestId,
        fieldId: formField.id,
        fieldName: formField.name,
        duration,
      });

      res.created({ formField }, 'Service form field created successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Create service form field failed', error, {
        requestId,
        serviceId: fieldData.serviceId,
        duration,
      });
      throw error;
    }
  });

  updateServiceFormField = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { id } = req.params;
    const updateData: UpdateServiceFormFieldRequest = req.body;

    this.logger.info('Update service form field request received', {
      requestId,
      fieldId: id,
      updatedFields: Object.keys(updateData),
      requestingUserId: req.userId,
    });

    try {
      const formField = await this.serviceService.updateServiceFormField(id, updateData);

      const duration = Date.now() - startTime;
      this.logger.info('Update service form field completed successfully', {
        requestId,
        fieldId: id,
        fieldName: formField.name,
        duration,
      });

      res.success({ formField }, 'Service form field updated successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Update service form field failed', error, {
        requestId,
        fieldId: id,
        duration,
      });
      throw error;
    }
  });

  deleteServiceFormField = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { id } = req.params;

    this.logger.info('Delete service form field request received', {
      requestId,
      fieldId: id,
      requestingUserId: req.userId,
    });

    try {
      const deleted = await this.serviceService.deleteServiceFormField(id);

      const duration = Date.now() - startTime;
      this.logger.info('Delete service form field completed successfully', {
        requestId,
        fieldId: id,
        deleted,
        duration,
      });

      res.success({ deleted }, 'Service form field deleted successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Delete service form field failed', error, {
        requestId,
        fieldId: id,
        duration,
      });
      throw error;
    }
  });

  // Service form field option operations
  getServiceFormFieldOptions = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { fieldId } = req.params;

    this.logger.info('Get service form field options request received', {
      requestId,
      fieldId,
      requestingUserId: req.userId,
    });

    try {
      const options = await this.serviceService.getServiceFormFieldOptions(fieldId);

      const duration = Date.now() - startTime;
      this.logger.info('Get service form field options completed successfully', {
        requestId,
        fieldId,
        count: options.length,
        duration,
      });

      res.success({ options });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get service form field options failed', error, {
        requestId,
        fieldId,
        duration,
      });
      throw error;
    }
  });

  createServiceFormFieldOption = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const optionData: CreateServiceFormFieldOptionRequest = req.body;

    this.logger.info('Create service form field option request received', {
      requestId,
      fieldId: optionData.fieldId,
      optionValue: optionData.value,
      requestingUserId: req.userId,
    });

    try {
      const option = await this.serviceService.createServiceFormFieldOption(optionData);

      const duration = Date.now() - startTime;
      this.logger.info('Create service form field option completed successfully', {
        requestId,
        optionId: option.id,
        optionValue: option.value,
        duration,
      });

      res.created({ option }, 'Service form field option created successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Create service form field option failed', error, {
        requestId,
        fieldId: optionData.fieldId,
        duration,
      });
      throw error;
    }
  });

  updateServiceFormFieldOption = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { id } = req.params;
    const updateData: UpdateServiceFormFieldOptionRequest = req.body;

    this.logger.info('Update service form field option request received', {
      requestId,
      optionId: id,
      updatedFields: Object.keys(updateData),
      requestingUserId: req.userId,
    });

    try {
      const option = await this.serviceService.updateServiceFormFieldOption(id, updateData);

      const duration = Date.now() - startTime;
      this.logger.info('Update service form field option completed successfully', {
        requestId,
        optionId: id,
        optionValue: option.value,
        duration,
      });

      res.success({ option }, 'Service form field option updated successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Update service form field option failed', error, {
        requestId,
        optionId: id,
        duration,
      });
      throw error;
    }
  });

  deleteServiceFormFieldOption = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { id } = req.params;

    this.logger.info('Delete service form field option request received', {
      requestId,
      optionId: id,
      requestingUserId: req.userId,
    });

    try {
      const deleted = await this.serviceService.deleteServiceFormFieldOption(id);

      const duration = Date.now() - startTime;
      this.logger.info('Delete service form field option completed successfully', {
        requestId,
        optionId: id,
        deleted,
        duration,
      });

      res.success({ deleted }, 'Service form field option deleted successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Delete service form field option failed', error, {
        requestId,
        optionId: id,
        duration,
      });
      throw error;
    }
  });

  // Price calculation
  calculateServicePrice = asyncHandler(
    RequestLogger.withLogging(
      this.logger,
      'Calculate service price',
      async (req: AuthenticatedRequest, res: Response): Promise<void> => {
        const calculationRequest: PriceCalculationRequest = req.body;
        const priceCalculation = await this.serviceService.calculateServicePrice(calculationRequest);
        res.success({ priceCalculation });
      }
    )
  );

  // Utility operations
  serviceExists = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { id } = req.params;

    this.logger.info('Check service exists request received', {
      requestId,
      serviceId: id,
      requestingUserId: req.userId,
    });

    try {
      const exists = await this.serviceService.serviceExists(id);

      const duration = Date.now() - startTime;
      this.logger.info('Check service exists completed successfully', {
        requestId,
        serviceId: id,
        exists,
        duration,
      });

      res.success({ exists });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Check service exists failed', error, {
        requestId,
        serviceId: id,
        duration,
      });
      throw error;
    }
  });

  serviceCategoryExists = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { id } = req.params;

    this.logger.info('Check service category exists request received', {
      requestId,
      categoryId: id,
      requestingUserId: req.userId,
    });

    try {
      const exists = await this.serviceService.serviceCategoryExists(id);

      const duration = Date.now() - startTime;
      this.logger.info('Check service category exists completed successfully', {
        requestId,
        categoryId: id,
        exists,
        duration,
      });

      res.success({ exists });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Check service category exists failed', error, {
        requestId,
        categoryId: id,
        duration,
      });
      throw error;
    }
  });

  countServices = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Count services request received', {
      requestId,
      requestingUserId: req.userId,
    });

    try {
      const count = await this.serviceService.countServices();

      const duration = Date.now() - startTime;
      this.logger.info('Count services completed successfully', {
        requestId,
        count,
        duration,
      });

      res.success({ count });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Count services failed', error, {
        requestId,
        duration,
      });
      throw error;
    }
  });

  countServicesByCategory = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { categoryId } = req.params;

    this.logger.info('Count services by category request received', {
      requestId,
      categoryId,
      requestingUserId: req.userId,
    });

    try {
      const count = await this.serviceService.countServicesByCategory(categoryId);

      const duration = Date.now() - startTime;
      this.logger.info('Count services by category completed successfully', {
        requestId,
        categoryId,
        count,
        duration,
      });

      res.success({ count });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Count services by category failed', error, {
        requestId,
        categoryId,
        duration,
      });
      throw error;
    }
  });
}

// Factory function to create controller with dependencies
export const createServiceController = (serviceService: IServiceService): ServiceController => {
  return new ServiceController(serviceService);
};

export default ServiceController;
