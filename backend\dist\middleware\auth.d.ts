import { Response, NextFunction } from 'express';
import { UserRole } from '@prisma/client';
import { AuthenticatedRequest, JWTPayload } from '../types/auth';
export declare const generateAccessToken: (payload: JWTPayload, secret?: string, expiresIn?: string, issuer?: string, audience?: string) => string;
export declare const generateRefreshToken: (payload: JWTPayload, secret?: string, expiresIn?: string, issuer?: string, audience?: string) => string;
export declare const verifyAccessToken: (token: string) => JWTPayload;
export declare const verifyRefreshToken: (token: string) => JWTPayload;
export declare const authenticate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const optionalAuthenticate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const authorize: (...roles: UserRole[]) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const requireEmailVerification: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const requireOwnershipOrAdmin: (userIdParam?: string) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const authRateLimit: (maxAttempts?: number, windowMs?: number) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
declare const _default: {
    generateAccessToken: (payload: JWTPayload, secret?: string, expiresIn?: string, issuer?: string, audience?: string) => string;
    generateRefreshToken: (payload: JWTPayload, secret?: string, expiresIn?: string, issuer?: string, audience?: string) => string;
    verifyAccessToken: (token: string) => JWTPayload;
    verifyRefreshToken: (token: string) => JWTPayload;
    authenticate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    optionalAuthenticate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    authorize: (...roles: UserRole[]) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    requireEmailVerification: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    requireOwnershipOrAdmin: (userIdParam?: string) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    authRateLimit: (maxAttempts?: number, windowMs?: number) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
};
export default _default;
//# sourceMappingURL=auth.d.ts.map