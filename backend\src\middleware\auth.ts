import {Response, NextFunction} from 'express';
import jwt from 'jsonwebtoken';
import {UserRole} from '@prisma/client';
import {AuthenticatedRequest, AuthUser, JWTPayload} from '../types/auth';
import {AuthenticationError, AuthorizationError} from './errorHandler';
import {authLogger, securityLogger} from '../utils/logger';

// JWT secret and options - these will be overridden by configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m';
const JWT_REFRESH_SECRET =
	process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

// Generate access token with configuration
export const generateAccessToken = (
	payload: JWTPayload,
	secret: string = JWT_SECRET,
	expiresIn: string = JWT_EXPIRES_IN,
	issuer?: string,
	audience?: string
): string => {
	const jwtPayload = {
		userId: payload.userId,
		email: payload.email,
		role: payload.role,
	};

	const options: jwt.SignOptions = {
		expiresIn: expiresIn as any,
		...(issuer && {issuer}),
		...(audience && {audience}),
	};

	// @ts-ignore - JWT types issue with version compatibility
	return jwt.sign(jwtPayload, secret, options);
};

// Generate refresh token with configuration
export const generateRefreshToken = (
	payload: JWTPayload,
	secret: string = JWT_REFRESH_SECRET,
	expiresIn: string = JWT_REFRESH_EXPIRES_IN,
	issuer?: string,
	audience?: string
): string => {
	const jwtPayload = {
		userId: payload.userId,
		email: payload.email,
		role: payload.role,
	};

	const options: jwt.SignOptions = {
		expiresIn: expiresIn as any,
		...(issuer && {issuer}),
		...(audience && {audience}),
	};

	// @ts-ignore - JWT types issue with version compatibility
	return jwt.sign(jwtPayload, secret, options);
};

// Verify access token
export const verifyAccessToken = (token: string): JWTPayload => {
	try {
		return jwt.verify(token, JWT_SECRET as string) as JWTPayload;
	} catch (error) {
		if (error instanceof jwt.TokenExpiredError) {
			throw new AuthenticationError('Access token expired');
		} else if (error instanceof jwt.JsonWebTokenError) {
			throw new AuthenticationError('Invalid access token');
		}
		throw new AuthenticationError('Token verification failed');
	}
};

// Verify refresh token
export const verifyRefreshToken = (token: string): JWTPayload => {
	try {
		return jwt.verify(token, JWT_REFRESH_SECRET as string) as JWTPayload;
	} catch (error) {
		if (error instanceof jwt.TokenExpiredError) {
			throw new AuthenticationError('Refresh token expired');
		} else if (error instanceof jwt.JsonWebTokenError) {
			throw new AuthenticationError('Invalid refresh token');
		}
		throw new AuthenticationError('Refresh token verification failed');
	}
};

// Authentication middleware - simplified to use service layer externally
export const authenticate = async (
	req: AuthenticatedRequest,
	res: Response,
	next: NextFunction
): Promise<void> => {
	const startTime = Date.now();

	try {
		// Get token from Authorization header
		const authHeader = req.headers.authorization;
		if (!authHeader || !authHeader.startsWith('Bearer ')) {
			authLogger.warn('Authentication failed: No Bearer token provided', {
				ip: req.ip || req.connection?.remoteAddress,
				userAgent: req.headers['user-agent'],
				path: req.path,
			});
			throw new AuthenticationError('Access token is required');
		}

		const token = authHeader.substring(7); // Remove 'Bearer ' prefix

		// Verify token
		const payload = verifyAccessToken(token);

		// Note: User lookup will be handled by the service layer
		// For now, we'll store the token payload and let the controller handle user validation
		req.userId = payload.userId;

		const duration = Date.now() - startTime;
		authLogger.info('Authentication successful', {
			userId: payload.userId,
			email: payload.email,
			role: payload.role,
			ip: req.ip || req.connection?.remoteAddress,
			userAgent: req.headers['user-agent'],
			path: req.path,
			duration,
		});

		// The actual user object will be populated by the service layer when needed
		// This maintains separation of concerns
		next();
	} catch (error) {
		const duration = Date.now() - startTime;

		if (error instanceof jwt.TokenExpiredError) {
			authLogger.warn('Authentication failed: Token expired', {
				ip: req.ip || req.connection?.remoteAddress,
				userAgent: req.headers['user-agent'],
				path: req.path,
				duration,
			});
		} else if (error instanceof jwt.JsonWebTokenError) {
			authLogger.warn('Authentication failed: Invalid token', {
				ip: req.ip || req.connection?.remoteAddress,
				userAgent: req.headers['user-agent'],
				path: req.path,
				duration,
			});
		} else {
			authLogger.error('Authentication failed: Unexpected error', error, {
				ip: req.ip || req.connection?.remoteAddress,
				userAgent: req.headers['user-agent'],
				path: req.path,
				duration,
			});
		}

		next(error);
	}
};

// Optional authentication middleware (doesn't fail if no token)
export const optionalAuthenticate = async (
	req: AuthenticatedRequest,
	res: Response,
	next: NextFunction
): Promise<void> => {
	const startTime = Date.now();

	try {
		const authHeader = req.headers.authorization;
		if (!authHeader || !authHeader.startsWith('Bearer ')) {
			authLogger.debug('Optional authentication: No token provided', {
				ip: req.ip || req.connection?.remoteAddress,
				userAgent: req.headers['user-agent'],
				path: req.path,
			});
			return next();
		}

		const token = authHeader.substring(7);
		const payload = verifyAccessToken(token);

		// Store the token payload for optional use by controllers
		req.userId = payload.userId;

		const duration = Date.now() - startTime;
		authLogger.debug('Optional authentication successful', {
			userId: payload.userId,
			email: payload.email,
			role: payload.role,
			ip: req.ip || req.connection?.remoteAddress,
			userAgent: req.headers['user-agent'],
			path: req.path,
			duration,
		});

		next();
	} catch (error) {
		const duration = Date.now() - startTime;

		// Don't fail on optional authentication, just log it
		authLogger.debug(
			'Optional authentication failed, continuing without auth',
			{
				error: error instanceof Error ? error.message : 'Unknown error',
				ip: req.ip || req.connection?.remoteAddress,
				userAgent: req.headers['user-agent'],
				path: req.path,
				duration,
			}
		);

		next();
	}
};

// Role-based authorization middleware
export const authorize = (...roles: UserRole[]) => {
	return (
		req: AuthenticatedRequest,
		res: Response,
		next: NextFunction
	): void => {
		const startTime = Date.now();

		try {
			if (!req.user) {
				authLogger.warn('Authorization failed: No user found', {
					ip: req.ip || req.connection?.remoteAddress,
					userAgent: req.headers['user-agent'],
					path: req.path,
					requiredRoles: roles,
				});
				throw new AuthenticationError('Authentication required');
			}

			if (!roles.includes(req.user.role)) {
				const duration = Date.now() - startTime;
				authLogger.warn('Authorization failed: Insufficient permissions', {
					userId: req.user.id,
					userRole: req.user.role,
					requiredRoles: roles,
					ip: req.ip || req.connection?.remoteAddress,
					userAgent: req.headers['user-agent'],
					path: req.path,
					duration,
				});
				throw new AuthorizationError('Insufficient permissions');
			}

			const duration = Date.now() - startTime;
			authLogger.debug('Authorization successful', {
				userId: req.user.id,
				userRole: req.user.role,
				requiredRoles: roles,
				ip: req.ip || req.connection?.remoteAddress,
				userAgent: req.headers['user-agent'],
				path: req.path,
				duration,
			});

			next();
		} catch (error) {
			const duration = Date.now() - startTime;
			authLogger.error('Authorization error', error, {
				ip: req.ip || req.connection?.remoteAddress,
				userAgent: req.headers['user-agent'],
				path: req.path,
				duration,
			});
			throw error;
		}
	};
};

// Email verification required middleware
export const requireEmailVerification = (
	req: AuthenticatedRequest,
	res: Response,
	next: NextFunction
): void => {
	const startTime = Date.now();

	try {
		if (!req.user) {
			authLogger.warn('Email verification check failed: No user found', {
				ip: req.ip || req.connection?.remoteAddress,
				userAgent: req.headers['user-agent'],
				path: req.path,
			});
			throw new AuthenticationError('Authentication required');
		}

		if (!req.user.isVerified) {
			const duration = Date.now() - startTime;
			authLogger.warn('Email verification required', {
				userId: req.user.id,
				email: req.user.email,
				ip: req.ip || req.connection?.remoteAddress,
				userAgent: req.headers['user-agent'],
				path: req.path,
				duration,
			});
			throw new AuthorizationError('Email verification required');
		}

		const duration = Date.now() - startTime;
		authLogger.debug('Email verification check passed', {
			userId: req.user.id,
			email: req.user.email,
			ip: req.ip || req.connection?.remoteAddress,
			userAgent: req.headers['user-agent'],
			path: req.path,
			duration,
		});

		next();
	} catch (error) {
		const duration = Date.now() - startTime;
		authLogger.error('Email verification check error', error, {
			ip: req.ip || req.connection?.remoteAddress,
			userAgent: req.headers['user-agent'],
			path: req.path,
			duration,
		});
		throw error;
	}
};

// Account owner or admin middleware
export const requireOwnershipOrAdmin = (userIdParam: string = 'userId') => {
	return (
		req: AuthenticatedRequest,
		res: Response,
		next: NextFunction
	): void => {
		const startTime = Date.now();

		try {
			if (!req.user) {
				authLogger.warn('Ownership check failed: No user found', {
					ip: req.ip || req.connection?.remoteAddress,
					userAgent: req.headers['user-agent'],
					path: req.path,
					userIdParam,
				});
				throw new AuthenticationError('Authentication required');
			}

			const targetUserId = req.params[userIdParam] || req.body[userIdParam];

			// Allow if user is admin or super admin
			if (req.user.role === UserRole.ADMIN) {
				const duration = Date.now() - startTime;
				authLogger.debug('Ownership check passed: Admin access', {
					adminUserId: req.user.id,
					targetUserId,
					ip: req.ip || req.connection?.remoteAddress,
					userAgent: req.headers['user-agent'],
					path: req.path,
					duration,
				});
				return next();
			}

			// Allow if user is accessing their own resource
			if (req.user.id === targetUserId) {
				const duration = Date.now() - startTime;
				authLogger.debug('Ownership check passed: Owner access', {
					userId: req.user.id,
					targetUserId,
					ip: req.ip || req.connection?.remoteAddress,
					userAgent: req.headers['user-agent'],
					path: req.path,
					duration,
				});
				return next();
			}

			const duration = Date.now() - startTime;
			authLogger.warn('Ownership check failed: Access denied', {
				userId: req.user.id,
				userRole: req.user.role,
				targetUserId,
				ip: req.ip || req.connection?.remoteAddress,
				userAgent: req.headers['user-agent'],
				path: req.path,
				duration,
			});
			throw new AuthorizationError('Access denied to this resource');
		} catch (error) {
			const duration = Date.now() - startTime;
			authLogger.error('Ownership check error', error, {
				ip: req.ip || req.connection?.remoteAddress,
				userAgent: req.headers['user-agent'],
				path: req.path,
				duration,
			});
			throw error;
		}
	};
};

// Rate limiting for authentication endpoints
export const authRateLimit = (
	maxAttempts: number = 5,
	windowMs: number = 15 * 60 * 1000
) => {
	const attempts = new Map<string, {count: number; resetTime: number}>();

	return (
		req: AuthenticatedRequest,
		res: Response,
		next: NextFunction
	): void => {
		const startTime = Date.now();
		const key = `${req.ip}:${req.path}`;
		const now = Date.now();
		const current = attempts.get(key);

		// Clean up expired entries
		if (current && now > current.resetTime) {
			attempts.delete(key);
		}

		const entry = attempts.get(key) || {count: 0, resetTime: now + windowMs};

		if (entry.count >= maxAttempts) {
			const remainingTime = Math.ceil((entry.resetTime - now) / 1000 / 60);
			const duration = Date.now() - startTime;

			securityLogger.warn('Rate limit exceeded', {
				ip: req.ip,
				path: req.path,
				attempts: entry.count,
				maxAttempts,
				remainingTime,
				userAgent: req.headers['user-agent'],
				duration,
			});

			throw new AuthenticationError(
				`Too many attempts. Try again in ${remainingTime} minutes.`
			);
		}

		entry.count++;
		attempts.set(key, entry);

		// Log rate limit attempts
		if (entry.count > 1) {
			const duration = Date.now() - startTime;
			securityLogger.info('Rate limit attempt', {
				ip: req.ip,
				path: req.path,
				attempts: entry.count,
				maxAttempts,
				userAgent: req.headers['user-agent'],
				duration,
			});
		}

		// Clean up old entries periodically
		if (Math.random() < 0.01) {
			// 1% chance
			for (const [k, v] of attempts.entries()) {
				if (now > v.resetTime) {
					attempts.delete(k);
				}
			}
		}

		next();
	};
};

export default {
	generateAccessToken,
	generateRefreshToken,
	verifyAccessToken,
	verifyRefreshToken,
	authenticate,
	optionalAuthenticate,
	authorize,
	requireEmailVerification,
	requireOwnershipOrAdmin,
	authRateLimit,
};
