import { Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { UserRole } from '@prisma/client';
import { AuthenticatedRequest, AuthUser, JWTPayload } from '../types/auth';
import { AuthenticationError, AuthorizationError } from './errorHandler';

// JWT secret and options
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

// Generate access token
export const generateAccessToken = (payload: JWTPayload): string => {
  const jwtPayload = {
    userId: payload.userId,
    email: payload.email,
    role: payload.role
  };
  // @ts-ignore - JWT types issue with version compatibility
  return jwt.sign(jwtPayload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
};

// Generate refresh token
export const generateRefreshToken = (payload: JWTPayload): string => {
  const jwtPayload = {
    userId: payload.userId,
    email: payload.email,
    role: payload.role
  };
  // @ts-ignore - JWT types issue with version compatibility
  return jwt.sign(jwtPayload, JWT_REFRESH_SECRET, { expiresIn: JWT_REFRESH_EXPIRES_IN });
};

// Verify access token
export const verifyAccessToken = (token: string): JWTPayload => {
  try {
    return jwt.verify(token, JWT_SECRET as string) as JWTPayload;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new AuthenticationError('Access token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new AuthenticationError('Invalid access token');
    }
    throw new AuthenticationError('Token verification failed');
  }
};

// Verify refresh token
export const verifyRefreshToken = (token: string): JWTPayload => {
  try {
    return jwt.verify(token, JWT_REFRESH_SECRET as string) as JWTPayload;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new AuthenticationError('Refresh token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new AuthenticationError('Invalid refresh token');
    }
    throw new AuthenticationError('Refresh token verification failed');
  }
};

// Authentication middleware - simplified to use service layer externally
export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('Access token is required');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Verify token
    const payload = verifyAccessToken(token);
    
    // Note: User lookup will be handled by the service layer
    // For now, we'll store the token payload and let the controller handle user validation
    req.userId = payload.userId;
    
    // The actual user object will be populated by the service layer when needed
    // This maintains separation of concerns
    next();
  } catch (error) {
    next(error);
  }
};

// Optional authentication middleware (doesn't fail if no token)
export const optionalAuthenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const token = authHeader.substring(7);
    const payload = verifyAccessToken(token);
    
    // Store the token payload for optional use by controllers
    req.userId = payload.userId;

    next();
  } catch (error) {
    // Don't fail on optional authentication
    next();
  }
};

// Role-based authorization middleware
export const authorize = (...roles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new AuthenticationError('Authentication required');
    }

    if (!roles.includes(req.user.role)) {
      throw new AuthorizationError('Insufficient permissions');
    }

    next();
  };
};

// Email verification required middleware
export const requireEmailVerification = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    throw new AuthenticationError('Authentication required');
  }

  if (!req.user.isVerified) {
    throw new AuthorizationError('Email verification required');
  }

  next();
};

// Account owner or admin middleware
export const requireOwnershipOrAdmin = (userIdParam: string = 'userId') => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new AuthenticationError('Authentication required');
    }

    const targetUserId = req.params[userIdParam] || req.body[userIdParam];
    
    // Allow if user is admin or super admin
    if (req.user.role === UserRole.ADMIN) {
      return next();
    }

    // Allow if user is accessing their own resource
    if (req.user.id === targetUserId) {
      return next();
    }

    throw new AuthorizationError('Access denied to this resource');
  };
};

// Rate limiting for authentication endpoints
export const authRateLimit = (maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000) => {
  const attempts = new Map<string, { count: number; resetTime: number }>();

  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    const key = `${req.ip}:${req.path}`;
    const now = Date.now();
    const current = attempts.get(key);

    // Clean up expired entries
    if (current && now > current.resetTime) {
      attempts.delete(key);
    }

    const entry = attempts.get(key) || { count: 0, resetTime: now + windowMs };

    if (entry.count >= maxAttempts) {
      const remainingTime = Math.ceil((entry.resetTime - now) / 1000 / 60);
      throw new AuthenticationError(`Too many attempts. Try again in ${remainingTime} minutes.`);
    }

    entry.count++;
    attempts.set(key, entry);

    // Clean up old entries periodically
    if (Math.random() < 0.01) { // 1% chance
      for (const [k, v] of attempts.entries()) {
        if (now > v.resetTime) {
          attempts.delete(k);
        }
      }
    }

    next();
  };
};

export default {
  generateAccessToken,
  generateRefreshToken,
  verifyAccessToken,
  verifyRefreshToken,
  authenticate,
  optionalAuthenticate,
  authorize,
  requireEmailVerification,
  requireOwnershipOrAdmin,
  authRateLimit
};