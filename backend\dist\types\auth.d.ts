import { Request } from 'express';
import { User, UserRole } from '@prisma/client';
export interface JWTPayload {
    userId: string;
    email: string;
    role: UserRole;
    iat?: number;
    exp?: number;
}
export interface TokenPair {
    accessToken: string;
    refreshToken: string;
}
export interface RegisterRequest {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
}
export interface LoginRequest {
    email: string;
    password: string;
}
export interface RefreshTokenRequest {
    refreshToken: string;
}
export interface PasswordResetRequest {
    email: string;
}
export interface PasswordResetConfirmRequest {
    token: string;
    newPassword: string;
}
export interface EmailVerificationRequest {
    token: string;
}
export interface ChangePasswordRequest {
    currentPassword: string;
    newPassword: string;
}
export interface UpdateProfileRequest {
    firstName?: string;
    lastName?: string;
    phone?: string;
    avatar?: string;
}
export interface AuthUser {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    isActive: boolean;
    isVerified: boolean;
    avatar: string | null;
    phone: string | null;
    createdAt: Date;
    updatedAt: Date;
    lastLoginAt: Date | null;
}
export interface AuthenticatedRequest extends Request {
    user?: AuthUser;
    userId?: string;
}
export interface AuthResponse {
    user: Omit<User, 'password'>;
    tokens: TokenPair;
}
export interface SessionInfo {
    id: string;
    ipAddress: string;
    userAgent: string;
    createdAt: Date;
    expiresAt: Date;
    isActive: boolean;
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface ErrorResponse {
    error: string;
    message: string;
    statusCode: number;
    timestamp: string;
    path: string;
}
export interface ValidationError {
    field: string;
    message: string;
    value?: any;
}
export interface LoginAttempt {
    email: string;
    ipAddress: string;
    userAgent: string;
    success: boolean;
    timestamp: Date;
    failureReason?: string;
}
export interface AccountLockStatus {
    isLocked: boolean;
    lockExpires?: Date;
    attempts: number;
    maxAttempts: number;
}
export declare enum SecurityEventType {
    LOGIN_SUCCESS = "LOGIN_SUCCESS",
    LOGIN_FAILURE = "LOGIN_FAILURE",
    PASSWORD_CHANGE = "PASSWORD_CHANGE",
    PASSWORD_RESET_REQUEST = "PASSWORD_RESET_REQUEST",
    PASSWORD_RESET_COMPLETE = "PASSWORD_RESET_COMPLETE",
    EMAIL_VERIFICATION_SENT = "EMAIL_VERIFICATION_SENT",
    EMAIL_VERIFIED = "EMAIL_VERIFIED",
    ACCOUNT_LOCKED = "ACCOUNT_LOCKED",
    ACCOUNT_UNLOCKED = "ACCOUNT_UNLOCKED",
    TOKEN_REFRESH = "TOKEN_REFRESH",
    LOGOUT = "LOGOUT"
}
export interface SecurityEvent {
    type: SecurityEventType;
    userId?: string;
    email?: string;
    ipAddress: string;
    userAgent: string;
    metadata?: Record<string, any>;
    timestamp: Date;
}
//# sourceMappingURL=auth.d.ts.map