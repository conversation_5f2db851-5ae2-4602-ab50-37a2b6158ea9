export { Configuration<PERSON>anager, AppConfig, DatabaseConfig, JWTConfig, ServerConfig, RedisConfig, AWSConfig, EmailConfig, LoggingConfig, SecurityConfig, GoogleOAuthConfig, } from './config';
import { ConfigurationManager } from './config';
export declare const config: ConfigurationManager;
export declare const getConfig: () => {
    database: {
        url: string;
        maxConnections: number;
        idleTimeout: number;
        connectionTimeout: number;
    };
    jwt: {
        secret: string;
        refreshSecret: string;
        accessExpiresIn: string;
        refreshExpiresIn: string;
        issuer: string;
        audience: string;
    };
    server: {
        port: number;
        host: string;
        nodeEnv: "development" | "production" | "test";
        apiBaseUrl: string;
        frontendUrl: string;
        corsOrigin: string;
    };
    redis: {
        url: string;
        db: number;
        keyPrefix: string;
        retryDelayOnFailover: number;
        maxRetriesPerRequest: number;
        password?: string | undefined;
    };
    aws: {
        accessKeyId: string;
        secretAccessKey: string;
        region: string;
        s3Bucket: string;
        cloudfrontUrl?: string | undefined;
    };
    email: {
        service: string;
        host: string;
        port: number;
        secure: boolean;
        user: string;
        password: string;
        from: string;
    };
    logging: {
        level: "error" | "warn" | "info" | "debug";
        enableConsole: boolean;
        enableFile: boolean;
        maxFileSize: number;
        maxFiles: number;
        format: "json" | "simple";
        file?: string | undefined;
    };
    security: {
        bcryptRounds: number;
        cookieSecret: string;
        rateLimitWindowMs: number;
        rateLimitMaxRequests: number;
        rateLimitAuthMax: number;
        maxFileSize: number;
        allowedFileTypes: string[];
    };
    googleOAuth: {
        clientId: string;
        clientSecret: string;
        callbackUrl: string;
    };
    enableSwagger: boolean;
    enableLogging: boolean;
    enableCors: boolean;
};
export declare const getDatabaseConfig: () => {
    url: string;
    maxConnections: number;
    idleTimeout: number;
    connectionTimeout: number;
};
export declare const getJWTConfig: () => {
    secret: string;
    refreshSecret: string;
    accessExpiresIn: string;
    refreshExpiresIn: string;
    issuer: string;
    audience: string;
};
export declare const getServerConfig: () => {
    port: number;
    host: string;
    nodeEnv: "development" | "production" | "test";
    apiBaseUrl: string;
    frontendUrl: string;
    corsOrigin: string;
};
export declare const getRedisConfig: () => {
    url: string;
    db: number;
    keyPrefix: string;
    retryDelayOnFailover: number;
    maxRetriesPerRequest: number;
    password?: string | undefined;
};
export declare const getAWSConfig: () => {
    accessKeyId: string;
    secretAccessKey: string;
    region: string;
    s3Bucket: string;
    cloudfrontUrl?: string | undefined;
};
export declare const getEmailConfig: () => {
    service: string;
    host: string;
    port: number;
    secure: boolean;
    user: string;
    password: string;
    from: string;
};
export declare const getLoggingConfig: () => {
    level: "error" | "warn" | "info" | "debug";
    enableConsole: boolean;
    enableFile: boolean;
    maxFileSize: number;
    maxFiles: number;
    format: "json" | "simple";
    file?: string | undefined;
};
export declare const getSecurityConfig: () => {
    bcryptRounds: number;
    cookieSecret: string;
    rateLimitWindowMs: number;
    rateLimitMaxRequests: number;
    rateLimitAuthMax: number;
    maxFileSize: number;
    allowedFileTypes: string[];
};
export declare const getGoogleOAuthConfig: () => {
    clientId: string;
    clientSecret: string;
    callbackUrl: string;
};
//# sourceMappingURL=index.d.ts.map