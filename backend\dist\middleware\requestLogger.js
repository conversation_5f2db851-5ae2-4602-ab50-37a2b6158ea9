"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.performanceLogger = exports.logError = exports.logAuthEvent = exports.logEvent = exports.requestLogger = void 0;
const logger_1 = require("../utils/logger");
function generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}
function getClientIP(req) {
    return (req.headers['x-forwarded-for'] ||
        req.headers['x-real-ip'] ||
        req.connection?.remoteAddress ||
        req.socket?.remoteAddress ||
        req.ip ||
        'unknown');
}
const requestLogger = (req, res, next) => {
    const startTime = Date.now();
    req.startTime = startTime;
    const requestId = generateRequestId();
    const context = {
        requestId,
        method: req.method,
        url: req.originalUrl || req.url,
        userAgent: req.headers['user-agent'],
        ip: getClientIP(req),
        userId: req.userId,
        userEmail: req.user?.email,
        startTime
    };
    res.setHeader('X-Request-ID', requestId);
    logger_1.httpLogger.info('Incoming request', {
        requestId: context.requestId,
        method: context.method,
        url: context.url,
        userAgent: context.userAgent,
        ip: context.ip,
        userId: context.userId,
        userEmail: context.userEmail,
        headers: req.headers,
        query: req.query,
        body: sanitizeRequestBody(req.body)
    });
    const originalEnd = res.end;
    res.end = function (chunk, encoding) {
        const duration = Date.now() - startTime;
        const responseSize = res.get('content-length') || (chunk ? Buffer.byteLength(chunk) : 0);
        const logLevel = res.statusCode >= 400 ? 'warn' : 'info';
        logger_1.httpLogger[logLevel]('Request completed', {
            requestId: context.requestId,
            method: context.method,
            url: context.url,
            statusCode: res.statusCode,
            duration,
            responseSize,
            userId: context.userId,
            userEmail: context.userEmail,
            ip: context.ip,
            userAgent: context.userAgent
        });
        if (duration > 1000) {
            logger_1.httpLogger.warn('Slow request detected', {
                requestId: context.requestId,
                method: context.method,
                url: context.url,
                duration,
                statusCode: res.statusCode,
                userId: context.userId
            });
        }
        return originalEnd.call(this, chunk, encoding);
    };
    next();
};
exports.requestLogger = requestLogger;
function sanitizeRequestBody(body) {
    if (!body || typeof body !== 'object') {
        return body;
    }
    const sensitiveFields = [
        'password',
        'currentPassword',
        'newPassword',
        'confirmPassword',
        'token',
        'refreshToken',
        'accessToken',
        'apiKey',
        'secret',
        'creditCard',
        'ssn',
        'socialSecurityNumber'
    ];
    const sanitized = { ...body };
    sensitiveFields.forEach(field => {
        if (field in sanitized) {
            sanitized[field] = '[REDACTED]';
        }
    });
    return sanitized;
}
const logEvent = (eventName, getData) => {
    return (req, res, next) => {
        const logger = (0, logger_1.createLogger)('Event');
        const eventData = getData ? getData(req) : {};
        logger.info(`Event: ${eventName}`, {
            event: eventName,
            requestId: res.getHeader('X-Request-ID'),
            userId: req.userId,
            ip: getClientIP(req),
            userAgent: req.headers['user-agent'],
            ...eventData
        });
        next();
    };
};
exports.logEvent = logEvent;
const logAuthEvent = (eventType) => {
    return (req, res, next) => {
        const logger = (0, logger_1.createLogger)('AuthEvent');
        const originalEnd = res.end;
        res.end = function (chunk, encoding) {
            const isSuccess = res.statusCode >= 200 && res.statusCode < 300;
            logger.auth(`${eventType}_${isSuccess ? 'SUCCESS' : 'FAILURE'}`, req.userId, {
                eventType,
                success: isSuccess,
                statusCode: res.statusCode,
                requestId: res.getHeader('X-Request-ID'),
                ip: getClientIP(req),
                userAgent: req.headers['user-agent'],
                email: req.body?.email || req.user?.email
            });
            return originalEnd.call(this, chunk, encoding);
        };
        next();
    };
};
exports.logAuthEvent = logAuthEvent;
const logError = (err, req, res, next) => {
    const logger = (0, logger_1.createLogger)('Error');
    logger.error('Request error', err, {
        requestId: res.getHeader('X-Request-ID'),
        method: req.method,
        url: req.originalUrl || req.url,
        statusCode: res.statusCode,
        userId: req.userId,
        ip: getClientIP(req),
        userAgent: req.headers['user-agent'],
        body: sanitizeRequestBody(req.body),
        query: req.query,
        params: req.params
    });
    next(err);
};
exports.logError = logError;
const performanceLogger = (req, res, next) => {
    const startTime = Date.now();
    req.startTime = startTime;
    const originalEnd = res.end;
    res.end = function (chunk, encoding) {
        const duration = Date.now() - startTime;
        const performanceLogger = (0, logger_1.createLogger)('Performance');
        performanceLogger.performance('Request performance', duration, {
            requestId: res.getHeader('X-Request-ID'),
            method: req.method,
            url: req.originalUrl || req.url,
            statusCode: res.statusCode,
            responseSize: res.get('content-length') || (chunk ? Buffer.byteLength(chunk) : 0)
        });
        return originalEnd.call(this, chunk, encoding);
    };
    next();
};
exports.performanceLogger = performanceLogger;
exports.default = {
    requestLogger: exports.requestLogger,
    logEvent: exports.logEvent,
    logAuthEvent: exports.logAuthEvent,
    logError: exports.logError,
    performanceLogger: exports.performanceLogger
};
//# sourceMappingURL=requestLogger.js.map