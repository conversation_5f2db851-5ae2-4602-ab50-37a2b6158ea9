import { ServiceFormFieldRepository } from '@/repositories/ServiceFormFieldRepository';

const mockPrisma = {
  serviceFormField: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  service_field_options: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
} as any;

describe('ServiceFormFieldRepository', () => {
  let repo: ServiceFormFieldRepository;

  beforeEach(() => {
    repo = new ServiceFormFieldRepository(mockPrisma);
    jest.clearAllMocks();
  });

  it('getServiceFormFields - queries active fields with options', async () => {
    mockPrisma.serviceFormField.findMany.mockResolvedValue([
      { id: 'fld-1', serviceId: 'svc-1', service_field_options: [] },
    ]);
    await repo.getServiceFormFields('svc-1');
    expect(mockPrisma.serviceFormField.findMany).toHaveBeenCalledWith({
      where: { serviceId: 'svc-1', isActive: true },
      include: {
        service_field_options: { where: { isActive: true }, orderBy: { sortOrder: 'asc' } },
      },
      orderBy: { sortOrder: 'asc' },
    });
  });

  it('findServiceFormFieldById - returns field with options', async () => {
    mockPrisma.serviceFormField.findUnique.mockResolvedValue({ id: 'fld-1', service_field_options: [] });
    const result = await repo.findServiceFormFieldById('fld-1');
    expect(mockPrisma.serviceFormField.findUnique).toHaveBeenCalledWith({
      where: { id: 'fld-1' },
      include: {
        service_field_options: { where: { isActive: true }, orderBy: { sortOrder: 'asc' } },
      },
    });
    expect(result?.id).toBe('fld-1');
  });

  it('create/update/delete field - delegates to prisma', async () => {
    mockPrisma.serviceFormField.create.mockResolvedValue({ id: 'fld-1' });
    await repo.createServiceFormField({ id: 'fld-1' } as any);
    expect(mockPrisma.serviceFormField.create).toHaveBeenCalledWith({ data: { id: 'fld-1' } });

    mockPrisma.serviceFormField.update.mockResolvedValue({});
    await repo.updateServiceFormField('fld-1', { name: 'New' } as any);
    expect(mockPrisma.serviceFormField.update).toHaveBeenCalledWith({ where: { id: 'fld-1' }, data: { name: 'New' } });

    mockPrisma.serviceFormField.delete.mockResolvedValue({});
    await expect(repo.deleteServiceFormField('fld-1')).resolves.toBe(true);
    mockPrisma.serviceFormField.delete.mockRejectedValue(new Error('fk'));
    await expect(repo.deleteServiceFormField('fld-1')).resolves.toBe(false);
  });

  it('options: find/create/update/delete', async () => {
    mockPrisma.service_field_options.findUnique.mockResolvedValue({ id: 'opt-1' });
    await repo.findServiceFormFieldOptionById('opt-1');
    expect(mockPrisma.service_field_options.findUnique).toHaveBeenCalledWith({ where: { id: 'opt-1' } });

    mockPrisma.service_field_options.findMany.mockResolvedValue([{ id: 'opt-1' }]);
    await repo.getServiceFormFieldOptions('fld-1');
    expect(mockPrisma.service_field_options.findMany).toHaveBeenCalledWith({
      where: { fieldId: 'fld-1', isActive: true },
      orderBy: { sortOrder: 'asc' },
    });

    mockPrisma.service_field_options.create.mockResolvedValue({ id: 'opt-1' });
    await repo.createServiceFormFieldOption({ id: 'opt-1' } as any);
    expect(mockPrisma.service_field_options.create).toHaveBeenCalledWith({ data: { id: 'opt-1' } });

    mockPrisma.service_field_options.update.mockResolvedValue({});
    await repo.updateServiceFormFieldOption('opt-1', { label: 'L' } as any);
    expect(mockPrisma.service_field_options.update).toHaveBeenCalledWith({
      where: { id: 'opt-1' },
      data: { label: 'L' },
    });

    mockPrisma.service_field_options.delete.mockResolvedValue({});
    await expect(repo.deleteServiceFormFieldOption('opt-1')).resolves.toBe(true);
    mockPrisma.service_field_options.delete.mockRejectedValue(new Error('fk'));
    await expect(repo.deleteServiceFormFieldOption('opt-1')).resolves.toBe(false);
  });
});
