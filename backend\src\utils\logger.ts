import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';
import fs from 'fs';
import {config, LoggingConfig} from '../config';

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
	fs.mkdirSync(logsDir, {recursive: true});
}

// Define log levels with enhanced colors
const logLevels = {
	error: 0,
	warn: 1,
	info: 2,
	http: 3,
	debug: 4,
	verbose: 5,
};

// Enhanced color scheme with better contrast and visual hierarchy
// Using ANSI color codes for better terminal compatibility
const logColors = {
	error: 'bold red', // Bright red for critical errors
	warn: 'bold yellow', // Bright yellow for warnings
	info: 'bold blue', // Blue for general info
	http: 'bold magenta', // Magenta for HTTP requests
	debug: 'bold cyan', // Cyan for debug info
	verbose: 'gray', // Gray for verbose details
};

// Service-specific colors for better visual separation
// Using background colors with contrasting text
const serviceColors: Record<string, string> = {
	Authentication: 'bgGreen bold white',
	Database: 'bgBlue bold white',
	API: 'bgMagenta bold white',
	Security: 'bgRed bold white',
	HTTP: 'bgCyan bold black',
	Performance: 'bgYellow bold black',
	Email: 'bgGreen bold black',
	FileUpload: 'bgBlue bold black',
	App: 'bgGray bold white',
	Server: 'bgBlack bold white',
	Routes: 'bgMagenta bold white',
	Redis: 'bgRed bold white',
	Event: 'bgBlue bold white',
	AuthEvent: 'bgGreen bold white',
	Error: 'bgRed bold white',
	ErrorHandler: 'bgRed bold white',
};

// Event type colors for specialized logging
const eventTypeColors: Record<string, string> = {
	AUTH_EVENT: 'bold green',
	SECURITY_EVENT: 'bold red',
	PERFORMANCE: 'bold yellow',
	DATABASE: 'bold blue',
	ERROR: 'bold red',
};

winston.addColors({...logColors, ...serviceColors, ...eventTypeColors});

// Enhanced terminal capability detection for better color support
const supportsColor = () => {
	// Force color if environment variable is set
	if (process.env.FORCE_COLOR) return true;

	// Disable color if explicitly set
	if (process.env.NO_COLOR || process.env.NODE_DISABLE_COLORS) return false;

	// Check if stdout is a TTY
	if (process.stdout && !process.stdout.isTTY) return false;

	// Check CI environments
	const {TERM, CI} = process.env;
	if (CI) {
		const ciEnvs = [
			'TRAVIS',
			'CIRCLECI',
			'APPVEYOR',
			'GITLAB_CI',
			'GITHUB_ACTIONS',
			'BUILDKITE',
		];
		return ciEnvs.some((ci) => ci in process.env);
	}

	// Check terminal type
	if (TERM === 'dumb') return false;

	// Check for common terminal types that support color
	const colorTerms = [
		'xterm',
		'xterm-256color',
		'screen',
		'screen-256color',
		'linux',
		'cygwin',
	];
	if (TERM && colorTerms.some((term) => TERM.includes(term))) return true;

	// Windows PowerShell and CMD support colors
	if (process.platform === 'win32') {
		return true;
	}

	return true;
};

// Box drawing characters for better visual separation
const BOX_CHARS = {
	horizontal: '─',
	vertical: '│',
	corner: '┌',
	junction: '├',
	end: '└',
};

// Enhanced development format with improved visual hierarchy
const developmentFormat = winston.format.combine(
	winston.format.timestamp({format: 'HH:mm:ss.SSS'}),
	winston.format.printf((info) => {
		const {timestamp, level, message, service, type, stack, ...meta} =
			info as any;

		// Check if colors are supported
		const useColors = supportsColor();

		// Format timestamp with subtle gray color
		const formattedTime = useColors ? `\x1b[90m${timestamp}\x1b[0m` : timestamp;

		// Format log level with appropriate color and consistent width
		const levelText = level.toUpperCase().padEnd(7);
		const formattedLevel = useColors
			? winston.format.colorize().colorize(level, levelText)
			: levelText;

		// Format service name with background color if available
		const serviceKey = service as string;
		let formattedService = '';
		if (useColors && serviceColors[serviceKey]) {
			formattedService = winston.format
				.colorize()
				.colorize(serviceKey, `[${serviceKey}]`);
		} else {
			formattedService = useColors
				? `\x1b[37m[${serviceKey}]\x1b[0m`
				: `[${serviceKey}]`;
		}

		// Format message with event type coloring if present
		let formattedMessage = message;
		if (useColors && type && eventTypeColors[type as string]) {
			formattedMessage = winston.format
				.colorize()
				.colorize(type as string, message);
		}

		// Format metadata in a more readable way
		let metaStr = '';
		if (Object.keys(meta).length > 0) {
			const cleanMeta = {...meta};
			delete cleanMeta.service;
			delete cleanMeta.timestamp;

			if (Object.keys(cleanMeta).length > 0) {
				// Format key-value pairs more readably
				const metaPairs = Object.entries(cleanMeta)
					.map(([key, value]) => {
						if (useColors) {
							if (typeof value === 'object') {
								return `\x1b[36m${key}\x1b[0m=${JSON.stringify(value)}`;
							}
							return `\x1b[36m${key}\x1b[0m=\x1b[33m${value}\x1b[0m`;
						} else {
							return `${key}=${
								typeof value === 'object' ? JSON.stringify(value) : value
							}`;
						}
					})
					.join(' ');
				metaStr = useColors
					? ` \x1b[90m│\x1b[0m ${metaPairs}`
					: ` | ${metaPairs}`;
			}
		}

		// Add stack trace formatting if present
		let stackStr = '';
		if (stack) {
			stackStr = useColors ? `\n\x1b[90m${stack}\x1b[0m` : `\n${stack}`;
		}

		// Construct final log line with proper spacing and separators
		return `${formattedTime} ${formattedLevel} ${formattedService} ${formattedMessage}${metaStr}${stackStr}`;
	})
);

// Enhanced production format with better structure
const productionFormat = winston.format.combine(
	winston.format.timestamp({format: 'YYYY-MM-DD HH:mm:ss.SSS'}),
	winston.format.errors({stack: true}),
	winston.format.json({
		replacer: (key, value) => {
			// Ensure consistent field ordering
			if (key === '') {
				const logEntry = value as any;
				const {timestamp, level, service, message, type, ...rest} = logEntry;
				return {timestamp, level, service, type, message, ...rest};
			}
			return value;
		},
	})
);

// Enhanced file format for better log analysis
const fileFormat = winston.format.combine(
	winston.format.timestamp({format: 'YYYY-MM-DD HH:mm:ss.SSS'}),
	winston.format.errors({stack: true}),
	winston.format.printf((info) => {
		const {level, message, timestamp, service, type, stack, ...meta} =
			info as any;

		// Clean up metadata
		const cleanMeta = {...meta};
		delete cleanMeta.service;
		delete cleanMeta.timestamp;

		// Format level with consistent width
		const formattedLevel = level.toUpperCase().padEnd(7);

		// Format service with consistent width
		const formattedService = (service as string).padEnd(12);

		// Add event type indicator if present
		const eventIndicator = type ? `[${type}] ` : '';

		// Format metadata in structured way
		const metaString =
			Object.keys(cleanMeta).length > 0
				? ` | ${Object.entries(cleanMeta)
						.map(
							([k, v]) =>
								`${k}=${typeof v === 'object' ? JSON.stringify(v) : v}`
						)
						.join(' ')}`
				: '';

		const stackString = stack ? `\n${stack}` : '';

		return `${timestamp} [${formattedLevel}] [${formattedService}] ${eventIndicator}${message}${metaString}${stackString}`;
	})
);

// Logger cache to prevent creating multiple instances
const loggerCache = new Map<string, AppLogger>();

// Application logger class with additional methods
class AppLogger {
	private logger: winston.Logger;
	private service: string;

	constructor(logger: winston.Logger, service: string) {
		this.logger = logger;
		this.service = service;
	}

	error(message: string, error?: Error | any, meta?: any): void {
		if (error instanceof Error) {
			this.logger.error(message, {
				service: this.service,
				error: error.message,
				stack: error.stack,
				...meta,
			});
		} else if (error) {
			this.logger.error(message, {service: this.service, error, ...meta});
		} else {
			this.logger.error(message, {service: this.service, ...meta});
		}
	}

	warn(message: string, meta?: any): void {
		this.logger.warn(message, {service: this.service, ...meta});
	}

	info(message: string, meta?: any): void {
		this.logger.info(message, {service: this.service, ...meta});
	}

	http(message: string, meta?: any): void {
		this.logger.http(message, {service: this.service, ...meta});
	}

	debug(message: string, meta?: any): void {
		this.logger.debug(message, {service: this.service, ...meta});
	}

	verbose(message: string, meta?: any): void {
		this.logger.verbose(message, {service: this.service, ...meta});
	}

	silly(message: string, meta?: any): void {
		this.logger.silly(message, {service: this.service, ...meta});
	}

	// Security-specific logging
	security(event: string, details: any): void {
		this.logger.info(`SECURITY: ${event}`, {
			service: this.service,
			type: 'SECURITY_EVENT',
			event,
			...details,
			timestamp: new Date().toISOString(),
		});
	}

	// Authentication-specific logging
	auth(event: string, userId?: string, details?: any): void {
		this.logger.info(`AUTH: ${event}`, {
			service: this.service,
			type: 'AUTH_EVENT',
			event,
			userId,
			...details,
			timestamp: new Date().toISOString(),
		});
	}

	// Performance logging
	performance(operation: string, duration: number, meta?: any): void {
		this.logger.info(`PERFORMANCE: ${operation}`, {
			service: this.service,
			type: 'PERFORMANCE',
			operation,
			duration,
			...meta,
			timestamp: new Date().toISOString(),
		});
	}

	// Database operation logging
	database(operation: string, table?: string, meta?: any): void {
		this.logger.debug(`DATABASE: ${operation}`, {
			service: this.service,
			type: 'DATABASE',
			operation,
			table,
			...meta,
			timestamp: new Date().toISOString(),
		});
	}
}

// Create logger instance with full transport configuration
function createLogger(service: string): AppLogger {
	// Return cached logger if it exists
	if (loggerCache.has(service)) {
		return loggerCache.get(service)!;
	}

	const loggingConfig = config.getLoggingConfig();
	const isProduction = process.env.NODE_ENV === 'production';
	const isDevelopment = process.env.NODE_ENV === 'development';

	// Create transports array
	const transports: winston.transport[] = [];

	// Console transport
	if (loggingConfig.enableConsole) {
		const consoleFormat = isProduction ? productionFormat : developmentFormat;
		transports.push(
			new winston.transports.Console({
				level: loggingConfig.level,
				format: consoleFormat,
			})
		);
	}

	// File transports for production and when explicitly enabled
	if (loggingConfig.enableFile || isProduction) {
		// General application logs
		transports.push(
			new DailyRotateFile({
				filename: path.join(logsDir, `${service}-%DATE%.log`),
				datePattern: 'YYYY-MM-DD',
				maxSize: '20m',
				maxFiles: '14d',
				format: fileFormat,
				level: 'info',
			})
		);

		// Error logs
		transports.push(
			new DailyRotateFile({
				filename: path.join(logsDir, `${service}-error-%DATE%.log`),
				datePattern: 'YYYY-MM-DD',
				maxSize: '20m',
				maxFiles: '30d',
				format: fileFormat,
				level: 'error',
			})
		);

		// HTTP logs for API services
		if (['HTTP', 'API', 'App'].includes(service)) {
			transports.push(
				new DailyRotateFile({
					filename: path.join(logsDir, 'http-%DATE%.log'),
					datePattern: 'YYYY-MM-DD',
					maxSize: '20m',
					maxFiles: '7d',
					format: fileFormat,
					level: 'http',
				})
			);
		}
	}

	// Create the winston logger
	const logger = winston.createLogger({
		level: loggingConfig.level,
		levels: logLevels,
		format: fileFormat,
		defaultMeta: {service},
		transports,
		// Exception handlers for production
		exceptionHandlers: isProduction
			? [
					new DailyRotateFile({
						filename: path.join(logsDir, 'exceptions-%DATE%.log'),
						datePattern: 'YYYY-MM-DD',
						maxSize: '20m',
						maxFiles: '30d',
						format: fileFormat,
					}),
			  ]
			: [],
		// Rejection handlers for production
		rejectionHandlers: isProduction
			? [
					new DailyRotateFile({
						filename: path.join(logsDir, 'rejections-%DATE%.log'),
						datePattern: 'YYYY-MM-DD',
						maxSize: '20m',
						maxFiles: '30d',
						format: fileFormat,
					}),
			  ]
			: [],
		exitOnError: false,
		silent: false,
	});

	// Create application logger wrapper
	const appLogger = new AppLogger(logger, service);

	// Cache the logger
	loggerCache.set(service, appLogger);

	return appLogger;
}

// Stream for Morgan HTTP logging middleware
export const morganStream = {
	write: (message: string) => {
		const httpLogger = createLogger('HTTP');
		httpLogger.http(message.trim());
	},
};

// Create specific loggers
export const authLogger = createLogger('Authentication');
export const dbLogger = createLogger('Database');
export const apiLogger = createLogger('API');
export const securityLogger = createLogger('Security');
export const emailLogger = createLogger('Email');
export const fileLogger = createLogger('FileUpload');
export const httpLogger = createLogger('HTTP');

// Export the createLogger function for custom loggers
export {createLogger};

// Export logger configuration for testing
export const getLoggerConfig = (): LoggingConfig => config.getLoggingConfig();

// Helper function to clear logger cache (for testing)
export const clearLoggerCache = (): void => {
	loggerCache.clear();
};

export default {
	createLogger,
	authLogger,
	dbLogger,
	apiLogger,
	securityLogger,
	emailLogger,
	fileLogger,
	httpLogger,
	morganStream,
	getLoggerConfig,
	clearLoggerCache,
};
