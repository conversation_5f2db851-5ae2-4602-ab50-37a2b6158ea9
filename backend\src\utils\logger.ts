import winston from 'winston';
import path from 'path';
import {config, LoggingConfig} from '../config';

// Custom log format for development
const developmentFormat = winston.format.combine(
	winston.format.timestamp({format: 'HH:mm:ss'}),
	winston.format.colorize(),
	winston.format.printf(({timestamp, level, message, service, ...meta}) => {
		const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
		return `${timestamp} ${level}: [${service}] ${message}${metaStr}`;
	})
);

// Custom log format for production
const productionFormat = winston.format.combine(
	winston.format.timestamp(),
	winston.format.errors({stack: true}),
	winston.format.json()
);

// Create logger instance
function createLogger(service: string): winston.Logger {
	const loggingConfig = config.getLoggingConfig();

	// Determine log level based on configuration
	const logLevel = loggingConfig.level;

	// Create transports array
	const transports: winston.transport[] = [];

	// Console transport
	if (loggingConfig.enableConsole) {
		const consoleFormat =
			loggingConfig.format === 'json' ? productionFormat : developmentFormat;

		transports.push(
			new winston.transports.Console({
				level: logLevel,
				format: consoleFormat,
			})
		);
	}

	// File transport
	if (loggingConfig.enableFile && loggingConfig.file) {
		const logDir = path.dirname(loggingConfig.file);
		const logFile = path.basename(loggingConfig.file);

		// Ensure log directory exists
		try {
			const fs = require('fs');
			if (!fs.existsSync(logDir)) {
				fs.mkdirSync(logDir, {recursive: true});
			}
		} catch (error) {
			console.error('Failed to create log directory:', error);
		}

		transports.push(
			new winston.transports.File({
				filename: path.join(logDir, `${service}-${logFile}`),
				level: logLevel,
				format: productionFormat,
				maxsize: loggingConfig.maxFileSize,
				maxFiles: loggingConfig.maxFiles,
				tailable: true,
			})
		);

		// Error file transport
		transports.push(
			new winston.transports.File({
				filename: path.join(logDir, `${service}-error-${logFile}`),
				level: 'error',
				format: productionFormat,
				maxsize: loggingConfig.maxFileSize,
				maxFiles: loggingConfig.maxFiles,
				tailable: true,
			})
		);
	}

	// Create logger
	const logger = winston.createLogger({
		level: logLevel,
		format: productionFormat,
		transports,
		// Handle uncaught exceptions
		exceptionHandlers:
			loggingConfig.enableFile && loggingConfig.file
				? [
						new winston.transports.File({
							filename: path.join(
								path.dirname(loggingConfig.file),
								'exceptions.log'
							),
							format: productionFormat,
						}),
				  ]
				: [],
		// Handle unhandled promise rejections
		rejectionHandlers:
			loggingConfig.enableFile && loggingConfig.file
				? [
						new winston.transports.File({
							filename: path.join(
								path.dirname(loggingConfig.file),
								'rejections.log'
							),
							format: productionFormat,
						}),
				  ]
				: [],
	});

	// Add service context to all log messages
	const loggerWithService = {
		error: (message: string, meta?: any) =>
			logger.error(message, {service, ...meta}),
		warn: (message: string, meta?: any) =>
			logger.warn(message, {service, ...meta}),
		info: (message: string, meta?: any) =>
			logger.info(message, {service, ...meta}),
		debug: (message: string, meta?: any) =>
			logger.debug(message, {service, ...meta}),
		verbose: (message: string, meta?: any) =>
			logger.verbose(message, {service, ...meta}),
		silly: (message: string, meta?: any) =>
			logger.silly(message, {service, ...meta}),
	};

	return loggerWithService as winston.Logger;
}

// Create specific loggers
export const authLogger = createLogger('Authentication');
export const dbLogger = createLogger('Database');
export const apiLogger = createLogger('API');
export const securityLogger = createLogger('Security');
export const emailLogger = createLogger('Email');
export const fileLogger = createLogger('FileUpload');

// Export the createLogger function for custom loggers
export {createLogger};

// Export logger configuration for testing
export const getLoggerConfig = (): LoggingConfig => config.getLoggingConfig();
