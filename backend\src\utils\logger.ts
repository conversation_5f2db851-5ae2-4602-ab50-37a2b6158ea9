import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';
import fs from 'fs';
import {config, LoggingConfig} from '../config';

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
	fs.mkdirSync(logsDir, {recursive: true});
}

// Define log levels with colors
const logLevels = {
	error: 0,
	warn: 1,
	info: 2,
	http: 3,
	debug: 4,
};

const logColors = {
	error: 'red',
	warn: 'yellow',
	info: 'green',
	http: 'magenta',
	debug: 'white',
};

winston.addColors(logColors);

// Custom log format for development
const developmentFormat = winston.format.combine(
	winston.format.timestamp({format: 'HH:mm:ss'}),
	winston.format.colorize({all: true}),
	winston.format.printf(({timestamp, level, message, service, ...meta}) => {
		const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
		return `${timestamp} ${level}: [${service}] ${message}${metaStr}`;
	})
);

// Custom log format for production
const productionFormat = winston.format.combine(
	winston.format.timestamp(),
	winston.format.errors({stack: true}),
	winston.format.json()
);

// File format for structured logs
const fileFormat = winston.format.combine(
	winston.format.timestamp({format: 'YYYY-MM-DD HH:mm:ss'}),
	winston.format.errors({stack: true}),
	winston.format.printf(
		({level, message, timestamp, service, stack, ...meta}) => {
			const metaString =
				Object.keys(meta).length > 0
					? `\n${JSON.stringify(meta, null, 2)}`
					: '';
			const stackString = stack ? `\n${stack}` : '';
			return `${timestamp} [${level.toUpperCase()}]: [${service}] ${message}${metaString}${stackString}`;
		}
	)
);

// Logger cache to prevent creating multiple instances
const loggerCache = new Map<string, AppLogger>();

// Application logger class with additional methods
class AppLogger {
	private logger: winston.Logger;
	private service: string;

	constructor(logger: winston.Logger, service: string) {
		this.logger = logger;
		this.service = service;
	}

	error(message: string, error?: Error | any, meta?: any): void {
		if (error instanceof Error) {
			this.logger.error(message, {
				service: this.service,
				error: error.message,
				stack: error.stack,
				...meta,
			});
		} else if (error) {
			this.logger.error(message, {service: this.service, error, ...meta});
		} else {
			this.logger.error(message, {service: this.service, ...meta});
		}
	}

	warn(message: string, meta?: any): void {
		this.logger.warn(message, {service: this.service, ...meta});
	}

	info(message: string, meta?: any): void {
		this.logger.info(message, {service: this.service, ...meta});
	}

	http(message: string, meta?: any): void {
		this.logger.http(message, {service: this.service, ...meta});
	}

	debug(message: string, meta?: any): void {
		this.logger.debug(message, {service: this.service, ...meta});
	}

	verbose(message: string, meta?: any): void {
		this.logger.verbose(message, {service: this.service, ...meta});
	}

	silly(message: string, meta?: any): void {
		this.logger.silly(message, {service: this.service, ...meta});
	}

	// Security-specific logging
	security(event: string, details: any): void {
		this.logger.info(`SECURITY: ${event}`, {
			service: this.service,
			type: 'SECURITY_EVENT',
			event,
			...details,
			timestamp: new Date().toISOString(),
		});
	}

	// Authentication-specific logging
	auth(event: string, userId?: string, details?: any): void {
		this.logger.info(`AUTH: ${event}`, {
			service: this.service,
			type: 'AUTH_EVENT',
			event,
			userId,
			...details,
			timestamp: new Date().toISOString(),
		});
	}

	// Performance logging
	performance(operation: string, duration: number, meta?: any): void {
		this.logger.info(`PERFORMANCE: ${operation}`, {
			service: this.service,
			type: 'PERFORMANCE',
			operation,
			duration,
			...meta,
			timestamp: new Date().toISOString(),
		});
	}

	// Database operation logging
	database(operation: string, table?: string, meta?: any): void {
		this.logger.debug(`DATABASE: ${operation}`, {
			service: this.service,
			type: 'DATABASE',
			operation,
			table,
			...meta,
			timestamp: new Date().toISOString(),
		});
	}
}

// Create logger instance with full transport configuration
function createLogger(service: string): AppLogger {
	// Return cached logger if it exists
	if (loggerCache.has(service)) {
		return loggerCache.get(service)!;
	}

	const loggingConfig = config.getLoggingConfig();
	const isProduction = process.env.NODE_ENV === 'production';
	const isDevelopment = process.env.NODE_ENV === 'development';

	// Create transports array
	const transports: winston.transport[] = [];

	// Console transport
	if (loggingConfig.enableConsole) {
		const consoleFormat = isProduction ? productionFormat : developmentFormat;
		transports.push(
			new winston.transports.Console({
				level: loggingConfig.level,
				format: consoleFormat,
			})
		);
	}

	// File transports for production and when explicitly enabled
	if (loggingConfig.enableFile || isProduction) {
		// General application logs
		transports.push(
			new DailyRotateFile({
				filename: path.join(logsDir, `${service}-%DATE%.log`),
				datePattern: 'YYYY-MM-DD',
				maxSize: '20m',
				maxFiles: '14d',
				format: fileFormat,
				level: 'info',
			})
		);

		// Error logs
		transports.push(
			new DailyRotateFile({
				filename: path.join(logsDir, `${service}-error-%DATE%.log`),
				datePattern: 'YYYY-MM-DD',
				maxSize: '20m',
				maxFiles: '30d',
				format: fileFormat,
				level: 'error',
			})
		);

		// HTTP logs for API services
		if (['HTTP', 'API', 'App'].includes(service)) {
			transports.push(
				new DailyRotateFile({
					filename: path.join(logsDir, 'http-%DATE%.log'),
					datePattern: 'YYYY-MM-DD',
					maxSize: '20m',
					maxFiles: '7d',
					format: fileFormat,
					level: 'http',
				})
			);
		}
	}

	// Create the winston logger
	const logger = winston.createLogger({
		level: loggingConfig.level,
		levels: logLevels,
		format: fileFormat,
		defaultMeta: {service},
		transports,
		// Exception handlers for production
		exceptionHandlers: isProduction
			? [
					new DailyRotateFile({
						filename: path.join(logsDir, 'exceptions-%DATE%.log'),
						datePattern: 'YYYY-MM-DD',
						maxSize: '20m',
						maxFiles: '30d',
						format: fileFormat,
					}),
			  ]
			: [],
		// Rejection handlers for production
		rejectionHandlers: isProduction
			? [
					new DailyRotateFile({
						filename: path.join(logsDir, 'rejections-%DATE%.log'),
						datePattern: 'YYYY-MM-DD',
						maxSize: '20m',
						maxFiles: '30d',
						format: fileFormat,
					}),
			  ]
			: [],
		exitOnError: false,
		silent: false,
	});

	// Create application logger wrapper
	const appLogger = new AppLogger(logger, service);

	// Cache the logger
	loggerCache.set(service, appLogger);

	return appLogger;
}

// Stream for Morgan HTTP logging middleware
export const morganStream = {
	write: (message: string) => {
		const httpLogger = createLogger('HTTP');
		httpLogger.http(message.trim());
	},
};

// Create specific loggers
export const authLogger = createLogger('Authentication');
export const dbLogger = createLogger('Database');
export const apiLogger = createLogger('API');
export const securityLogger = createLogger('Security');
export const emailLogger = createLogger('Email');
export const fileLogger = createLogger('FileUpload');
export const httpLogger = createLogger('HTTP');

// Export the createLogger function for custom loggers
export {createLogger};

// Export logger configuration for testing
export const getLoggerConfig = (): LoggingConfig => config.getLoggingConfig();

// Helper function to clear logger cache (for testing)
export const clearLoggerCache = (): void => {
	loggerCache.clear();
};

export default {
	createLogger,
	authLogger,
	dbLogger,
	apiLogger,
	securityLogger,
	emailLogger,
	fileLogger,
	httpLogger,
	morganStream,
	getLoggerConfig,
	clearLoggerCache,
};
