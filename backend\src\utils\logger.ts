import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';
import fs from 'fs';

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
try {
	if (!fs.existsSync(logsDir)) {
		fs.mkdirSync(logsDir, {recursive: true});
	}
} catch (error) {
	console.warn('⚠️  Could not create logs directory:', error);
	console.warn('📝 Logging will continue but files may not be saved');
}

// Define log levels with colors
const logLevels = {
	error: 0,
	warn: 1,
	info: 2,
	http: 3,
	debug: 4,
};

const logColors = {
	error: 'red',
	warn: 'yellow',
	info: 'green',
	http: 'magenta',
	debug: 'white',
};

winston.addColors(logColors);

// Custom format for better readability
const logFormat = winston.format.combine(
	winston.format.timestamp({format: 'YYYY-MM-DD HH:mm:ss'}),
	winston.format.errors({stack: true}),
	winston.format.printf(({level, message, timestamp, stack, ...meta}) => {
		const metaString =
			Object.keys(meta).length > 0 ? `\n${JSON.stringify(meta, null, 2)}` : '';
		const stackString = stack ? `\n${stack}` : '';
		return `${timestamp} [${level.toUpperCase()}]: ${message}${metaString}${stackString}`;
	})
);

// Console format for development
const consoleFormat = winston.format.combine(
	winston.format.colorize({all: true}),
	winston.format.timestamp({format: 'HH:mm:ss'}),
	winston.format.printf(({level, message, timestamp, stack, ...meta}) => {
		const metaString =
			Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
		const stackString = stack ? `\n${stack}` : '';
		return `${timestamp} ${level}: ${message}${metaString}${stackString}`;
	})
);

// File rotation transport for general logs
const fileRotateTransport = new DailyRotateFile({
	filename: path.join(logsDir, 'application-%DATE%.log'),
	datePattern: 'YYYY-MM-DD',
	maxSize: '20m',
	maxFiles: '30d',
	format: logFormat,
	level: 'info',
});

// File rotation transport for error logs
const errorFileRotateTransport = new DailyRotateFile({
	filename: path.join(logsDir, 'error-%DATE%.log'),
	datePattern: 'YYYY-MM-DD',
	maxSize: '20m',
	maxFiles: '30d',
	format: logFormat,
	level: 'error',
});

// File rotation transport for HTTP requests
const httpFileRotateTransport = new DailyRotateFile({
	filename: path.join(logsDir, 'http-%DATE%.log'),
	datePattern: 'YYYY-MM-DD',
	maxSize: '20m',
	maxFiles: '14d',
	format: logFormat,
	level: 'http',
});

// Create the winston logger instance
const logger = winston.createLogger({
	level:
		process.env.LOG_LEVEL ||
		(process.env.NODE_ENV === 'production' ? 'info' : 'debug'),
	levels: logLevels,
	format: logFormat,
	defaultMeta: {service: 'printco-backend'},
	transports: [
		fileRotateTransport,
		errorFileRotateTransport,
		httpFileRotateTransport,
	],
	// Handle uncaught exceptions and unhandled rejections
	exceptionHandlers: [
		new DailyRotateFile({
			filename: path.join(logsDir, 'exceptions-%DATE%.log'),
			datePattern: 'YYYY-MM-DD',
			maxSize: '20m',
			maxFiles: '30d',
			format: logFormat,
		}),
	],
	rejectionHandlers: [
		new DailyRotateFile({
			filename: path.join(logsDir, 'rejections-%DATE%.log'),
			datePattern: 'YYYY-MM-DD',
			maxSize: '20m',
			maxFiles: '30d',
			format: logFormat,
		}),
	],
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
	logger.add(
		new winston.transports.Console({
			format: consoleFormat,
			level: 'debug',
		})
	);
}

// Add console transport for production (errors only)
if (process.env.NODE_ENV === 'production') {
	logger.add(
		new winston.transports.Console({
			format: winston.format.combine(
				winston.format.timestamp(),
				winston.format.errors({stack: true}),
				winston.format.json()
			),
			level: 'error',
		})
	);
}

// Create specialized loggers for different contexts
export class Logger {
	private context: string;

	constructor(context: string = 'Application') {
		this.context = context;
	}

	private formatMessage(message: string): string {
		return `[${this.context}] ${message}`;
	}

	error(message: string, error?: Error | any, meta?: any): void {
		if (error instanceof Error) {
			logger.error(this.formatMessage(message), {
				error: error.message,
				stack: error.stack,
				...meta,
			});
		} else if (error) {
			logger.error(this.formatMessage(message), {error, ...meta});
		} else {
			logger.error(this.formatMessage(message), meta);
		}
	}

	warn(message: string, meta?: any): void {
		logger.warn(this.formatMessage(message), meta);
	}

	info(message: string, meta?: any): void {
		logger.info(this.formatMessage(message), meta);
	}

	http(message: string, meta?: any): void {
		logger.http(this.formatMessage(message), meta);
	}

	debug(message: string, meta?: any): void {
		logger.debug(this.formatMessage(message), meta);
	}

	// Security-specific logging
	security(event: string, details: any): void {
		logger.info(this.formatMessage(`SECURITY: ${event}`), {
			type: 'SECURITY_EVENT',
			event,
			...details,
			timestamp: new Date().toISOString(),
		});
	}

	// Authentication-specific logging
	auth(event: string, userId?: string, details?: any): void {
		logger.info(this.formatMessage(`AUTH: ${event}`), {
			type: 'AUTH_EVENT',
			event,
			userId,
			...details,
			timestamp: new Date().toISOString(),
		});
	}

	// Performance logging
	performance(operation: string, duration: number, meta?: any): void {
		logger.info(this.formatMessage(`PERFORMANCE: ${operation}`), {
			type: 'PERFORMANCE',
			operation,
			duration,
			...meta,
			timestamp: new Date().toISOString(),
		});
	}

	// Database operation logging
	database(operation: string, table?: string, meta?: any): void {
		logger.debug(this.formatMessage(`DATABASE: ${operation}`), {
			type: 'DATABASE',
			operation,
			table,
			...meta,
			timestamp: new Date().toISOString(),
		});
	}
}

// Default logger instance
export const defaultLogger = new Logger('Application');

// Context-specific logger instances
export const authLogger = new Logger('Authentication');
export const dbLogger = new Logger('Database');
export const httpLogger = new Logger('HTTP');
export const securityLogger = new Logger('Security');

// Stream for Morgan HTTP logging middleware
export const morganStream = {
	write: (message: string) => {
		httpLogger.http(message.trim());
	},
};

// Helper function to create context-specific loggers
export const createLogger = (context: string): Logger => {
	return new Logger(context);
};

// Export the underlying winston logger for advanced use cases
export {logger as winstonLogger};

export default defaultLogger;
