// Common Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface PageProps extends BaseComponentProps {
  title?: string;
}

// Navigation Types
export interface NavigationItem {
  name: string;
  route: string;
  category?: string;
  icon?: React.ComponentType;
}

export interface BreadcrumbItem {
  label: string;
  href?: string;
}

// Form Types
export interface FormFieldProps {
  label?: string;
  error?: string;
  required?: boolean;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}