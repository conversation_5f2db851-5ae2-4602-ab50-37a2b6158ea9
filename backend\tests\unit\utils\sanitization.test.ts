import {
  sanitizeRequestBody,
  sanitizeUser,
  sanitizeHeaders,
  sanitizeQuery,
  sanitizeObject,
} from '@/utils/sanitization';

describe('Sanitization Utils', () => {
  describe('sanitizeRequestBody', () => {
    it('should sanitize password fields', () => {
      const body = {
        email: '<EMAIL>',
        password: 'secret123',
        confirmPassword: 'secret123',
        firstName: '<PERSON>',
      };

      const result = sanitizeRequestBody(body);

      expect(result).toEqual({
        email: '<EMAIL>',
        password: '[REDACTED]',
        confirmPassword: '[REDACTED]',
        firstName: 'John',
      });
    });

    it('should sanitize token fields', () => {
      const body = {
        userId: 'user-123',
        accessToken: 'jwt-token-here',
        refreshToken: 'refresh-token-here',
        apiKey: 'api-key-here',
      };

      const result = sanitizeRequestBody(body);

      expect(result).toEqual({
        userId: 'user-123',
        accessToken: '[REDACTED]',
        refreshToken: '[REDACTED]',
        apiKey: '[REDACTED]',
      });
    });

    it('should sanitize nested objects', () => {
      const body = {
        user: {
          email: '<EMAIL>',
          password: 'secret123',
          profile: {
            name: 'John Doe',
            ssn: '***********',
          },
        },
        settings: {
          apiKey: 'secret-key',
          theme: 'dark',
        },
      };

      const result = sanitizeRequestBody(body);

      expect(result).toEqual({
        user: {
          email: '<EMAIL>',
          password: '[REDACTED]',
          profile: {
            name: 'John Doe',
            ssn: '[REDACTED]',
          },
        },
        settings: {
          apiKey: '[REDACTED]',
          theme: 'dark',
        },
      });
    });

    it('should sanitize arrays', () => {
      const body = {
        users: [
          {
            email: '<EMAIL>',
            password: 'secret1',
          },
          {
            email: '<EMAIL>',
            password: 'secret2',
          },
        ],
        tokens: ['token1', 'token2'],
      };

      const result = sanitizeRequestBody(body);

      expect(result).toEqual({
        users: [
          {
            email: '<EMAIL>',
            password: '[REDACTED]',
          },
          {
            email: '<EMAIL>',
            password: '[REDACTED]',
          },
        ],
        tokens: ['token1', 'token2'], // Array values aren't sanitized unless they're objects with sensitive keys
      });
    });

    it('should handle null and undefined values', () => {
      const body = {
        email: '<EMAIL>',
        password: null,
        token: undefined,
        data: {
          secret: null,
          value: 'test',
        },
      };

      const result = sanitizeRequestBody(body);

      expect(result).toEqual({
        email: '<EMAIL>',
        password: '[REDACTED]',
        token: '[REDACTED]',
        data: {
          secret: '[REDACTED]',
          value: 'test',
        },
      });
    });

    it('should handle circular references by limiting depth', () => {
      const body: any = {
        email: '<EMAIL>',
        password: 'secret123',
      };
      body.circular = body; // Create circular reference

      const result = sanitizeRequestBody(body);

      expect(result.email).toBe('<EMAIL>');
      expect(result.password).toBe('[REDACTED]');
      expect(typeof result.circular).toBe('object');
    });

    it('should preserve Date objects', () => {
      const date = new Date('2023-01-01');
      const body = {
        createdAt: date,
        password: 'secret123',
      };

      const result = sanitizeRequestBody(body);

      expect(result).toEqual({
        createdAt: date,
        password: '[REDACTED]',
      });
    });

    it('should handle empty and null inputs', () => {
      expect(sanitizeRequestBody(null)).toBeNull();
      expect(sanitizeRequestBody(undefined)).toBeUndefined();
      expect(sanitizeRequestBody({})).toEqual({});
      expect(sanitizeRequestBody([])).toEqual([]);
    });

    it('should sanitize credit card information', () => {
      const body = {
        payment: {
          creditCard: '4111-1111-1111-1111',
          cardNumber: '4111-1111-1111-1111',
          cvv: '123',
          expiryDate: '12/25',
        },
      };

      const result = sanitizeRequestBody(body);

      expect(result).toEqual({
        payment: {
          creditCard: '[REDACTED]',
          cardNumber: '[REDACTED]',
          cvv: '[REDACTED]',
          expiryDate: '[REDACTED]',
        },
      });
    });

    it('should sanitize case-insensitive field names', () => {
      const body = {
        PASSWORD: 'secret123',
        ApiKey: 'api-key-here',
        REFRESH_TOKEN: 'refresh-token',
        Social_Security_Number: '***********',
      };

      const result = sanitizeRequestBody(body);

      expect(result).toEqual({
        PASSWORD: '[REDACTED]',
        ApiKey: '[REDACTED]',
        REFRESH_TOKEN: '[REDACTED]',
        Social_Security_Number: '[REDACTED]',
      });
    });
  });

  describe('sanitizeUser', () => {
    it('should remove sensitive user fields', () => {
      const user = {
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: 'hashed-password',
        passwordResetToken: 'reset-token',
        passwordResetTokenExpires: new Date(),
        emailVerificationToken: 'verify-token',
        emailVerificationTokenExpires: new Date(),
        loginAttempts: 3,
        lockedUntil: new Date(),
        sessions: [{ id: 'session-1' }],
        role: 'customer',
        isActive: true,
      };

      const result = sanitizeUser(user);

      expect(result).toEqual({
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'customer',
        isActive: true,
      });

      // Ensure sensitive fields are removed
      expect(result).not.toHaveProperty('password');
      expect(result).not.toHaveProperty('passwordResetToken');
      expect(result).not.toHaveProperty('emailVerificationToken');
      expect(result).not.toHaveProperty('loginAttempts');
      expect(result).not.toHaveProperty('lockedUntil');
      expect(result).not.toHaveProperty('sessions');
    });

    it('should handle null and non-object inputs', () => {
      expect(sanitizeUser(null)).toBeNull();
      expect(sanitizeUser(undefined)).toBeUndefined();
      expect(sanitizeUser('string')).toBe('string');
      expect(sanitizeUser(123)).toBe(123);
    });

    it('should handle user without sensitive fields', () => {
      const user = {
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'John',
      };

      const result = sanitizeUser(user);

      expect(result).toEqual(user);
    });
  });

  describe('sanitizeHeaders', () => {
    it('should sanitize authorization headers', () => {
      const headers = {
        'content-type': 'application/json',
        'authorization': 'Bearer jwt-token-here',
        'x-api-key': 'api-key-here',
        'user-agent': 'Mozilla/5.0',
        'cookie': 'session=abc123; token=xyz789',
      };

      const result = sanitizeHeaders(headers);

      expect(result).toEqual({
        'content-type': 'application/json',
        'authorization': '[REDACTED]',
        'x-api-key': '[REDACTED]',
        'user-agent': 'Mozilla/5.0',
        'cookie': '[REDACTED]',
      });
    });

    it('should handle case-insensitive header names', () => {
      const headers = {
        'Authorization': 'Bearer token',
        'X-API-KEY': 'secret-key',
        'Cookie': 'session=abc123',
        'X-Auth-Token': 'auth-token',
        'Content-Type': 'application/json',
      };

      const result = sanitizeHeaders(headers);

      expect(result).toEqual({
        'Authorization': '[REDACTED]',
        'X-API-KEY': '[REDACTED]',
        'Cookie': '[REDACTED]',
        'X-Auth-Token': '[REDACTED]',
        'Content-Type': 'application/json',
      });
    });

    it('should handle null and non-object inputs', () => {
      expect(sanitizeHeaders(null)).toBeNull();
      expect(sanitizeHeaders(undefined)).toBeUndefined();
      expect(sanitizeHeaders('string')).toBe('string');
    });

    it('should handle empty headers', () => {
      const result = sanitizeHeaders({});
      expect(result).toEqual({});
    });
  });

  describe('sanitizeQuery', () => {
    it('should sanitize sensitive query parameters', () => {
      const query = {
        page: '1',
        limit: '20',
        token: 'query-token',
        apiKey: 'query-api-key',
        search: 'user search',
        password: 'query-password',
      };

      const result = sanitizeQuery(query);

      expect(result).toEqual({
        page: '1',
        limit: '20',
        token: '[REDACTED]',
        apiKey: '[REDACTED]',
        search: 'user search',
        password: '[REDACTED]',
      });
    });

    it('should handle nested query objects', () => {
      const query = {
        filter: {
          status: 'active',
          secret: 'hidden-value',
        },
        sort: 'name',
        token: 'query-token',
      };

      const result = sanitizeQuery(query);

      expect(result).toEqual({
        filter: {
          status: 'active',
          secret: '[REDACTED]',
        },
        sort: 'name',
        token: '[REDACTED]',
      });
    });

    it('should handle null and non-object inputs', () => {
      expect(sanitizeQuery(null)).toBeNull();
      expect(sanitizeQuery(undefined)).toBeUndefined();
      expect(sanitizeQuery('string')).toBe('string');
    });
  });

  describe('sanitizeObject', () => {
    it('should sanitize with custom sensitive fields', () => {
      const obj = {
        email: '<EMAIL>',
        password: 'secret123',
        customSecret: 'custom-secret-value',
        publicData: 'public-data',
      };

      const result = sanitizeObject(obj, {
        customSensitiveFields: ['customSecret'],
      });

      expect(result).toEqual({
        email: '<EMAIL>',
        password: '[REDACTED]',
        customSecret: '[REDACTED]',
        publicData: 'public-data',
      });
    });

    it('should respect custom max depth', () => {
      const deepObj = {
        level1: {
          level2: {
            level3: {
              password: 'secret',
              data: 'value',
            },
          },
        },
      };

      const result = sanitizeObject(deepObj, { maxDepth: 2 });

      expect(result.level1.level2).toBe('[MAX_DEPTH_REACHED]');
    });

    it('should handle objects with mixed data types', () => {
      const obj = {
        string: 'test',
        number: 42,
        boolean: true,
        date: new Date('2023-01-01'),
        array: [1, 2, 3],
        null: null,
        undefined: undefined,
        password: 'secret123',
        nested: {
          token: 'secret-token',
          value: 100,
        },
      };

      const result = sanitizeObject(obj);

      expect(result).toEqual({
        string: 'test',
        number: 42,
        boolean: true,
        date: new Date('2023-01-01'),
        array: [1, 2, 3],
        null: null,
        undefined: undefined,
        password: '[REDACTED]',
        nested: {
          token: '[REDACTED]',
          value: 100,
        },
      });
    });

    it('should handle empty options', () => {
      const obj = {
        password: 'secret123',
        data: 'public',
      };

      const result = sanitizeObject(obj, {});

      expect(result).toEqual({
        password: '[REDACTED]',
        data: 'public',
      });
    });
  });

  describe('pattern matching', () => {
    it('should match password patterns', () => {
      const obj = {
        userPassword: 'secret1',
        confirmPassword: 'secret2',
        passwordHash: 'secret3',
        currentPassword: 'secret4',
        newPassword: 'secret5',
        plainPassword: 'secret6',
      };

      const result = sanitizeRequestBody(obj);

      Object.keys(obj).forEach(key => {
        expect(result[key]).toBe('[REDACTED]');
      });
    });

    it('should match token patterns', () => {
      const obj = {
        accessToken: 'token1',
        refreshToken: 'token2',
        authToken: 'token3',
        bearerToken: 'token4',
        sessionToken: 'token5',
        verificationToken: 'token6',
      };

      const result = sanitizeRequestBody(obj);

      Object.keys(obj).forEach(key => {
        expect(result[key]).toBe('[REDACTED]');
      });
    });

    it('should match secret patterns', () => {
      const obj = {
        clientSecret: 'secret1',
        appSecret: 'secret2',
        sharedSecret: 'secret3',
        secretKey: 'secret4',
      };

      const result = sanitizeRequestBody(obj);

      Object.keys(obj).forEach(key => {
        expect(result[key]).toBe('[REDACTED]');
      });
    });

    it('should match key patterns', () => {
      const obj = {
        apiKey: 'key1',
        privateKey: 'key2',
        publicKey: 'key3',
        encryptionKey: 'key4',
        signingKey: 'key5',
      };

      const result = sanitizeRequestBody(obj);

      Object.keys(obj).forEach(key => {
        expect(result[key]).toBe('[REDACTED]');
      });
    });

    it('should match financial patterns', () => {
      const obj = {
        creditCard: '4111-1111-1111-1111',
        cardNumber: '4111-1111-1111-1111',
        accountNumber: '*********',
        routingNumber: '*********',
        iban: '**********************',
        swift: 'CHASUS33',
        cvv: '123',
        cvc: '456',
      };

      const result = sanitizeRequestBody(obj);

      Object.keys(obj).forEach(key => {
        expect(result[key]).toBe('[REDACTED]');
      });
    });

    it('should match PII patterns', () => {
      const obj = {
        ssn: '***********',
        socialSecurityNumber: '***********',
        phone: '******-123-4567',
        address: '123 Main St',
      };

      const result = sanitizeRequestBody(obj);

      Object.keys(obj).forEach(key => {
        expect(result[key]).toBe('[REDACTED]');
      });
    });
  });
});