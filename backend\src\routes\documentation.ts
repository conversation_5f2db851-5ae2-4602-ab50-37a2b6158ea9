import { Request, Response, Router } from 'express';
import { config } from '../config';
import { getAppVersion } from '../utils/appHelpers';
import { createApiLinks } from '../utils/responseWrapper';

const router = Router();

// API endpoint definitions
const apiEndpoints = {
  auth: {
    register: {
      path: '/api/auth/register',
      method: 'POST',
      description: 'Create a new user account',
      requiresAuth: false,
      rateLimit: { maxRequests: 5, windowMs: 900000 },
      requestBody: {
        email: { type: 'string', required: true, example: '<EMAIL>' },
        password: { type: 'string', required: true, example: 'StrongPassword123!' },
        firstName: { type: 'string', required: true, example: 'John' },
        lastName: { type: 'string', required: true, example: 'Doe' },
        phone: { type: 'string', required: false, example: '+**********' },
      },
      responseType: 'AuthResponse',
      statusCodes: [201, 400, 409, 422, 429],
      example: {
        request: {
          email: '<EMAIL>',
          password: 'StrongPassword123!',
          firstName: 'John',
          lastName: 'Doe',
          phone: '+**********',
        },
        response: {
          success: true,
          message: 'Account created successfully',
          data: {
            user: {
              id: 'cuid123...',
              email: '<EMAIL>',
              firstName: 'John',
              lastName: 'Doe',
              role: 'CUSTOMER',
              isActive: true,
              isVerified: false,
            },
            tokens: {
              accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
              refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
            },
          },
        },
      },
    },
    login: {
      path: '/api/auth/login',
      method: 'POST',
      description: 'Authenticate user and receive access tokens',
      requiresAuth: false,
      rateLimit: { maxRequests: 5, windowMs: 900000 },
      requestBody: {
        email: { type: 'string', required: true, example: '<EMAIL>' },
        password: { type: 'string', required: true, example: 'StrongPassword123!' },
      },
      responseType: 'AuthResponse',
      statusCodes: [200, 400, 401, 423, 429],
      example: {
        request: {
          email: '<EMAIL>',
          password: 'StrongPassword123!',
        },
        response: {
          success: true,
          message: 'Login successful',
          data: {
            user: {
              id: 'cuid123...',
              email: '<EMAIL>',
              firstName: 'John',
              lastName: 'Doe',
              role: 'CUSTOMER',
              isActive: true,
              isVerified: true,
            },
            tokens: {
              accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
              refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
            },
          },
        },
      },
    },
    refresh: {
      path: '/api/auth/refresh',
      method: 'POST',
      description: 'Refresh access token using refresh token',
      requiresAuth: false,
      requestBody: {
        refreshToken: { type: 'string', required: true, example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
      },
      responseType: 'TokenRefreshResponse',
      statusCodes: [200, 400, 401],
      example: {
        request: {
          refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        },
        response: {
          success: true,
          message: 'Tokens refreshed successfully',
          data: {
            tokens: {
              accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
              refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
            },
          },
        },
      },
    },
    logout: {
      path: '/api/auth/logout',
      method: 'POST',
      description: 'Invalidate refresh token and logout from current device',
      requiresAuth: true,
      requestBody: {
        refreshToken: { type: 'string', required: true, example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
      },
      responseType: 'MessageResponse',
      statusCodes: [200, 401],
      example: {
        request: {
          refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        },
        response: {
          success: true,
          message: 'Logged out successfully',
          data: null,
        },
      },
    },
    profile: {
      path: '/api/auth/profile',
      method: 'GET',
      description: 'Get current user profile information',
      requiresAuth: true,
      responseType: 'UserResponse',
      statusCodes: [200, 401],
      example: {
        response: {
          success: true,
          data: {
            user: {
              id: 'cuid123...',
              email: '<EMAIL>',
              firstName: 'John',
              lastName: 'Doe',
              role: 'CUSTOMER',
              isActive: true,
              isVerified: true,
            },
          },
        },
      },
    },
    updateProfile: {
      path: '/api/auth/profile',
      method: 'PUT',
      description: 'Update user profile information',
      requiresAuth: true,
      requestBody: {
        firstName: { type: 'string', required: false, example: 'John' },
        lastName: { type: 'string', required: false, example: 'Smith' },
        phone: { type: 'string', required: false, example: '+**********' },
        avatar: { type: 'string', required: false, example: 'https://example.com/avatar.jpg' },
      },
      responseType: 'UserResponse',
      statusCodes: [200, 400, 401, 422],
      example: {
        request: {
          firstName: 'John',
          lastName: 'Smith',
          phone: '+**********',
        },
        response: {
          success: true,
          message: 'Profile updated successfully',
          data: {
            user: {
              id: 'cuid123...',
              email: '<EMAIL>',
              firstName: 'John',
              lastName: 'Smith',
              role: 'CUSTOMER',
              isActive: true,
              isVerified: true,
            },
          },
        },
      },
    },
  },
  users: {
    getUsers: {
      path: '/api/users',
      method: 'GET',
      description: 'Get paginated list of users (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      queryParams: {
        page: { type: 'number', required: false, example: 1, default: 1 },
        limit: { type: 'number', required: false, example: 10, default: 10 },
        search: { type: 'string', required: false, example: 'john' },
        role: { type: 'string', required: false, example: 'CUSTOMER' },
        isActive: { type: 'boolean', required: false, example: true },
      },
      responseType: 'PaginatedUsersResponse',
      statusCodes: [200, 401, 403],
      example: {
        request: 'GET /api/users?page=1&limit=10&search=john',
        response: {
          success: true,
          data: {
            users: [
              {
                id: 'cuid123...',
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                role: 'CUSTOMER',
                isActive: true,
                isVerified: true,
              },
            ],
          },
          pagination: {
            page: 1,
            limit: 10,
            total: 25,
            totalPages: 3,
            hasNext: true,
            hasPrev: false,
          },
        },
      },
    },
    getUserById: {
      path: '/api/users/:id',
      method: 'GET',
      description: 'Get user by ID (admin or own profile)',
      requiresAuth: true,
      pathParams: {
        id: { type: 'string', required: true, example: 'cuid123...' },
      },
      responseType: 'UserResponse',
      statusCodes: [200, 401, 403, 404],
      example: {
        request: 'GET /api/users/cuid123...',
        response: {
          success: true,
          data: {
            user: {
              id: 'cuid123...',
              email: '<EMAIL>',
              firstName: 'John',
              lastName: 'Doe',
              role: 'CUSTOMER',
              isActive: true,
              isVerified: true,
            },
          },
        },
      },
    },
    createUser: {
      path: '/api/users',
      method: 'POST',
      description: 'Create new user (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      requestBody: {
        email: { type: 'string', required: true, example: '<EMAIL>' },
        password: { type: 'string', required: true, example: 'StrongPassword123!' },
        firstName: { type: 'string', required: true, example: 'Jane' },
        lastName: { type: 'string', required: true, example: 'Smith' },
        role: { type: 'string', required: false, example: 'CUSTOMER', enum: ['CUSTOMER', 'PROVIDER', 'ADMIN'] },
      },
      responseType: 'UserResponse',
      statusCodes: [201, 400, 401, 403, 409, 422],
      example: {
        request: {
          email: '<EMAIL>',
          password: 'StrongPassword123!',
          firstName: 'Jane',
          lastName: 'Smith',
          role: 'CUSTOMER',
        },
        response: {
          success: true,
          message: 'User created successfully',
          data: {
            user: {
              id: 'cuid456...',
              email: '<EMAIL>',
              firstName: 'Jane',
              lastName: 'Smith',
              role: 'CUSTOMER',
              isActive: true,
              isVerified: false,
            },
          },
        },
      },
    },
  },
  services: {
    list: {
      path: '/api/services',
      method: 'GET',
      description: 'List services with pagination and filters',
      requiresAuth: false,
      queryParams: {
        page: { type: 'number', required: false, example: 1 },
        limit: { type: 'number', required: false, example: 20 },
        search: { type: 'string', required: false, example: 'cards' },
        categoryId: { type: 'string', required: false, example: 'cat_1' },
        pricingType: { type: 'string', required: false, example: 'FIXED', enum: ['FIXED', 'VARIABLE', 'QUOTE'] },
        isActive: { type: 'boolean', required: false, example: true },
        sortBy: { type: 'string', required: false, example: 'sortOrder' },
        sortOrder: { type: 'string', required: false, example: 'asc', enum: ['asc', 'desc'] },
        minPrice: { type: 'number', required: false, example: 0 },
        maxPrice: { type: 'number', required: false, example: 100 },
      },
      responseType: 'SuccessResponse',
      statusCodes: [200],
    },
    search: {
      path: '/api/services/search',
      method: 'GET',
      description: 'Search services with advanced filters',
      requiresAuth: false,
      queryParams: {
        q: { type: 'string', required: false, example: 'business' },
        category: { type: 'string', required: false, example: 'business-cards' },
        minPrice: { type: 'number', required: false, example: 0 },
        maxPrice: { type: 'number', required: false, example: 100 },
        pricingType: { type: 'string', required: false, example: 'FIXED', enum: ['FIXED', 'VARIABLE', 'QUOTE'] },
        features: { type: 'array', required: false, example: ['matte', 'gloss'] },
        sortBy: { type: 'string', required: false, example: 'name', enum: ['name', 'price', 'popularity', 'rating'] },
        sortOrder: { type: 'string', required: false, example: 'asc', enum: ['asc', 'desc'] },
      },
      responseType: 'SuccessResponse',
      statusCodes: [200],
    },
    getById: {
      path: '/api/services/:id',
      method: 'GET',
      description: 'Get service by ID with full details',
      requiresAuth: false,
      pathParams: { id: { type: 'string', required: true, example: 'svc_1' } },
      responseType: 'SuccessResponse',
      statusCodes: [200, 404],
    },
    create: {
      path: '/api/services',
      method: 'POST',
      description: 'Create a new service (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      requestBody: {
        name: { type: 'string', required: true, example: 'Business Cards' },
        description: { type: 'string', required: true, example: 'Professional printing...' },
        detailedDesc: { type: 'string', required: false, example: 'Long description' },
        features: { type: 'array', required: false, example: ['Premium cardstock'] },
        notes: { type: 'string', required: false, example: 'Optional note' },
        categoryId: { type: 'string', required: true, example: 'cat_1' },
        image: { type: 'string', required: true, example: 'https://...' },
        basePrice: { type: 'number', required: true, example: 9.99 },
        pricingType: { type: 'string', required: false, example: 'FIXED', enum: ['FIXED', 'VARIABLE', 'QUOTE'] },
        isActive: { type: 'boolean', required: false, example: true },
        sortOrder: { type: 'number', required: false, example: 0 },
      },
      responseType: 'SuccessResponse',
      statusCodes: [201, 400, 401, 403, 404, 409, 422],
    },
    update: {
      path: '/api/services/:id',
      method: 'PUT',
      description: 'Update a service (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      pathParams: { id: { type: 'string', required: true, example: 'svc_1' } },
      requestBody: {
        name: { type: 'string', required: false, example: 'Updated name' },
        description: { type: 'string', required: false, example: 'Updated description' },
        detailedDesc: { type: 'string', required: false, example: 'Updated long description' },
        features: { type: 'array', required: false, example: ['Feature A'] },
        notes: { type: 'string', required: false, example: 'Notes' },
        categoryId: { type: 'string', required: false, example: 'cat_2' },
        image: { type: 'string', required: false, example: 'https://...' },
        basePrice: { type: 'number', required: false, example: 12.5 },
        pricingType: { type: 'string', required: false, example: 'VARIABLE', enum: ['FIXED', 'VARIABLE', 'QUOTE'] },
        isActive: { type: 'boolean', required: false, example: true },
        sortOrder: { type: 'number', required: false, example: 1 },
      },
      responseType: 'SuccessResponse',
      statusCodes: [200, 400, 401, 403, 404, 422],
    },
    remove: {
      path: '/api/services/:id',
      method: 'DELETE',
      description: 'Delete a service (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      pathParams: { id: { type: 'string', required: true, example: 'svc_1' } },
      responseType: 'SuccessResponse',
      statusCodes: [200, 401, 403, 404],
    },
    byPricingType: {
      path: '/api/services/pricing/:pricingType',
      method: 'GET',
      description: 'List services by pricing type',
      requiresAuth: false,
      pathParams: {
        pricingType: { type: 'string', required: true, example: 'FIXED', enum: ['FIXED', 'VARIABLE', 'QUOTE'] },
      },
      responseType: 'SuccessResponse',
      statusCodes: [200, 400],
    },
    popular: {
      path: '/api/services/popular',
      method: 'GET',
      description: 'Get popular services',
      requiresAuth: false,
      queryParams: { limit: { type: 'number', required: false, example: 10, default: 10 } },
      responseType: 'SuccessResponse',
      statusCodes: [200],
    },
    stats: {
      path: '/api/services/stats',
      method: 'GET',
      description: 'Get service statistics (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      responseType: 'SuccessResponse',
      statusCodes: [200, 401, 403],
    },
    exists: {
      path: '/api/services/exists/:id',
      method: 'GET',
      description: 'Check if service exists (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      pathParams: { id: { type: 'string', required: true, example: 'svc_1' } },
      responseType: 'SuccessResponse',
      statusCodes: [200, 401, 403],
    },
    count: {
      path: '/api/services/count',
      method: 'GET',
      description: 'Count total services (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      responseType: 'SuccessResponse',
      statusCodes: [200, 401, 403],
    },
    // Categories
    categories: {
      path: '/api/services/categories',
      method: 'GET',
      description: 'List service categories',
      requiresAuth: false,
      responseType: 'SuccessResponse',
      statusCodes: [200],
    },
    categoryById: {
      path: '/api/services/categories/:id',
      method: 'GET',
      description: 'Get service category by ID',
      requiresAuth: false,
      pathParams: { id: { type: 'string', required: true, example: 'cat_1' } },
      responseType: 'SuccessResponse',
      statusCodes: [200, 404],
    },
    categoryByRoute: {
      path: '/api/services/categories/route/:route',
      method: 'GET',
      description: 'Get service category by route slug',
      requiresAuth: false,
      pathParams: { route: { type: 'string', required: true, example: 'business-cards' } },
      responseType: 'SuccessResponse',
      statusCodes: [200, 404],
    },
    servicesByCategoryId: {
      path: '/api/services/categories/:categoryId/services',
      method: 'GET',
      description: 'List services by category ID',
      requiresAuth: false,
      pathParams: { categoryId: { type: 'string', required: true, example: 'cat_1' } },
      responseType: 'SuccessResponse',
      statusCodes: [200, 404],
    },
    servicesByCategoryRoute: {
      path: '/api/services/categories/route/:route/services',
      method: 'GET',
      description: 'List services by category route slug',
      requiresAuth: false,
      pathParams: { route: { type: 'string', required: true, example: 'business-cards' } },
      responseType: 'SuccessResponse',
      statusCodes: [200, 404],
    },
    createCategory: {
      path: '/api/services/categories',
      method: 'POST',
      description: 'Create a new service category (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      requestBody: {
        name: { type: 'string', required: true, example: 'Business Cards' },
        description: { type: 'string', required: false, example: 'Category description' },
        icon: { type: 'string', required: false, example: 'icon-name' },
        route: { type: 'string', required: true, example: 'business-cards' },
        isActive: { type: 'boolean', required: false, example: true },
        sortOrder: { type: 'number', required: false, example: 0 },
      },
      responseType: 'SuccessResponse',
      statusCodes: [201, 400, 401, 403, 409, 422],
    },
    updateCategory: {
      path: '/api/services/categories/:id',
      method: 'PUT',
      description: 'Update a service category (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      pathParams: { id: { type: 'string', required: true, example: 'cat_1' } },
      requestBody: {
        name: { type: 'string', required: false, example: 'New Name' },
        description: { type: 'string', required: false, example: 'Updated description' },
        icon: { type: 'string', required: false, example: 'icon-name' },
        route: { type: 'string', required: false, example: 'business-cards' },
        isActive: { type: 'boolean', required: false, example: true },
        sortOrder: { type: 'number', required: false, example: 1 },
      },
      responseType: 'SuccessResponse',
      statusCodes: [200, 400, 401, 403, 404, 409, 422],
    },
    deleteCategory: {
      path: '/api/services/categories/:id',
      method: 'DELETE',
      description: 'Delete a service category (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      pathParams: { id: { type: 'string', required: true, example: 'cat_1' } },
      responseType: 'SuccessResponse',
      statusCodes: [200, 401, 403, 404, 409],
    },
    // Form fields
    getFormFields: {
      path: '/api/services/:serviceId/form-fields',
      method: 'GET',
      description: 'Get service form fields by service ID',
      requiresAuth: false,
      pathParams: { serviceId: { type: 'string', required: true, example: 'svc_1' } },
      responseType: 'SuccessResponse',
      statusCodes: [200, 404],
    },
    createFormField: {
      path: '/api/services/:serviceId/form-fields',
      method: 'POST',
      description: 'Create a new form field for a service (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      pathParams: { serviceId: { type: 'string', required: true, example: 'svc_1' } },
      requestBody: {
        name: { type: 'string', required: true, example: 'size' },
        label: { type: 'string', required: true, example: 'Size' },
        type: {
          type: 'string',
          required: true,
          example: 'SELECT',
          enum: ['TEXT', 'NUMBER', 'SELECT', 'CHECKBOX', 'RADIO', 'FILE'],
        },
        required: { type: 'boolean', required: false, example: true },
        placeholder: { type: 'string', required: false, example: 'Choose a size' },
        defaultValue: { type: 'string', required: false, example: 'standard' },
        validation: { type: 'object', required: false, example: { min: 0, max: 10 } },
        sortOrder: { type: 'number', required: false, example: 0 },
        isActive: { type: 'boolean', required: false, example: true },
        options: {
          type: 'array',
          required: false,
          example: [{ value: 'standard', label: 'Standard', priceModifier: 0 }],
        },
      },
      responseType: 'SuccessResponse',
      statusCodes: [201, 400, 401, 403, 404, 422],
    },
    getFormFieldById: {
      path: '/api/services/form-fields/:fieldId',
      method: 'GET',
      description: 'Get form field by ID (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      pathParams: { fieldId: { type: 'string', required: true, example: 'fld_1' } },
      responseType: 'SuccessResponse',
      statusCodes: [200, 401, 403, 404],
    },
    updateFormField: {
      path: '/api/services/form-fields/:fieldId',
      method: 'PUT',
      description: 'Update form field (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      pathParams: { fieldId: { type: 'string', required: true, example: 'fld_1' } },
      requestBody: {
        name: { type: 'string', required: false, example: 'size' },
        label: { type: 'string', required: false, example: 'Card Size' },
        type: {
          type: 'string',
          required: false,
          example: 'SELECT',
          enum: ['TEXT', 'NUMBER', 'SELECT', 'CHECKBOX', 'RADIO', 'FILE'],
        },
        required: { type: 'boolean', required: false, example: true },
        placeholder: { type: 'string', required: false, example: 'Choose a size' },
        defaultValue: { type: 'string', required: false, example: 'standard' },
        validation: { type: 'object', required: false, example: { min: 0, max: 10 } },
        sortOrder: { type: 'number', required: false, example: 0 },
        isActive: { type: 'boolean', required: false, example: true },
      },
      responseType: 'SuccessResponse',
      statusCodes: [200, 400, 401, 403, 404, 422],
    },
    deleteFormField: {
      path: '/api/services/form-fields/:fieldId',
      method: 'DELETE',
      description: 'Delete form field (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      pathParams: { fieldId: { type: 'string', required: true, example: 'fld_1' } },
      responseType: 'SuccessResponse',
      statusCodes: [200, 401, 403, 404],
    },
    getFieldOptions: {
      path: '/api/services/form-fields/:fieldId/options',
      method: 'GET',
      description: 'Get options for a given form field',
      requiresAuth: false,
      pathParams: { fieldId: { type: 'string', required: true, example: 'fld_1' } },
      responseType: 'SuccessResponse',
      statusCodes: [200, 404],
    },
    createFieldOption: {
      path: '/api/services/form-fields/:fieldId/options',
      method: 'POST',
      description: 'Create a new option for a form field (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      pathParams: { fieldId: { type: 'string', required: true, example: 'fld_1' } },
      requestBody: {
        value: { type: 'string', required: true, example: 'standard' },
        label: { type: 'string', required: true, example: 'Standard' },
        priceModifier: { type: 'number', required: false, example: 0 },
        sortOrder: { type: 'number', required: false, example: 0 },
        isActive: { type: 'boolean', required: false, example: true },
      },
      responseType: 'SuccessResponse',
      statusCodes: [201, 400, 401, 403, 404, 422],
    },
    updateFieldOption: {
      path: '/api/services/form-fields/options/:optionId',
      method: 'PUT',
      description: 'Update an option for a form field (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      pathParams: { optionId: { type: 'string', required: true, example: 'opt_1' } },
      requestBody: {
        value: { type: 'string', required: false, example: 'standard' },
        label: { type: 'string', required: false, example: 'Standard' },
        priceModifier: { type: 'number', required: false, example: 0 },
        sortOrder: { type: 'number', required: false, example: 0 },
        isActive: { type: 'boolean', required: false, example: true },
      },
      responseType: 'SuccessResponse',
      statusCodes: [200, 400, 401, 403, 404, 422],
    },
    deleteFieldOption: {
      path: '/api/services/form-fields/options/:optionId',
      method: 'DELETE',
      description: 'Delete an option for a form field (admin only)',
      requiresAuth: true,
      requiredRole: 'ADMIN',
      pathParams: { optionId: { type: 'string', required: true, example: 'opt_1' } },
      responseType: 'SuccessResponse',
      statusCodes: [200, 401, 403, 404],
    },
    calculatePrice: {
      path: '/api/services/calculate-price',
      method: 'POST',
      description: 'Calculate service price from form selections',
      requiresAuth: true,
      requestBody: {
        serviceId: { type: 'string', required: true, example: 'svc_1' },
        formData: { type: 'object', required: true, example: { size: 'standard' } },
        quantity: { type: 'number', required: false, example: 2 },
        providerId: { type: 'string', required: false, example: 'prov_1' },
      },
      responseType: 'SuccessResponse',
      statusCodes: [200, 400, 401, 404, 422],
    },
    calculatePriceById: {
      path: '/api/services/:id/calculate-price',
      method: 'POST',
      description: 'Calculate service price (service ID in path)',
      requiresAuth: true,
      pathParams: { id: { type: 'string', required: true, example: 'svc_1' } },
      requestBody: {
        formData: { type: 'object', required: true, example: { size: 'standard' } },
        quantity: { type: 'number', required: false, example: 2 },
        providerId: { type: 'string', required: false, example: 'prov_1' },
      },
      responseType: 'SuccessResponse',
      statusCodes: [200, 400, 401, 404, 422],
    },
  },
};

// Data models/schemas
const dataModels = {
  User: {
    id: { type: 'string', description: 'Unique user identifier' },
    email: { type: 'string', description: 'User email address' },
    firstName: { type: 'string', description: 'User first name' },
    lastName: { type: 'string', description: 'User last name' },
    phone: { type: 'string', description: 'User phone number (optional)' },
    role: { type: 'string', enum: ['CUSTOMER', 'PROVIDER', 'ADMIN'], description: 'User role' },
    isActive: { type: 'boolean', description: 'Whether user account is active' },
    isVerified: { type: 'boolean', description: 'Whether user email is verified' },
    avatar: { type: 'string', description: 'User avatar URL (optional)' },
    createdAt: { type: 'string', format: 'date-time', description: 'Account creation timestamp' },
    updatedAt: { type: 'string', format: 'date-time', description: 'Last update timestamp' },
    lastLoginAt: { type: 'string', format: 'date-time', description: 'Last login timestamp (optional)' },
  },
  AuthTokens: {
    accessToken: { type: 'string', description: 'JWT access token' },
    refreshToken: { type: 'string', description: 'JWT refresh token' },
  },
  PaginationMeta: {
    page: { type: 'number', description: 'Current page number' },
    limit: { type: 'number', description: 'Items per page' },
    total: { type: 'number', description: 'Total number of items' },
    totalPages: { type: 'number', description: 'Total number of pages' },
    hasNext: { type: 'boolean', description: 'Whether there is a next page' },
    hasPrev: { type: 'boolean', description: 'Whether there is a previous page' },
    nextPage: { type: 'number', description: 'Next page number (optional)' },
    prevPage: { type: 'number', description: 'Previous page number (optional)' },
  },
  ErrorDetail: {
    field: { type: 'string', description: 'Field name that caused the error' },
    code: { type: 'string', description: 'Error code' },
    message: { type: 'string', description: 'Human-readable error message' },
    value: { type: 'any', description: 'Value that caused the error' },
    suggestion: { type: 'string', description: 'Suggestion to fix the error' },
  },
};

// Response types
const responseTypes = {
  SuccessResponse: {
    success: { type: 'boolean', value: true },
    data: { type: 'any', description: 'Response data' },
    message: { type: 'string', description: 'Optional success message' },
    pagination: { type: 'PaginationMeta', description: 'Pagination metadata (for paginated responses)' },
    links: { type: 'array', description: 'HATEOAS links for API discovery' },
    warnings: { type: 'array', description: 'Optional warnings about the response' },
    meta: { type: 'object', description: 'Response metadata' },
  },
  ErrorResponse: {
    success: { type: 'boolean', value: false },
    error: {
      type: { type: 'string', description: 'Error type' },
      message: { type: 'string', description: 'Human-readable error message' },
      code: { type: 'string', description: 'Error code' },
      details: { type: 'array', description: 'Validation error details' },
      retryAfter: { type: 'number', description: 'Retry after seconds (for rate limits)' },
      helpUrl: { type: 'string', description: 'Help URL for the error' },
    },
    meta: { type: 'object', description: 'Response metadata' },
  },
};

/**
 * API Documentation Endpoint
 */
router.get('/', (req: Request, res: Response) => {
  const serverConfig = config.getServerConfig();
  const baseUrl = serverConfig.apiBaseUrl;

  const documentation = {
    info: {
      title: 'PrintWeditt API Documentation',
      version: getAppVersion(),
      description: 'Comprehensive API documentation for PrintWeditt backend services',
      baseUrl: baseUrl,
      contact: {
        name: 'PrintWeditt Support',
        email: '<EMAIL>',
      },
    },
    servers: [
      {
        url: baseUrl,
        description: 'Production server',
      },
      {
        url: 'http://localhost:3001',
        description: 'Development server',
      },
    ],
    authentication: {
      type: 'Bearer Token',
      description: 'Most endpoints require authentication using JWT Bearer tokens',
      example: 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    },
    rateLimiting: {
      description: 'API endpoints are rate-limited to prevent abuse',
      limits: {
        auth: '5 requests per 15 minutes',
        general: '100 requests per 15 minutes',
      },
    },
    responseFormat: {
      description: 'All responses follow a unified format',
      success: responseTypes.SuccessResponse,
      error: responseTypes.ErrorResponse,
    },
    endpoints: apiEndpoints,
    models: dataModels,
    examples: {
      authentication: {
        register: apiEndpoints.auth.register.example,
        login: apiEndpoints.auth.login.example,
        refresh: apiEndpoints.auth.refresh.example,
      },
      users: {
        getUsers: apiEndpoints.users.getUsers.example,
        getUserById: apiEndpoints.users.getUserById.example,
        createUser: apiEndpoints.users.createUser.example,
      },
    },
    errorCodes: {
      '400': 'Bad Request - Invalid request data',
      '401': 'Unauthorized - Authentication required',
      '403': 'Forbidden - Insufficient permissions',
      '404': 'Not Found - Resource not found',
      '409': 'Conflict - Resource already exists',
      '422': 'Unprocessable Entity - Validation failed',
      '429': 'Too Many Requests - Rate limit exceeded',
      '500': 'Internal Server Error - Server error',
    },
    links: createApiLinks(baseUrl, 'docs'),
    tools: {
      interactive: `${baseUrl}/docs/interactive`,
      postman: `${baseUrl}/docs/postman`,
      openapi: `${baseUrl}/docs/openapi.json`,
    },
  };

  res.success(documentation, 'API documentation retrieved successfully');
});

/**
 * Interactive Documentation Endpoint
 */
router.get('/interactive', (req: Request, res: Response) => {
  const serverConfig = config.getServerConfig();
  const baseUrl = serverConfig.apiBaseUrl;

  const interactiveDoc = {
    title: 'Interactive API Documentation',
    description: 'Test API endpoints directly from this interface',
    baseUrl: baseUrl,
    endpoints: Object.entries(apiEndpoints).map(([category, endpoints]) => ({
      category,
      endpoints: Object.entries(endpoints).map(([name, endpoint]) => ({
        name,
        ...endpoint,
        testUrl: `${baseUrl}${endpoint.path}`,
        curlExample: generateCurlExample(endpoint, baseUrl),
      })),
    })),
    features: [
      'Live endpoint testing',
      'Request/response examples',
      'Authentication token management',
      'Response validation',
      'Error handling examples',
    ],
  };

  res.success(interactiveDoc, 'Interactive documentation loaded');
});

/**
 * Interactive Documentation HTML Interface
 */
router.get('/interactive.html', (req: Request, res: Response) => {
  const serverConfig = config.getServerConfig();
  const baseUrl = serverConfig.apiBaseUrl;

  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PrintWeditt API - Interactive Documentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2563eb;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            margin-bottom: 15px;
        }

        .auth-section {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .auth-section h3 {
            margin-bottom: 15px;
            color: #2563eb;
        }

        .token-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            font-family: monospace;
        }

        .endpoint-category {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .category-header {
            background: #2563eb;
            color: white;
            padding: 15px 20px;
            cursor: pointer;
            user-select: none;
        }

        .category-header:hover {
            background: #1d4ed8;
        }

        .endpoint-list {
            display: none;
        }

        .endpoint-list.active {
            display: block;
        }

        .endpoint-item {
            border-bottom: 1px solid #eee;
            padding: 20px;
        }

        .endpoint-item:last-child {
            border-bottom: none;
        }

        .endpoint-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .method {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
            min-width: 60px;
            text-align: center;
        }

        .method.get { background: #10b981; color: white; }
        .method.post { background: #3b82f6; color: white; }
        .method.put { background: #f59e0b; color: white; }
        .method.delete { background: #ef4444; color: white; }

        .endpoint-path {
            font-family: monospace;
            font-size: 14px;
            color: #666;
        }

        .endpoint-description {
            color: #666;
            margin-bottom: 15px;
        }

        .test-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .test-section h4 {
            margin-bottom: 10px;
            color: #333;
        }

        .form-group {
            margin-bottom: 10px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }

        .form-group textarea {
            height: 100px;
            resize: vertical;
        }

        .test-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .test-button:hover {
            background: #1d4ed8;
        }

        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .response-section {
            margin-top: 15px;
        }

        .response-section h4 {
            margin-bottom: 10px;
            color: #333;
        }

        .response-display {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .response-display.success {
            border-left: 4px solid #10b981;
        }

        .response-display.error {
            border-left: 4px solid #ef4444;
        }

        .loading {
            color: #666;
            font-style: italic;
        }

        .example-section {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .example-section h4 {
            margin-bottom: 10px;
            color: #0369a1;
        }

        .example-code {
            background: #1f2937;
            color: #f9fafb;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PrintWeditt API - Interactive Documentation</h1>
            <p>Test API endpoints directly from this interface. All responses follow the unified format with metadata, correlation IDs, and HATEOAS links.</p>
            <p><strong>Base URL:</strong> <code>${baseUrl}</code></p>
        </div>

        <div class="auth-section">
            <h3>Authentication</h3>
            <p>Enter your access token to test authenticated endpoints:</p>
            <input type="text" id="accessToken" class="token-input" placeholder="Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...">
            <button onclick="clearToken()" style="background: #6b7280; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">Clear Token</button>
        </div>

        <div id="endpoints-container">
            <!-- Endpoints will be loaded here -->
        </div>
    </div>

    <script>
        let endpoints = {};

        // Load endpoints from the API
        async function loadEndpoints() {
            try {
                const response = await fetch('${baseUrl}/api/docs');
                const data = await response.json();
                endpoints = data.data.endpoints;
                renderEndpoints();
            } catch (error) {
                console.error('Failed to load endpoints:', error);
                document.getElementById('endpoints-container').innerHTML =
                    '<div style="background: #fff; padding: 20px; border-radius: 8px; color: #ef4444;">Failed to load endpoints. Please check the API connection.</div>';
            }
        }

        function renderEndpoints() {
            const container = document.getElementById('endpoints-container');
            let html = '';

            Object.entries(endpoints).forEach(([category, categoryEndpoints]) => {
                html += \`
                    <div class="endpoint-category">
                        <div class="category-header" onclick="toggleCategory('\${category}')">
                            <h3>\${category.charAt(0).toUpperCase() + category.slice(1)} (\${Object.keys(categoryEndpoints).length} endpoints)</h3>
                        </div>
                        <div class="endpoint-list" id="\${category}-endpoints">
                \`;

                Object.entries(categoryEndpoints).forEach(([name, endpoint]) => {
                    html += renderEndpoint(name, endpoint);
                });

                html += '</div></div>';
            });

            container.innerHTML = html;
        }

        function renderEndpoint(name, endpoint) {
            const methodClass = endpoint.method.toLowerCase();
            const requiresAuth = endpoint.requiresAuth ? ' (Requires Auth)' : '';

            return \`
                <div class="endpoint-item">
                    <div class="endpoint-header">
                        <span class="method \${methodClass}">\${endpoint.method.toUpperCase()}</span>
                        <span class="endpoint-path">\${endpoint.path}</span>
                    </div>
                    <div class="endpoint-description">
                        \${endpoint.description}\${requiresAuth}
                    </div>

                    \${endpoint.example ? \`
                        <div class="example-section">
                            <h4>Example</h4>
                            <div class="example-code">\${JSON.stringify(endpoint.example, null, 2)}</div>
                        </div>
                    \` : ''}

                    <div class="test-section">
                        <h4>Test Endpoint</h4>
                        \${renderTestForm(name, endpoint)}
                    </div>

                    <div class="response-section">
                        <h4>Response</h4>
                        <div id="response-\${name}" class="response-display">Click "Send Request" to test the endpoint</div>
                    </div>
                </div>
            \`;
        }

        function renderTestForm(name, endpoint) {
            let html = '';

            // Path parameters
            if (endpoint.pathParams) {
                html += '<div class="form-group"><label>Path Parameters:</label>';
                Object.entries(endpoint.pathParams).forEach(([paramName, param]) => {
                    html += \`<input type="text" id="\${name}-\${paramName}" placeholder="\${param.example || paramName}" \${param.required ? 'required' : ''}>\`;
                });
                html += '</div>';
            }

            // Query parameters
            if (endpoint.queryParams) {
                html += '<div class="form-group"><label>Query Parameters:</label>';
                Object.entries(endpoint.queryParams).forEach(([paramName, param]) => {
                    html += \`<input type="text" id="\${name}-\${paramName}" placeholder="\${param.example || paramName}" \${param.required ? 'required' : ''}>\`;
                });
                html += '</div>';
            }

            // Request body
            if (endpoint.requestBody) {
                html += '<div class="form-group"><label>Request Body (JSON):</label>';
                const exampleBody = {};
                Object.entries(endpoint.requestBody).forEach(([key, field]) => {
                    if (field.example !== undefined) {
                        exampleBody[key] = field.example;
                    }
                });
                html += \`<textarea id="\${name}-body" placeholder="\${JSON.stringify(exampleBody, null, 2)}"></textarea>\`;
            }

            html += \`<button class="test-button" onclick="testEndpoint('\${name}', '\${endpoint.path}', '\${endpoint.method}')">Send Request</button>\`;

            return html;
        }

        async function testEndpoint(name, path, method) {
            const responseDiv = document.getElementById(\`response-\${name}\`);
            responseDiv.innerHTML = '<span class="loading">Sending request...</span>';
            responseDiv.className = 'response-display';

            try {
                // Build URL with path parameters
                let url = \`${baseUrl}\${path}\`;
                const endpoint = endpoints[getEndpointCategory(name)][name];

                if (endpoint.pathParams) {
                    Object.entries(endpoint.pathParams).forEach(([paramName, param]) => {
                        const value = document.getElementById(\`\${name}-\${paramName}\`).value;
                        if (value) {
                            url = url.replace(\`:\${paramName}\`, value);
                        }
                    });
                }

                // Add query parameters
                const queryParams = new URLSearchParams();
                if (endpoint.queryParams) {
                    Object.entries(endpoint.queryParams).forEach(([paramName, param]) => {
                        const value = document.getElementById(\`\${name}-\${paramName}\`).value;
                        if (value) {
                            queryParams.append(paramName, value);
                        }
                    });
                }

                if (queryParams.toString()) {
                    url += \`?\${queryParams.toString()}\`;
                }

                // Prepare headers
                const headers = {
                    'Content-Type': 'application/json'
                };

                const accessToken = document.getElementById('accessToken').value;
                if (accessToken && endpoint.requiresAuth) {
                    headers['Authorization'] = accessToken.startsWith('Bearer ') ? accessToken : \`Bearer \${accessToken}\`;
                }

                // Prepare request options
                const options = {
                    method: method.toUpperCase(),
                    headers: headers
                };

                // Add request body for POST/PUT requests
                if (['POST', 'PUT', 'PATCH'].includes(method.toUpperCase()) && endpoint.requestBody) {
                    const bodyElement = document.getElementById(\`\${name}-body\`);
                    if (bodyElement && bodyElement.value.trim()) {
                        options.body = bodyElement.value;
                    }
                }

                // Send request
                const response = await fetch(url, options);
                const responseData = await response.json();

                // Display response
                responseDiv.innerHTML = \`Status: \${response.status} \${response.statusText}\n\n\${JSON.stringify(responseData, null, 2)}\`;
                responseDiv.className = \`response-display \${response.ok ? 'success' : 'error'}\`;

            } catch (error) {
                responseDiv.innerHTML = \`Error: \${error.message}\`;
                responseDiv.className = 'response-display error';
            }
        }

        function getEndpointCategory(name) {
            for (const [category, categoryEndpoints] of Object.entries(endpoints)) {
                if (categoryEndpoints[name]) {
                    return category;
                }
            }
            return null;
        }

        function toggleCategory(category) {
            const element = document.getElementById(\`\${category}-endpoints\`);
            element.classList.toggle('active');
        }

        function clearToken() {
            document.getElementById('accessToken').value = '';
        }

        // Load endpoints when page loads
        document.addEventListener('DOMContentLoaded', loadEndpoints);
    </script>
</body>
</html>
  `;

  res.setHeader('Content-Type', 'text/html');
  res.send(html);
});

/**
 * Postman Collection Endpoint
 */
router.get('/postman', (req: Request, res: Response) => {
  const serverConfig = config.getServerConfig();
  const baseUrl = serverConfig.apiBaseUrl;

  const postmanCollection = {
    info: {
      name: 'PrintWeditt API',
      description: 'Complete API collection for PrintWeditt',
      version: getAppVersion(),
      schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json',
    },
    variable: [
      {
        key: 'baseUrl',
        value: baseUrl,
        type: 'string',
      },
      {
        key: 'accessToken',
        value: '',
        type: 'string',
      },
    ],
    item: generatePostmanItems(apiEndpoints, baseUrl),
  };

  res.success(postmanCollection, 'Postman collection generated');
});

/**
 * OpenAPI Specification Endpoint
 */
router.get('/openapi.json', (req: Request, res: Response) => {
  const serverConfig = config.getServerConfig();
  const baseUrl = serverConfig.apiBaseUrl;

  const openApiSpec = {
    openapi: '3.0.0',
    info: {
      title: 'PrintWeditt API',
      version: getAppVersion(),
      description: 'API specification for PrintWeditt backend services',
    },
    servers: [
      {
        url: baseUrl,
        description: 'Production server',
      },
    ],
    paths: generateOpenApiPaths(apiEndpoints),
    components: {
      schemas: generateOpenApiSchemas(dataModels),
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
  };

  res.success(openApiSpec, 'OpenAPI specification generated');
});

// Helper functions
function generateCurlExample(endpoint: any, baseUrl: string): string {
  const method = endpoint.method.toUpperCase();
  const url = `${baseUrl}${endpoint.path}`;

  let curl = `curl -X ${method} "${url}"`;

  if (endpoint.requiresAuth) {
    curl += ` \\\n  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"`;
  }

  if (endpoint.requestBody) {
    const exampleBody = Object.entries(endpoint.requestBody)
      .filter(([_, field]: [string, any]) => field.example !== undefined)
      .reduce((acc, [key, field]: [string, any]) => {
        acc[key] = field.example;
        return acc;
      }, {} as any);

    curl += ` \\\n  -H "Content-Type: application/json"`;
    curl += ` \\\n  -d '${JSON.stringify(exampleBody, null, 2)}'`;
  }

  return curl;
}

function generatePostmanItems(endpoints: any, baseUrl: string): any[] {
  const items: any[] = [];

  Object.entries(endpoints).forEach(([category, categoryEndpoints]: [string, any]) => {
    const categoryItem = {
      name: category.charAt(0).toUpperCase() + category.slice(1),
      item: Object.entries(categoryEndpoints).map(([name, endpoint]: [string, any]) => ({
        name: endpoint.description,
        request: {
          method: endpoint.method,
          header: generatePostmanHeaders(endpoint),
          url: {
            raw: `{{baseUrl}}${endpoint.path}`,
            host: ['{{baseUrl}}'],
            path: endpoint.path.split('/').filter(Boolean),
          },
          body: endpoint.requestBody
            ? {
                mode: 'raw',
                raw: JSON.stringify(generateExampleBody(endpoint.requestBody), null, 2),
                options: {
                  raw: {
                    language: 'json',
                  },
                },
              }
            : undefined,
        },
      })),
    };

    items.push(categoryItem);
  });

  return items;
}

function generatePostmanHeaders(endpoint: any): any[] {
  const headers = [
    {
      key: 'Content-Type',
      value: 'application/json',
    },
  ];

  if (endpoint.requiresAuth) {
    headers.push({
      key: 'Authorization',
      value: 'Bearer {{accessToken}}',
    });
  }

  return headers;
}

function generateExampleBody(requestBody: any): any {
  return Object.entries(requestBody)
    .filter(([_, field]: [string, any]) => field.example !== undefined)
    .reduce((acc, [key, field]: [string, any]) => {
      acc[key] = field.example;
      return acc;
    }, {} as any);
}

function generateOpenApiPaths(endpoints: any): any {
  const paths: any = {};

  Object.values(endpoints).forEach((categoryEndpoints: any) => {
    Object.values(categoryEndpoints).forEach((endpoint: any) => {
      const path = endpoint.path;
      const method = endpoint.method.toLowerCase();

      if (!paths[path]) {
        paths[path] = {};
      }

      paths[path][method] = {
        summary: endpoint.description,
        tags: [endpoint.path.split('/')[2] || 'general'],
        security: endpoint.requiresAuth ? [{ bearerAuth: [] }] : [],
        parameters: generateOpenApiParameters(endpoint),
        requestBody: endpoint.requestBody
          ? {
              required: true,
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: generateOpenApiProperties(endpoint.requestBody),
                    required: Object.entries(endpoint.requestBody)
                      .filter(([_, field]: [string, any]) => field.required)
                      .map(([key]: [string, any]) => key),
                  },
                },
              },
            }
          : undefined,
        responses: generateOpenApiResponses(endpoint),
      };
    });
  });

  return paths;
}

function generateOpenApiParameters(endpoint: any): any[] {
  const parameters: any[] = [];

  if (endpoint.pathParams) {
    Object.entries(endpoint.pathParams).forEach(([name, param]: [string, any]) => {
      parameters.push({
        name,
        in: 'path',
        required: true,
        schema: { type: param.type },
        description: param.description || `${name} parameter`,
      });
    });
  }

  if (endpoint.queryParams) {
    Object.entries(endpoint.queryParams).forEach(([name, param]: [string, any]) => {
      parameters.push({
        name,
        in: 'query',
        required: param.required || false,
        schema: { type: param.type },
        description: param.description || `${name} query parameter`,
      });
    });
  }

  return parameters;
}

function generateOpenApiProperties(requestBody: any): any {
  const properties: any = {};

  Object.entries(requestBody).forEach(([key, field]: [string, any]) => {
    properties[key] = {
      type: field.type,
      description: field.description || `${key} field`,
      example: field.example,
    };

    if (field.enum) {
      properties[key].enum = field.enum;
    }
  });

  return properties;
}

function generateOpenApiResponses(endpoint: any): any {
  const responses: any = {};

  endpoint.statusCodes.forEach((code: number) => {
    if (code >= 200 && code < 300) {
      responses[code] = {
        description: 'Success',
        content: {
          'application/json': {
            schema: {
              $ref: `#/components/schemas/${endpoint.responseType}`,
            },
          },
        },
      };
    } else {
      responses[code] = {
        description: getErrorDescription(code),
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse',
            },
          },
        },
      };
    }
  });

  return responses;
}

function generateOpenApiSchemas(models: any): any {
  const schemas: any = {};

  Object.entries(models).forEach(([name, model]: [string, any]) => {
    schemas[name] = {
      type: 'object',
      properties: Object.entries(model).reduce((acc, [key, field]: [string, any]) => {
        acc[key] = {
          type: field.type,
          description: field.description,
        };

        if (field.enum) {
          acc[key].enum = field.enum;
        }

        if (field.format) {
          acc[key].format = field.format;
        }

        return acc;
      }, {} as any),
    };
  });

  // Add response schemas
  schemas.SuccessResponse = {
    type: 'object',
    properties: {
      success: { type: 'boolean', example: true },
      data: { type: 'object' },
      message: { type: 'string' },
      meta: { type: 'object' },
    },
  };

  schemas.ErrorResponse = {
    type: 'object',
    properties: {
      success: { type: 'boolean', example: false },
      error: {
        type: 'object',
        properties: {
          type: { type: 'string' },
          message: { type: 'string' },
          code: { type: 'string' },
        },
      },
      meta: { type: 'object' },
    },
  };

  return schemas;
}

function getErrorDescription(code: number): string {
  const descriptions: { [key: number]: string } = {
    400: 'Bad Request',
    401: 'Unauthorized',
    403: 'Forbidden',
    404: 'Not Found',
    409: 'Conflict',
    422: 'Unprocessable Entity',
    429: 'Too Many Requests',
    500: 'Internal Server Error',
  };

  return descriptions[code] || 'Error';
}

export default router;
