import { ServicePricingRepository } from '@/repositories/ServicePricingRepository';

const mockPrisma = {
  service: {
    count: jest.fn(),
    groupBy: jest.fn(),
    aggregate: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
  },
  providerService: {
    count: jest.fn(),
    findFirst: jest.fn(),
  },
  service_categories: {
    findMany: jest.fn(),
  },
} as any;

describe('ServicePricingRepository', () => {
  let repo: ServicePricingRepository;

  beforeEach(() => {
    repo = new ServicePricingRepository(mockPrisma);
    jest.clearAllMocks();
  });

  it('getServiceStats - aggregates and maps stats', async () => {
    mockPrisma.service.count
      .mockResolvedValueOnce(10) // total
      .mockResolvedValueOnce(8); // active
    mockPrisma.service.groupBy
      .mockResolvedValueOnce([{ categoryId: 'cat-1', _count: { categoryId: 6 } }])
      .mockResolvedValueOnce([{ pricingType: 'FIXED', _count: { pricingType: 4 } }]);
    mockPrisma.service.aggregate.mockResolvedValue({ _avg: { basePrice: 12.5 } });
    mockPrisma.providerService.count.mockResolvedValue(20);
    mockPrisma.service_categories.findMany.mockResolvedValue([{ id: 'cat-1', name: 'Cards' }]);

    const stats = await repo.getServiceStats();
    expect(stats.total).toBe(10);
    expect(stats.active).toBe(8);
    expect(stats.byCategory['Cards']).toBe(6);
    expect(stats.byPricingType.FIXED).toBe(4);
    expect(stats.averageBasePrice).toBe(12.5);
    expect(stats.totalProviders).toBe(20);
  });

  it('getPopularServices - sorts by order count', async () => {
    mockPrisma.service.findMany.mockResolvedValue([
      { id: 's1', orders: [{ id: 1 }], service_categories: { name: 'Cat' }, providers: [] },
      { id: 's2', orders: [{ id: 1 }, { id: 2 }], service_categories: { name: 'Cat' }, providers: [] },
    ]);
    const result = await repo.getPopularServices(2);
    expect(mockPrisma.service.findMany).toHaveBeenCalledWith({
      where: { isActive: true },
      include: {
        service_categories: { select: { name: true } },
        providers: { where: { isActive: true }, select: { id: true } },
        orders: { select: { id: true } },
      },
      take: 2,
    });
    expect(result[0].id).toBe('s2');
  });

  it('getServicesByPricingType - filters and maps', async () => {
    mockPrisma.service.findMany.mockResolvedValue([
      { id: 's1', pricingType: 'FIXED', service_categories: { name: 'Cat' }, providers: [] },
    ]);
    await repo.getServicesByPricingType('FIXED' as any);
    expect(mockPrisma.service.findMany).toHaveBeenCalledWith({
      where: { pricingType: 'FIXED', isActive: true },
      include: {
        service_categories: { select: { name: true } },
        providers: { where: { isActive: true }, select: { id: true } },
      },
      orderBy: { sortOrder: 'asc' },
    });
  });

  it('calculateServicePrice - sums base + modifiers and multiplies by quantity', async () => {
    mockPrisma.service.findUnique.mockResolvedValue({
      id: 'svc-1',
      basePrice: 10,
      formFields: [
        {
          name: 'size',
          service_field_options: [
            { value: 'std', priceModifier: 0 },
            { value: 'xl', priceModifier: 5 },
          ],
        },
      ],
    });
    mockPrisma.providerService.findFirst.mockResolvedValue({ price: 50 });
    const result = await repo.calculateServicePrice({
      serviceId: 'svc-1',
      formData: { size: 'xl' },
      quantity: 3,
      providerId: 'p1',
    } as any);
    expect(result.basePrice).toBe(10);
    expect(result.subtotal).toBe(15);
    expect(result.total).toBe(45);
    expect(result.providerPrice).toBe(50);
  });

  it('countServices - delegates to prisma', async () => {
    mockPrisma.service.count.mockResolvedValue(7);
    await expect(repo.countServices()).resolves.toBe(7);
  });
});
