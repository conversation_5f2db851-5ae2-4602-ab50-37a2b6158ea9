import app from '@/app';
import { PrismaClient } from '@prisma/client';
import request from 'supertest';

const prisma = new PrismaClient();

describe('User Management Integration Tests', () => {
  let adminToken: string;
  let userToken: string;
  let adminUserId: string;
  let regularUserId: string;

  beforeEach(async () => {
    // Create admin user and get token
    const adminData = {
      email: '<EMAIL>',
      password: 'AdminPass123!',
      firstName: 'Admin',
      lastName: 'User',
      role: 'ADMIN',
    };

    const adminResponse = await request(app).post('/api/auth/register').send(adminData).expect(201);

    adminToken = adminResponse.body.data.tokens.accessToken;
    adminUserId = adminResponse.body.data.user.id;

    // Create regular user and get token
    const userData = {
      email: '<EMAIL>',
      password: 'UserPass123!',
      firstName: 'Regular',
      lastName: 'User',
      role: 'CUSTOMER',
    };

    const userResponse = await request(app).post('/api/auth/register').send(userData).expect(201);

    userToken = userResponse.body.data.tokens.accessToken;
    regularUserId = userResponse.body.data.user.id;
  });

  afterAll(async () => {
    // Disconnect from database (cleanup is handled by test setup)
    await prisma.$disconnect();
  });

  describe('GET /api/users/:id', () => {
    it('should get user by ID with admin token', async () => {
      const response = await request(app)
        .get(`/api/users/${regularUserId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user).toMatchObject({
        id: regularUserId,
        email: '<EMAIL>',
        firstName: 'Regular',
        lastName: 'User',
        role: 'CUSTOMER',
      });

      // Should not include sensitive fields
      expect(response.body.data.user).not.toHaveProperty('password');
      expect(response.body.data.user).not.toHaveProperty('passwordResetToken');
    });

    it('should allow user to get their own data', async () => {
      const response = await request(app)
        .get(`/api/users/${regularUserId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.data.user.id).toBe(regularUserId);
    });

    it('should deny access to other user data for non-admin', async () => {
      await request(app).get(`/api/users/${adminUserId}`).set('Authorization', `Bearer ${userToken}`).expect(403);
    });

    it('should require authentication', async () => {
      await request(app).get(`/api/users/${regularUserId}`).expect(401);
    });

    it('should return 404 for non-existent user', async () => {
      await request(app).get('/api/users/non-existent-id').set('Authorization', `Bearer ${adminToken}`).expect(404);
    });
  });

  describe('POST /api/users', () => {
    it('should create new user with admin token', async () => {
      const newUserData = {
        email: '<EMAIL>',
        password: 'NewUserPass123!',
        firstName: 'New',
        lastName: 'User',
        role: 'CUSTOMER',
      };

      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(newUserData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user).toMatchObject({
        email: '<EMAIL>',
        firstName: 'New',
        lastName: 'User',
        role: 'CUSTOMER',
      });

      // Clean up created user
      await prisma.user.delete({
        where: { id: response.body.data.user.id },
      });
    });

    it('should deny user creation for non-admin', async () => {
      const newUserData = {
        email: '<EMAIL>',
        password: 'DeniedPass123!',
        firstName: 'Denied',
        lastName: 'User',
        role: 'CUSTOMER',
      };

      await request(app).post('/api/users').set('Authorization', `Bearer ${userToken}`).send(newUserData).expect(403);
    });

    it('should validate required fields', async () => {
      const invalidData = {
        email: 'invalid-email',
        // missing password
        firstName: 'Test',
      };

      await request(app).post('/api/users').set('Authorization', `Bearer ${adminToken}`).send(invalidData).expect(400);
    });

    it('should prevent duplicate email', async () => {
      const duplicateData = {
        email: '<EMAIL>', // Already exists
        password: 'DupePass123!',
        firstName: 'Duplicate',
        lastName: 'User',
        role: 'CUSTOMER',
      };

      await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(duplicateData)
        .expect(409);
    });
  });

  describe('PUT /api/users/:id', () => {
    it('should update user with admin token', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'Name',
      };

      const response = await request(app)
        .put(`/api/users/${regularUserId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user).toMatchObject({
        id: regularUserId,
        firstName: 'Updated',
        lastName: 'Name',
      });
    });

    it('should allow user to update their own data', async () => {
      const updateData = {
        firstName: 'Self',
        lastName: 'Updated',
      };

      const response = await request(app)
        .put(`/api/users/${regularUserId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.data.user.firstName).toBe('Self');
    });

    it('should deny update of other user data for non-admin', async () => {
      const updateData = {
        firstName: 'Unauthorized',
      };

      await request(app)
        .put(`/api/users/${adminUserId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData)
        .expect(403);
    });

    it('should validate email uniqueness on update', async () => {
      const updateData = {
        email: '<EMAIL>', // Already exists
      };

      await request(app)
        .put(`/api/users/${regularUserId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(409);
    });

    it('should return 404 for non-existent user', async () => {
      const updateData = {
        firstName: 'Test',
      };

      await request(app)
        .put('/api/users/non-existent-id')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(404);
    });
  });

  describe('DELETE /api/users/:id', () => {
    let userToDelete: string;

    beforeEach(async () => {
      // Create a user to delete
      const userData = {
        email: '<EMAIL>',
        password: 'DeletePass123!',
        firstName: 'Delete',
        lastName: 'Me',
        role: 'CUSTOMER',
      };

      const response = await request(app).post('/api/auth/register').send(userData).expect(201);

      userToDelete = response.body.data.user.id;
    });

    it('should delete user with admin token', async () => {
      const response = await request(app)
        .delete(`/api/users/${userToDelete}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.deleted).toBe(true);

      // Verify user is deleted
      await request(app).get(`/api/users/${userToDelete}`).set('Authorization', `Bearer ${adminToken}`).expect(404);
    });

    it('should deny user deletion for non-admin', async () => {
      await request(app).delete(`/api/users/${userToDelete}`).set('Authorization', `Bearer ${userToken}`).expect(403);
    });

    it('should return 404 for non-existent user', async () => {
      await request(app).delete('/api/users/non-existent-id').set('Authorization', `Bearer ${adminToken}`).expect(404);
    });
  });

  describe('GET /api/users', () => {
    it('should get users list with admin token', async () => {
      const response = await request(app).get('/api/users').set('Authorization', `Bearer ${adminToken}`).expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.users)).toBe(true);
      expect(response.body.data.users.length).toBeGreaterThanOrEqual(2);
      expect(response.body.pagination).toBeDefined();
    });

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/users?page=1&limit=1')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.users).toHaveLength(1);
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(1);
    });

    it('should support search', async () => {
      const response = await request(app)
        .get('/api/users?search=admin')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.users.length).toBeGreaterThanOrEqual(1);
      expect(response.body.data.users[0].email).toContain('admin');
    });

    it('should support role filtering', async () => {
      const response = await request(app)
        .get('/api/users?role=ADMIN')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.users.length).toBeGreaterThanOrEqual(1);
      response.body.data.users.forEach((user: any) => {
        expect(user.role).toBe('ADMIN');
      });
    });

    it('should deny access for non-admin', async () => {
      await request(app).get('/api/users').set('Authorization', `Bearer ${userToken}`).expect(403);
    });
  });

  describe('GET /api/users/search', () => {
    it('should search users with admin token', async () => {
      const response = await request(app)
        .get('/api/users/search?search=user&limit=5')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.users)).toBe(true);
      expect(response.body.data.users.length).toBeLessThanOrEqual(5);
    });

    it('should return empty array for no matches', async () => {
      const response = await request(app)
        .get('/api/users/search?search=nonexistentuser12345')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.users).toHaveLength(0);
    });

    it('should deny access for non-admin', async () => {
      await request(app).get('/api/users/search?search=test').set('Authorization', `Bearer ${userToken}`).expect(403);
    });
  });

  describe('GET /api/users/stats', () => {
    it('should get user statistics with admin token', async () => {
      const response = await request(app)
        .get('/api/users/stats')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.stats).toMatchObject({
        totalUsers: expect.any(Number),
        activeUsers: expect.any(Number),
        verifiedUsers: expect.any(Number),
        newUsersThisMonth: expect.any(Number),
      });
    });

    it('should deny access for non-admin', async () => {
      await request(app).get('/api/users/stats').set('Authorization', `Bearer ${userToken}`).expect(403);
    });
  });

  describe('GET /api/users/role/:role', () => {
    it('should get users by role with admin token', async () => {
      const response = await request(app)
        .get('/api/users/role/ADMIN')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.users)).toBe(true);
      response.body.data.users.forEach((user: any) => {
        expect(user.role).toBe('ADMIN');
      });
    });

    it('should deny access for non-admin', async () => {
      await request(app).get('/api/users/role/CUSTOMER').set('Authorization', `Bearer ${userToken}`).expect(403);
    });
  });

  describe('POST /api/users/bulk/update', () => {
    let bulkUpdateUsers: string[] = [];

    beforeEach(async () => {
      // Create users for bulk operations
      for (let i = 1; i <= 3; i++) {
        const userData = {
          email: `bulk${i}@test.com`,
          password: 'BulkPass123!',
          firstName: `Bulk${String.fromCharCode(64 + i)}`, // Use letters A, B, C instead of numbers
          lastName: 'User',
          role: 'CUSTOMER',
        };

        const response = await request(app).post('/api/auth/register').send(userData).expect(201);

        bulkUpdateUsers.push(response.body.data.user.id);
      }
    });

    afterEach(async () => {
      // Clean up bulk update users
      await prisma.user_sessions.deleteMany({
        where: { userId: { in: bulkUpdateUsers } },
      });
      await prisma.user.deleteMany({
        where: { id: { in: bulkUpdateUsers } },
      });
      bulkUpdateUsers = [];
    });

    it('should bulk update users with admin token', async () => {
      const updateData = {
        userIds: bulkUpdateUsers,
        updates: {
          isActive: false,
        },
      };

      const response = await request(app)
        .post('/api/users/bulk/update')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.updatedCount).toBe(3);
    });

    it('should deny access for non-admin', async () => {
      const updateData = {
        userIds: [regularUserId],
        updates: { isActive: false },
      };

      await request(app)
        .post('/api/users/bulk/update')
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData)
        .expect(403);
    });
  });

  describe('POST /api/users/bulk/delete', () => {
    let bulkDeleteUsers: string[] = [];

    beforeEach(async () => {
      // Create users for bulk delete
      for (let i = 1; i <= 2; i++) {
        const userData = {
          email: `bulkdelete${i}@test.com`,
          password: 'BulkDeletePass123!',
          firstName: `BulkDelete${String.fromCharCode(64 + i)}`, // Use letters A, B instead of numbers
          lastName: 'User',
          role: 'CUSTOMER',
        };

        const response = await request(app).post('/api/auth/register').send(userData).expect(201);

        bulkDeleteUsers.push(response.body.data.user.id);
      }
    });

    it('should bulk delete users with admin token', async () => {
      const deleteData = {
        userIds: bulkDeleteUsers,
      };

      const response = await request(app)
        .post('/api/users/bulk/delete')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(deleteData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.deletedCount).toBe(2);

      // Reset for cleanup
      bulkDeleteUsers = [];
    });

    it('should deny access for non-admin', async () => {
      const deleteData = {
        userIds: bulkDeleteUsers,
      };

      await request(app)
        .post('/api/users/bulk/delete')
        .set('Authorization', `Bearer ${userToken}`)
        .send(deleteData)
        .expect(403);
    });

    afterEach(async () => {
      // Clean up any remaining users
      if (bulkDeleteUsers.length > 0) {
        await prisma.user_sessions.deleteMany({
          where: { userId: { in: bulkDeleteUsers } },
        });
        await prisma.user.deleteMany({
          where: { id: { in: bulkDeleteUsers } },
        });
      }
    });
  });

  describe('GET /api/users/exists/:email', () => {
    it('should check if user exists with admin token', async () => {
      const response = await request(app)
        .get('/api/users/exists/<EMAIL>')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.exists).toBe(true);
    });

    it('should return false for non-existent user', async () => {
      const response = await request(app)
        .get('/api/users/exists/<EMAIL>')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.exists).toBe(false);
    });

    it('should deny access for non-admin', async () => {
      await request(app).get('/api/users/exists/<EMAIL>').set('Authorization', `Bearer ${userToken}`).expect(403);
    });
  });

  describe('GET /api/users/count', () => {
    it('should get user count with admin token', async () => {
      const response = await request(app)
        .get('/api/users/count')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(typeof response.body.data.count).toBe('number');
      expect(response.body.data.count).toBeGreaterThanOrEqual(2);
    });

    it('should deny access for non-admin', async () => {
      await request(app).get('/api/users/count').set('Authorization', `Bearer ${userToken}`).expect(403);
    });
  });

  describe('GET /api/users/count/:role', () => {
    it('should get user count by role with admin token', async () => {
      const response = await request(app)
        .get('/api/users/count/ADMIN')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(typeof response.body.data.count).toBe('number');
      expect(response.body.data.count).toBeGreaterThanOrEqual(1);
    });

    it('should deny access for non-admin', async () => {
      await request(app).get('/api/users/count/CUSTOMER').set('Authorization', `Bearer ${userToken}`).expect(403);
    });
  });
});
