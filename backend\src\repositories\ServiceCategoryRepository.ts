import { PrismaClient } from '@prisma/client';
import {
  ServiceCategorySummary,
  ServiceCategoryDetail,
  CreateServiceCategoryData,
  UpdateServiceCategoryData,
} from '../types/service';
import { ServiceMapper } from '../utils/ServiceMapper';

/**
 * ServiceCategoryRepository
 * Handles all service category-related database operations
 * Follows Single Responsibility Principle - only category management
 */
export interface IServiceCategoryRepository {
  // Category CRUD operations
  getServiceCategories(): Promise<ServiceCategorySummary[]>;
  findServiceCategoryById(id: string): Promise<ServiceCategoryDetail | null>;
  findServiceCategoryByRoute(route: string): Promise<ServiceCategoryDetail | null>;
  createServiceCategory(categoryData: CreateServiceCategoryData): Promise<any>;
  updateServiceCategory(id: string, data: UpdateServiceCategoryData): Promise<any>;
  deleteServiceCategory(id: string): Promise<boolean>;
  
  // Category utility operations
  serviceCategoryExists(id: string): Promise<boolean>;
  countServicesByCategory(categoryId: string): Promise<number>;
}

export class ServiceCategoryRepository implements IServiceCategoryRepository {
  constructor(private prisma: PrismaClient) {}

  async getServiceCategories(): Promise<ServiceCategorySummary[]> {
    const categories = await this.prisma.service_categories.findMany({
      where: { isActive: true },
      include: {
        _count: {
          select: { services: true },
        },
      },
      orderBy: { sortOrder: 'asc' },
    });

    return ServiceMapper.toServiceCategorySummaryArray(categories);
  }

  async findServiceCategoryById(id: string): Promise<ServiceCategoryDetail | null> {
    const category = await this.prisma.service_categories.findUnique({
      where: { id },
      include: {
        services: {
          where: { isActive: true },
          include: {
            service_categories: {
              select: { name: true },
            },
            providers: {
              where: { isActive: true },
              select: { id: true },
            },
          },
          orderBy: { sortOrder: 'asc' },
        },
      },
    });

    if (!category) return null;
    return ServiceMapper.toServiceCategoryDetail(category);
  }

  async findServiceCategoryByRoute(route: string): Promise<ServiceCategoryDetail | null> {
    const category = await this.prisma.service_categories.findUnique({
      where: { route },
      include: {
        services: {
          where: { isActive: true },
          include: {
            service_categories: {
              select: { name: true },
            },
            providers: {
              where: { isActive: true },
              select: { id: true },
            },
          },
          orderBy: { sortOrder: 'asc' },
        },
      },
    });

    if (!category) return null;
    return ServiceMapper.toServiceCategoryDetail(category);
  }

  async createServiceCategory(categoryData: CreateServiceCategoryData): Promise<any> {
    return this.prisma.service_categories.create({
      data: {
        id: categoryData.id,
        name: categoryData.name,
        description: categoryData.description,
        icon: categoryData.icon,
        route: categoryData.route,
        isActive: categoryData.isActive,
        sortOrder: categoryData.sortOrder,
        updatedAt: new Date(),
      },
    });
  }

  async updateServiceCategory(id: string, data: UpdateServiceCategoryData): Promise<any> {
    return this.prisma.service_categories.update({
      where: { id },
      data,
    });
  }

  async deleteServiceCategory(id: string): Promise<boolean> {
    try {
      await this.prisma.service_categories.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  async serviceCategoryExists(id: string): Promise<boolean> {
    const count = await this.prisma.service_categories.count({
      where: { id },
    });
    return count > 0;
  }

  async countServicesByCategory(categoryId: string): Promise<number> {
    return this.prisma.service.count({
      where: { categoryId },
    });
  }
}