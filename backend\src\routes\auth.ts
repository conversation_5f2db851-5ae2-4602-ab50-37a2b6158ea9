import express from 'express';
import { PrismaClient, UserRole } from '@prisma/client';
import { 
  RegisterRequest, 
  LoginRequest, 
  RefreshTokenRequest,
  AuthenticatedRequest,
  SecurityEventType
} from '../types/auth';
import { 
  authenticate, 
  authRateLimit,
  verifyRefreshToken,
} from '../middleware/auth';
import { asyncHandler, AuthenticationError, ConflictError, NotFoundError } from '../middleware/errorHandler';
import {
  validate,
  registerSchema,
  loginSchema,
  refreshTokenSchema,
  changePasswordSchema,
  updateProfileSchema
} from '../validation/auth';
import {
  hashPassword,
  verifyPassword,
  generateTokenPair,
  revokeRefreshToken,
  revokeAllUserSessions,
  isRefreshTokenValid,
  incrementLoginAttempts,
  resetLoginAttempts,
  isAccountLocked,
  getAccountLockInfo,
  logSecurityEvent,
  getClientIP,
  getUserAgent,
  sanitizeUser,
  createApiResponse
} from '../utils/auth';

const router = express.Router();
const prisma = new PrismaClient();

// Register endpoint
router.post('/register', 
  authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
  validate(registerSchema),
  asyncHandler(async (req, res) => {
    const { email, password, firstName, lastName, phone }: RegisterRequest = req.body;
    const ipAddress = getClientIP(req);
    const userAgent = getUserAgent(req);

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    });

    if (existingUser) {
      await logSecurityEvent({
        type: SecurityEventType.LOGIN_FAILURE,
        email: email.toLowerCase(),
        ipAddress,
        userAgent,
        metadata: { reason: 'Email already registered' }
      });
      throw new ConflictError('Email address is already registered');
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create user
    const user = await prisma.user.create({
      data: {
        email: email.toLowerCase(),
        password: hashedPassword,
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        phone: phone?.trim() || null,
        role: UserRole.CUSTOMER,
        isActive: true,
        isVerified: false
      }
    });

    // Generate tokens
    const tokens = await generateTokenPair(
      user.id,
      user.email,
      user.role,
      ipAddress,
      userAgent
    );

    // Log security event
    await logSecurityEvent({
      type: SecurityEventType.LOGIN_SUCCESS,
      userId: user.id,
      email: user.email,
      ipAddress,
      userAgent,
      metadata: { action: 'registration' }
    });

    // Return response
    const sanitizedUser = sanitizeUser(user);
    res.status(201).json(createApiResponse(true, {
      user: sanitizedUser,
      tokens
    }, 'Account created successfully'));
  })
);

// Login endpoint
router.post('/login',
  authRateLimit(5, 15 * 60 * 1000), // 5 attempts per 15 minutes
  validate(loginSchema),
  asyncHandler(async (req, res) => {
    const { email, password }: LoginRequest = req.body;
    const ipAddress = getClientIP(req);
    const userAgent = getUserAgent(req);

    // Find user
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    });

    if (!user) {
      await logSecurityEvent({
        type: SecurityEventType.LOGIN_FAILURE,
        email: email.toLowerCase(),
        ipAddress,
        userAgent,
        metadata: { reason: 'User not found' }
      });
      throw new AuthenticationError('Invalid email or password');
    }

    // Check if account is locked
    const isLocked = await isAccountLocked(user.id);
    if (isLocked) {
      const lockInfo = await getAccountLockInfo(user.id);
      await logSecurityEvent({
        type: SecurityEventType.LOGIN_FAILURE,
        userId: user.id,
        email: user.email,
        ipAddress,
        userAgent,
        metadata: { reason: 'Account locked', lockInfo }
      });
      throw new AuthenticationError(
        `Account is temporarily locked. Try again after ${lockInfo?.lockExpires?.toLocaleTimeString()}`
      );
    }

    // Check if user is active
    if (!user.isActive) {
      await logSecurityEvent({
        type: SecurityEventType.LOGIN_FAILURE,
        userId: user.id,
        email: user.email,
        ipAddress,
        userAgent,
        metadata: { reason: 'Account deactivated' }
      });
      throw new AuthenticationError('Account is deactivated');
    }

    // Check password
    if (!user.password) {
      throw new AuthenticationError('Password not set. Please use password reset.');
    }

    const isPasswordValid = await verifyPassword(password, user.password);
    if (!isPasswordValid) {
      await incrementLoginAttempts(user.id);
      await logSecurityEvent({
        type: SecurityEventType.LOGIN_FAILURE,
        userId: user.id,
        email: user.email,
        ipAddress,
        userAgent,
        metadata: { reason: 'Invalid password' }
      });
      throw new AuthenticationError('Invalid email or password');
    }

    // Reset login attempts on successful login
    await resetLoginAttempts(user.id);

    // Generate tokens
    const tokens = await generateTokenPair(
      user.id,
      user.email,
      user.role,
      ipAddress,
      userAgent
    );

    // Log successful login
    await logSecurityEvent({
      type: SecurityEventType.LOGIN_SUCCESS,
      userId: user.id,
      email: user.email,
      ipAddress,
      userAgent
    });

    // Return response
    const sanitizedUser = sanitizeUser(user);
    res.json(createApiResponse(true, {
      user: sanitizedUser,
      tokens
    }, 'Login successful'));
  })
);

// Refresh token endpoint
router.post('/refresh',
  validate(refreshTokenSchema),
  asyncHandler(async (req, res) => {
    const { refreshToken }: RefreshTokenRequest = req.body;
    const ipAddress = getClientIP(req);
    const userAgent = getUserAgent(req);

    // Verify refresh token format
    const payload = verifyRefreshToken(refreshToken);

    // Check if refresh token exists and is valid
    const isValid = await isRefreshTokenValid(refreshToken);
    if (!isValid) {
      throw new AuthenticationError('Invalid or expired refresh token');
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: payload.userId }
    });

    if (!user || !user.isActive) {
      throw new AuthenticationError('User not found or inactive');
    }

    // Revoke old refresh token
    await revokeRefreshToken(refreshToken);

    // Generate new token pair
    const tokens = await generateTokenPair(
      user.id,
      user.email,
      user.role,
      ipAddress,
      userAgent
    );

    // Log token refresh
    await logSecurityEvent({
      type: SecurityEventType.TOKEN_REFRESH,
      userId: user.id,
      email: user.email,
      ipAddress,
      userAgent
    });

    res.json(createApiResponse(true, { tokens }, 'Tokens refreshed successfully'));
  })
);

// Logout endpoint
router.post('/logout',
  authenticate,
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const refreshToken = req.body.refreshToken;
    const ipAddress = getClientIP(req);
    const userAgent = getUserAgent(req);

    if (refreshToken) {
      await revokeRefreshToken(refreshToken);
    }

    // Log logout
    await logSecurityEvent({
      type: SecurityEventType.LOGOUT,
      userId: req.user!.id,
      email: req.user!.email,
      ipAddress,
      userAgent
    });

    res.json(createApiResponse(true, null, 'Logged out successfully'));
  })
);

// Logout from all devices
router.post('/logout-all',
  authenticate,
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const ipAddress = getClientIP(req);
    const userAgent = getUserAgent(req);

    await revokeAllUserSessions(req.user!.id);

    // Log logout from all devices
    await logSecurityEvent({
      type: SecurityEventType.LOGOUT,
      userId: req.user!.id,
      email: req.user!.email,
      ipAddress,
      userAgent,
      metadata: { action: 'logout_all_devices' }
    });

    res.json(createApiResponse(true, null, 'Logged out from all devices'));
  })
);

// Get current user profile
router.get('/me',
  authenticate,
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const user = await prisma.user.findUnique({
      where: { id: req.user!.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        isVerified: true,
        avatar: true,
        phone: true,
        createdAt: true,
        updatedAt: true,
        lastLoginAt: true
      }
    });

    if (!user) {
      throw new NotFoundError('User not found');
    }

    res.json(createApiResponse(true, { user }));
  })
);

// Update user profile
router.patch('/profile',
  authenticate,
  validate(updateProfileSchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { firstName, lastName, phone, avatar } = req.body;
    
    const updatedUser = await prisma.user.update({
      where: { id: req.user!.id },
      data: {
        ...(firstName && { firstName: firstName.trim() }),
        ...(lastName && { lastName: lastName.trim() }),
        ...(phone !== undefined && { phone: phone?.trim() || null }),
        ...(avatar !== undefined && { avatar: avatar?.trim() || null }),
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        isVerified: true,
        avatar: true,
        phone: true,
        createdAt: true,
        updatedAt: true,
        lastLoginAt: true
      }
    });

    res.json(createApiResponse(true, { user: updatedUser }, 'Profile updated successfully'));
  })
);

// Change password
router.post('/change-password',
  authenticate,
  validate(changePasswordSchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { currentPassword, newPassword } = req.body;
    const ipAddress = getClientIP(req);
    const userAgent = getUserAgent(req);

    // Get user with password
    const user = await prisma.user.findUnique({
      where: { id: req.user!.id },
      select: { id: true, email: true, password: true }
    });

    if (!user || !user.password) {
      throw new AuthenticationError('Current password verification failed');
    }

    // Verify current password
    const isCurrentPasswordValid = await verifyPassword(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new AuthenticationError('Current password is incorrect');
    }

    // Hash new password
    const hashedNewPassword = await hashPassword(newPassword);

    // Update password
    await prisma.user.update({
      where: { id: user.id },
      data: { password: hashedNewPassword }
    });

    // Revoke all existing sessions for security
    await revokeAllUserSessions(user.id);

    // Log password change
    await logSecurityEvent({
      type: SecurityEventType.PASSWORD_CHANGE,
      userId: user.id,
      email: user.email,
      ipAddress,
      userAgent
    });

    res.json(createApiResponse(true, null, 'Password changed successfully. Please log in again.'));
  })
);

// Get user sessions
router.get('/sessions',
  authenticate,
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const sessions = await prisma.user_sessions.findMany({
      where: { 
        userId: req.user!.id,
        isActive: true,
        expiresAt: { gt: new Date() }
      },
      select: {
        id: true,
        ipAddress: true,
        userAgent: true,
        createdAt: true,
        expiresAt: true,
        isActive: true
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json(createApiResponse(true, { sessions }));
  })
);

// Revoke specific session
router.delete('/sessions/:sessionId',
  authenticate,
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { sessionId } = req.params;

    // Find and revoke session
    const session = await prisma.user_sessions.findFirst({
      where: { 
        id: sessionId,
        userId: req.user!.id 
      }
    });

    if (!session) {
      throw new NotFoundError('Session not found');
    }

    await prisma.user_sessions.update({
      where: { id: sessionId },
      data: {
        isActive: false,
        revokedAt: new Date()
      }
    });

    res.json(createApiResponse(true, null, 'Session revoked successfully'));
  })
);

export default router;