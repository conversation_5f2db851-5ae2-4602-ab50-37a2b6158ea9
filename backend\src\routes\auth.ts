import express from 'express';
import { config } from '../config';
import { authenticate, authRateLimit } from '../middleware/auth';
import { requireRefreshTokenCookie } from '../middleware/refreshTokenValidator';
import { logAuthEvent } from '../middleware/requestLogger';
import { authLogger } from '../utils/logger';
import {
  changePasswordSchema,
  emailVerificationSchema,
  loginSchema,
  passwordResetConfirmSchema,
  passwordResetRequestSchema,
  refreshTokenSchema,
  registerSchema,
  updateProfileSchema,
  validate,
} from '../validation/auth';

// Import the new layered architecture components
import { createAuthController } from '../controllers/AuthController';
import { AuthRepository } from '../repositories/AuthRepository';
import { AuthService } from '../services/AuthService';
import { createEmailService } from '../services/EmailService';

const router = express.Router();

// Lazy initialization of auth components
let authController: any = null;

function getAuthController() {
  if (!authController) {
    // Use test Prisma client in test environment, otherwise use database service
    let prisma;
    if (process.env.NODE_ENV === 'test' && (global as any).__PRISMA__) {
      prisma = (global as any).__PRISMA__;
    } else {
      const { databaseService } = require('../services');
      prisma = databaseService.getClient();
    }

    // Create email service - use mock in test environment
    let emailService;
    if (process.env.NODE_ENV === 'test') {
      // Mock email service for tests
      emailService = {
        sendPasswordResetEmail: async () => Promise.resolve(),
        sendEmailVerification: async () => Promise.resolve(),
        sendWelcomeEmail: async () => Promise.resolve(),
      };
    } else {
      emailService = createEmailService(config.getEmailConfig());
    }

    const authRepository = new AuthRepository(prisma);
    const authService = new AuthService(
      authRepository,
      emailService,
      config.getJWTConfig(),
      config.getSecurityConfig()
    );
    authController = createAuthController(authService);

    // Log route setup with configuration context
    authLogger.info('Authentication controller initialized', {
      jwtIssuer: config.getJWTConfig().issuer,
      jwtAudience: config.getJWTConfig().audience,
      accessTokenExpiry: config.getJWTConfig().accessExpiresIn,
      refreshTokenExpiry: config.getJWTConfig().refreshExpiresIn,
      bcryptRounds: config.getSecurityConfig().bcryptRounds,
      rateLimitAuthMax: config.getSecurityConfig().rateLimitAuthMax,
      rateLimitWindowMs: config.getSecurityConfig().rateLimitWindowMs,
    });
  }
  return authController;
}

// Register endpoint
router.post(
  '/register',
  authRateLimit(config.getSecurityConfig().rateLimitAuthMax, config.getSecurityConfig().rateLimitWindowMs),
  validate(registerSchema),
  logAuthEvent('REGISTER'),
  (req, res, next) => getAuthController().register(req, res, next)
);

// Login endpoint
router.post(
  '/login',
  authRateLimit(config.getSecurityConfig().rateLimitAuthMax, config.getSecurityConfig().rateLimitWindowMs),
  validate(loginSchema),
  logAuthEvent('LOGIN'),
  (req, res, next) => getAuthController().login(req, res, next)
);

// Refresh token endpoint - 2025 Security: Cookie-only validation
router.post(
  '/refresh',
  requireRefreshTokenCookie,
  validate(refreshTokenSchema),
  logAuthEvent('TOKEN_REFRESH'),
  (req, res, next) => getAuthController().refreshTokens(req, res, next)
);

// Logout endpoint
router.post('/logout', authenticate, logAuthEvent('LOGOUT'), (req, res, next) =>
  getAuthController().logout(req, res, next)
);

// Logout from all devices
router.post('/logout-all', authenticate, logAuthEvent('LOGOUT'), (req, res, next) =>
  getAuthController().logoutAllDevices(req, res, next)
);

// Get current user profile
router.get('/profile', authenticate, (req, res, next) => getAuthController().getProfile(req, res, next));

// Update user profile
router.put('/profile', authenticate, validate(updateProfileSchema), (req, res, next) =>
  getAuthController().updateProfile(req, res, next)
);

// Change password
router.put(
  '/change-password',
  authenticate,
  validate(changePasswordSchema),
  logAuthEvent('PASSWORD_CHANGE'),
  (req, res, next) => getAuthController().changePassword(req, res, next)
);

// Get user sessions
router.get('/sessions', authenticate, (req, res, next) => getAuthController().getUserSessions(req, res, next));

// Revoke specific session
router.delete('/sessions/:sessionId', authenticate, (req, res, next) =>
  getAuthController().revokeSession(req, res, next)
);

// Password reset request
router.post(
  '/password-reset/request',
  authRateLimit(config.getSecurityConfig().rateLimitAuthMax, config.getSecurityConfig().rateLimitWindowMs),
  validate(passwordResetRequestSchema),
  logAuthEvent('PASSWORD_RESET_REQUEST'),
  (req, res, next) => getAuthController().requestPasswordReset(req, res, next)
);

// Password reset confirmation
router.post(
  '/password-reset/confirm',
  authRateLimit(config.getSecurityConfig().rateLimitAuthMax, config.getSecurityConfig().rateLimitWindowMs),
  validate(passwordResetConfirmSchema),
  logAuthEvent('PASSWORD_RESET_CONFIRM'),
  (req, res, next) => getAuthController().resetPassword(req, res, next)
);

// Send email verification
router.post('/email/verify/send', authenticate, logAuthEvent('EMAIL_VERIFICATION_SEND'), (req, res, next) =>
  getAuthController().sendEmailVerification(req, res, next)
);

// Verify email
router.post(
  '/email/verify/confirm',
  validate(emailVerificationSchema),
  logAuthEvent('EMAIL_VERIFICATION_CONFIRM'),
  (req, res, next) => getAuthController().verifyEmail(req, res, next)
);

export default router;
