import express from 'express';
import {PrismaClient} from '@prisma/client';
import {AuthenticatedRequest} from '../types/auth';
import {authenticate, authRateLimit} from '../middleware/auth';
import {validate} from '../validation/auth';
import {
	registerSchema,
	loginSchema,
	refreshTokenSchema,
	changePasswordSchema,
	updateProfileSchema,
} from '../validation/auth';
import {logAuthEvent} from '../middleware/requestLogger';
import {authLogger} from '../utils/logger';
import {config} from '../config';

// Import the new layered architecture components
import {AuthRepository} from '../repositories/AuthRepository';
import {AuthService} from '../services/AuthService';
import {createAuthController} from '../controllers/AuthController';

const router = express.Router();

// Dependency injection setup with configuration
const prisma = new PrismaClient();
const authRepository = new AuthRepository(prisma);
const authService = new AuthService(
	authRepository,
	config.getJWTConfig(),
	config.getSecurityConfig()
);
const authController = createAuthController(authService);

// Log route setup with configuration context
authLogger.info('Setting up authentication routes', {
	jwtIssuer: config.getJWTConfig().issuer,
	jwtAudience: config.getJWTConfig().audience,
	accessTokenExpiry: config.getJWTConfig().accessExpiresIn,
	refreshTokenExpiry: config.getJWTConfig().refreshExpiresIn,
	bcryptRounds: config.getSecurityConfig().bcryptRounds,
	rateLimitAuthMax: config.getSecurityConfig().rateLimitAuthMax,
	rateLimitWindowMs: config.getSecurityConfig().rateLimitWindowMs,
});

// Register endpoint
router.post(
	'/register',
	authRateLimit(
		config.getSecurityConfig().rateLimitAuthMax,
		config.getSecurityConfig().rateLimitWindowMs
	),
	validate(registerSchema),
	logAuthEvent('REGISTER'),
	authController.register
);

// Login endpoint
router.post(
	'/login',
	authRateLimit(
		config.getSecurityConfig().rateLimitAuthMax,
		config.getSecurityConfig().rateLimitWindowMs
	),
	validate(loginSchema),
	logAuthEvent('LOGIN'),
	authController.login
);

// Refresh token endpoint
router.post(
	'/refresh',
	validate(refreshTokenSchema),
	logAuthEvent('TOKEN_REFRESH'),
	authController.refreshTokens
);

// Logout endpoint
router.post(
	'/logout',
	authenticate,
	logAuthEvent('LOGOUT'),
	authController.logout
);

// Logout from all devices
router.post(
	'/logout-all',
	authenticate,
	logAuthEvent('LOGOUT'),
	authController.logoutAllDevices
);

// Get current user profile
router.get('/me', authenticate, authController.getProfile);

// Update user profile
router.patch(
	'/profile',
	authenticate,
	validate(updateProfileSchema),
	authController.updateProfile
);

// Change password
router.post(
	'/change-password',
	authenticate,
	validate(changePasswordSchema),
	logAuthEvent('PASSWORD_CHANGE'),
	authController.changePassword
);

// Get user sessions
router.get('/sessions', authenticate, authController.getUserSessions);

// Revoke specific session
router.delete(
	'/sessions/:sessionId',
	authenticate,
	authController.revokeSession
);

export default router;
