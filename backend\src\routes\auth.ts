import express from 'express';
import {PrismaClient} from '@prisma/client';
import {AuthenticatedRequest} from '../types/auth';
import {authenticate, authRateLimit} from '../middleware/auth';
import {validate} from '../validation/auth';
import {
	registerSchema,
	loginSchema,
	refreshTokenSchema,
	changePasswordSchema,
	updateProfileSchema,
} from '../validation/auth';
import {logAuthEvent} from '../middleware/requestLogger';
import {authLogger} from '../utils/logger';
import {config} from '../config';

// Import the new layered architecture components
import {AuthRepository} from '../repositories/AuthRepository';
import {AuthService} from '../services/AuthService';
import {createAuthController} from '../controllers/AuthController';

const router = express.Router();

// Lazy initialization of auth components
let authController: any = null;

function getAuthController() {
	if (!authController) {
		// Import database service at runtime after initialization
		const {databaseService} = require('../services');
		const prisma = databaseService.getClient();
		const authRepository = new AuthRepository(prisma);
		const authService = new AuthService(
			authRepository,
			config.getJWTConfig(),
			config.getSecurityConfig()
		);
		authController = createAuthController(authService);
		
		// Log route setup with configuration context
		authLogger.info('Authentication controller initialized', {
			jwtIssuer: config.getJWTConfig().issuer,
			jwtAudience: config.getJWTConfig().audience,
			accessTokenExpiry: config.getJWTConfig().accessExpiresIn,
			refreshTokenExpiry: config.getJWTConfig().refreshExpiresIn,
			bcryptRounds: config.getSecurityConfig().bcryptRounds,
			rateLimitAuthMax: config.getSecurityConfig().rateLimitAuthMax,
			rateLimitWindowMs: config.getSecurityConfig().rateLimitWindowMs,
		});
	}
	return authController;
}

// Register endpoint
router.post(
	'/register',
	authRateLimit(
		config.getSecurityConfig().rateLimitAuthMax,
		config.getSecurityConfig().rateLimitWindowMs
	),
	validate(registerSchema),
	logAuthEvent('REGISTER'),
	(req, res, next) => getAuthController().register(req, res, next)
);

// Login endpoint
router.post(
	'/login',
	authRateLimit(
		config.getSecurityConfig().rateLimitAuthMax,
		config.getSecurityConfig().rateLimitWindowMs
	),
	validate(loginSchema),
	logAuthEvent('LOGIN'),
	(req, res, next) => getAuthController().login(req, res, next)
);

// Refresh token endpoint
router.post(
	'/refresh',
	validate(refreshTokenSchema),
	logAuthEvent('TOKEN_REFRESH'),
	(req, res, next) => getAuthController().refreshTokens(req, res, next)
);

// Logout endpoint
router.post(
	'/logout',
	authenticate,
	logAuthEvent('LOGOUT'),
	(req, res, next) => getAuthController().logout(req, res, next)
);

// Logout from all devices
router.post(
	'/logout-all',
	authenticate,
	logAuthEvent('LOGOUT'),
	(req, res, next) => getAuthController().logoutAllDevices(req, res, next)
);

// Get current user profile
router.get('/me', authenticate, (req, res, next) => getAuthController().getProfile(req, res, next));

// Update user profile
router.patch(
	'/profile',
	authenticate,
	validate(updateProfileSchema),
	(req, res, next) => getAuthController().updateProfile(req, res, next)
);

// Change password
router.post(
	'/change-password',
	authenticate,
	validate(changePasswordSchema),
	logAuthEvent('PASSWORD_CHANGE'),
	(req, res, next) => getAuthController().changePassword(req, res, next)
);

// Get user sessions
router.get('/sessions', authenticate, (req, res, next) => getAuthController().getUserSessions(req, res, next));

// Revoke specific session
router.delete(
	'/sessions/:sessionId',
	authenticate,
	(req, res, next) => getAuthController().revokeSession(req, res, next)
);

export default router;
