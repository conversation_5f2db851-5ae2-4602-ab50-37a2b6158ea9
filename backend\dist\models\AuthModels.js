"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionInfo = exports.AuthenticationResponse = exports.UserProfile = exports.PasswordChangeData = exports.ProfileUpdateData = exports.LoginData = exports.RegistrationData = exports.AuthenticationModel = void 0;
class AuthenticationModel {
    static createRegistrationData(request) {
        return new RegistrationData(request.email, request.password, request.firstName, request.lastName, request.phone);
    }
    static createLoginData(request) {
        return new LoginData(request.email, request.password);
    }
    static createProfileUpdateData(request) {
        return new ProfileUpdateData(request.firstName, request.lastName, request.phone, request.avatar);
    }
    static createPasswordChangeData(request) {
        return new PasswordChangeData(request.currentPassword, request.newPassword);
    }
}
exports.AuthenticationModel = AuthenticationModel;
class RegistrationData {
    email;
    password;
    firstName;
    lastName;
    phone;
    constructor(email, password, firstName, lastName, phone) {
        this.email = email;
        this.password = password;
        this.firstName = firstName;
        this.lastName = lastName;
        this.phone = phone;
        this.validate();
    }
    validate() {
        if (!this.email || !this.isValidEmail(this.email)) {
            throw new Error('Valid email is required');
        }
        if (!this.password || !this.isValidPassword(this.password)) {
            throw new Error('Password must meet security requirements');
        }
        if (!this.firstName?.trim()) {
            throw new Error('First name is required');
        }
        if (!this.lastName?.trim()) {
            throw new Error('Last name is required');
        }
        if (this.phone && !this.isValidPhone(this.phone)) {
            throw new Error('Invalid phone number format');
        }
    }
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email) && email.length <= 255;
    }
    isValidPassword(password) {
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,128}$/;
        return passwordRegex.test(password);
    }
    isValidPhone(phone) {
        const phoneRegex = /^\+?[1-9]\d{1,14}$/;
        return phoneRegex.test(phone) && phone.length <= 20;
    }
    getStorageData() {
        return {
            email: this.email.toLowerCase().trim(),
            firstName: this.firstName.trim(),
            lastName: this.lastName.trim(),
            phone: this.phone?.trim() || null
        };
    }
}
exports.RegistrationData = RegistrationData;
class LoginData {
    email;
    password;
    constructor(email, password) {
        this.email = email;
        this.password = password;
        this.validate();
    }
    validate() {
        if (!this.email?.trim()) {
            throw new Error('Email is required');
        }
        if (!this.password) {
            throw new Error('Password is required');
        }
        if (this.password.length > 128) {
            throw new Error('Password is too long');
        }
    }
    getNormalizedEmail() {
        return this.email.toLowerCase().trim();
    }
}
exports.LoginData = LoginData;
class ProfileUpdateData {
    firstName;
    lastName;
    phone;
    avatar;
    constructor(firstName, lastName, phone, avatar) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.phone = phone;
        this.avatar = avatar;
        this.validate();
    }
    validate() {
        if (this.firstName !== undefined && (!this.firstName || !this.isValidName(this.firstName))) {
            throw new Error('Invalid first name');
        }
        if (this.lastName !== undefined && (!this.lastName || !this.isValidName(this.lastName))) {
            throw new Error('Invalid last name');
        }
        if (this.phone !== undefined && this.phone !== null && !this.isValidPhone(this.phone)) {
            throw new Error('Invalid phone number format');
        }
        if (this.avatar !== undefined && this.avatar !== null && !this.isValidUrl(this.avatar)) {
            throw new Error('Invalid avatar URL');
        }
    }
    isValidName(name) {
        const nameRegex = /^[a-zA-Z\s'-]+$/;
        return nameRegex.test(name.trim()) && name.trim().length >= 1 && name.length <= 150;
    }
    isValidPhone(phone) {
        const phoneRegex = /^\+?[1-9]\d{1,14}$/;
        return phoneRegex.test(phone) && phone.length <= 20;
    }
    isValidUrl(url) {
        try {
            new URL(url);
            return url.length <= 500;
        }
        catch {
            return false;
        }
    }
    hasUpdates() {
        return this.firstName !== undefined ||
            this.lastName !== undefined ||
            this.phone !== undefined ||
            this.avatar !== undefined;
    }
    getStorageData() {
        const data = {};
        if (this.firstName !== undefined)
            data.firstName = this.firstName.trim();
        if (this.lastName !== undefined)
            data.lastName = this.lastName.trim();
        if (this.phone !== undefined)
            data.phone = this.phone?.trim() || null;
        if (this.avatar !== undefined)
            data.avatar = this.avatar?.trim() || null;
        return data;
    }
}
exports.ProfileUpdateData = ProfileUpdateData;
class PasswordChangeData {
    currentPassword;
    newPassword;
    constructor(currentPassword, newPassword) {
        this.currentPassword = currentPassword;
        this.newPassword = newPassword;
        this.validate();
    }
    validate() {
        if (!this.currentPassword) {
            throw new Error('Current password is required');
        }
        if (this.currentPassword.length > 128) {
            throw new Error('Current password is too long');
        }
        if (!this.newPassword || !this.isValidPassword(this.newPassword)) {
            throw new Error('New password must meet security requirements');
        }
        if (this.currentPassword === this.newPassword) {
            throw new Error('New password must be different from current password');
        }
    }
    isValidPassword(password) {
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,128}$/;
        return passwordRegex.test(password);
    }
}
exports.PasswordChangeData = PasswordChangeData;
class UserProfile {
    id;
    email;
    firstName;
    lastName;
    role;
    isActive;
    isVerified;
    avatar;
    phone;
    createdAt;
    updatedAt;
    lastLoginAt;
    constructor(id, email, firstName, lastName, role, isActive, isVerified, avatar, phone, createdAt, updatedAt, lastLoginAt) {
        this.id = id;
        this.email = email;
        this.firstName = firstName;
        this.lastName = lastName;
        this.role = role;
        this.isActive = isActive;
        this.isVerified = isVerified;
        this.avatar = avatar;
        this.phone = phone;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.lastLoginAt = lastLoginAt;
    }
    static fromAuthUser(user) {
        return new UserProfile(user.id, user.email, user.firstName, user.lastName, user.role, user.isActive, user.isVerified, user.avatar, user.phone, user.createdAt, user.updatedAt, user.lastLoginAt);
    }
    getDisplayName() {
        return `${this.firstName} ${this.lastName}`.trim();
    }
    isProfileComplete() {
        return !!(this.firstName && this.lastName && this.email);
    }
    getCompletionPercentage() {
        let completed = 0;
        const total = 5;
        if (this.firstName)
            completed++;
        if (this.lastName)
            completed++;
        if (this.email)
            completed++;
        if (this.phone)
            completed++;
        if (this.avatar)
            completed++;
        return Math.round((completed / total) * 100);
    }
    toJSON() {
        return {
            id: this.id,
            email: this.email,
            firstName: this.firstName,
            lastName: this.lastName,
            role: this.role,
            isActive: this.isActive,
            isVerified: this.isVerified,
            avatar: this.avatar,
            phone: this.phone,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            lastLoginAt: this.lastLoginAt,
            displayName: this.getDisplayName(),
            isProfileComplete: this.isProfileComplete(),
            completionPercentage: this.getCompletionPercentage()
        };
    }
}
exports.UserProfile = UserProfile;
class AuthenticationResponse {
    user;
    tokens;
    constructor(user, tokens) {
        this.user = user;
        this.tokens = tokens;
    }
    toApiResponse() {
        return {
            success: true,
            data: {
                user: this.user,
                tokens: this.tokens
            },
            message: 'Authentication successful'
        };
    }
}
exports.AuthenticationResponse = AuthenticationResponse;
class SessionInfo {
    id;
    ipAddress;
    userAgent;
    createdAt;
    expiresAt;
    isActive;
    constructor(id, ipAddress, userAgent, createdAt, expiresAt, isActive) {
        this.id = id;
        this.ipAddress = ipAddress;
        this.userAgent = userAgent;
        this.createdAt = createdAt;
        this.expiresAt = expiresAt;
        this.isActive = isActive;
    }
    isCurrent(currentIpAddress, currentUserAgent) {
        return this.ipAddress === currentIpAddress && this.userAgent === currentUserAgent;
    }
    getDeviceInfo() {
        const userAgent = this.userAgent.toLowerCase();
        let browser = 'Unknown';
        let os = 'Unknown';
        let device = 'Desktop';
        if (userAgent.includes('chrome'))
            browser = 'Chrome';
        else if (userAgent.includes('firefox'))
            browser = 'Firefox';
        else if (userAgent.includes('safari'))
            browser = 'Safari';
        else if (userAgent.includes('edge'))
            browser = 'Edge';
        if (userAgent.includes('windows'))
            os = 'Windows';
        else if (userAgent.includes('mac'))
            os = 'macOS';
        else if (userAgent.includes('linux'))
            os = 'Linux';
        else if (userAgent.includes('android'))
            os = 'Android';
        else if (userAgent.includes('ios'))
            os = 'iOS';
        if (userAgent.includes('mobile') || userAgent.includes('android') || userAgent.includes('iphone')) {
            device = 'Mobile';
        }
        else if (userAgent.includes('tablet') || userAgent.includes('ipad')) {
            device = 'Tablet';
        }
        return { browser, os, device };
    }
    getTimeUntilExpiration() {
        return Math.max(0, this.expiresAt.getTime() - Date.now());
    }
    isExpired() {
        return this.expiresAt <= new Date();
    }
    toJSON() {
        const deviceInfo = this.getDeviceInfo();
        return {
            id: this.id,
            ipAddress: this.ipAddress,
            createdAt: this.createdAt,
            expiresAt: this.expiresAt,
            isActive: this.isActive,
            isExpired: this.isExpired(),
            timeUntilExpiration: this.getTimeUntilExpiration(),
            deviceInfo
        };
    }
}
exports.SessionInfo = SessionInfo;
exports.default = {
    AuthenticationModel,
    RegistrationData,
    LoginData,
    ProfileUpdateData,
    PasswordChangeData,
    UserProfile,
    AuthenticationResponse,
    SessionInfo
};
//# sourceMappingURL=AuthModels.js.map