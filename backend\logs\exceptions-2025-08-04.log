2025-08-04 02:46:07 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::3000
Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1912:16)
    at listenInCluster (node:net:1969:12)
    at Server.listen (node:net:2074:7)
    at Function.listen (C:\projects\PrintWedittV1\backend\node_modules\express\lib\application.js:635:24)
    at startServer (C:\projects\PrintWedittV1\backend\src\index.ts:118:7)
{
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 3000
  },
  "exception": true,
  "date": "Mon Aug 04 2025 02:46:07 GMT+0100 (GMT+01:00)",
  "process": {
    "pid": 7540,
    "uid": null,
    "gid": null,
    "cwd": "C:\\projects\\PrintWedittV1\\backend",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.12.0",
    "argv": [
      "C:\\projects\\PrintWedittV1\\backend\\node_modules\\ts-node\\dist\\bin.js",
      "C:\\projects\\PrintWedittV1\\backend\\src\\index.ts"
    ],
    "memoryUsage": {
      "rss": 263655424,
      "heapTotal": 209448960,
      "heapUsed": 180449504,
      "external": 7884040,
      "arrayBuffers": 4929818
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 180689.234
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1912,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1969,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2074,
      "method": "listen",
      "native": false
    },
    {
      "column": 24,
      "file": "C:\\projects\\PrintWedittV1\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.listen",
      "line": 635,
      "method": "listen",
      "native": false
    },
    {
      "column": 7,
      "file": "C:\\projects\\PrintWedittV1\\backend\\src\\index.ts",
      "function": "startServer",
      "line": 118,
      "method": null,
      "native": false
    }
  ],
  "service": "printco-backend"
}
Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1912:16)
    at listenInCluster (node:net:1969:12)
    at Server.listen (node:net:2074:7)
    at Function.listen (C:\projects\PrintWedittV1\backend\node_modules\express\lib\application.js:635:24)
    at startServer (C:\projects\PrintWedittV1\backend\src\index.ts:118:7)
2025-08-04 02:46:42 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::3000
Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1912:16)
    at listenInCluster (node:net:1969:12)
    at Server.listen (node:net:2074:7)
    at Function.listen (C:\projects\PrintWedittV1\backend\node_modules\express\lib\application.js:635:24)
    at startServer (C:\projects\PrintWedittV1\backend\src\index.ts:118:7)
{
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 3000
  },
  "exception": true,
  "date": "Mon Aug 04 2025 02:46:42 GMT+0100 (GMT+01:00)",
  "process": {
    "pid": 40348,
    "uid": null,
    "gid": null,
    "cwd": "C:\\projects\\PrintWedittV1\\backend",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.12.0",
    "argv": [
      "C:\\projects\\PrintWedittV1\\backend\\node_modules\\ts-node\\dist\\bin.js",
      "C:\\projects\\PrintWedittV1\\backend\\src\\index.ts"
    ],
    "memoryUsage": {
      "rss": 264953856,
      "heapTotal": 211283968,
      "heapUsed": 175893040,
      "external": 7899530,
      "arrayBuffers": 4869911
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 180724.703
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1912,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1969,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2074,
      "method": "listen",
      "native": false
    },
    {
      "column": 24,
      "file": "C:\\projects\\PrintWedittV1\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.listen",
      "line": 635,
      "method": "listen",
      "native": false
    },
    {
      "column": 7,
      "file": "C:\\projects\\PrintWedittV1\\backend\\src\\index.ts",
      "function": "startServer",
      "line": 118,
      "method": null,
      "native": false
    }
  ],
  "service": "printco-backend"
}
Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1912:16)
    at listenInCluster (node:net:1969:12)
    at Server.listen (node:net:2074:7)
    at Function.listen (C:\projects\PrintWedittV1\backend\node_modules\express\lib\application.js:635:24)
    at startServer (C:\projects\PrintWedittV1\backend\src\index.ts:118:7)
2025-08-04 02:46:47 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::3000
Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1912:16)
    at listenInCluster (node:net:1969:12)
    at Server.listen (node:net:2074:7)
    at Function.listen (C:\projects\PrintWedittV1\backend\node_modules\express\lib\application.js:635:24)
    at startServer (C:\projects\PrintWedittV1\backend\src\index.ts:118:7)
{
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 3000
  },
  "exception": true,
  "date": "Mon Aug 04 2025 02:46:47 GMT+0100 (GMT+01:00)",
  "process": {
    "pid": 10088,
    "uid": null,
    "gid": null,
    "cwd": "C:\\projects\\PrintWedittV1\\backend",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.12.0",
    "argv": [
      "C:\\projects\\PrintWedittV1\\backend\\node_modules\\ts-node\\dist\\bin.js",
      "C:\\projects\\PrintWedittV1\\backend\\src\\index.ts"
    ],
    "memoryUsage": {
      "rss": 263929856,
      "heapTotal": 209711104,
      "heapUsed": 180170176,
      "external": 7883968,
      "arrayBuffers": 4929746
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 180729.828
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1912,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1969,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2074,
      "method": "listen",
      "native": false
    },
    {
      "column": 24,
      "file": "C:\\projects\\PrintWedittV1\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.listen",
      "line": 635,
      "method": "listen",
      "native": false
    },
    {
      "column": 7,
      "file": "C:\\projects\\PrintWedittV1\\backend\\src\\index.ts",
      "function": "startServer",
      "line": 118,
      "method": null,
      "native": false
    }
  ],
  "service": "printco-backend"
}
Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1912:16)
    at listenInCluster (node:net:1969:12)
    at Server.listen (node:net:2074:7)
    at Function.listen (C:\projects\PrintWedittV1\backend\node_modules\express\lib\application.js:635:24)
    at startServer (C:\projects\PrintWedittV1\backend\src\index.ts:118:7)
2025-08-04 02:47:34 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::3000
Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1912:16)
    at listenInCluster (node:net:1969:12)
    at Server.listen (node:net:2074:7)
    at Function.listen (C:\projects\PrintWedittV1\backend\node_modules\express\lib\application.js:635:24)
    at startServer (C:\projects\PrintWedittV1\backend\src\index.ts:118:7)
{
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 3000
  },
  "exception": true,
  "date": "Mon Aug 04 2025 02:47:34 GMT+0100 (GMT+01:00)",
  "process": {
    "pid": 38616,
    "uid": null,
    "gid": null,
    "cwd": "C:\\projects\\PrintWedittV1\\backend",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.12.0",
    "argv": [
      "C:\\projects\\PrintWedittV1\\backend\\node_modules\\ts-node\\dist\\bin.js",
      "C:\\projects\\PrintWedittV1\\backend\\src\\index.ts"
    ],
    "memoryUsage": {
      "rss": 264273920,
      "heapTotal": 209186816,
      "heapUsed": 179605488,
      "external": 7884056,
      "arrayBuffers": 4929834
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 180776.328
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1912,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1969,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2074,
      "method": "listen",
      "native": false
    },
    {
      "column": 24,
      "file": "C:\\projects\\PrintWedittV1\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.listen",
      "line": 635,
      "method": "listen",
      "native": false
    },
    {
      "column": 7,
      "file": "C:\\projects\\PrintWedittV1\\backend\\src\\index.ts",
      "function": "startServer",
      "line": 118,
      "method": null,
      "native": false
    }
  ],
  "service": "printco-backend"
}
Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1912:16)
    at listenInCluster (node:net:1969:12)
    at Server.listen (node:net:2074:7)
    at Function.listen (C:\projects\PrintWedittV1\backend\node_modules\express\lib\application.js:635:24)
    at startServer (C:\projects\PrintWedittV1\backend\src\index.ts:118:7)
2025-08-04 02:49:52 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::3000
Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1912:16)
    at listenInCluster (node:net:1969:12)
    at Server.listen (node:net:2074:7)
    at Function.listen (C:\projects\PrintWedittV1\backend\node_modules\express\lib\application.js:635:24)
    at startServer (C:\projects\PrintWedittV1\backend\src\index.ts:118:7)
{
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 3000
  },
  "exception": true,
  "date": "Mon Aug 04 2025 02:49:52 GMT+0100 (GMT+01:00)",
  "process": {
    "pid": 30824,
    "uid": null,
    "gid": null,
    "cwd": "C:\\projects\\PrintWedittV1\\backend",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.12.0",
    "argv": [
      "C:\\projects\\PrintWedittV1\\backend\\node_modules\\ts-node\\dist\\bin.js",
      "C:\\projects\\PrintWedittV1\\backend\\src\\index.ts"
    ],
    "memoryUsage": {
      "rss": 262426624,
      "heapTotal": 208662528,
      "heapUsed": 181031480,
      "external": 7884088,
      "arrayBuffers": 4929866
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 180914.968
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1912,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1969,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2074,
      "method": "listen",
      "native": false
    },
    {
      "column": 24,
      "file": "C:\\projects\\PrintWedittV1\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.listen",
      "line": 635,
      "method": "listen",
      "native": false
    },
    {
      "column": 7,
      "file": "C:\\projects\\PrintWedittV1\\backend\\src\\index.ts",
      "function": "startServer",
      "line": 118,
      "method": null,
      "native": false
    }
  ],
  "service": "printco-backend"
}
Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1912:16)
    at listenInCluster (node:net:1969:12)
    at Server.listen (node:net:2074:7)
    at Function.listen (C:\projects\PrintWedittV1\backend\node_modules\express\lib\application.js:635:24)
    at startServer (C:\projects\PrintWedittV1\backend\src\index.ts:118:7)
2025-08-04 02:50:00 [ERROR]: uncaughtException: listen EADDRINUSE: address already in use :::3000
Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1912:16)
    at listenInCluster (node:net:1969:12)
    at Server.listen (node:net:2074:7)
    at Function.listen (C:\projects\PrintWedittV1\backend\node_modules\express\lib\application.js:635:24)
    at startServer (C:\projects\PrintWedittV1\backend\src\index.ts:118:7)
{
  "error": {
    "code": "EADDRINUSE",
    "errno": -4091,
    "syscall": "listen",
    "address": "::",
    "port": 3000
  },
  "exception": true,
  "date": "Mon Aug 04 2025 02:50:00 GMT+0100 (GMT+01:00)",
  "process": {
    "pid": 12116,
    "uid": null,
    "gid": null,
    "cwd": "C:\\projects\\PrintWedittV1\\backend",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v22.12.0",
    "argv": [
      "C:\\projects\\PrintWedittV1\\backend\\node_modules\\ts-node\\dist\\bin.js",
      "C:\\projects\\PrintWedittV1\\backend\\src\\index.ts"
    ],
    "memoryUsage": {
      "rss": 263405568,
      "heapTotal": 208662528,
      "heapUsed": 180497592,
      "external": 7884000,
      "arrayBuffers": 4929778
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 180922.875
  },
  "trace": [
    {
      "column": 16,
      "file": "node:net",
      "function": "Server.setupListenHandle [as _listen2]",
      "line": 1912,
      "method": "setupListenHandle [as _listen2]",
      "native": false
    },
    {
      "column": 12,
      "file": "node:net",
      "function": "listenInCluster",
      "line": 1969,
      "method": null,
      "native": false
    },
    {
      "column": 7,
      "file": "node:net",
      "function": "Server.listen",
      "line": 2074,
      "method": "listen",
      "native": false
    },
    {
      "column": 24,
      "file": "C:\\projects\\PrintWedittV1\\backend\\node_modules\\express\\lib\\application.js",
      "function": "Function.listen",
      "line": 635,
      "method": "listen",
      "native": false
    },
    {
      "column": 7,
      "file": "C:\\projects\\PrintWedittV1\\backend\\src\\index.ts",
      "function": "startServer",
      "line": 118,
      "method": null,
      "native": false
    }
  ],
  "service": "printco-backend"
}
Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1912:16)
    at listenInCluster (node:net:1969:12)
    at Server.listen (node:net:2074:7)
    at Function.listen (C:\projects\PrintWedittV1\backend\node_modules\express\lib\application.js:635:24)
    at startServer (C:\projects\PrintWedittV1\backend\src\index.ts:118:7)
