/**
 * Express Middleware Configuration
 * Separated to follow SRP - handles only middleware setup
 */

import compression from 'compression';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import express, { Express } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import morgan from 'morgan';
import { requestLogger } from '../middleware/requestLogger';
import { config } from './index';

export function configureSecurityMiddleware(app: Express): void {
  const securityConfig = config.getSecurityConfig();
  const serverConfig = config.getServerConfig();

  // Helmet security headers
  app.use(
    helmet({
      crossOriginResourcePolicy: { policy: 'cross-origin' },
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", 'data:', 'https:'],
        },
      },
    })
  );

  // Rate limiting (disabled during tests to avoid flakiness)
  if (serverConfig.nodeEnv !== 'test') {
    const limiter = rateLimit({
      windowMs: securityConfig.rateLimitWindowMs,
      max: securityConfig.rateLimitMaxRequests,
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
    });

    app.use(limiter);
  }
}

export function configureCorsMiddleware(app: Express): void {
  const serverConfig = config.getServerConfig();

  app.use(
    cors({
      origin: serverConfig.corsOrigin,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    })
  );
}

export function configureBodyParsingMiddleware(app: Express): void {
  const securityConfig = config.getSecurityConfig();

  app.use(compression());
  app.use(express.json({ limit: `${securityConfig.maxFileSize}mb` }));
  app.use(express.urlencoded({ extended: true, limit: `${securityConfig.maxFileSize}mb` }));
  app.use(cookieParser(securityConfig.cookieSecret));
}

export function configureLoggingMiddleware(app: Express): void {
  const serverConfig = config.getServerConfig();
  const loggingConfig = config.getLoggingConfig();

  if (loggingConfig.enableConsole) {
    const morganFormat = serverConfig.nodeEnv !== 'production' ? 'dev' : 'combined';
    app.use(morgan(morganFormat));
  }

  app.use(requestLogger);
}
