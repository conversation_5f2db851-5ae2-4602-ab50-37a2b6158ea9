// Test the updated logger
require('dotenv').config();

console.log('✅ Starting logger test...');

try {
  // Test logger creation
  const { createLogger } = require('./src/utils/logger');
  console.log('✅ Logger module imported successfully');
  
  const testLogger = createLogger('TEST');
  console.log('✅ Logger instance created successfully');
  
  // Test logging methods
  testLogger.info('Test info message', { test: true });
  testLogger.warn('Test warning message');
  testLogger.error('Test error message');
  
  // Test specialized methods
  testLogger.auth('LOGIN_SUCCESS', 'user123', { ip: '127.0.0.1' });
  testLogger.performance('database_query', 150, { query: 'SELECT * FROM users' });
  
  console.log('✅ All logger methods working successfully');
  
} catch (error) {
  console.error('❌ Logger test failed:', error.message);
  console.error(error.stack);
}

console.log('✅ Logger test completed');
process.exit(0);