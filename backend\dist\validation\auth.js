"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateQuery = exports.validate = exports.revokeSessionSchema = exports.paginationSchema = exports.updateProfileSchema = exports.changePasswordSchema = exports.emailVerificationSchema = exports.passwordResetConfirmSchema = exports.passwordResetRequestSchema = exports.refreshTokenSchema = exports.loginSchema = exports.registerSchema = void 0;
const joi_1 = __importDefault(require("joi"));
const passwordSchema = joi_1.default.string()
    .min(8)
    .max(128)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .required()
    .messages({
    'string.min': 'Password must be at least 8 characters long',
    'string.max': 'Password must not exceed 128 characters',
    'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character (@$!%*?&)',
    'any.required': 'Password is required'
});
const emailSchema = joi_1.default.string()
    .email({ tlds: { allow: false } })
    .max(255)
    .required()
    .messages({
    'string.email': 'Please provide a valid email address',
    'string.max': 'Email must not exceed 255 characters',
    'any.required': 'Email is required'
});
const nameSchema = joi_1.default.string()
    .min(1)
    .max(150)
    .pattern(/^[a-zA-Z\s'-]+$/)
    .trim()
    .required()
    .messages({
    'string.min': 'Name must not be empty',
    'string.max': 'Name must not exceed 150 characters',
    'string.pattern.base': 'Name can only contain letters, spaces, hyphens, and apostrophes',
    'any.required': 'Name is required'
});
const phoneSchema = joi_1.default.string()
    .pattern(/^\+?[1-9]\d{1,14}$/)
    .max(20)
    .optional()
    .messages({
    'string.pattern.base': 'Please provide a valid phone number',
    'string.max': 'Phone number must not exceed 20 characters'
});
exports.registerSchema = joi_1.default.object({
    email: emailSchema,
    password: passwordSchema,
    firstName: nameSchema.messages({
        'any.required': 'First name is required'
    }),
    lastName: nameSchema.messages({
        'any.required': 'Last name is required'
    }),
    phone: phoneSchema
});
exports.loginSchema = joi_1.default.object({
    email: emailSchema,
    password: joi_1.default.string()
        .min(1)
        .max(128)
        .required()
        .messages({
        'string.min': 'Password is required',
        'string.max': 'Password must not exceed 128 characters',
        'any.required': 'Password is required'
    })
});
exports.refreshTokenSchema = joi_1.default.object({
    refreshToken: joi_1.default.string()
        .required()
        .messages({
        'any.required': 'Refresh token is required'
    })
});
exports.passwordResetRequestSchema = joi_1.default.object({
    email: emailSchema
});
exports.passwordResetConfirmSchema = joi_1.default.object({
    token: joi_1.default.string()
        .required()
        .messages({
        'any.required': 'Reset token is required'
    }),
    newPassword: passwordSchema
});
exports.emailVerificationSchema = joi_1.default.object({
    token: joi_1.default.string()
        .required()
        .messages({
        'any.required': 'Verification token is required'
    })
});
exports.changePasswordSchema = joi_1.default.object({
    currentPassword: joi_1.default.string()
        .min(1)
        .max(128)
        .required()
        .messages({
        'string.min': 'Current password is required',
        'string.max': 'Password must not exceed 128 characters',
        'any.required': 'Current password is required'
    }),
    newPassword: passwordSchema
});
exports.updateProfileSchema = joi_1.default.object({
    firstName: nameSchema.optional(),
    lastName: nameSchema.optional(),
    phone: phoneSchema,
    avatar: joi_1.default.string()
        .uri()
        .max(500)
        .optional()
        .messages({
        'string.uri': 'Avatar must be a valid URL',
        'string.max': 'Avatar URL must not exceed 500 characters'
    })
}).min(1).messages({
    'object.min': 'At least one field must be provided for update'
});
exports.paginationSchema = joi_1.default.object({
    page: joi_1.default.number()
        .integer()
        .min(1)
        .default(1)
        .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be at least 1'
    }),
    limit: joi_1.default.number()
        .integer()
        .min(1)
        .max(100)
        .default(10)
        .messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be at least 1',
        'number.max': 'Limit must not exceed 100'
    })
});
exports.revokeSessionSchema = joi_1.default.object({
    sessionId: joi_1.default.string()
        .required()
        .messages({
        'any.required': 'Session ID is required'
    })
});
const validate = (schema) => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req.body, {
            abortEarly: false,
            allowUnknown: false,
            stripUnknown: true
        });
        if (error) {
            const validationErrors = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message.replace(/"/g, ''),
                value: detail.context?.value
            }));
            return res.status(400).json({
                error: 'Validation Error',
                message: 'Invalid input data',
                details: { validationErrors },
                timestamp: new Date().toISOString(),
                path: req.path
            });
        }
        req.body = value;
        next();
    };
};
exports.validate = validate;
const validateQuery = (schema) => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req.query, {
            abortEarly: false,
            allowUnknown: true,
            stripUnknown: true
        });
        if (error) {
            const validationErrors = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message.replace(/"/g, ''),
                value: detail.context?.value
            }));
            return res.status(400).json({
                error: 'Validation Error',
                message: 'Invalid query parameters',
                details: { validationErrors },
                timestamp: new Date().toISOString(),
                path: req.path
            });
        }
        req.query = value;
        next();
    };
};
exports.validateQuery = validateQuery;
exports.default = {
    registerSchema: exports.registerSchema,
    loginSchema: exports.loginSchema,
    refreshTokenSchema: exports.refreshTokenSchema,
    passwordResetRequestSchema: exports.passwordResetRequestSchema,
    passwordResetConfirmSchema: exports.passwordResetConfirmSchema,
    emailVerificationSchema: exports.emailVerificationSchema,
    changePasswordSchema: exports.changePasswordSchema,
    updateProfileSchema: exports.updateProfileSchema,
    paginationSchema: exports.paginationSchema,
    revokeSessionSchema: exports.revokeSessionSchema,
    validate: exports.validate,
    validateQuery: exports.validateQuery
};
//# sourceMappingURL=auth.js.map