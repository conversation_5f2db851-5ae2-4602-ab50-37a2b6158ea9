import { PrismaClient, ServicePricingType } from '@prisma/client';
import {
  ServiceStats,
  ServiceSummary,
  PriceCalculationRequest,
  PriceCalculationResponse,
} from '../types/service';
import { ServiceMapper } from '../utils/ServiceMapper';

/**
 * ServicePricingRepository
 * Handles all service pricing, statistics, and popularity operations
 * Follows Single Responsibility Principle - only pricing and analytics
 */
export interface IServicePricingRepository {
  // Statistics operations
  getServiceStats(): Promise<ServiceStats>;
  
  // Popularity and filtering operations
  getPopularServices(limit?: number): Promise<ServiceSummary[]>;
  getServicesByPricingType(pricingType: ServicePricingType): Promise<ServiceSummary[]>;
  
  // Price calculation
  calculateServicePrice(request: PriceCalculationRequest): Promise<PriceCalculationResponse>;
  
  // Utility operations
  countServices(): Promise<number>;
}

export class ServicePricingRepository implements IServicePricingRepository {
  constructor(private prisma: PrismaClient) {}

  async getServiceStats(): Promise<ServiceStats> {
    const [
      total,
      active,
      servicesByCategory,
      servicesByPricingType,
      priceStats,
      totalProviders,
    ] = await Promise.all([
      this.prisma.service.count(),
      this.prisma.service.count({ where: { isActive: true } }),
      this.prisma.service.groupBy({
        by: ['categoryId'],
        _count: { categoryId: true },
      }),
      this.prisma.service.groupBy({
        by: ['pricingType'],
        _count: { pricingType: true },
      }),
      this.prisma.service.aggregate({
        _avg: { basePrice: true },
      }),
      this.prisma.providerService.count({ where: { isActive: true } }),
    ]);

    // Get category names
    const categories = await this.prisma.service_categories.findMany({
      select: { id: true, name: true },
    });

    const categoryMap = categories.reduce((acc, cat) => {
      acc[cat.id] = cat.name;
      return acc;
    }, {} as Record<string, string>);

    // Convert category counts
    const byCategoryName: Record<string, number> = {};
    servicesByCategory.forEach(({ categoryId, _count }) => {
      const categoryName = categoryMap[categoryId] || categoryId;
      byCategoryName[categoryName] = _count.categoryId;
    });

    // Convert pricing type counts
    const byPricingType: Record<ServicePricingType, number> = {
      [ServicePricingType.FIXED]: 0,
      [ServicePricingType.VARIABLE]: 0,
      [ServicePricingType.QUOTE]: 0,
    };

    servicesByPricingType.forEach(({ pricingType, _count }) => {
      byPricingType[pricingType] = _count.pricingType;
    });

    return {
      total,
      active,
      inactive: total - active,
      byCategory: byCategoryName,
      byPricingType,
      averageBasePrice: Number(priceStats._avg.basePrice) || 0,
      totalProviders,
      averageProvidersPerService: total > 0 ? totalProviders / total : 0,
    };
  }

  async getPopularServices(limit: number = 10): Promise<ServiceSummary[]> {
    const services = await this.prisma.service.findMany({
      where: { isActive: true },
      include: {
        service_categories: {
          select: { name: true },
        },
        providers: {
          where: { isActive: true },
          select: { id: true },
        },
        orders: {
          select: { id: true },
        },
      },
      take: limit,
    });

    // Sort by order count (popularity)
    return services
      .sort((a, b) => b.orders.length - a.orders.length)
      .map(service => ServiceMapper.toServiceSummary(service));
  }

  async getServicesByPricingType(pricingType: ServicePricingType): Promise<ServiceSummary[]> {
    const services = await this.prisma.service.findMany({
      where: {
        pricingType,
        isActive: true,
      },
      include: {
        service_categories: {
          select: { name: true },
        },
        providers: {
          where: { isActive: true },
          select: { id: true },
        },
      },
      orderBy: { sortOrder: 'asc' },
    });

    return ServiceMapper.toServiceSummaryArray(services);
  }

  async calculateServicePrice(request: PriceCalculationRequest): Promise<PriceCalculationResponse> {
    const { serviceId, formData, quantity = 1, providerId } = request;

    // Get service with form fields
    const service = await this.prisma.service.findUnique({
      where: { id: serviceId },
      include: {
        formFields: {
          where: { isActive: true },
          include: {
            service_field_options: {
              where: { isActive: true },
              orderBy: { sortOrder: 'asc' },
            },
          },
          orderBy: { sortOrder: 'asc' },
        },
      },
    });

    if (!service) {
      throw new Error('Service not found');
    }

    let basePrice = Number(service.basePrice);
    const modifiers: Array<{ fieldName: string; optionValue: string; modifier: number }> = [];

    // Calculate modifiers from form data
    for (const field of service.formFields) {
      const fieldValue = formData[field.name];
      if (!fieldValue) continue;

      // Find option for this value
      const option = field.service_field_options.find(opt => opt.value === fieldValue);
      if (option && option.priceModifier) {
        modifiers.push({
          fieldName: field.name,
          optionValue: option.value,
          modifier: Number(option.priceModifier),
        });
      }
    }

    // Calculate subtotal with modifiers
    const modifierTotal = modifiers.reduce((sum, mod) => sum + mod.modifier, 0);
    const subtotal = basePrice + modifierTotal;
    const total = subtotal * quantity;

    // Get provider-specific pricing if requested
    let providerPrice;
    if (providerId) {
      const providerService = await this.prisma.providerService.findFirst({
        where: {
          providerId,
          serviceId,
          isActive: true,
        },
      });
      providerPrice = providerService ? Number(providerService.price) : undefined;
    }

    return {
      basePrice,
      modifiers,
      subtotal,
      quantity,
      total,
      providerId,
      providerPrice,
    };
  }

  async countServices(): Promise<number> {
    return this.prisma.service.count();
  }
}