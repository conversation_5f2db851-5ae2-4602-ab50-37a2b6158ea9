"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = exports.databaseService = void 0;
const client_1 = require("@prisma/client");
const config_1 = require("../config");
const logger_1 = require("../utils/logger");
const logger = (0, logger_1.createLogger)('Database');
class DatabaseService {
    static instance;
    prisma;
    isConnected = false;
    isInitialized = false;
    constructor() {
        this.prisma = new client_1.PrismaClient({
            log: [
                {
                    emit: 'stdout',
                    level: 'query',
                },
                {
                    emit: 'stdout',
                    level: 'error',
                },
                {
                    emit: 'stdout',
                    level: 'info',
                },
                {
                    emit: 'stdout',
                    level: 'warn',
                },
            ],
        });
    }
    static getInstance() {
        if (!DatabaseService.instance) {
            DatabaseService.instance = new DatabaseService();
        }
        return DatabaseService.instance;
    }
    async initialize() {
        if (this.isInitialized) {
            logger.warn('Database service already initialized');
            return;
        }
        try {
            const dbConfig = config_1.config.getDatabaseConfig();
            logger.info('Initializing database connection', {
                url: dbConfig.url.replace(/\/\/.*@/, '//***:***@'),
                maxConnections: dbConfig.maxConnections,
                idleTimeout: dbConfig.idleTimeout,
                connectionTimeout: dbConfig.connectionTimeout,
            });
            await this.prisma.$connect();
            this.isConnected = true;
            this.isInitialized = true;
            logger.info('Database connection established successfully');
        }
        catch (error) {
            logger.error('Failed to initialize database connection', error);
            throw error;
        }
    }
    getClient() {
        if (!this.isInitialized) {
            throw new Error('Database service not initialized. Call initialize() first.');
        }
        return this.prisma;
    }
    isConnectedToDatabase() {
        return this.isConnected;
    }
    async disconnect() {
        if (!this.isConnected) {
            logger.warn('Database already disconnected');
            return;
        }
        try {
            await this.prisma.$disconnect();
            this.isConnected = false;
            this.isInitialized = false;
            logger.info('Database disconnected successfully');
        }
        catch (error) {
            logger.error('Error disconnecting from database', error);
            throw error;
        }
    }
    async healthCheck() {
        try {
            await this.prisma.$queryRaw `SELECT 1`;
            return true;
        }
        catch (error) {
            logger.error('Database health check failed', error);
            return false;
        }
    }
}
exports.DatabaseService = DatabaseService;
exports.databaseService = DatabaseService.getInstance();
//# sourceMappingURL=database.js.map