import { Response } from 'express';
import { AuthenticatedRequest } from '../types/auth';
import { AppLogger } from './logger';

/**
 * Request logging decorator utility
 * Centralizes request logging pattern used across controllers
 * Eliminates the repeated logging code in all controller methods
 */
export class RequestLogger {
  /**
   * Wraps a controller method with standardized request logging
   * <PERSON><PERSON> request start time, duration calculation, and error logging
   */
  static withLogging<T extends any[], R>(
    logger: AppLogger,
    operationName: string,
    handler: (req: AuthenticatedRequest, res: Response, ...args: T) => Promise<R>
  ) {
    return async (req: AuthenticatedRequest, res: Response, ...args: T): Promise<R> => {
      const startTime = Date.now();
      const requestId = res.getHeader('X-Request-ID') as string;
      
      // Extract relevant request data for logging
      const logContext = this.buildLogContext(req, operationName);
      
      logger.info(`${operationName} request received`, {
        requestId,
        ...logContext,
        requestingUserId: req.userId,
      });

      try {
        const result = await handler(req, res, ...args);
        
        const duration = Date.now() - startTime;
        logger.info(`${operationName} completed successfully`, {
          requestId,
          ...this.buildSuccessLogContext(req, result, operationName),
          duration,
        });

        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        logger.error(`${operationName} failed`, error, {
          requestId,
          ...this.buildErrorLogContext(req, operationName),
          duration,
        });
        throw error;
      }
    };
  }

  /**
   * Builds initial log context from request
   */
  private static buildLogContext(req: AuthenticatedRequest, operationName: string): Record<string, any> {
    const context: Record<string, any> = {};

    // Add ID parameters
    if (req.params.id) context.serviceId = req.params.id;
    if (req.params.categoryId) context.categoryId = req.params.categoryId;
    if (req.params.fieldId) context.fieldId = req.params.fieldId;
    if (req.params.optionId) context.optionId = req.params.optionId;
    if (req.params.route) context.route = req.params.route;
    if (req.params.pricingType) context.pricingType = req.params.pricingType;

    // Add query parameters for certain operations
    if (operationName.includes('list') || operationName.includes('search')) {
      if (req.query) context.query = req.query;
    }
    
    // Add specific data from request body
    if (req.body) {
      if (req.body.name) context.name = req.body.name;
      if (req.body.serviceId) context.serviceId = req.body.serviceId;
      if (req.body.fieldId) context.fieldId = req.body.fieldId;
      if (req.body.value) context.optionValue = req.body.value;
      if (req.body.quantity) context.quantity = req.body.quantity;
      if (req.body.providerId) context.providerId = req.body.providerId;
    }

    return context;
  }

  /**
   * Builds success-specific log context
   */
  private static buildSuccessLogContext(
    req: AuthenticatedRequest, 
    result: any, 
    operationName: string
  ): Record<string, any> {
    const context: Record<string, any> = {};

    // Add result-specific data based on operation
    if (result) {
      // For single entity operations
      if (result.service) {
        context.serviceId = result.service.id;
        context.serviceName = result.service.name;
      }
      if (result.category) {
        context.categoryId = result.category.id;
        context.categoryName = result.category.name;
      }
      if (result.formField) {
        context.fieldId = result.formField.id;
        context.fieldName = result.formField.name;
      }
      if (result.option) {
        context.optionId = result.option.id;
        context.optionValue = result.option.value;
      }
      if (result.priceCalculation) {
        context.total = result.priceCalculation.total;
      }

      // For list operations
      if (result.services) {
        context.count = result.services.length;
      }
      if (result.categories) {
        context.count = result.categories.length;
      }
      if (result.formFields) {
        context.count = result.formFields.length;
      }
      if (result.options) {
        context.count = result.options.length;
      }

      // For pagination
      if (result.pagination) {
        context.totalServices = result.pagination.total;
        context.page = result.pagination.page;
        context.limit = result.pagination.limit;
      }

      // For stats
      if (result.stats) {
        context.totalServices = result.stats.total;
        context.activeServices = result.stats.active;
      }

      // For utility operations
      if (typeof result.exists === 'boolean') {
        context.exists = result.exists;
      }
      if (typeof result.count === 'number') {
        context.count = result.count;
      }
      if (typeof result.deleted === 'boolean') {
        context.deleted = result.deleted;
      }

      // For search results
      if (typeof result.total === 'number' && operationName.includes('search')) {
        context.resultsCount = result.total;
        if (req.query?.q) context.searchQuery = req.query.q;
      }

      // For updated data
      if (operationName.includes('update') && req.body) {
        context.updatedFields = Object.keys(req.body);
      }
    }

    return context;
  }

  /**
   * Builds error-specific log context
   */
  private static buildErrorLogContext(req: AuthenticatedRequest, operationName: string): Record<string, any> {
    const context: Record<string, any> = {};

    // Add relevant identifiers for error context
    if (req.params.id) context.serviceId = req.params.id;
    if (req.params.categoryId) context.categoryId = req.params.categoryId;
    if (req.params.fieldId) context.fieldId = req.params.fieldId;
    if (req.params.optionId) context.optionId = req.params.optionId;
    if (req.params.route) context.route = req.params.route;

    // Add search query for search errors
    if (operationName.includes('search') && req.query?.q) {
      context.searchQuery = req.query.q;
    }

    // Add body data that might be relevant for debugging
    if (req.body && operationName.includes('create')) {
      if (req.body.name) context.name = req.body.name;
      if (req.body.serviceId) context.serviceId = req.body.serviceId;
    }

    return context;
  }
}