import { PrismaClient, Service, ServicePricingType, FormFieldType } from '@prisma/client';
import {
  CreateServiceData,
  UpdateServiceData,
  ServiceListQuery,
  ServiceListResponse,
  ServiceStats,
  ServiceSummary,
  ServiceDetail,
  ServiceSearchQuery,
  ServiceSearchResult,
  ServiceCategorySummary,
  ServiceCategoryDetail,
  CreateServiceCategoryData,
  UpdateServiceCategoryData,
  ServiceFormFieldDetail,
  ServiceFormFieldSummary,
  CreateServiceFormFieldData,
  UpdateServiceFormFieldData,
  ServiceFormFieldOptionDetail,
  CreateServiceFormFieldOptionData,
  UpdateServiceFormFieldOptionData,
  PriceCalculationRequest,
  PriceCalculationResponse,
} from '../types/service';
import { ServiceMapper } from '../utils/ServiceMapper';

// Repository interface for dependency inversion - Core service operations only
export interface IServiceRepository {
  // Service CRUD operations
  findServiceById(id: string, includeFormFields?: boolean): Promise<ServiceDetail | null>;
  createService(serviceData: CreateServiceData): Promise<Service>;
  updateService(id: string, data: UpdateServiceData): Promise<Service>;
  deleteService(id: string): Promise<boolean>;

  // Service list and search operations
  getServices(query: ServiceListQuery): Promise<ServiceListResponse>;
  searchServices(query: ServiceSearchQuery): Promise<ServiceSearchResult>;
  getServicesByCategory(categoryId: string): Promise<ServiceSummary[]>;

  // Utility operations
  serviceExists(id: string): Promise<boolean>;
}

// Prisma implementation of the repository - Core service operations only
export class ServiceRepository implements IServiceRepository {
  constructor(private prisma: PrismaClient) {}

  async findServiceById(id: string, includeFormFields: boolean = true): Promise<ServiceDetail | null> {
    const service = await this.prisma.service.findUnique({
      where: { id },
      include: {
        service_categories: true,
        ...(includeFormFields && {
          formFields: {
            where: { isActive: true },
            include: {
              service_field_options: {
                where: { isActive: true },
                orderBy: { sortOrder: 'asc' },
              },
            },
            orderBy: { sortOrder: 'asc' },
          }
        }),
        providers: {
          where: { isActive: true },
          include: {
            provider: {
              select: {
                id: true,
                businessName: true,
                isVerified: true,
                isActive: true,
              },
            },
          },
        },
      },
    });

    if (!service) return null;

    return ServiceMapper.toServiceDetail(service);
  }

  async createService(serviceData: CreateServiceData): Promise<Service> {
    return this.prisma.service.create({
      data: {
        id: serviceData.id,
        name: serviceData.name,
        description: serviceData.description,
        detailedDesc: serviceData.detailedDesc,
        features: serviceData.features,
        notes: serviceData.notes,
        categoryId: serviceData.categoryId,
        image: serviceData.image,
        basePrice: serviceData.basePrice,
        pricingType: serviceData.pricingType,
        isActive: serviceData.isActive,
        sortOrder: serviceData.sortOrder,
      },
    });
  }

  async updateService(id: string, data: UpdateServiceData): Promise<Service> {
    return this.prisma.service.update({
      where: { id },
      data,
    });
  }

  async deleteService(id: string): Promise<boolean> {
    try {
      await this.prisma.service.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  async getServices(query: ServiceListQuery): Promise<ServiceListResponse> {
    const {
      page = 1,
      limit = 20,
      search,
      categoryId,
      pricingType,
      isActive = true,
      sortBy = 'sortOrder',
      sortOrder = 'asc',
      minPrice,
      maxPrice,
    } = query;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { features: { has: search } },
      ];
    }

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (pricingType) {
      where.pricingType = pricingType;
    }

    if (typeof isActive === 'boolean') {
      where.isActive = isActive;
    }

    if (minPrice !== undefined || maxPrice !== undefined) {
      where.basePrice = {};
      if (minPrice !== undefined) where.basePrice.gte = minPrice;
      if (maxPrice !== undefined) where.basePrice.lte = maxPrice;
    }

    // Get total count
    const total = await this.prisma.service.count({ where });

    // Get services with pagination
    const services = await this.prisma.service.findMany({
      where,
      include: {
        service_categories: {
          select: {
            name: true,
          },
        },
        providers: {
          where: { isActive: true },
          select: { id: true },
        },
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      services: ServiceMapper.toServiceSummaryArray(services),
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  async searchServices(query: ServiceSearchQuery): Promise<ServiceSearchResult> {
    const {
      q,
      category,
      minPrice,
      maxPrice,
      pricingType,
      features,
      sortBy = 'name',
      sortOrder = 'asc',
    } = query;

    // Build where clause
    const where: any = { isActive: true };

    if (q) {
      where.OR = [
        { name: { contains: q, mode: 'insensitive' } },
        { description: { contains: q, mode: 'insensitive' } },
        { detailedDesc: { contains: q, mode: 'insensitive' } },
      ];
    }

    if (category) {
      where.service_categories = {
        route: category,
      };
    }

    if (pricingType) {
      where.pricingType = pricingType;
    }

    if (features && features.length > 0) {
      where.features = {
        hasEvery: features,
      };
    }

    if (minPrice !== undefined || maxPrice !== undefined) {
      where.basePrice = {};
      if (minPrice !== undefined) where.basePrice.gte = minPrice;
      if (maxPrice !== undefined) where.basePrice.lte = maxPrice;
    }

    // Get services
    const services = await this.prisma.service.findMany({
      where,
      include: {
        service_categories: true,
        formFields: {
          where: { isActive: true },
          include: {
            service_field_options: {
              where: { isActive: true },
              orderBy: { sortOrder: 'asc' },
            },
          },
          orderBy: { sortOrder: 'asc' },
        },
        providers: {
          where: { isActive: true },
          include: {
            provider: {
              select: {
                id: true,
                businessName: true,
                isVerified: true,
                isActive: true,
              },
            },
          },
        },
      },
      orderBy: { [sortBy]: sortOrder },
    });

    // Get filters
    const [categories, priceRange] = await Promise.all([
      this.prisma.service_categories.findMany({
        where: { isActive: true },
        include: {
          _count: {
            select: { services: true },
          },
        },
      }),
      this.prisma.service.aggregate({
        where: { isActive: true },
        _min: { basePrice: true },
        _max: { basePrice: true },
      }),
    ]);

    // Extract features for filtering
    const allFeatures = services.reduce((acc, service) => {
      service.features.forEach(feature => {
        if (!acc[feature]) acc[feature] = 0;
        acc[feature]++;
      });
      return acc;
    }, {} as Record<string, number>);

    return {
      services: ServiceMapper.toServiceDetailArray(services),
      total: services.length,
      query,
      filters: {
        categories: categories.map(cat => ServiceMapper.toCategoryFilter(cat)),
        priceRange: ServiceMapper.toPriceRangeFilter(priceRange),
        features: Object.entries(allFeatures).map(([feature, count]) => ServiceMapper.toFeatureFilter(feature, count)),
      },
    };
  }

  async getServicesByCategory(categoryId: string): Promise<ServiceSummary[]> {
    const services = await this.prisma.service.findMany({
      where: {
        categoryId,
        isActive: true,
      },
      include: {
        service_categories: {
          select: {
            name: true,
          },
        },
        providers: {
          where: { isActive: true },
          select: { id: true },
        },
      },
      orderBy: { sortOrder: 'asc' },
    });

    return ServiceMapper.toServiceSummaryArray(services);
  }




  // Service Categories
  async getServiceCategories(): Promise<ServiceCategorySummary[]> {
    const categories = await this.prisma.service_categories.findMany({
      where: { isActive: true },
      include: {
        _count: {
          select: { services: true },
        },
      },
      orderBy: { sortOrder: 'asc' },
    });

    return ServiceMapper.toServiceCategorySummaryArray(categories);
  }

  async findServiceCategoryById(id: string): Promise<ServiceCategoryDetail | null> {
    const category = await this.prisma.service_categories.findUnique({
      where: { id },
      include: {
        services: {
          where: { isActive: true },
          include: {
            service_categories: {
              select: { name: true },
            },
            providers: {
              where: { isActive: true },
              select: { id: true },
            },
          },
          orderBy: { sortOrder: 'asc' },
        },
      },
    });

    if (!category) return null;

    return ServiceMapper.toServiceCategoryDetail(category);
  }

  async findServiceCategoryByRoute(route: string): Promise<ServiceCategoryDetail | null> {
    const category = await this.prisma.service_categories.findUnique({
      where: { route },
      include: {
        services: {
          where: { isActive: true },
          include: {
            service_categories: {
              select: { name: true },
            },
            providers: {
              where: { isActive: true },
              select: { id: true },
            },
          },
          orderBy: { sortOrder: 'asc' },
        },
      },
    });

    if (!category) return null;

    return ServiceMapper.toServiceCategoryDetail(category);
  }

  async createServiceCategory(categoryData: CreateServiceCategoryData): Promise<any> {
    return this.prisma.service_categories.create({
      data: {
        id: categoryData.id,
        name: categoryData.name,
        description: categoryData.description,
        icon: categoryData.icon,
        route: categoryData.route,
        isActive: categoryData.isActive,
        sortOrder: categoryData.sortOrder,
        updatedAt: new Date(),
      },
    });
  }

  async updateServiceCategory(id: string, data: UpdateServiceCategoryData): Promise<any> {
    return this.prisma.service_categories.update({
      where: { id },
      data,
    });
  }

  async deleteServiceCategory(id: string): Promise<boolean> {
    try {
      await this.prisma.service_categories.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  // Service Form Fields
  async getServiceFormFields(serviceId: string): Promise<ServiceFormFieldDetail[]> {
    const fields = await this.prisma.serviceFormField.findMany({
      where: { serviceId, isActive: true },
      include: {
        service_field_options: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
        },
      },
      orderBy: { sortOrder: 'asc' },
    });

    return ServiceMapper.toFormFieldDetailArray(fields);
  }

  async findServiceFormFieldById(id: string): Promise<ServiceFormFieldDetail | null> {
    const field = await this.prisma.serviceFormField.findUnique({
      where: { id },
      include: {
        service_field_options: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
        },
      },
    });

    if (!field) return null;

    return ServiceMapper.toFormFieldDetail(field);
  }

  async createServiceFormField(fieldData: CreateServiceFormFieldData): Promise<any> {
    return this.prisma.serviceFormField.create({
      data: fieldData,
    });
  }

  async updateServiceFormField(id: string, data: UpdateServiceFormFieldData): Promise<any> {
    return this.prisma.serviceFormField.update({
      where: { id },
      data,
    });
  }

  async deleteServiceFormField(id: string): Promise<boolean> {
    try {
      await this.prisma.serviceFormField.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  // Service Form Field Options
  async getServiceFormFieldOptions(fieldId: string): Promise<ServiceFormFieldOptionDetail[]> {
    const options = await this.prisma.service_field_options.findMany({
      where: { fieldId, isActive: true },
      orderBy: { sortOrder: 'asc' },
    });

    return ServiceMapper.toFormFieldOptionDetailArray(options);
  }

  async createServiceFormFieldOption(optionData: CreateServiceFormFieldOptionData): Promise<any> {
    return this.prisma.service_field_options.create({
      data: optionData,
    });
  }

  async updateServiceFormFieldOption(id: string, data: UpdateServiceFormFieldOptionData): Promise<any> {
    return this.prisma.service_field_options.update({
      where: { id },
      data,
    });
  }

  async deleteServiceFormFieldOption(id: string): Promise<boolean> {
    try {
      await this.prisma.service_field_options.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  // Utility methods
  async serviceExists(id: string): Promise<boolean> {
    const count = await this.prisma.service.count({
      where: { id },
    });
    return count > 0;
  }

  async serviceCategoryExists(id: string): Promise<boolean> {
    const count = await this.prisma.service_categories.count({
      where: { id },
    });
    return count > 0;
  }

  async countServices(): Promise<number> {
    return this.prisma.service.count();
  }

  async countServicesByCategory(categoryId: string): Promise<number> {
    return this.prisma.service.count({
      where: { categoryId },
    });
  }

  // Price calculation
  async calculateServicePrice(request: PriceCalculationRequest): Promise<PriceCalculationResponse> {
    const { serviceId, formData, quantity = 1, providerId } = request;

    // Get service with form fields
    const service = await this.findServiceById(serviceId, true);
    if (!service) {
      throw new Error('Service not found');
    }

    let basePrice = service.basePrice;
    const modifiers: Array<{ fieldName: string; optionValue: string; modifier: number }> = [];

    // Calculate modifiers from form data
    for (const field of service.formFields) {
      const fieldValue = formData[field.name];
      if (!fieldValue) continue;

      // Find option for this value
      const option = field.options.find(opt => opt.value === fieldValue);
      if (option && option.priceModifier) {
        modifiers.push({
          fieldName: field.name,
          optionValue: option.value,
          modifier: option.priceModifier,
        });
      }
    }

    // Calculate subtotal with modifiers
    const modifierTotal = modifiers.reduce((sum, mod) => sum + mod.modifier, 0);
    const subtotal = basePrice + modifierTotal;
    const total = subtotal * quantity;

    // Get provider-specific pricing if requested
    let providerPrice;
    if (providerId) {
      const providerService = await this.prisma.providerService.findFirst({
        where: {
          providerId,
          serviceId,
          isActive: true,
        },
      });
      providerPrice = providerService ? Number(providerService.price) : undefined;
    }

    return {
      basePrice,
      modifiers,
      subtotal,
      quantity,
      total,
      providerId,
      providerPrice,
    };
  }

}