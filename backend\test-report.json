{"timestamp": "2025-08-04T01:47:28.354Z", "environment": "development", "logLevel": "debug", "totalTests": 11, "passedTests": 5, "failedTests": 6, "successRate": "45.45", "results": [{"category": "Environment Setup", "test": "API Connectivity", "passed": false, "message": "Request failed with status code 404", "timestamp": "2025-08-04T01:47:28.281Z"}, {"category": "Authentication", "test": "Authentication Flow", "passed": false, "message": "Request failed with status code 400", "timestamp": "2025-08-04T01:47:28.323Z"}, {"category": "Rate Limiting", "test": "Rate Limiting Test", "passed": false, "message": "Request failed with status code 400", "timestamp": "2025-08-04T01:47:28.333Z"}, {"category": "Error <PERSON>", "test": "Malformed Request", "passed": true, "message": "Malformed request error properly logged", "timestamp": "2025-08-04T01:47:28.343Z"}, {"category": "Performance", "test": "Performance Test", "passed": false, "message": "Request failed with status code 400", "timestamp": "2025-08-04T01:47:28.351Z"}, {"category": "Security", "test": "Security Tests", "passed": false, "message": "No valid token available", "timestamp": "2025-08-04T01:47:28.352Z"}, {"category": "Database", "test": "Database Tests", "passed": false, "message": "No valid token available", "timestamp": "2025-08-04T01:47:28.352Z"}, {"category": "Log Rotation", "test": "Log Files Exist", "passed": true, "message": "Found 5 log files", "timestamp": "2025-08-04T01:47:28.353Z"}, {"category": "Log Rotation", "test": "Error Log <PERSON>", "passed": true, "message": "Error log file exists", "timestamp": "2025-08-04T01:47:28.353Z"}, {"category": "Log Rotation", "test": "Application Log File", "passed": true, "message": "Application log file exists", "timestamp": "2025-08-04T01:47:28.354Z"}, {"category": "Log Rotation", "test": "HTTP Log File", "passed": true, "message": "HTTP log file exists", "timestamp": "2025-08-04T01:47:28.354Z"}]}