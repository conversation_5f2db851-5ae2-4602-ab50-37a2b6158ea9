"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const client_1 = require("@prisma/client");
const auth_1 = require("../types/auth");
const auth_2 = require("../middleware/auth");
const auth_3 = require("../utils/auth");
const errorHandler_1 = require("../middleware/errorHandler");
class AuthService {
    authRepository;
    constructor(authRepository) {
        this.authRepository = authRepository;
    }
    async register(data, context) {
        const { email, password, firstName, lastName, phone } = data;
        const existingUser = await this.authRepository.findUserByEmail(email);
        if (existingUser) {
            await this.logSecurityEvent({
                type: auth_1.SecurityEventType.LOGIN_FAILURE,
                email: email.toLowerCase(),
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                metadata: { reason: 'Email already registered' }
            });
            throw new errorHandler_1.ConflictError('Email address is already registered');
        }
        const hashedPassword = await (0, auth_3.hashPassword)(password);
        const userData = {
            email,
            password: hashedPassword,
            firstName,
            lastName,
            phone,
            role: client_1.UserRole.CUSTOMER,
            isActive: true,
            isVerified: false
        };
        const user = await this.authRepository.createUser(userData);
        const tokens = await this.generateTokenPair(user.id, user.email, user.role, context);
        await this.logSecurityEvent({
            type: auth_1.SecurityEventType.LOGIN_SUCCESS,
            userId: user.id,
            email: user.email,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
            metadata: { action: 'registration' }
        });
        return {
            user: (0, auth_3.sanitizeUser)(user),
            tokens
        };
    }
    async login(data, context) {
        const { email, password } = data;
        const user = await this.authRepository.findUserByEmail(email);
        if (!user) {
            await this.logSecurityEvent({
                type: auth_1.SecurityEventType.LOGIN_FAILURE,
                email: email.toLowerCase(),
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                metadata: { reason: 'User not found' }
            });
            throw new errorHandler_1.AuthenticationError('Invalid email or password');
        }
        const isLocked = await this.authRepository.isAccountLocked(user.id);
        if (isLocked) {
            const lockInfo = await this.authRepository.getAccountLockInfo(user.id);
            await this.logSecurityEvent({
                type: auth_1.SecurityEventType.LOGIN_FAILURE,
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                metadata: { reason: 'Account locked', lockInfo }
            });
            throw new errorHandler_1.AuthenticationError(`Account is temporarily locked. Try again after ${lockInfo?.lockExpires?.toLocaleTimeString()}`);
        }
        if (!user.isActive) {
            await this.logSecurityEvent({
                type: auth_1.SecurityEventType.LOGIN_FAILURE,
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                metadata: { reason: 'Account deactivated' }
            });
            throw new errorHandler_1.AuthenticationError('Account is deactivated');
        }
        if (!user.password) {
            throw new errorHandler_1.AuthenticationError('Password not set. Please use password reset.');
        }
        const isPasswordValid = await (0, auth_3.verifyPassword)(password, user.password);
        if (!isPasswordValid) {
            await this.authRepository.incrementLoginAttempts(user.id);
            await this.logSecurityEvent({
                type: auth_1.SecurityEventType.LOGIN_FAILURE,
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                metadata: { reason: 'Invalid password' }
            });
            throw new errorHandler_1.AuthenticationError('Invalid email or password');
        }
        await this.authRepository.resetLoginAttempts(user.id);
        const tokens = await this.generateTokenPair(user.id, user.email, user.role, context);
        await this.logSecurityEvent({
            type: auth_1.SecurityEventType.LOGIN_SUCCESS,
            userId: user.id,
            email: user.email,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent
        });
        return {
            user: (0, auth_3.sanitizeUser)(user),
            tokens
        };
    }
    async refreshTokens(refreshToken, context) {
        const payload = (0, auth_2.verifyRefreshToken)(refreshToken);
        const session = await this.authRepository.findSessionByRefreshToken(refreshToken);
        if (!session || !session.isActive || session.expiresAt < new Date()) {
            throw new errorHandler_1.AuthenticationError('Invalid or expired refresh token');
        }
        const user = await this.authRepository.findUserById(payload.userId);
        if (!user || !user.isActive) {
            throw new errorHandler_1.AuthenticationError('User not found or inactive');
        }
        await this.authRepository.revokeSession(refreshToken);
        const tokens = await this.generateTokenPair(user.id, user.email, user.role, context);
        await this.logSecurityEvent({
            type: auth_1.SecurityEventType.TOKEN_REFRESH,
            userId: user.id,
            email: user.email,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent
        });
        return { tokens };
    }
    async logout(refreshToken, user, context) {
        if (refreshToken) {
            await this.authRepository.revokeSession(refreshToken);
        }
        await this.logSecurityEvent({
            type: auth_1.SecurityEventType.LOGOUT,
            userId: user.id,
            email: user.email,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent
        });
    }
    async logoutAllDevices(user, context) {
        await this.authRepository.revokeAllUserSessions(user.id);
        await this.logSecurityEvent({
            type: auth_1.SecurityEventType.LOGOUT,
            userId: user.id,
            email: user.email,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
            metadata: { action: 'logout_all_devices' }
        });
    }
    async getProfile(userId) {
        const user = await this.authRepository.findUserByIdSafe(userId);
        if (!user) {
            throw new errorHandler_1.NotFoundError('User not found');
        }
        return user;
    }
    async updateProfile(userId, data) {
        const updateData = {};
        if (data.firstName !== undefined)
            updateData.firstName = data.firstName;
        if (data.lastName !== undefined)
            updateData.lastName = data.lastName;
        if (data.phone !== undefined)
            updateData.phone = data.phone;
        if (data.avatar !== undefined)
            updateData.avatar = data.avatar;
        const updatedUser = await this.authRepository.updateUser(userId, updateData);
        return (0, auth_3.sanitizeUser)(updatedUser);
    }
    async changePassword(userId, data, context) {
        const { currentPassword, newPassword } = data;
        const user = await this.authRepository.findUserById(userId);
        if (!user || !user.password) {
            throw new errorHandler_1.AuthenticationError('Current password verification failed');
        }
        const isCurrentPasswordValid = await (0, auth_3.verifyPassword)(currentPassword, user.password);
        if (!isCurrentPasswordValid) {
            throw new errorHandler_1.AuthenticationError('Current password is incorrect');
        }
        const hashedNewPassword = await (0, auth_3.hashPassword)(newPassword);
        await this.authRepository.updateUserPassword(userId, hashedNewPassword);
        await this.authRepository.revokeAllUserSessions(userId);
        await this.logSecurityEvent({
            type: auth_1.SecurityEventType.PASSWORD_CHANGE,
            userId: user.id,
            email: user.email,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent
        });
    }
    async getUserSessions(userId) {
        return this.authRepository.getUserSessions(userId);
    }
    async revokeSession(sessionId, userId) {
        const success = await this.authRepository.revokeSpecificSession(sessionId, userId);
        if (!success) {
            throw new errorHandler_1.NotFoundError('Session not found');
        }
    }
    async isAccountLocked(userId) {
        return this.authRepository.isAccountLocked(userId);
    }
    async getAccountLockInfo(userId) {
        return this.authRepository.getAccountLockInfo(userId);
    }
    async generateTokenPair(userId, email, role, context) {
        const payload = {
            userId,
            email,
            role
        };
        const accessToken = (0, auth_2.generateAccessToken)(payload);
        const refreshToken = (0, auth_2.generateRefreshToken)(payload);
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 7);
        const sessionData = {
            id: (0, auth_3.generateSecureToken)(16),
            userId,
            refreshToken,
            expiresAt,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent
        };
        await this.authRepository.createSession(sessionData);
        return { accessToken, refreshToken };
    }
    async logSecurityEvent(event) {
        await (0, auth_3.logSecurityEvent)(event);
    }
}
exports.AuthService = AuthService;
exports.default = AuthService;
//# sourceMappingURL=AuthService.js.map