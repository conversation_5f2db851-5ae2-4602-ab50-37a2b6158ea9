"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const client_1 = require("@prisma/client");
const auth_1 = require("../types/auth");
const auth_2 = require("../middleware/auth");
const auth_3 = require("../utils/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const logger_1 = require("../utils/logger");
class AuthService {
    authRepository;
    emailService;
    jwtConfig;
    securityConfig;
    constructor(authRepository, emailService, jwtConfig, securityConfig) {
        this.authRepository = authRepository;
        this.emailService = emailService;
        this.jwtConfig = jwtConfig;
        this.securityConfig = securityConfig;
        logger_1.authLogger.info('AuthService initialized', {
            jwtIssuer: jwtConfig.issuer,
            jwtAudience: jwtConfig.audience,
            accessTokenExpiry: jwtConfig.accessExpiresIn,
            refreshTokenExpiry: jwtConfig.refreshExpiresIn,
            bcryptRounds: securityConfig.bcryptRounds,
        });
    }
    async register(data, context) {
        const startTime = Date.now();
        const { email, password, firstName, lastName, phone } = data;
        logger_1.authLogger.info('User registration attempt', {
            email: email.toLowerCase(),
            firstName,
            lastName,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
        });
        try {
            const existingUser = await this.authRepository.findUserByEmail(email);
            if (existingUser) {
                const duration = Date.now() - startTime;
                logger_1.authLogger.warn('Registration failed: Email already exists', {
                    email: email.toLowerCase(),
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    duration,
                });
                await this.logSecurityEvent({
                    type: auth_1.SecurityEventType.LOGIN_FAILURE,
                    email: email.toLowerCase(),
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    metadata: { reason: 'Email already registered' },
                });
                throw new errorHandler_1.ConflictError('Email address is already registered');
            }
            const hashedPassword = await (0, auth_3.hashPassword)(password, this.securityConfig.bcryptRounds);
            const userData = {
                email,
                password: hashedPassword,
                firstName,
                lastName,
                phone,
                role: client_1.UserRole.CUSTOMER,
                isActive: true,
                isVerified: false,
            };
            logger_1.dbLogger.info('Creating new user', {
                table: 'User',
                email: email.toLowerCase(),
                role: client_1.UserRole.CUSTOMER,
            });
            const user = await this.authRepository.createUser(userData);
            const tokens = await this.generateTokenPair(user.id, user.email, user.role, context);
            const duration = Date.now() - startTime;
            logger_1.authLogger.info('User registration successful', {
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            await this.logSecurityEvent({
                type: auth_1.SecurityEventType.LOGIN_SUCCESS,
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                metadata: { action: 'registration' },
            });
            return {
                user: (0, auth_3.sanitizeUser)(user),
                tokens,
            };
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger_1.authLogger.error('User registration failed', error, {
                email: email.toLowerCase(),
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            throw error;
        }
    }
    async login(data, context) {
        const startTime = Date.now();
        const { email, password } = data;
        logger_1.authLogger.info('User login attempt', {
            email: email.toLowerCase(),
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
        });
        try {
            const user = await this.authRepository.findUserByEmail(email);
            if (!user) {
                const duration = Date.now() - startTime;
                logger_1.authLogger.warn('Login failed: User not found', {
                    email: email.toLowerCase(),
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    duration,
                });
                await this.logSecurityEvent({
                    type: auth_1.SecurityEventType.LOGIN_FAILURE,
                    email: email.toLowerCase(),
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    metadata: { reason: 'User not found' },
                });
                throw new errorHandler_1.AuthenticationError('Invalid email or password');
            }
            const isLocked = await this.authRepository.isAccountLocked(user.id);
            if (isLocked) {
                const lockInfo = await this.authRepository.getAccountLockInfo(user.id);
                const duration = Date.now() - startTime;
                logger_1.authLogger.warn('Login failed: Account locked', {
                    userId: user.id,
                    email: user.email,
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    lockInfo,
                    duration,
                });
                await this.logSecurityEvent({
                    type: auth_1.SecurityEventType.LOGIN_FAILURE,
                    userId: user.id,
                    email: user.email,
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    metadata: { reason: 'Account locked', lockInfo },
                });
                throw new errorHandler_1.AuthenticationError(`Account is temporarily locked. Try again after ${lockInfo?.lockExpires?.toLocaleTimeString()}`);
            }
            if (!user.isActive) {
                const duration = Date.now() - startTime;
                logger_1.authLogger.warn('Login failed: Account deactivated', {
                    userId: user.id,
                    email: user.email,
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    duration,
                });
                await this.logSecurityEvent({
                    type: auth_1.SecurityEventType.LOGIN_FAILURE,
                    userId: user.id,
                    email: user.email,
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    metadata: { reason: 'Account deactivated' },
                });
                throw new errorHandler_1.AuthenticationError('Account is deactivated');
            }
            if (!user.password) {
                const duration = Date.now() - startTime;
                logger_1.authLogger.warn('Login failed: Password not set', {
                    userId: user.id,
                    email: user.email,
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    duration,
                });
                throw new errorHandler_1.AuthenticationError('Password not set. Please use password reset.');
            }
            const isPasswordValid = await (0, auth_3.verifyPassword)(password, user.password);
            if (!isPasswordValid) {
                await this.authRepository.incrementLoginAttempts(user.id);
                const duration = Date.now() - startTime;
                logger_1.authLogger.warn('Login failed: Invalid password', {
                    userId: user.id,
                    email: user.email,
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    duration,
                });
                await this.logSecurityEvent({
                    type: auth_1.SecurityEventType.LOGIN_FAILURE,
                    userId: user.id,
                    email: user.email,
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    metadata: { reason: 'Invalid password' },
                });
                throw new errorHandler_1.AuthenticationError('Invalid email or password');
            }
            await this.authRepository.resetLoginAttempts(user.id);
            const tokens = await this.generateTokenPair(user.id, user.email, user.role, context);
            const duration = Date.now() - startTime;
            logger_1.authLogger.info('User login successful', {
                userId: user.id,
                email: user.email,
                role: user.role,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            await this.logSecurityEvent({
                type: auth_1.SecurityEventType.LOGIN_SUCCESS,
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                metadata: { action: 'login' },
            });
            return {
                user: (0, auth_3.sanitizeUser)(user),
                tokens,
            };
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger_1.authLogger.error('User login failed', error, {
                email: email.toLowerCase(),
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            throw error;
        }
    }
    async refreshTokens(refreshToken, context) {
        const startTime = Date.now();
        const payload = (0, auth_2.verifyRefreshToken)(refreshToken);
        const session = await this.authRepository.findSessionByRefreshToken(refreshToken);
        if (!session || !session.isActive || session.expiresAt < new Date()) {
            const duration = Date.now() - startTime;
            logger_1.authLogger.warn('Refresh token failed: Invalid or expired', {
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            throw new errorHandler_1.AuthenticationError('Invalid or expired refresh token');
        }
        const user = await this.authRepository.findUserById(payload.userId);
        if (!user || !user.isActive) {
            const duration = Date.now() - startTime;
            logger_1.authLogger.warn('Refresh token failed: User not found or inactive', {
                userId: payload.userId,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            throw new errorHandler_1.AuthenticationError('User not found or inactive');
        }
        await this.authRepository.revokeSession(refreshToken);
        const tokens = await this.generateTokenPair(user.id, user.email, user.role, context);
        const duration = Date.now() - startTime;
        logger_1.authLogger.info('Refresh token successful', {
            userId: user.id,
            email: user.email,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
            duration,
        });
        await this.logSecurityEvent({
            type: auth_1.SecurityEventType.TOKEN_REFRESH,
            userId: user.id,
            email: user.email,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
        });
        return { tokens };
    }
    async logout(refreshToken, user, context) {
        const startTime = Date.now();
        if (refreshToken) {
            await this.authRepository.revokeSession(refreshToken);
            const duration = Date.now() - startTime;
            logger_1.authLogger.info('Logout successful (by refresh token)', {
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
        }
        await this.logSecurityEvent({
            type: auth_1.SecurityEventType.LOGOUT,
            userId: user.id,
            email: user.email,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
        });
    }
    async logoutAllDevices(user, context) {
        const startTime = Date.now();
        await this.authRepository.revokeAllUserSessions(user.id);
        const duration = Date.now() - startTime;
        logger_1.authLogger.info('Logout from all devices successful', {
            userId: user.id,
            email: user.email,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
            duration,
        });
        await this.logSecurityEvent({
            type: auth_1.SecurityEventType.LOGOUT,
            userId: user.id,
            email: user.email,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
            metadata: { action: 'logout_all_devices' },
        });
    }
    async getProfile(userId) {
        const startTime = Date.now();
        const user = await this.authRepository.findUserByIdSafe(userId);
        if (!user) {
            const duration = Date.now() - startTime;
            logger_1.authLogger.warn('Get profile failed: User not found', {
                userId,
                duration,
            });
            throw new errorHandler_1.NotFoundError('User not found');
        }
        const duration = Date.now() - startTime;
        logger_1.authLogger.info('Get profile successful', {
            userId: user.id,
            email: user.email,
            duration,
        });
        return user;
    }
    async updateProfile(userId, data) {
        const startTime = Date.now();
        const updateData = {};
        if (data.firstName !== undefined)
            updateData.firstName = data.firstName;
        if (data.lastName !== undefined)
            updateData.lastName = data.lastName;
        if (data.phone !== undefined)
            updateData.phone = data.phone;
        if (data.avatar !== undefined)
            updateData.avatar = data.avatar;
        logger_1.dbLogger.info('Updating user profile', {
            table: 'User',
            userId,
            updatedFields: Object.keys(updateData),
        });
        const updatedUser = await this.authRepository.updateUser(userId, updateData);
        const duration = Date.now() - startTime;
        logger_1.authLogger.info('Profile updated successful', {
            userId: updatedUser.id,
            email: updatedUser.email,
            duration,
        });
        return (0, auth_3.sanitizeUser)(updatedUser);
    }
    async changePassword(userId, data, context) {
        const startTime = Date.now();
        const { currentPassword, newPassword } = data;
        logger_1.authLogger.info('User password change attempt', {
            userId,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
        });
        try {
            const user = await this.authRepository.findUserById(userId);
            if (!user || !user.password) {
                const duration = Date.now() - startTime;
                logger_1.authLogger.warn('Password change failed: User not found or password not set', {
                    userId,
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    duration,
                });
                throw new errorHandler_1.AuthenticationError('Current password verification failed');
            }
            const isCurrentPasswordValid = await (0, auth_3.verifyPassword)(currentPassword, user.password);
            if (!isCurrentPasswordValid) {
                const duration = Date.now() - startTime;
                logger_1.authLogger.warn('Password change failed: Current password incorrect', {
                    userId,
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    duration,
                });
                throw new errorHandler_1.AuthenticationError('Current password is incorrect');
            }
            const hashedNewPassword = await (0, auth_3.hashPassword)(newPassword, this.securityConfig.bcryptRounds);
            await this.authRepository.updateUserPassword(userId, hashedNewPassword);
            await this.authRepository.revokeAllUserSessions(userId);
            const duration = Date.now() - startTime;
            logger_1.authLogger.info('Password change successful', {
                userId,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            await this.logSecurityEvent({
                type: auth_1.SecurityEventType.PASSWORD_CHANGE,
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
            });
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger_1.authLogger.error('Password change failed', error, {
                userId,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            throw error;
        }
    }
    async getUserSessions(userId) {
        const startTime = Date.now();
        const sessions = await this.authRepository.getUserSessions(userId);
        const duration = Date.now() - startTime;
        logger_1.authLogger.info('Get user sessions successful', {
            userId,
            sessionCount: sessions.length,
            duration,
        });
        return sessions;
    }
    async revokeSession(sessionId, userId) {
        const startTime = Date.now();
        const success = await this.authRepository.revokeSpecificSession(sessionId, userId);
        if (!success) {
            const duration = Date.now() - startTime;
            logger_1.authLogger.warn('Revoke session failed: Session not found', {
                sessionId,
                userId,
                duration,
            });
            throw new errorHandler_1.NotFoundError('Session not found');
        }
        const duration = Date.now() - startTime;
        logger_1.authLogger.info('Revoke session successful', {
            sessionId,
            userId,
            duration,
        });
    }
    async isAccountLocked(userId) {
        const startTime = Date.now();
        const isLocked = await this.authRepository.isAccountLocked(userId);
        const duration = Date.now() - startTime;
        logger_1.authLogger.info('Check account lock status', {
            userId,
            isLocked,
            duration,
        });
        return isLocked;
    }
    async getAccountLockInfo(userId) {
        const startTime = Date.now();
        const lockInfo = await this.authRepository.getAccountLockInfo(userId);
        const duration = Date.now() - startTime;
        logger_1.authLogger.info('Get account lock info', {
            userId,
            lockInfo,
            duration,
        });
        return lockInfo;
    }
    async requestPasswordReset(email, context) {
        const startTime = Date.now();
        logger_1.authLogger.info('Password reset request', {
            email: email.toLowerCase(),
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
        });
        try {
            const user = await this.authRepository.findUserByEmail(email);
            if (!user) {
                const duration = Date.now() - startTime;
                logger_1.authLogger.warn('Password reset request for non-existent user', {
                    email: email.toLowerCase(),
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    duration,
                });
                return;
            }
            if (!user.isActive) {
                const duration = Date.now() - startTime;
                logger_1.authLogger.warn('Password reset request for deactivated user', {
                    userId: user.id,
                    email: user.email,
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    duration,
                });
                return;
            }
            const resetToken = (0, auth_3.generateSecureToken)(32);
            const expiresAt = new Date();
            expiresAt.setHours(expiresAt.getHours() + 1);
            await this.authRepository.setPasswordResetToken(user.id, resetToken, expiresAt);
            await this.emailService.sendPasswordResetEmail(user.email, resetToken, user.firstName);
            const duration = Date.now() - startTime;
            logger_1.authLogger.info('Password reset email sent', {
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            await this.logSecurityEvent({
                type: auth_1.SecurityEventType.PASSWORD_RESET_REQUEST,
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
            });
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger_1.authLogger.error('Password reset request failed', error, {
                email: email.toLowerCase(),
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            throw error;
        }
    }
    async resetPassword(token, newPassword, context) {
        const startTime = Date.now();
        logger_1.authLogger.info('Password reset attempt', {
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
        });
        try {
            const user = await this.authRepository.findUserByPasswordResetToken(token);
            if (!user) {
                const duration = Date.now() - startTime;
                logger_1.authLogger.warn('Password reset failed: Invalid or expired token', {
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    duration,
                });
                throw new errorHandler_1.AuthenticationError('Invalid or expired reset token');
            }
            const hashedPassword = await (0, auth_3.hashPassword)(newPassword, this.securityConfig.bcryptRounds);
            await this.authRepository.updateUserPassword(user.id, hashedPassword);
            await this.authRepository.clearPasswordResetToken(user.id);
            await this.authRepository.revokeAllUserSessions(user.id);
            const duration = Date.now() - startTime;
            logger_1.authLogger.info('Password reset successful', {
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            await this.logSecurityEvent({
                type: auth_1.SecurityEventType.PASSWORD_RESET_COMPLETE,
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
            });
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger_1.authLogger.error('Password reset failed', error, {
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            throw error;
        }
    }
    async sendEmailVerification(userId, context) {
        const startTime = Date.now();
        try {
            const user = await this.authRepository.findUserById(userId);
            if (!user) {
                const duration = Date.now() - startTime;
                logger_1.authLogger.warn('Email verification request for non-existent user', {
                    userId,
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    duration,
                });
                throw new errorHandler_1.NotFoundError('User not found');
            }
            if (user.isVerified) {
                const duration = Date.now() - startTime;
                logger_1.authLogger.info('Email verification request for already verified user', {
                    userId: user.id,
                    email: user.email,
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    duration,
                });
                return;
            }
            const verificationToken = (0, auth_3.generateSecureToken)(32);
            const expiresAt = new Date();
            expiresAt.setHours(expiresAt.getHours() + 24);
            await this.authRepository.setEmailVerificationToken(user.id, verificationToken, expiresAt);
            await this.emailService.sendEmailVerification(user.email, verificationToken, user.firstName);
            const duration = Date.now() - startTime;
            logger_1.authLogger.info('Email verification sent', {
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            await this.logSecurityEvent({
                type: auth_1.SecurityEventType.EMAIL_VERIFICATION_SENT,
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
            });
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger_1.authLogger.error('Email verification send failed', error, {
                userId,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            throw error;
        }
    }
    async verifyEmail(token, context) {
        const startTime = Date.now();
        logger_1.authLogger.info('Email verification attempt', {
            ipAddress: context.ipAddress,
            userAgent: context.userAgent,
        });
        try {
            const user = await this.authRepository.findUserByEmailVerificationToken(token);
            if (!user) {
                const duration = Date.now() - startTime;
                logger_1.authLogger.warn('Email verification failed: Invalid or expired token', {
                    ipAddress: context.ipAddress,
                    userAgent: context.userAgent,
                    duration,
                });
                throw new errorHandler_1.AuthenticationError('Invalid or expired verification token');
            }
            await this.authRepository.markEmailAsVerified(user.id);
            await this.emailService.sendWelcomeEmail(user.email, user.firstName);
            const duration = Date.now() - startTime;
            logger_1.authLogger.info('Email verification successful', {
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            await this.logSecurityEvent({
                type: auth_1.SecurityEventType.EMAIL_VERIFIED,
                userId: user.id,
                email: user.email,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
            });
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger_1.authLogger.error('Email verification failed', error, {
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                duration,
            });
            throw error;
        }
    }
    async generateTokenPair(userId, email, role, context) {
        const startTime = Date.now();
        try {
            const accessToken = (0, auth_2.generateAccessToken)({ userId, email, role }, this.jwtConfig.secret, this.jwtConfig.accessExpiresIn, this.jwtConfig.issuer, this.jwtConfig.audience);
            const refreshToken = (0, auth_2.generateRefreshToken)({ userId, email, role }, this.jwtConfig.refreshSecret, this.jwtConfig.refreshExpiresIn, this.jwtConfig.issuer, this.jwtConfig.audience);
            const refreshTokenExpiry = new Date();
            refreshTokenExpiry.setTime(refreshTokenExpiry.getTime() +
                this.parseJWTExpiry(this.jwtConfig.refreshExpiresIn));
            const sessionData = {
                id: (0, auth_3.generateSecureToken)(16),
                userId,
                refreshToken,
                ipAddress: context.ipAddress,
                userAgent: context.userAgent,
                expiresAt: refreshTokenExpiry,
            };
            logger_1.dbLogger.info('Creating user session', {
                table: 'Session',
                userId,
                ipAddress: context.ipAddress,
                expiresAt: sessionData.expiresAt,
            });
            await this.authRepository.createSession(sessionData);
            const duration = Date.now() - startTime;
            logger_1.authLogger.debug('Token pair generated successfully', {
                userId,
                email,
                role,
                duration,
                accessTokenExpiry: this.jwtConfig.accessExpiresIn,
                refreshTokenExpiry: this.jwtConfig.refreshExpiresIn,
            });
            return { accessToken, refreshToken };
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger_1.authLogger.error('Token pair generation failed', error, {
                userId,
                email,
                role,
                duration,
            });
            throw error;
        }
    }
    parseJWTExpiry(expiry) {
        const unit = expiry.slice(-1);
        const value = parseInt(expiry.slice(0, -1));
        switch (unit) {
            case 's':
                return value * 1000;
            case 'm':
                return value * 60 * 1000;
            case 'h':
                return value * 60 * 60 * 1000;
            case 'd':
                return value * 24 * 60 * 60 * 1000;
            default:
                return 15 * 60 * 1000;
        }
    }
    async logSecurityEvent(event) {
        try {
            await (0, auth_3.logSecurityEvent)(event);
            logger_1.securityLogger.info('Security event logged', {
                eventType: event.type,
                userId: event.userId,
                email: event.email,
                ipAddress: event.ipAddress,
            });
        }
        catch (error) {
            logger_1.securityLogger.error('Failed to log security event', error, {
                eventType: event.type,
                userId: event.userId,
                email: event.email,
            });
        }
    }
}
exports.AuthService = AuthService;
exports.default = AuthService;
//# sourceMappingURL=AuthService.js.map