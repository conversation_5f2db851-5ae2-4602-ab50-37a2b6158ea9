{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "rootDir": "./src", "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "moduleResolution": "node", "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/lib/*": ["./lib/*"], "@/types/*": ["./types/*"], "@/utils/*": ["./utils/*"], "@/controllers/*": ["./controllers/*"], "@/services/*": ["./services/*"], "@/middleware/*": ["./middleware/*"], "@/routes/*": ["./routes/*"], "@/config/*": ["./config/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"], "types": ["node", "jest"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "src/services/legacy/**/*"], "ts-node": {"esm": false, "experimentalSpecifierResolution": "node"}}