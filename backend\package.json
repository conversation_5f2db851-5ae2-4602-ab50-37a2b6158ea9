{"name": "printco-backend", "version": "1.0.0", "description": "PrintCo Backend API - Node.js/Express printing services platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate deploy", "db:migrate:dev": "prisma migrate dev", "db:seed": "tsx -P tsconfig.prisma.json prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset --force && npm run db:seed", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@prisma/client": "^6.12.0", "@types/nodemailer": "^6.4.17", "aws-sdk": "^2.1519.0", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.0-rc.3", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "redis": "^4.6.11", "sharp": "^0.33.1", "socket.io": "^4.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^4.0.10"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/passport": "^1.0.16", "@types/passport-google-oauth20": "^2.0.14", "@types/passport-jwt": "^3.0.13", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prisma": "^6.12.0", "supertest": "^7.1.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "keywords": ["printing", "api", "express", "typescript", "prisma", "postgresql"], "author": "PrintCo Team", "license": "MIT", "engines": {"node": ">=18.0.0"}}