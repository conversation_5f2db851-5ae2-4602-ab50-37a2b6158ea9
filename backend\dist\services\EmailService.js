"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createEmailService = exports.NodemailerEmailService = void 0;
const nodemailer_1 = __importDefault(require("nodemailer"));
const logger_1 = require("../utils/logger");
const logger = (0, logger_1.createLogger)('Email');
class NodemailerEmailService {
    transporter;
    emailConfig;
    constructor(emailConfig) {
        this.emailConfig = emailConfig;
        this.transporter = nodemailer_1.default.createTransport({
            service: emailConfig.service,
            host: emailConfig.host,
            port: emailConfig.port,
            secure: emailConfig.secure,
            auth: {
                user: emailConfig.user,
                pass: emailConfig.password,
            },
            tls: {
                rejectUnauthorized: false,
            },
        });
        logger.info('Email service initialized', {
            service: emailConfig.service,
            host: emailConfig.host,
            port: emailConfig.port,
            secure: emailConfig.secure,
            from: emailConfig.from,
        });
    }
    async sendPasswordResetEmail(email, resetToken, firstName) {
        const startTime = Date.now();
        try {
            const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
            const mailOptions = {
                from: this.emailConfig.from,
                to: email,
                subject: 'Password Reset Request - PrintWeditt',
                html: this.generatePasswordResetHTML(firstName, resetUrl, resetToken),
                text: this.generatePasswordResetText(firstName, resetUrl),
            };
            await this.transporter.sendMail(mailOptions);
            const duration = Date.now() - startTime;
            logger.info('Password reset email sent successfully', {
                email,
                firstName,
                duration,
            });
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger.error('Failed to send password reset email', error, {
                email,
                firstName,
                duration,
            });
            throw new Error('Failed to send password reset email');
        }
    }
    async sendEmailVerification(email, verificationToken, firstName) {
        const startTime = Date.now();
        try {
            const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${verificationToken}`;
            const mailOptions = {
                from: this.emailConfig.from,
                to: email,
                subject: 'Email Verification - PrintWeditt',
                html: this.generateEmailVerificationHTML(firstName, verificationUrl, verificationToken),
                text: this.generateEmailVerificationText(firstName, verificationUrl),
            };
            await this.transporter.sendMail(mailOptions);
            const duration = Date.now() - startTime;
            logger.info('Email verification sent successfully', {
                email,
                firstName,
                duration,
            });
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger.error('Failed to send email verification', error, {
                email,
                firstName,
                duration,
            });
            throw new Error('Failed to send email verification');
        }
    }
    async sendWelcomeEmail(email, firstName) {
        const startTime = Date.now();
        try {
            const loginUrl = `${process.env.FRONTEND_URL}/login`;
            const mailOptions = {
                from: this.emailConfig.from,
                to: email,
                subject: 'Welcome to PrintWeditt!',
                html: this.generateWelcomeHTML(firstName, loginUrl),
                text: this.generateWelcomeText(firstName, loginUrl),
            };
            await this.transporter.sendMail(mailOptions);
            const duration = Date.now() - startTime;
            logger.info('Welcome email sent successfully', {
                email,
                firstName,
                duration,
            });
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger.error('Failed to send welcome email', error, {
                email,
                firstName,
                duration,
            });
        }
    }
    generatePasswordResetHTML(firstName, resetUrl, token) {
        return `
			<!DOCTYPE html>
			<html>
			<head>
				<meta charset="utf-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<title>Password Reset - PrintWeditt</title>
				<style>
					body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
					.header { background: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
					.content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
					.button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
					.warning { background: #fef2f2; border: 1px solid #fecaca; padding: 15px; border-radius: 6px; margin: 20px 0; }
					.footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
				</style>
			</head>
			<body>
				<div class="header">
					<h1>Password Reset Request</h1>
				</div>
				<div class="content">
					<p>Hello ${firstName},</p>
					<p>We received a request to reset your password for your PrintWeditt account. If you made this request, click the button below to reset your password:</p>
					<p style="text-align: center;">
						<a href="${resetUrl}" class="button">Reset Password</a>
					</p>
					<div class="warning">
						<strong>Security Notice:</strong>
						<ul>
							<li>This link will expire in 1 hour</li>
							<li>If you didn't request this reset, please ignore this email</li>
							<li>Your password won't change until you create a new one</li>
						</ul>
					</div>
					<p>If the button doesn't work, copy and paste this link into your browser:</p>
					<p style="word-break: break-all; background: #e5e7eb; padding: 10px; border-radius: 4px;">${resetUrl}</p>
					<p>If you have any questions, please contact our support team.</p>
					<p>Best regards,<br>The PrintWeditt Team</p>
				</div>
				<div class="footer">
					<p>This is an automated message from PrintWeditt. Please do not reply to this email.</p>
					<p>Reset Token: ${token.substring(0, 8)}...</p>
				</div>
			</body>
			</html>
		`;
    }
    generatePasswordResetText(firstName, resetUrl) {
        return `
Hello ${firstName},

We received a request to reset your password for your PrintWeditt account.

To reset your password, visit this link:
${resetUrl}

Security Notice:
- This link will expire in 1 hour
- If you didn't request this reset, please ignore this email
- Your password won't change until you create a new one

If you have any questions, please contact our support team.

Best regards,
The PrintWeditt Team

This is an automated message from PrintWeditt. Please do not reply to this email.
		`;
    }
    generateEmailVerificationHTML(firstName, verificationUrl, token) {
        return `
			<!DOCTYPE html>
			<html>
			<head>
				<meta charset="utf-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<title>Email Verification - PrintWeditt</title>
				<style>
					body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
					.header { background: #059669; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
					.content { background: #f0fdf4; padding: 30px; border-radius: 0 0 8px 8px; }
					.button { display: inline-block; background: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
					.info { background: #eff6ff; border: 1px solid #bfdbfe; padding: 15px; border-radius: 6px; margin: 20px 0; }
					.footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
				</style>
			</head>
			<body>
				<div class="header">
					<h1>Verify Your Email</h1>
				</div>
				<div class="content">
					<p>Hello ${firstName},</p>
					<p>Thank you for registering with PrintWeditt! To complete your account setup, please verify your email address by clicking the button below:</p>
					<p style="text-align: center;">
						<a href="${verificationUrl}" class="button">Verify Email</a>
					</p>
					<div class="info">
						<strong>Important:</strong>
						<ul>
							<li>This verification link will expire in 24 hours</li>
							<li>You won't be able to access all features until your email is verified</li>
							<li>If you didn't create this account, please ignore this email</li>
						</ul>
					</div>
					<p>If the button doesn't work, copy and paste this link into your browser:</p>
					<p style="word-break: break-all; background: #e5e7eb; padding: 10px; border-radius: 4px;">${verificationUrl}</p>
					<p>Welcome to PrintWeditt! We're excited to have you on board.</p>
					<p>Best regards,<br>The PrintWeditt Team</p>
				</div>
				<div class="footer">
					<p>This is an automated message from PrintWeditt. Please do not reply to this email.</p>
					<p>Verification Token: ${token.substring(0, 8)}...</p>
				</div>
			</body>
			</html>
		`;
    }
    generateEmailVerificationText(firstName, verificationUrl) {
        return `
Hello ${firstName},

Thank you for registering with PrintWeditt!

To complete your account setup, please verify your email address by visiting this link:
${verificationUrl}

Important:
- This verification link will expire in 24 hours
- You won't be able to access all features until your email is verified
- If you didn't create this account, please ignore this email

Welcome to PrintWeditt! We're excited to have you on board.

Best regards,
The PrintWeditt Team

This is an automated message from PrintWeditt. Please do not reply to this email.
		`;
    }
    generateWelcomeHTML(firstName, loginUrl) {
        return `
			<!DOCTYPE html>
			<html>
			<head>
				<meta charset="utf-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<title>Welcome to PrintWeditt!</title>
				<style>
					body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
					.header { background: #7c3aed; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
					.content { background: #faf5ff; padding: 30px; border-radius: 0 0 8px 8px; }
					.button { display: inline-block; background: #7c3aed; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
					.features { background: white; border: 1px solid #e5e7eb; padding: 20px; border-radius: 6px; margin: 20px 0; }
					.footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
				</style>
			</head>
			<body>
				<div class="header">
					<h1>Welcome to PrintWeditt!</h1>
				</div>
				<div class="content">
					<p>Hello ${firstName},</p>
					<p>Welcome to PrintWeditt! Your account has been successfully created and verified. We're thrilled to have you join our printing services platform.</p>
					<div class="features">
						<h3>What you can do now:</h3>
						<ul>
							<li>Browse our wide range of printing services</li>
							<li>Upload and manage your design files</li>
							<li>Get quotes from trusted printing providers</li>
							<li>Track your orders in real-time</li>
							<li>Build your professional printing network</li>
						</ul>
					</div>
					<p style="text-align: center;">
						<a href="${loginUrl}" class="button">Get Started</a>
					</p>
					<p>If you have any questions or need assistance, our support team is here to help. Don't hesitate to reach out!</p>
					<p>Happy printing!<br>The PrintWeditt Team</p>
				</div>
				<div class="footer">
					<p>This is an automated message from PrintWeditt. Please do not reply to this email.</p>
				</div>
			</body>
			</html>
		`;
    }
    generateWelcomeText(firstName, loginUrl) {
        return `
Hello ${firstName},

Welcome to PrintWeditt! Your account has been successfully created and verified.

What you can do now:
- Browse our wide range of printing services
- Upload and manage your design files
- Get quotes from trusted printing providers
- Track your orders in real-time
- Build your professional printing network

Get started: ${loginUrl}

If you have any questions or need assistance, our support team is here to help.

Happy printing!
The PrintWeditt Team

This is an automated message from PrintWeditt. Please do not reply to this email.
		`;
    }
    async verifyConnection() {
        try {
            await this.transporter.verify();
            logger.info('Email service connection verified successfully');
            return true;
        }
        catch (error) {
            logger.error('Email service connection failed', error);
            return false;
        }
    }
}
exports.NodemailerEmailService = NodemailerEmailService;
const createEmailService = (emailConfig) => {
    return new NodemailerEmailService(emailConfig);
};
exports.createEmailService = createEmailService;
exports.default = NodemailerEmailService;
//# sourceMappingURL=EmailService.js.map