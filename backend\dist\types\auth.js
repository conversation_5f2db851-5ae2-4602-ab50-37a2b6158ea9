"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityEventType = void 0;
var SecurityEventType;
(function (SecurityEventType) {
    SecurityEventType["LOGIN_SUCCESS"] = "LOGIN_SUCCESS";
    SecurityEventType["LOGIN_FAILURE"] = "LOGIN_FAILURE";
    SecurityEventType["PASSWORD_CHANGE"] = "PASSWORD_CHANGE";
    SecurityEventType["PASSWORD_RESET_REQUEST"] = "PASSWORD_RESET_REQUEST";
    SecurityEventType["PASSWORD_RESET_COMPLETE"] = "PASSWORD_RESET_COMPLETE";
    SecurityEventType["EMAIL_VERIFICATION_SENT"] = "EMAIL_VERIFICATION_SENT";
    SecurityEventType["EMAIL_VERIFIED"] = "EMAIL_VERIFIED";
    SecurityEventType["ACCOUNT_LOCKED"] = "ACCOUNT_LOCKED";
    SecurityEventType["ACCOUNT_UNLOCKED"] = "ACCOUNT_UNLOCKED";
    SecurityEventType["TOKEN_REFRESH"] = "TOKEN_REFRESH";
    SecurityEventType["LOGOUT"] = "LOGOUT";
})(SecurityEventType || (exports.SecurityEventType = SecurityEventType = {}));
//# sourceMappingURL=auth.js.map