import { User, UserRole } from '@prisma/client';

// User creation request
export interface CreateUserRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role?: UserRole;
  isActive?: boolean;
  isVerified?: boolean;
}

// User update request
export interface UpdateUserRequest {
  email?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  role?: UserRole;
  isActive?: boolean;
  isVerified?: boolean;
  password?: string;
}

// User list query parameters
export interface UserListQuery {
  page?: number;
  limit?: number;
  search?: string;
  role?: UserRole;
  isActive?: boolean;
  isVerified?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// User list response
export interface UserListResponse {
  users: UserSummary[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// User summary (for list views)
export interface UserSummary {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  isActive: boolean;
  isVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// User detail (for single user views)
export interface UserDetail extends UserSummary {
  phone?: string;
  lastLoginAt?: Date;
  avatar?: string;
  emailVerified?: Date;
  loginAttempts?: number;
  lockedUntil?: Date;
}

// User statistics
export interface UserStats {
  total: number;
  totalUsers: number;
  active: number;
  activeUsers: number;
  inactive: number;
  verified: number;
  verifiedUsers: number;
  unverified: number;
  byRole: Record<UserRole, number>;
  usersByRole: Record<UserRole, number>;
  recentRegistrations: number;
  averageRegistrationsPerDay: number;
  newUsersThisMonth: number;
}

// Bulk user operations
export interface BulkUserOperation {
  userIds: string[];
  updates?: Partial<UpdateUserRequest>;
}

// User search result
export interface UserSearchResult {
  users: UserSummary[];
  total: number;
  query: string;
}

// Repository data types (sanitized for storage)
export interface CreateUserData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string | null;
  role: UserRole;
  isActive: boolean;
  isVerified: boolean;
}

export interface UpdateUserData {
  email?: string;
  firstName?: string;
  lastName?: string;
  phone?: string | null;
  role?: UserRole;
  isActive?: boolean;
  isVerified?: boolean;
  password?: string;
} 