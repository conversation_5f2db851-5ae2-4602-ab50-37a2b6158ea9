/**
 * Application Information Utilities
 * Following DRY principle - centralized app info functions
 */

/**
 * Get application version from package.json or default
 */
export const getAppVersion = (): string => 
  process.env.npm_package_version || '1.0.0';

/**
 * Get current ISO timestamp
 */
export const getCurrentTimestamp = (): string => 
  new Date().toISOString();

/**
 * Get application runtime info
 */
export const getAppRuntimeInfo = () => ({
  version: getAppVersion(),
  nodeVersion: process.version,
  platform: process.platform,
  architecture: process.arch,
  memoryUsage: process.memoryUsage(),
  uptime: process.uptime(),
  pid: process.pid,
});

/**
 * Generate consistent error response format
 */
export const createErrorResponse = (error: string, message: string, path: string, method: string) => ({
  error,
  message,
  path,
  method,
  timestamp: getCurrentTimestamp(),
});

/**
 * Generate consistent success response format
 */
export const createSuccessResponse = (message: string, data?: any) => ({
  success: true,
  message,
  timestamp: getCurrentTimestamp(),
  ...(data && { data }),
});