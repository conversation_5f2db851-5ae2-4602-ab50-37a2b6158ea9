{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/utils/auth.ts"], "names": [], "mappings": ";;;;;;AAAA,wDAA8B;AAC9B,oDAA4B;AAC5B,2CAA8C;AAE9C,6CAA+E;AAE/E,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAG3B,MAAM,YAAY,GAAG,KAAK,EAAE,QAAgB,EAAmB,EAAE;IACtE,MAAM,UAAU,GAAG,EAAE,CAAC;IACtB,OAAO,kBAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAC3C,CAAC,CAAC;AAHW,QAAA,YAAY,gBAGvB;AAGK,MAAM,cAAc,GAAG,KAAK,EAAE,QAAgB,EAAE,IAAY,EAAoB,EAAE;IACvF,OAAO,kBAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACxC,CAAC,CAAC;AAFW,QAAA,cAAc,kBAEzB;AAGK,MAAM,mBAAmB,GAAG,CAAC,SAAiB,EAAE,EAAU,EAAE;IACjE,OAAO,gBAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACpD,CAAC,CAAC;AAFW,QAAA,mBAAmB,uBAE9B;AAGK,MAAM,yBAAyB,GAAG,GAAqC,EAAE;IAC9E,MAAM,KAAK,GAAG,IAAA,2BAAmB,EAAC,EAAE,CAAC,CAAC;IACtC,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;IAC3B,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC;IAE1C,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;AAC5B,CAAC,CAAC;AANW,QAAA,yBAAyB,6BAMpC;AAGK,MAAM,0BAA0B,GAAG,GAAqC,EAAE;IAC/E,MAAM,KAAK,GAAG,IAAA,2BAAmB,EAAC,EAAE,CAAC,CAAC;IACtC,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;IAC3B,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAEzC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;AAC5B,CAAC,CAAC;AANW,QAAA,0BAA0B,8BAMrC;AAGK,MAAM,iBAAiB,GAAG,KAAK,EACpC,MAAc,EACd,KAAa,EACb,IAAS,EACT,SAAiB,EACjB,SAAiB,EACG,EAAE;IACtB,MAAM,OAAO,GAAe;QAC1B,MAAM;QACN,KAAK;QACL,IAAI;KACL,CAAC;IAEF,MAAM,WAAW,GAAG,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;IACjD,MAAM,YAAY,GAAG,IAAA,2BAAoB,EAAC,OAAO,CAAC,CAAC;IAGnD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;IAE3C,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;QAChC,IAAI,EAAE;YACJ,EAAE,EAAE,IAAA,2BAAmB,EAAC,EAAE,CAAC;YAC3B,MAAM;YACN,YAAY;YACZ,SAAS;YACT,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;YACrC,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;YACtC,QAAQ,EAAE,IAAI;SACf;KACF,CAAC,CAAC;IAEH,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,CAAC;AACvC,CAAC,CAAC;AAjCW,QAAA,iBAAiB,qBAiC5B;AAGK,MAAM,kBAAkB,GAAG,KAAK,EAAE,YAAoB,EAAiB,EAAE;IAC9E,MAAM,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;QACpC,KAAK,EAAE,EAAE,YAAY,EAAE;QACvB,IAAI,EAAE;YACJ,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB;KACF,CAAC,CAAC;AACL,CAAC,CAAC;AARW,QAAA,kBAAkB,sBAQ7B;AAGK,MAAM,qBAAqB,GAAG,KAAK,EAAE,MAAc,EAAiB,EAAE;IAC3E,MAAM,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;QACpC,KAAK,EAAE,EAAE,MAAM,EAAE;QACjB,IAAI,EAAE;YACJ,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB;KACF,CAAC,CAAC;AACL,CAAC,CAAC;AARW,QAAA,qBAAqB,yBAQhC;AAGK,MAAM,mBAAmB,GAAG,KAAK,EAAE,YAAoB,EAAoB,EAAE;IAClF,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;QACpD,KAAK,EAAE,EAAE,YAAY,EAAE;KACxB,CAAC,CAAC;IAEH,IAAI,CAAC,OAAO;QAAE,OAAO,KAAK,CAAC;IAC3B,IAAI,CAAC,OAAO,CAAC,QAAQ;QAAE,OAAO,KAAK,CAAC;IACpC,IAAI,OAAO,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE;QAAE,OAAO,KAAK,CAAC;IAEjD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAVW,QAAA,mBAAmB,uBAU9B;AAGK,MAAM,sBAAsB,GAAG,KAAK,IAAqB,EAAE;IAChE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;QACnD,KAAK,EAAE;YACL,EAAE,EAAE;gBACF,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,EAAE;gBACjC,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE;aACxF;SACF;KACF,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC,KAAK,CAAC;AACtB,CAAC,CAAC;AAXW,QAAA,sBAAsB,0BAWjC;AAGK,MAAM,sBAAsB,GAAG,KAAK,EAAE,MAAc,EAAiB,EAAE;IAC5E,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACxC,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;QACrB,MAAM,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE;KAChC,CAAC,CAAC;IAEH,IAAI,CAAC,IAAI;QAAE,OAAO;IAElB,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;IAC3C,MAAM,WAAW,GAAG,CAAC,CAAC;IAEtB,MAAM,UAAU,GAAQ;QACtB,aAAa,EAAE,WAAW;KAC3B,CAAC;IAGF,IAAI,WAAW,IAAI,WAAW,EAAE,CAAC;QAC/B,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;QAClD,UAAU,CAAC,WAAW,GAAG,SAAS,CAAC;IACrC,CAAC;IAED,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;QACvB,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;QACrB,IAAI,EAAE,UAAU;KACjB,CAAC,CAAC;AACL,CAAC,CAAC;AA1BW,QAAA,sBAAsB,0BA0BjC;AAGK,MAAM,kBAAkB,GAAG,KAAK,EAAE,MAAc,EAAiB,EAAE;IACxE,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;QACvB,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;QACrB,IAAI,EAAE;YACJ,aAAa,EAAE,CAAC;YAChB,WAAW,EAAE,IAAI;YACjB,WAAW,EAAE,IAAI,IAAI,EAAE;SACxB;KACF,CAAC,CAAC;AACL,CAAC,CAAC;AATW,QAAA,kBAAkB,sBAS7B;AAGK,MAAM,eAAe,GAAG,KAAK,EAAE,MAAc,EAAoB,EAAE;IACxE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACxC,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;QACrB,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;KAC9B,CAAC,CAAC;IAEH,IAAI,CAAC,IAAI,EAAE,WAAW;QAAE,OAAO,KAAK,CAAC;IAErC,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;SAAM,CAAC;QAEN,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;YACrB,IAAI,EAAE;gBACJ,WAAW,EAAE,IAAI;gBACjB,aAAa,EAAE,CAAC;aACjB;SACF,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAC;AArBW,QAAA,eAAe,mBAqB1B;AAGK,MAAM,kBAAkB,GAAG,KAAK,EAAE,MAAc,EAAE,EAAE;IACzD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACxC,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;QACrB,MAAM,EAAE;YACN,aAAa,EAAE,IAAI;YACnB,WAAW,EAAE,IAAI;SAClB;KACF,CAAC,CAAC;IAEH,IAAI,CAAC,IAAI;QAAE,OAAO,IAAI,CAAC;IAEvB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IAC1E,MAAM,WAAW,GAAG,CAAC,CAAC;IAEtB,OAAO;QACL,QAAQ;QACR,WAAW,EAAE,IAAI,CAAC,WAAW;QAC7B,QAAQ,EAAE,IAAI,CAAC,aAAa;QAC5B,WAAW;QACX,iBAAiB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC;KACjE,CAAC;AACJ,CAAC,CAAC;AArBW,QAAA,kBAAkB,sBAqB7B;AAGK,MAAM,gBAAgB,GAAG,KAAK,EAAE,KAAuC,EAAiB,EAAE;IAG/F,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE;YAC7B,GAAG,KAAK;YACR,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;AAGH,CAAC,CAAC;AAXW,QAAA,gBAAgB,oBAW3B;AAGK,MAAM,WAAW,GAAG,CAAC,GAAQ,EAAU,EAAE;IAC9C,OAAO,CACL,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7C,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC;QACxB,GAAG,CAAC,UAAU,EAAE,aAAa;QAC7B,GAAG,CAAC,MAAM,EAAE,aAAa;QACzB,GAAG,CAAC,EAAE;QACN,SAAS,CACV,CAAC;AACJ,CAAC,CAAC;AATW,QAAA,WAAW,eAStB;AAGK,MAAM,YAAY,GAAG,CAAC,GAAQ,EAAU,EAAE;IAC/C,OAAO,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,SAAS,CAAC;AAChD,CAAC,CAAC;AAFW,QAAA,YAAY,gBAEvB;AAGK,MAAM,YAAY,GAAG,CAAC,IAAS,EAAE,EAAE;IACxC,MAAM,EAAE,QAAQ,EAAE,kBAAkB,EAAE,yBAAyB,EACvD,sBAAsB,EAAE,6BAA6B,EACrD,aAAa,EAAE,WAAW,EAAE,GAAG,aAAa,EAAE,GAAG,IAAI,CAAC;IAC9D,OAAO,aAAa,CAAC;AACvB,CAAC,CAAC;AALW,QAAA,YAAY,gBAKvB;AAGK,MAAM,iBAAiB,GAAG,CAAC,OAAgB,EAAE,IAAU,EAAE,OAAgB,EAAE,EAAE;IAClF,OAAO;QACL,OAAO;QACP,IAAI;QACJ,OAAO;QACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC;AACJ,CAAC,CAAC;AAPW,QAAA,iBAAiB,qBAO5B;AAEF,kBAAe;IACb,YAAY,EAAZ,oBAAY;IACZ,cAAc,EAAd,sBAAc;IACd,mBAAmB,EAAnB,2BAAmB;IACnB,yBAAyB,EAAzB,iCAAyB;IACzB,0BAA0B,EAA1B,kCAA0B;IAC1B,iBAAiB,EAAjB,yBAAiB;IACjB,kBAAkB,EAAlB,0BAAkB;IAClB,qBAAqB,EAArB,6BAAqB;IACrB,mBAAmB,EAAnB,2BAAmB;IACnB,sBAAsB,EAAtB,8BAAsB;IACtB,sBAAsB,EAAtB,8BAAsB;IACtB,kBAAkB,EAAlB,0BAAkB;IAClB,eAAe,EAAf,uBAAe;IACf,kBAAkB,EAAlB,0BAAkB;IAClB,gBAAgB,EAAhB,wBAAgB;IAChB,WAAW,EAAX,mBAAW;IACX,YAAY,EAAZ,oBAAY;IACZ,YAAY,EAAZ,oBAAY;IACZ,iBAAiB,EAAjB,yBAAiB;CAClB,CAAC"}