{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/utils/auth.ts"], "names": [], "mappings": ";;;;;;AAAA,wDAA8B;AAC9B,oDAA4B;AAO5B,6CAA6E;AAC7E,0CAA4C;AAC5C,wDAAuE;AAGvE,MAAM,eAAe,GAAG,GAAG,EAAE,CAAC,0BAAe,CAAC,SAAS,EAAE,CAAC;AAGnD,MAAM,YAAY,GAAG,KAAK,EAChC,QAAgB,EAChB,eAAuB,EAAE,EACP,EAAE;IACpB,OAAO,kBAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AAC5C,CAAC,CAAC;AALW,QAAA,YAAY,gBAKvB;AAGK,MAAM,cAAc,GAAG,KAAK,EAClC,QAAgB,EAChB,IAAY,EACO,EAAE;IACrB,OAAO,kBAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACvC,CAAC,CAAC;AALW,QAAA,cAAc,kBAKzB;AAGK,MAAM,mBAAmB,GAAG,CAAC,SAAiB,EAAE,EAAU,EAAE;IAClE,OAAO,gBAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACnD,CAAC,CAAC;AAFW,QAAA,mBAAmB,uBAE9B;AAGK,MAAM,yBAAyB,GAAG,GAAmC,EAAE;IAC7E,MAAM,KAAK,GAAG,IAAA,2BAAmB,EAAC,EAAE,CAAC,CAAC;IACtC,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;IAC3B,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC;IAE1C,OAAO,EAAC,KAAK,EAAE,OAAO,EAAC,CAAC;AACzB,CAAC,CAAC;AANW,QAAA,yBAAyB,6BAMpC;AAGK,MAAM,0BAA0B,GAAG,GAGxC,EAAE;IACH,MAAM,KAAK,GAAG,IAAA,2BAAmB,EAAC,EAAE,CAAC,CAAC;IACtC,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;IAC3B,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAEzC,OAAO,EAAC,KAAK,EAAE,OAAO,EAAC,CAAC;AACzB,CAAC,CAAC;AATW,QAAA,0BAA0B,8BASrC;AAGK,MAAM,iBAAiB,GAAG,KAAK,EACrC,MAAc,EACd,KAAa,EACb,IAAS,EACT,SAAiB,EACjB,SAAiB,EACI,EAAE;IACvB,MAAM,OAAO,GAAe;QAC3B,MAAM;QACN,KAAK;QACL,IAAI;KACJ,CAAC;IAEF,MAAM,WAAW,GAAG,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;IACjD,MAAM,YAAY,GAAG,IAAA,2BAAoB,EAAC,OAAO,CAAC,CAAC;IAGnD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;IAE3C,MAAM,eAAe,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC;QAC5C,IAAI,EAAE;YACL,EAAE,EAAE,IAAA,2BAAmB,EAAC,EAAE,CAAC;YAC3B,MAAM;YACN,YAAY;YACZ,SAAS;YACT,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;YACrC,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;YACtC,QAAQ,EAAE,IAAI;SACd;KACD,CAAC,CAAC;IAEH,OAAO,EAAC,WAAW,EAAE,YAAY,EAAC,CAAC;AACpC,CAAC,CAAC;AAjCW,QAAA,iBAAiB,qBAiC5B;AAGK,MAAM,kBAAkB,GAAG,KAAK,EACtC,YAAoB,EACJ,EAAE;IAClB,MAAM,eAAe,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC;QAChD,KAAK,EAAE,EAAC,YAAY,EAAC;QACrB,IAAI,EAAE;YACL,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,IAAI,IAAI,EAAE;SACrB;KACD,CAAC,CAAC;AACJ,CAAC,CAAC;AAVW,QAAA,kBAAkB,sBAU7B;AAGK,MAAM,qBAAqB,GAAG,KAAK,EAAE,MAAc,EAAiB,EAAE;IAC5E,MAAM,eAAe,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC;QAChD,KAAK,EAAE,EAAC,MAAM,EAAC;QACf,IAAI,EAAE;YACL,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,IAAI,IAAI,EAAE;SACrB;KACD,CAAC,CAAC;AACJ,CAAC,CAAC;AARW,QAAA,qBAAqB,yBAQhC;AAGK,MAAM,mBAAmB,GAAG,KAAK,EACvC,YAAoB,EACD,EAAE;IACrB,MAAM,OAAO,GAAG,MAAM,eAAe,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC;QAChE,KAAK,EAAE,EAAC,YAAY,EAAC;KACrB,CAAC,CAAC;IAEH,IAAI,CAAC,OAAO;QAAE,OAAO,KAAK,CAAC;IAC3B,IAAI,CAAC,OAAO,CAAC,QAAQ;QAAE,OAAO,KAAK,CAAC;IACpC,IAAI,OAAO,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE;QAAE,OAAO,KAAK,CAAC;IAEjD,OAAO,IAAI,CAAC;AACb,CAAC,CAAC;AAZW,QAAA,mBAAmB,uBAY9B;AAGK,MAAM,sBAAsB,GAAG,KAAK,IAAqB,EAAE;IACjE,MAAM,MAAM,GAAG,MAAM,eAAe,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC;QAC/D,KAAK,EAAE;YACN,EAAE,EAAE;gBACH,EAAC,SAAS,EAAE,EAAC,EAAE,EAAE,IAAI,IAAI,EAAE,EAAC,EAAC;gBAC7B;oBACC,QAAQ,EAAE,KAAK;oBACf,SAAS,EAAE,EAAC,EAAE,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAC;iBAChE;aACD;SACD;KACD,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC,KAAK,CAAC;AACrB,CAAC,CAAC;AAdW,QAAA,sBAAsB,0BAcjC;AAGK,MAAM,sBAAsB,GAAG,KAAK,EAAE,MAAc,EAAiB,EAAE;IAC7E,MAAM,IAAI,GAAG,MAAM,eAAe,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC;QACpD,KAAK,EAAE,EAAC,EAAE,EAAE,MAAM,EAAC;QACnB,MAAM,EAAE,EAAC,aAAa,EAAE,IAAI,EAAC;KAC7B,CAAC,CAAC;IAEH,IAAI,CAAC,IAAI;QAAE,OAAO;IAElB,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;IAC3C,MAAM,WAAW,GAAG,CAAC,CAAC;IAEtB,MAAM,UAAU,GAAQ;QACvB,aAAa,EAAE,WAAW;KAC1B,CAAC;IAGF,IAAI,WAAW,IAAI,WAAW,EAAE,CAAC;QAChC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;QAClD,UAAU,CAAC,WAAW,GAAG,SAAS,CAAC;IACpC,CAAC;IAED,MAAM,eAAe,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;QACnC,KAAK,EAAE,EAAC,EAAE,EAAE,MAAM,EAAC;QACnB,IAAI,EAAE,UAAU;KAChB,CAAC,CAAC;AACJ,CAAC,CAAC;AA1BW,QAAA,sBAAsB,0BA0BjC;AAGK,MAAM,kBAAkB,GAAG,KAAK,EAAE,MAAc,EAAiB,EAAE;IACzE,MAAM,eAAe,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;QACnC,KAAK,EAAE,EAAC,EAAE,EAAE,MAAM,EAAC;QACnB,IAAI,EAAE;YACL,aAAa,EAAE,CAAC;YAChB,WAAW,EAAE,IAAI;YACjB,WAAW,EAAE,IAAI,IAAI,EAAE;SACvB;KACD,CAAC,CAAC;AACJ,CAAC,CAAC;AATW,QAAA,kBAAkB,sBAS7B;AAGK,MAAM,eAAe,GAAG,KAAK,EAAE,MAAc,EAAoB,EAAE;IACzE,MAAM,IAAI,GAAG,MAAM,eAAe,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC;QACpD,KAAK,EAAE,EAAC,EAAE,EAAE,MAAM,EAAC;QACnB,MAAM,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;KAC3B,CAAC,CAAC;IAEH,IAAI,CAAC,IAAI,EAAE,WAAW;QAAE,OAAO,KAAK,CAAC;IAErC,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;QACnC,OAAO,IAAI,CAAC;IACb,CAAC;SAAM,CAAC;QAEP,MAAM,eAAe,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;YACnC,KAAK,EAAE,EAAC,EAAE,EAAE,MAAM,EAAC;YACnB,IAAI,EAAE;gBACL,WAAW,EAAE,IAAI;gBACjB,aAAa,EAAE,CAAC;aAChB;SACD,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACd,CAAC;AACF,CAAC,CAAC;AArBW,QAAA,eAAe,mBAqB1B;AAGK,MAAM,kBAAkB,GAAG,KAAK,EAAE,MAAc,EAAE,EAAE;IAC1D,MAAM,IAAI,GAAG,MAAM,eAAe,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC;QACpD,KAAK,EAAE,EAAC,EAAE,EAAE,MAAM,EAAC;QACnB,MAAM,EAAE;YACP,aAAa,EAAE,IAAI;YACnB,WAAW,EAAE,IAAI;SACjB;KACD,CAAC,CAAC;IAEH,IAAI,CAAC,IAAI;QAAE,OAAO,IAAI,CAAC;IAEvB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IAC1E,MAAM,WAAW,GAAG,CAAC,CAAC;IAEtB,OAAO;QACN,QAAQ;QACR,WAAW,EAAE,IAAI,CAAC,WAAW;QAC7B,QAAQ,EAAE,IAAI,CAAC,aAAa;QAC5B,WAAW;QACX,iBAAiB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC;KAChE,CAAC;AACH,CAAC,CAAC;AArBW,QAAA,kBAAkB,sBAqB7B;AAGK,MAAM,gBAAgB,GAAG,KAAK,EACpC,KAAuC,EACvB,EAAE;IAGlB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE;YAC9B,GAAG,KAAK;YACR,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACnC,CAAC,CAAC;IACJ,CAAC;AAGF,CAAC,CAAC;AAbW,QAAA,gBAAgB,oBAa3B;AAGK,MAAM,WAAW,GAAG,CAAC,GAAQ,EAAU,EAAE;IAC/C,OAAO,CACN,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7C,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC;QACxB,GAAG,CAAC,UAAU,EAAE,aAAa;QAC7B,GAAG,CAAC,MAAM,EAAE,aAAa;QACzB,GAAG,CAAC,EAAE;QACN,SAAS,CACT,CAAC;AACH,CAAC,CAAC;AATW,QAAA,WAAW,eAStB;AAGK,MAAM,YAAY,GAAG,CAAC,GAAQ,EAAU,EAAE;IAChD,OAAO,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,SAAS,CAAC;AAC/C,CAAC,CAAC;AAFW,QAAA,YAAY,gBAEvB;AAGK,MAAM,YAAY,GAAG,CAAC,IAAS,EAAE,EAAE;IACzC,OAAO,IAAA,2BAAgB,EAAC,IAAI,CAAC,CAAC;AAC/B,CAAC,CAAC;AAFW,QAAA,YAAY,gBAEvB;AAGK,MAAM,iBAAiB,GAAG,CAChC,OAAgB,EAChB,IAAU,EACV,OAAgB,EACf,EAAE;IACH,OAAO;QACN,OAAO;QACP,IAAI;QACJ,OAAO;QACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACnC,CAAC;AACH,CAAC,CAAC;AAXW,QAAA,iBAAiB,qBAW5B;AAEF,kBAAe;IACd,YAAY,EAAZ,oBAAY;IACZ,cAAc,EAAd,sBAAc;IACd,mBAAmB,EAAnB,2BAAmB;IACnB,yBAAyB,EAAzB,iCAAyB;IACzB,0BAA0B,EAA1B,kCAA0B;IAC1B,iBAAiB,EAAjB,yBAAiB;IACjB,kBAAkB,EAAlB,0BAAkB;IAClB,qBAAqB,EAArB,6BAAqB;IACrB,mBAAmB,EAAnB,2BAAmB;IACnB,sBAAsB,EAAtB,8BAAsB;IACtB,sBAAsB,EAAtB,8BAAsB;IACtB,kBAAkB,EAAlB,0BAAkB;IAClB,eAAe,EAAf,uBAAe;IACf,kBAAkB,EAAlB,0BAAkB;IAClB,gBAAgB,EAAhB,wBAAgB;IAChB,WAAW,EAAX,mBAAW;IACX,YAAY,EAAZ,oBAAY;IACZ,YAAY,EAAZ,oBAAY;IACZ,iBAAiB,EAAjB,yBAAiB;CACjB,CAAC"}