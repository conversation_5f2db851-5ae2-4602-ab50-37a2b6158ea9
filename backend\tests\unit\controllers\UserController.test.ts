import { UserController } from '@/controllers/UserController';
import { IUserService } from '@/services/UserService';
import { AuthenticatedRequest } from '@/types/auth';
import {
  CreateUserRequest,
  UpdateUserRequest,
  UserDetail,
  UserListResponse,
  UserStats,
  UserSummary,
} from '@/types/user';
import { UserRole } from '@prisma/client';
import { Response } from 'express';

jest.mock('@/middleware/errorHandler', () => ({
  asyncHandler: (fn: any) => fn,
}));

jest.mock('@/utils/logger', () => ({
  createLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  }),
}));

jest.mock('@/utils/responseWrapper', () => ({
  createPaginationMeta: jest.fn().mockReturnValue({
    currentPage: 1,
    totalPages: 1,
    hasNext: false,
    hasPrev: false,
  }),
}));

describe('UserController', () => {
  let userController: UserController;
  let mockUserService: jest.Mocked<IUserService>;
  let mockResponse: Partial<Response>;
  let mockRequest: Partial<AuthenticatedRequest>;

  beforeEach(() => {
    // Mock the logger
    const { createLogger } = require('@/utils/logger');
    createLogger.mockReturnValue({
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    });

    mockUserService = {
      getUserById: jest.fn(),
      createUser: jest.fn(),
      updateUser: jest.fn(),
      deleteUser: jest.fn(),
      getUsers: jest.fn(),
      searchUsers: jest.fn(),
      getUserStats: jest.fn(),
      getUsersByRole: jest.fn(),
      getActiveUsers: jest.fn(),
      getVerifiedUsers: jest.fn(),
      bulkUpdateUsers: jest.fn(),
      bulkDeleteUsers: jest.fn(),
      userExists: jest.fn(),
      countUsers: jest.fn(),
      countUsersByRole: jest.fn(),
    } as any;

    mockResponse = {
      getHeader: jest.fn().mockReturnValue('request-id-123'),
      success: jest.fn(),
      created: jest.fn(),
    };

    mockRequest = {
      userId: 'requesting-user-123',
      params: {},
      body: {},
      query: {},
    };

    userController = new UserController(mockUserService);
    jest.clearAllMocks();
  });

  describe('getUserById', () => {
    it('should successfully get user by ID', async () => {
      const mockUser: UserDetail = {
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: UserRole.CUSTOMER,
        isActive: true,
        isVerified: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        phone: undefined,
        lastLoginAt: undefined,
        avatar: undefined,
        emailVerified: undefined,
        loginAttempts: 0,
        lockedUntil: undefined,
      };
      mockRequest.params = { id: 'user-123' };
      mockUserService.getUserById.mockResolvedValue(mockUser);

      await userController.getUserById(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.getUserById).toHaveBeenCalledWith('user-123');
      expect(mockResponse.success).toHaveBeenCalledWith({ user: mockUser });
    });

    it('should handle errors when getting user by ID', async () => {
      mockRequest.params = { id: 'user-123' };
      const error = new Error('User not found');
      mockUserService.getUserById.mockRejectedValue(error);

      await expect(
        userController.getUserById(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn())
      ).rejects.toThrow('User not found');
    });
  });

  describe('createUser', () => {
    it('should successfully create a new user', async () => {
      const userData: CreateUserRequest = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: 'password123',
        role: UserRole.CUSTOMER,
      };
      const mockCreatedUser: UserDetail = {
        id: 'new-user-123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: UserRole.CUSTOMER,
        isActive: true,
        isVerified: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        phone: undefined,
        lastLoginAt: undefined,
        avatar: undefined,
        emailVerified: undefined,
        loginAttempts: 0,
        lockedUntil: undefined,
      };

      mockRequest.body = userData;
      mockUserService.createUser.mockResolvedValue(mockCreatedUser);

      await userController.createUser(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.createUser).toHaveBeenCalledWith(userData);
      expect(mockResponse.created).toHaveBeenCalledWith({ user: mockCreatedUser }, 'User created successfully');
    });

    it('should handle errors when creating user', async () => {
      const userData: CreateUserRequest = {
        email: 'invalid-email',
        firstName: 'John',
        lastName: 'Doe',
        password: 'weak',
        role: UserRole.CUSTOMER,
      };

      mockRequest.body = userData;
      const error = new Error('Validation failed');
      mockUserService.createUser.mockRejectedValue(error);

      await expect(
        userController.createUser(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn())
      ).rejects.toThrow('Validation failed');
    });
  });

  describe('updateUser', () => {
    it('should successfully update a user', async () => {
      const updateData: UpdateUserRequest = {
        firstName: 'Jane',
        lastName: 'Smith',
      };
      const mockUpdatedUser: UserDetail = {
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        role: UserRole.CUSTOMER,
        isActive: true,
        isVerified: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        phone: undefined,
        lastLoginAt: undefined,
        avatar: undefined,
        emailVerified: undefined,
        loginAttempts: 0,
        lockedUntil: undefined,
      };

      mockRequest.params = { id: 'user-123' };
      mockRequest.body = updateData;
      mockUserService.updateUser.mockResolvedValue(mockUpdatedUser);

      await userController.updateUser(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.updateUser).toHaveBeenCalledWith('user-123', updateData);
      expect(mockResponse.success).toHaveBeenCalledWith({ user: mockUpdatedUser }, 'User updated successfully');
    });

    it('should handle errors when updating user', async () => {
      mockRequest.params = { id: 'user-123' };
      mockRequest.body = { firstName: 'Jane' };
      const error = new Error('User not found');
      mockUserService.updateUser.mockRejectedValue(error);

      await expect(
        userController.updateUser(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn())
      ).rejects.toThrow('User not found');
    });
  });

  describe('deleteUser', () => {
    it('should successfully delete a user', async () => {
      mockRequest.params = { id: 'user-123' };
      mockUserService.deleteUser.mockResolvedValue(true);

      await userController.deleteUser(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.deleteUser).toHaveBeenCalledWith('user-123');
      expect(mockResponse.success).toHaveBeenCalledWith({ deleted: true }, 'User deleted successfully');
    });

    it('should handle errors when deleting user', async () => {
      mockRequest.params = { id: 'user-123' };
      const error = new Error('Cannot delete user');
      mockUserService.deleteUser.mockRejectedValue(error);

      await expect(
        userController.deleteUser(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn())
      ).rejects.toThrow('Cannot delete user');
    });
  });

  describe('getUsers', () => {
    it('should successfully get users list with pagination', async () => {
      const query = { page: '1', limit: '10' };
      const mockUsers: UserSummary[] = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'One',
          role: UserRole.CUSTOMER,
          isActive: true,
          isVerified: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'user-2',
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'Two',
          role: UserRole.CUSTOMER,
          isActive: true,
          isVerified: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      const mockResult: UserListResponse = {
        users: mockUsers,
        pagination: { page: 1, limit: 10, total: 2, totalPages: 1, hasNext: false, hasPrev: false },
      };

      mockRequest.query = query;
      mockUserService.getUsers.mockResolvedValue(mockResult);

      await userController.getUsers(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.getUsers).toHaveBeenCalledWith(query);
      expect(mockResponse.success).toHaveBeenCalledWith({ users: mockResult.users }, undefined, undefined, undefined);
    });

    it('should handle users list without pagination', async () => {
      const query = {};
      const mockUsers: UserSummary[] = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'One',
          role: UserRole.CUSTOMER,
          isActive: true,
          isVerified: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      const mockResult: UserListResponse = {
        users: mockUsers,
        pagination: { page: 1, limit: 10, total: 1, totalPages: 1, hasNext: false, hasPrev: false },
      };

      mockRequest.query = query;
      mockUserService.getUsers.mockResolvedValue(mockResult);

      await userController.getUsers(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockResponse.success).toHaveBeenCalledWith({ users: mockResult.users }, undefined, undefined, undefined);
    });
  });

  describe('searchUsers', () => {
    it('should successfully search users', async () => {
      const mockUsers: UserSummary[] = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          role: UserRole.CUSTOMER,
          isActive: true,
          isVerified: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'user-2',
          email: '<EMAIL>',
          firstName: 'Jane',
          lastName: 'Smith',
          role: UserRole.CUSTOMER,
          isActive: true,
          isVerified: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockRequest.query = { search: 'john', limit: '5' };
      mockUserService.searchUsers.mockResolvedValue(mockUsers);

      await userController.searchUsers(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.searchUsers).toHaveBeenCalledWith('john', 5);
      expect(mockResponse.success).toHaveBeenCalledWith({ users: mockUsers });
    });

    it('should use default limit when not provided', async () => {
      const mockUsers: UserSummary[] = [];
      mockRequest.query = { search: 'nonexistent' };
      mockUserService.searchUsers.mockResolvedValue(mockUsers);

      await userController.searchUsers(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.searchUsers).toHaveBeenCalledWith('nonexistent', 10);
    });
  });

  describe('getUserStats', () => {
    it('should successfully get user statistics', async () => {
      const mockStats: UserStats = {
        total: 100,
        totalUsers: 100,
        active: 85,
        activeUsers: 85,
        inactive: 15,
        verified: 75,
        verifiedUsers: 75,
        unverified: 25,
        byRole: { CUSTOMER: 80, PROVIDER: 10, ADMIN: 20 },
        usersByRole: { CUSTOMER: 80, PROVIDER: 10, ADMIN: 20 },
        recentRegistrations: 10,
        averageRegistrationsPerDay: 5,
        newUsersThisMonth: 15,
      };

      mockUserService.getUserStats.mockResolvedValue(mockStats);

      await userController.getUserStats(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.getUserStats).toHaveBeenCalled();
      expect(mockResponse.success).toHaveBeenCalledWith({ stats: mockStats });
    });
  });

  describe('getUsersByRole', () => {
    it('should successfully get users by role', async () => {
      const mockUsers: UserSummary[] = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          firstName: 'Admin',
          lastName: 'One',
          role: UserRole.ADMIN,
          isActive: true,
          isVerified: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'user-2',
          email: '<EMAIL>',
          firstName: 'Admin',
          lastName: 'Two',
          role: UserRole.ADMIN,
          isActive: true,
          isVerified: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockRequest.params = { role: 'admin' };
      mockUserService.getUsersByRole.mockResolvedValue(mockUsers);

      await userController.getUsersByRole(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.getUsersByRole).toHaveBeenCalledWith('admin');
      expect(mockResponse.success).toHaveBeenCalledWith({ users: mockUsers });
    });
  });

  describe('getActiveUsers', () => {
    it('should successfully get active users', async () => {
      const mockUsers: UserSummary[] = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          firstName: 'Active',
          lastName: 'User',
          role: UserRole.CUSTOMER,
          isActive: true,
          isVerified: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'user-2',
          email: '<EMAIL>',
          firstName: 'Active',
          lastName: 'User',
          role: UserRole.CUSTOMER,
          isActive: true,
          isVerified: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockUserService.getActiveUsers.mockResolvedValue(mockUsers);

      await userController.getActiveUsers(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.getActiveUsers).toHaveBeenCalled();
      expect(mockResponse.success).toHaveBeenCalledWith({ users: mockUsers });
    });
  });

  describe('getVerifiedUsers', () => {
    it('should successfully get verified users', async () => {
      const mockUsers: UserSummary[] = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          firstName: 'Verified',
          lastName: 'User',
          role: UserRole.CUSTOMER,
          isActive: true,
          isVerified: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'user-2',
          email: '<EMAIL>',
          firstName: 'Verified',
          lastName: 'User',
          role: UserRole.CUSTOMER,
          isActive: true,
          isVerified: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockUserService.getVerifiedUsers.mockResolvedValue(mockUsers);

      await userController.getVerifiedUsers(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.getVerifiedUsers).toHaveBeenCalled();
      expect(mockResponse.success).toHaveBeenCalledWith({ users: mockUsers });
    });
  });

  describe('bulkUpdateUsers', () => {
    it('should successfully bulk update users', async () => {
      const requestData = {
        userIds: ['user-1', 'user-2', 'user-3'],
        updates: { isActive: false },
      };

      mockRequest.body = requestData;
      mockUserService.bulkUpdateUsers.mockResolvedValue(3);

      await userController.bulkUpdateUsers(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.bulkUpdateUsers).toHaveBeenCalledWith(requestData.userIds, requestData.updates);
      expect(mockResponse.success).toHaveBeenCalledWith({ updatedCount: 3 }, 'Successfully updated 3 users');
    });
  });

  describe('bulkDeleteUsers', () => {
    it('should successfully bulk delete users', async () => {
      const requestData = {
        userIds: ['user-1', 'user-2'],
      };

      mockRequest.body = requestData;
      mockUserService.bulkDeleteUsers.mockResolvedValue(2);

      await userController.bulkDeleteUsers(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.bulkDeleteUsers).toHaveBeenCalledWith(requestData.userIds);
      expect(mockResponse.success).toHaveBeenCalledWith({ deletedCount: 2 }, 'Successfully deleted 2 users');
    });
  });

  describe('userExists', () => {
    it('should check if user exists by email', async () => {
      mockRequest.params = { email: '<EMAIL>' };
      mockUserService.userExists.mockResolvedValue(true);

      await userController.userExists(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.userExists).toHaveBeenCalledWith('<EMAIL>');
      expect(mockResponse.success).toHaveBeenCalledWith({ exists: true });
    });

    it('should return false when user does not exist', async () => {
      mockRequest.params = { email: '<EMAIL>' };
      mockUserService.userExists.mockResolvedValue(false);

      await userController.userExists(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.userExists).toHaveBeenCalledWith('<EMAIL>');
      expect(mockResponse.success).toHaveBeenCalledWith({ exists: false });
    });
  });

  describe('countUsers', () => {
    it('should successfully count all users', async () => {
      mockUserService.countUsers.mockResolvedValue(150);

      await userController.countUsers(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.countUsers).toHaveBeenCalled();
      expect(mockResponse.success).toHaveBeenCalledWith({ count: 150 });
    });
  });

  describe('countUsersByRole', () => {
    it('should successfully count users by role', async () => {
      mockRequest.params = { role: 'customer' };
      mockUserService.countUsersByRole.mockResolvedValue(120);

      await userController.countUsersByRole(mockRequest as AuthenticatedRequest, mockResponse as Response, jest.fn());

      expect(mockUserService.countUsersByRole).toHaveBeenCalledWith('customer');
      expect(mockResponse.success).toHaveBeenCalledWith({ count: 120 });
    });
  });
});
