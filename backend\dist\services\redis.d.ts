import { RedisClientType } from 'redis';
declare class RedisService {
    private static instance;
    private client;
    private isConnected;
    private isInitialized;
    private constructor();
    static getInstance(): RedisService;
    initialize(): Promise<void>;
    getClient(): RedisClientType;
    isConnectedToRedis(): boolean;
    disconnect(): Promise<void>;
    healthCheck(): Promise<boolean>;
    getConfig(): {
        url: string;
        db: number;
        keyPrefix: string;
        retryDelayOnFailover: number;
        maxRetriesPerRequest: number;
        password?: string | undefined;
    };
}
export declare const redisService: RedisService;
export { RedisService };
//# sourceMappingURL=redis.d.ts.map