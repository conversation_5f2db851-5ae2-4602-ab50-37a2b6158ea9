import { PrismaClient } from '@prisma/client';
declare class DatabaseService {
    private static instance;
    private prisma;
    private isConnected;
    private isInitialized;
    private constructor();
    static getInstance(): DatabaseService;
    initialize(): Promise<void>;
    getClient(): PrismaClient;
    isConnectedToDatabase(): boolean;
    disconnect(): Promise<void>;
    healthCheck(): Promise<boolean>;
}
export declare const databaseService: DatabaseService;
export { DatabaseService };
//# sourceMappingURL=database.d.ts.map