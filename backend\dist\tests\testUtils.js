"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validationTestData = exports.testUsers = exports.mockAuthenticate = void 0;
exports.createTestUser = createTestUser;
exports.generateAuthToken = generateAuthToken;
exports.cleanupTestData = cleanupTestData;
exports.createMockRequest = createMockRequest;
exports.createMockResponse = createMockResponse;
exports.createBulkTestUsers = createBulkTestUsers;
exports.resetTestDatabase = resetTestDatabase;
exports.getTestDatabaseStats = getTestDatabaseStats;
const client_1 = require("@prisma/client");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const prisma = new client_1.PrismaClient();
const mockAuthenticate = (user) => {
    return (req, res, next) => {
        req.user = {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            isActive: user.isActive,
            isVerified: user.isVerified,
            avatar: user.avatar,
            phone: user.phone,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            lastLoginAt: user.lastLoginAt
        };
        req.userId = user.id;
        next();
    };
};
exports.mockAuthenticate = mockAuthenticate;
async function createTestUser(userData) {
    const hashedPassword = await bcryptjs_1.default.hash('TestPass123!', 10);
    return await prisma.user.create({
        data: {
            email: userData.email,
            password: hashedPassword,
            firstName: userData.firstName || 'Test',
            lastName: userData.lastName || 'User',
            role: userData.role || client_1.UserRole.CUSTOMER,
            isActive: userData.isActive ?? true,
            isVerified: userData.isVerified ?? false,
            phone: userData.phone || null,
            avatar: userData.avatar || null
        }
    });
}
function generateAuthToken(user) {
    const payload = {
        userId: user.id,
        email: user.email,
        role: user.role
    };
    return jsonwebtoken_1.default.sign(payload, 'test-jwt-secret-key-for-testing-only', { expiresIn: '1h' });
}
async function cleanupTestData(emails) {
    await prisma.user.deleteMany({
        where: {
            email: {
                in: emails
            }
        }
    });
}
function createMockRequest(user) {
    const mockReq = {
        user: user ? {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            isActive: user.isActive,
            isVerified: user.isVerified,
            avatar: user.avatar,
            phone: user.phone,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            lastLoginAt: user.lastLoginAt
        } : undefined,
        userId: user?.id,
        body: {},
        params: {},
        query: {},
        headers: {},
        get: jest.fn()
    };
    return mockReq;
}
function createMockResponse() {
    const res = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockReturnThis(),
        send: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        get: jest.fn().mockReturnThis()
    };
    return res;
}
exports.testUsers = {
    admin: {
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        role: client_1.UserRole.ADMIN,
        isVerified: true
    },
    customer: {
        email: '<EMAIL>',
        firstName: 'Customer',
        lastName: 'User',
        role: client_1.UserRole.CUSTOMER,
        isVerified: true,
        phone: '+**********'
    },
    provider: {
        email: '<EMAIL>',
        firstName: 'Provider',
        lastName: 'User',
        role: client_1.UserRole.PROVIDER,
        isVerified: true,
        phone: '+**********'
    },
    unverified: {
        email: '<EMAIL>',
        firstName: 'Unverified',
        lastName: 'User',
        role: client_1.UserRole.CUSTOMER,
        isVerified: false
    },
    inactive: {
        email: '<EMAIL>',
        firstName: 'Inactive',
        lastName: 'User',
        role: client_1.UserRole.CUSTOMER,
        isActive: false
    }
};
exports.validationTestData = {
    validUser: {
        email: '<EMAIL>',
        password: 'ValidPass123!',
        firstName: 'Valid',
        lastName: 'User',
        role: client_1.UserRole.CUSTOMER
    },
    invalidEmail: {
        email: 'invalid-email',
        password: 'ValidPass123!',
        firstName: 'Invalid',
        lastName: 'Email'
    },
    weakPassword: {
        email: '<EMAIL>',
        password: 'weak',
        firstName: 'Weak',
        lastName: 'Password'
    },
    missingFields: {
        email: '<EMAIL>',
        password: 'ValidPass123!'
    },
    longName: {
        email: '<EMAIL>',
        password: 'ValidPass123!',
        firstName: 'a'.repeat(151),
        lastName: 'User'
    }
};
async function createBulkTestUsers(count, prefix = 'bulk') {
    const users = [];
    for (let i = 0; i < count; i++) {
        const user = await createTestUser({
            email: `${prefix}${i}@test.com`,
            firstName: `${prefix}${i}`,
            lastName: 'User',
            role: client_1.UserRole.CUSTOMER
        });
        users.push(user);
    }
    return users;
}
async function resetTestDatabase() {
    await prisma.user.deleteMany({
        where: {
            email: {
                contains: '@test.com'
            }
        }
    });
}
async function getTestDatabaseStats() {
    const totalUsers = await prisma.user.count();
    const activeUsers = await prisma.user.count({ where: { isActive: true } });
    const verifiedUsers = await prisma.user.count({ where: { isVerified: true } });
    return {
        totalUsers,
        activeUsers,
        verifiedUsers,
        inactiveUsers: totalUsers - activeUsers,
        unverifiedUsers: totalUsers - verifiedUsers
    };
}
//# sourceMappingURL=testUtils.js.map