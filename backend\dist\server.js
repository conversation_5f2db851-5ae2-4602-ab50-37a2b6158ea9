"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceManager = exports.ServerManager = void 0;
exports.startServer = startServer;
const http_1 = require("http");
const config_1 = require("./config");
const logger_1 = require("./utils/logger");
const app_1 = __importDefault(require("./app"));
const services_1 = require("./services");
const logger = (0, logger_1.createLogger)('Server');
const serverConfig = config_1.config.getServerConfig();
class ServiceManager {
    static instance;
    isInitialized = false;
    constructor() { }
    static getInstance() {
        if (!ServiceManager.instance) {
            ServiceManager.instance = new ServiceManager();
        }
        return ServiceManager.instance;
    }
    async initialize() {
        if (this.isInitialized) {
            logger.warn('Services already initialized');
            return;
        }
        try {
            await services_1.databaseService.initialize();
            await services_1.redisService.initialize();
            this.isInitialized = true;
            logger.info('All services initialized successfully');
        }
        catch (error) {
            logger.error('Service initialization failed', error);
            throw error;
        }
    }
    async disconnect() {
        try {
            await services_1.redisService.disconnect();
            await services_1.databaseService.disconnect();
            this.isInitialized = false;
            logger.info('All services disconnected successfully');
        }
        catch (error) {
            logger.error('Service disconnection failed', error);
            throw error;
        }
    }
    getInitializedStatus() {
        return this.isInitialized;
    }
    async healthCheck() {
        const [dbHealth, redisHealth] = await Promise.all([
            services_1.databaseService.healthCheck(),
            services_1.redisService.healthCheck(),
        ]);
        return {
            database: dbHealth,
            redis: redisHealth,
        };
    }
}
exports.ServiceManager = ServiceManager;
class ServerManager {
    server = null;
    serviceManager;
    constructor() {
        this.serviceManager = ServiceManager.getInstance();
    }
    async start() {
        try {
            await this.serviceManager.initialize();
            this.server = (0, http_1.createServer)(app_1.default);
            await new Promise((resolve, reject) => {
                if (!this.server) {
                    reject(new Error('Server not initialized'));
                    return;
                }
                this.server.listen(serverConfig.port, serverConfig.host, () => {
                    logger.info('Server started successfully', {
                        port: serverConfig.port,
                        host: serverConfig.host,
                        environment: serverConfig.nodeEnv,
                        apiBaseUrl: serverConfig.apiBaseUrl,
                        frontendUrl: serverConfig.frontendUrl,
                        healthCheck: `http://${serverConfig.host}:${serverConfig.port}/health`,
                        pid: process.pid,
                    });
                    this.logStartupInfo();
                    resolve();
                });
                this.server.on('error', (error) => {
                    logger.error('Server startup failed', error);
                    reject(error);
                });
            });
            this.setupGracefulShutdown();
        }
        catch (error) {
            logger.error('Failed to start server', error);
            throw error;
        }
    }
    async stop() {
        try {
            if (this.server) {
                await new Promise((resolve) => {
                    this.server.close(() => {
                        logger.info('HTTP server closed');
                        resolve();
                    });
                });
            }
            await this.serviceManager.disconnect();
        }
        catch (error) {
            logger.error('Error during server shutdown', error);
            throw error;
        }
    }
    logStartupInfo() {
        logger.info('PrintWedittV1 Backend Server', {
            version: process.env.npm_package_version || '1.0.0',
            nodeVersion: process.version,
            platform: process.platform,
            architecture: process.arch,
            memoryUsage: process.memoryUsage(),
            uptime: process.uptime(),
        });
        logger.info('Configuration Summary', {
            environment: serverConfig.nodeEnv,
            port: serverConfig.port,
            host: serverConfig.host,
            corsOrigin: serverConfig.corsOrigin,
            database: {
                maxConnections: config_1.config.getDatabaseConfig().maxConnections,
                idleTimeout: config_1.config.getDatabaseConfig().idleTimeout,
            },
            redis: {
                url: services_1.redisService.getConfig().url,
                db: services_1.redisService.getConfig().db,
                keyPrefix: services_1.redisService.getConfig().keyPrefix,
            },
            security: {
                rateLimitMaxRequests: config_1.config.getSecurityConfig().rateLimitMaxRequests,
                rateLimitWindowMs: config_1.config.getSecurityConfig().rateLimitWindowMs,
                bcryptRounds: config_1.config.getSecurityConfig().bcryptRounds,
            },
            logging: {
                level: config_1.config.getLoggingConfig().level,
                format: config_1.config.getLoggingConfig().format,
                enableConsole: config_1.config.getLoggingConfig().enableConsole,
                enableFile: config_1.config.getLoggingConfig().enableFile,
            },
        });
    }
    setupGracefulShutdown() {
        const shutdown = async (signal) => {
            logger.info(`Received ${signal}. Starting graceful shutdown...`);
            try {
                await this.stop();
                logger.info('Graceful shutdown completed');
                process.exit(0);
            }
            catch (error) {
                logger.error('Error during graceful shutdown', error);
                process.exit(1);
            }
        };
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('SIGINT', () => shutdown('SIGINT'));
        process.on('uncaughtException', (error) => {
            logger.error('Uncaught Exception', error);
            shutdown('uncaughtException');
        });
        process.on('unhandledRejection', (reason, promise) => {
            logger.error('Unhandled Rejection', { reason, promise });
            shutdown('unhandledRejection');
        });
    }
}
exports.ServerManager = ServerManager;
async function startServer() {
    const serverManager = new ServerManager();
    try {
        await serverManager.start();
        logger.info('PrintWedittV1 backend server is ready to handle requests');
    }
    catch (error) {
        logger.error('Failed to start server', error);
        process.exit(1);
    }
}
if (require.main === module) {
    startServer().catch((error) => {
        logger.error('Server startup failed', error);
        process.exit(1);
    });
}
//# sourceMappingURL=server.js.map