"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseManager = exports.ServerManager = void 0;
exports.startServer = startServer;
const http_1 = require("http");
const client_1 = require("@prisma/client");
const config_1 = require("./config");
const logger_1 = require("./utils/logger");
const app_1 = __importDefault(require("./app"));
const logger = (0, logger_1.createLogger)('Server');
const prisma = new client_1.PrismaClient();
const serverConfig = config_1.config.getServerConfig();
const dbConfig = config_1.config.getDatabaseConfig();
class DatabaseManager {
    static instance;
    isConnected = false;
    constructor() { }
    static getInstance() {
        if (!DatabaseManager.instance) {
            DatabaseManager.instance = new DatabaseManager();
        }
        return DatabaseManager.instance;
    }
    async connect() {
        try {
            await prisma.$connect();
            this.isConnected = true;
            logger.info('Database connected successfully', {
                url: dbConfig.url.replace(/\/\/.*@/, '//***:***@'),
                maxConnections: dbConfig.maxConnections,
                idleTimeout: dbConfig.idleTimeout,
                connectionTimeout: dbConfig.connectionTimeout,
            });
        }
        catch (error) {
            logger.error('Database connection failed', error);
            throw error;
        }
    }
    async disconnect() {
        try {
            await prisma.$disconnect();
            this.isConnected = false;
            logger.info('Database disconnected successfully');
        }
        catch (error) {
            logger.error('Database disconnection failed', error);
            throw error;
        }
    }
    isConnectedToDatabase() {
        return this.isConnected;
    }
}
exports.DatabaseManager = DatabaseManager;
class ServerManager {
    server = null;
    dbManager;
    constructor() {
        this.dbManager = DatabaseManager.getInstance();
    }
    async start() {
        try {
            await this.dbManager.connect();
            this.server = (0, http_1.createServer)(app_1.default);
            await new Promise((resolve, reject) => {
                if (!this.server) {
                    reject(new Error('Server not initialized'));
                    return;
                }
                this.server.listen(serverConfig.port, serverConfig.host, () => {
                    logger.info('Server started successfully', {
                        port: serverConfig.port,
                        host: serverConfig.host,
                        environment: serverConfig.nodeEnv,
                        apiBaseUrl: serverConfig.apiBaseUrl,
                        frontendUrl: serverConfig.frontendUrl,
                        healthCheck: `http://${serverConfig.host}:${serverConfig.port}/health`,
                        pid: process.pid,
                    });
                    this.logStartupInfo();
                    resolve();
                });
                this.server.on('error', (error) => {
                    logger.error('Server startup failed', error);
                    reject(error);
                });
            });
            this.setupGracefulShutdown();
        }
        catch (error) {
            logger.error('Failed to start server', error);
            throw error;
        }
    }
    async stop() {
        try {
            if (this.server) {
                await new Promise((resolve) => {
                    this.server.close(() => {
                        logger.info('HTTP server closed');
                        resolve();
                    });
                });
            }
            await this.dbManager.disconnect();
        }
        catch (error) {
            logger.error('Error during server shutdown', error);
            throw error;
        }
    }
    logStartupInfo() {
        logger.info('PrintWedittV1 Backend Server', {
            version: process.env.npm_package_version || '1.0.0',
            nodeVersion: process.version,
            platform: process.platform,
            architecture: process.arch,
            memoryUsage: process.memoryUsage(),
            uptime: process.uptime(),
        });
        logger.info('Configuration Summary', {
            environment: serverConfig.nodeEnv,
            port: serverConfig.port,
            host: serverConfig.host,
            corsOrigin: serverConfig.corsOrigin,
            database: {
                maxConnections: dbConfig.maxConnections,
                idleTimeout: dbConfig.idleTimeout,
            },
            security: {
                rateLimitMaxRequests: config_1.config.getSecurityConfig().rateLimitMaxRequests,
                rateLimitWindowMs: config_1.config.getSecurityConfig().rateLimitWindowMs,
                bcryptRounds: config_1.config.getSecurityConfig().bcryptRounds,
            },
            logging: {
                level: config_1.config.getLoggingConfig().level,
                format: config_1.config.getLoggingConfig().format,
                enableConsole: config_1.config.getLoggingConfig().enableConsole,
                enableFile: config_1.config.getLoggingConfig().enableFile,
            },
        });
    }
    setupGracefulShutdown() {
        const shutdown = async (signal) => {
            logger.info(`Received ${signal}. Starting graceful shutdown...`);
            try {
                await this.stop();
                logger.info('Graceful shutdown completed');
                process.exit(0);
            }
            catch (error) {
                logger.error('Error during graceful shutdown', error);
                process.exit(1);
            }
        };
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('SIGINT', () => shutdown('SIGINT'));
        process.on('uncaughtException', (error) => {
            logger.error('Uncaught Exception', error);
            shutdown('uncaughtException');
        });
        process.on('unhandledRejection', (reason, promise) => {
            logger.error('Unhandled Rejection', { reason, promise });
            shutdown('unhandledRejection');
        });
    }
}
exports.ServerManager = ServerManager;
async function startServer() {
    const serverManager = new ServerManager();
    try {
        await serverManager.start();
        logger.info('PrintWedittV1 backend server is ready to handle requests');
    }
    catch (error) {
        logger.error('Failed to start server', error);
        process.exit(1);
    }
}
if (require.main === module) {
    startServer().catch((error) => {
        logger.error('Server startup failed', error);
        process.exit(1);
    });
}
//# sourceMappingURL=server.js.map