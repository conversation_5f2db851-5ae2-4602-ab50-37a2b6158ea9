const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const API_BASE_URL = 'http://localhost:3000/api/auth';
const TEST_TIMEOUT = 30000;

// Test users for different scenarios
const testUsers = [
	{
		email: '<EMAIL>',
		password: 'TestPassword123!',
		firstName: 'Test',
		lastName: 'User1',
	},
	{
		email: '<EMAIL>',
		password: 'TestPassword123!',
		firstName: 'Test',
		lastName: 'User2',
	},
	{
		email: '<EMAIL>',
		password: 'TestPassword123!',
		firstName: 'Test',
		lastName: 'User3',
	},
];

class LoggingTestSuite {
	constructor() {
		this.tokens = {};
		this.testResults = [];
	}

	async runAllTests() {
		console.log('🧪 Starting comprehensive logging test suite...\n');

		try {
			await this.testEnvironmentSetup();
			await this.testAuthenticationLogging();
			await this.testRateLimitingLogging();
			await this.testErrorLogging();
			await this.testPerformanceLogging();
			await this.testSecurityLogging();
			await this.testDatabaseLogging();
			await this.testLogFileRotation();

			this.generateTestReport();
			console.log('\n✅ All logging tests completed successfully!');
		} catch (error) {
			console.error('❌ Test suite failed:', error.message);
			throw error;
		}
	}

	async testEnvironmentSetup() {
		console.log('🔧 Testing environment setup...');

		try {
			// Test basic API connectivity
			const response = await axios.get(`${API_BASE_URL}/health`, {
				timeout: 5000,
			});
			this.addTestResult(
				'Environment Setup',
				'API Connectivity',
				true,
				'API is accessible'
			);

			// Check if logs directory exists
			const logsDir = path.join(__dirname, 'logs');
			if (fs.existsSync(logsDir)) {
				this.addTestResult(
					'Environment Setup',
					'Logs Directory',
					true,
					'Logs directory exists'
				);
			} else {
				this.addTestResult(
					'Environment Setup',
					'Logs Directory',
					false,
					'Logs directory not found'
				);
			}
		} catch (error) {
			this.addTestResult(
				'Environment Setup',
				'API Connectivity',
				false,
				error.message
			);
		}
	}

	async testAuthenticationLogging() {
		console.log('🔐 Testing authentication logging...');

		try {
			// Test user registration
			const user = testUsers[0];
			const registerResponse = await axios.post(
				`${API_BASE_URL}/register`,
				user
			);
			this.addTestResult(
				'Authentication',
				'User Registration',
				true,
				'User registered successfully'
			);

			// Test user login
			const loginResponse = await axios.post(`${API_BASE_URL}/login`, {
				email: user.email,
				password: user.password,
			});

			this.tokens[user.email] = loginResponse.data.data.tokens;
			this.addTestResult(
				'Authentication',
				'User Login',
				true,
				'User logged in successfully'
			);

			// Test token refresh
			const refreshResponse = await axios.post(`${API_BASE_URL}/refresh`, {
				refreshToken: this.tokens[user.email].refreshToken,
			});

			this.tokens[user.email] = refreshResponse.data.data.tokens;
			this.addTestResult(
				'Authentication',
				'Token Refresh',
				true,
				'Tokens refreshed successfully'
			);

			// Test failed login attempts
			try {
				await axios.post(`${API_BASE_URL}/login`, {
					email: user.email,
					password: 'wrongpassword',
				});
			} catch (error) {
				if (error.response?.status === 401) {
					this.addTestResult(
						'Authentication',
						'Failed Login Logging',
						true,
						'Failed login properly logged'
					);
				}
			}
		} catch (error) {
			this.addTestResult(
				'Authentication',
				'Authentication Flow',
				false,
				error.message
			);
		}
	}

	async testRateLimitingLogging() {
		console.log('🚦 Testing rate limiting logging...');

		try {
			const user = testUsers[1];

			// Register a new user for rate limiting tests
			await axios.post(`${API_BASE_URL}/register`, user);

			// Attempt multiple rapid requests to trigger rate limiting
			const promises = [];
			for (let i = 0; i < 10; i++) {
				promises.push(
					axios
						.post(`${API_BASE_URL}/login`, {
							email: user.email,
							password: 'wrongpassword',
						})
						.catch((error) => error)
				);
			}

			const results = await Promise.all(promises);
			const rateLimited = results.some(
				(result) => result.response?.status === 429
			);

			if (rateLimited) {
				this.addTestResult(
					'Rate Limiting',
					'Rate Limit Detection',
					true,
					'Rate limiting properly triggered'
				);
			} else {
				this.addTestResult(
					'Rate Limiting',
					'Rate Limit Detection',
					false,
					'Rate limiting not triggered'
				);
			}
		} catch (error) {
			this.addTestResult(
				'Rate Limiting',
				'Rate Limiting Test',
				false,
				error.message
			);
		}
	}

	async testErrorLogging() {
		console.log('❌ Testing error logging...');

		try {
			// Test invalid token
			try {
				await axios.get(`${API_BASE_URL}/profile`, {
					headers: {Authorization: 'Bearer invalid-token'},
				});
			} catch (error) {
				if (error.response?.status === 401) {
					this.addTestResult(
						'Error Logging',
						'Invalid Token',
						true,
						'Invalid token error properly logged'
					);
				}
			}

			// Test expired token (if we can simulate it)
			try {
				await axios.get(`${API_BASE_URL}/profile`, {
					headers: {
						Authorization:
							'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJ0ZXN0IiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.test',
					},
				});
			} catch (error) {
				if (error.response?.status === 401) {
					this.addTestResult(
						'Error Logging',
						'Expired Token',
						true,
						'Expired token error properly logged'
					);
				}
			}

			// Test malformed request
			try {
				await axios.post(`${API_BASE_URL}/register`, {invalid: 'data'});
			} catch (error) {
				if (error.response?.status === 400) {
					this.addTestResult(
						'Error Logging',
						'Malformed Request',
						true,
						'Malformed request error properly logged'
					);
				}
			}
		} catch (error) {
			this.addTestResult(
				'Error Logging',
				'Error Logging Test',
				false,
				error.message
			);
		}
	}

	async testPerformanceLogging() {
		console.log('⚡ Testing performance logging...');

		try {
			// Test concurrent requests
			const user = testUsers[2];
			await axios.post(`${API_BASE_URL}/register`, user);

			const loginPromises = [];
			for (let i = 0; i < 5; i++) {
				loginPromises.push(
					axios.post(`${API_BASE_URL}/login`, {
						email: user.email,
						password: user.password,
					})
				);
			}

			const startTime = Date.now();
			await Promise.all(loginPromises);
			const duration = Date.now() - startTime;

			this.addTestResult(
				'Performance',
				'Concurrent Requests',
				true,
				`Handled 5 concurrent requests in ${duration}ms`
			);
		} catch (error) {
			this.addTestResult(
				'Performance',
				'Performance Test',
				false,
				error.message
			);
		}
	}

	async testSecurityLogging() {
		console.log('🔒 Testing security logging...');

		try {
			const user = testUsers[0];

			if (this.tokens[user.email]) {
				// Test password change
				await axios.put(
					`${API_BASE_URL}/change-password`,
					{
						currentPassword: user.password,
						newPassword: 'NewPassword123!',
					},
					{
						headers: {
							Authorization: `Bearer ${this.tokens[user.email].accessToken}`,
						},
					}
				);

				this.addTestResult(
					'Security',
					'Password Change',
					true,
					'Password change properly logged'
				);

				// Test session management
				const sessionsResponse = await axios.get(`${API_BASE_URL}/sessions`, {
					headers: {
						Authorization: `Bearer ${this.tokens[user.email].accessToken}`,
					},
				});

				if (sessionsResponse.data.success) {
					this.addTestResult(
						'Security',
						'Session Management',
						true,
						'Session management properly logged'
					);
				}
			} else {
				this.addTestResult(
					'Security',
					'Security Tests',
					false,
					'No valid token available'
				);
			}
		} catch (error) {
			this.addTestResult('Security', 'Security Logging', false, error.message);
		}
	}

	async testDatabaseLogging() {
		console.log('🗄️ Testing database logging...');

		try {
			const user = testUsers[0];

			if (this.tokens[user.email]) {
				// Test profile update
				await axios.put(
					`${API_BASE_URL}/profile`,
					{
						firstName: 'Updated',
						lastName: 'Name',
					},
					{
						headers: {
							Authorization: `Bearer ${this.tokens[user.email].accessToken}`,
						},
					}
				);

				this.addTestResult(
					'Database',
					'Profile Update',
					true,
					'Profile update database operation logged'
				);

				// Test profile retrieval
				const profileResponse = await axios.get(`${API_BASE_URL}/profile`, {
					headers: {
						Authorization: `Bearer ${this.tokens[user.email].accessToken}`,
					},
				});

				if (profileResponse.data.success) {
					this.addTestResult(
						'Database',
						'Profile Retrieval',
						true,
						'Profile retrieval database operation logged'
					);
				}
			} else {
				this.addTestResult(
					'Database',
					'Database Tests',
					false,
					'No valid token available'
				);
			}
		} catch (error) {
			this.addTestResult('Database', 'Database Logging', false, error.message);
		}
	}

	async testLogFileRotation() {
		console.log('📁 Testing log file rotation...');

		try {
			const logsDir = path.join(__dirname, 'logs');

			if (fs.existsSync(logsDir)) {
				const files = fs.readdirSync(logsDir);
				const logFiles = files.filter((file) => file.endsWith('.log'));

				if (logFiles.length > 0) {
					this.addTestResult(
						'Log Rotation',
						'Log Files Exist',
						true,
						`Found ${logFiles.length} log files`
					);

					// Check for different log types
					const hasErrorLog = logFiles.some((file) => file.includes('error'));
					const hasApplicationLog = logFiles.some((file) =>
						file.includes('application')
					);
					const hasHttpLog = logFiles.some((file) => file.includes('http'));

					if (hasErrorLog)
						this.addTestResult(
							'Log Rotation',
							'Error Log File',
							true,
							'Error log file exists'
						);
					if (hasApplicationLog)
						this.addTestResult(
							'Log Rotation',
							'Application Log File',
							true,
							'Application log file exists'
						);
					if (hasHttpLog)
						this.addTestResult(
							'Log Rotation',
							'HTTP Log File',
							true,
							'HTTP log file exists'
						);
				} else {
					this.addTestResult(
						'Log Rotation',
						'Log Files',
						false,
						'No log files found'
					);
				}
			} else {
				this.addTestResult(
					'Log Rotation',
					'Logs Directory',
					false,
					'Logs directory not found'
				);
			}
		} catch (error) {
			this.addTestResult(
				'Log Rotation',
				'Log File Rotation',
				false,
				error.message
			);
		}
	}

	addTestResult(category, test, passed, message) {
		this.testResults.push({
			category,
			test,
			passed,
			message,
			timestamp: new Date().toISOString(),
		});

		const status = passed ? '✅' : '❌';
		console.log(`${status} ${category} - ${test}: ${message}`);
	}

	generateTestReport() {
		const report = {
			timestamp: new Date().toISOString(),
			environment: process.env.NODE_ENV || 'development',
			logLevel: process.env.LOG_LEVEL || 'info',
			totalTests: this.testResults.length,
			passedTests: this.testResults.filter((r) => r.passed).length,
			failedTests: this.testResults.filter((r) => !r.passed).length,
			successRate: (
				(this.testResults.filter((r) => r.passed).length /
					this.testResults.length) *
				100
			).toFixed(2),
			results: this.testResults,
		};

		// Save report to file
		const reportPath = path.join(__dirname, 'test-report.json');
		fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

		console.log('\n📊 Test Report Generated:');
		console.log('========================');
		console.log(`Total Tests: ${report.totalTests}`);
		console.log(`Passed: ${report.passedTests}`);
		console.log(`Failed: ${report.failedTests}`);
		console.log(`Success Rate: ${report.successRate}%`);
		console.log(`Report saved to: ${reportPath}`);
	}
}

// Export for use in other scripts
module.exports = LoggingTestSuite;

// Run directly if this file is executed
if (require.main === module) {
	const testSuite = new LoggingTestSuite();
	testSuite.runAllTests().catch(console.error);
}
