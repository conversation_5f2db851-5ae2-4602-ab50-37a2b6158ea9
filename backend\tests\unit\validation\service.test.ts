import {
  createServiceCategorySchema,
  createServiceFormFieldOptionSchema,
  createServiceFormFieldSchema,
  createServiceSchema,
  priceCalculationSchema,
  serviceListQuerySchema,
  serviceSearchQuerySchema,
  updateServiceCategorySchema,
  updateServiceFormFieldOptionSchema,
  updateServiceFormFieldSchema,
  updateServiceSchema,
} from '@/validation/service';

describe('Service Validation Schemas', () => {
  describe('createServiceSchema', () => {
    it('validates a correct service payload', () => {
      const { error, value } = createServiceSchema.validate({
        name: 'Business Cards',
        description: 'Desc',
        categoryId: 'cat-1',
        image: 'https://img',
        basePrice: 9.99,
        features: ['Premium'],
      });
      expect(error).toBeUndefined();
      expect(value.pricingType).toBeDefined();
    });

    it('rejects when required fields missing', () => {
      const { error } = createServiceSchema.validate({});
      expect(error).toBeDefined();
    });
  });

  describe('updateServiceSchema', () => {
    it('accepts partial update', () => {
      const { error } = updateServiceSchema.validate({ description: 'New' });
      expect(error).toBeUndefined();
    });

    it('rejects empty object', () => {
      const { error } = updateServiceSchema.validate({});
      expect(error).toBeDefined();
    });
  });

  describe('serviceListQuerySchema', () => {
    it('parses pagination and sorting', () => {
      const { error, value } = serviceListQuerySchema.validate({
        page: '2',
        limit: '5',
        sortBy: 'name',
        sortOrder: 'desc',
      });
      expect(error).toBeUndefined();
      expect(value.page).toBe(2);
      expect(value.limit).toBe(5);
    });

    it('rejects invalid price range', () => {
      const { error } = serviceListQuerySchema.validate({ minPrice: 100, maxPrice: 10 });
      expect(error).toBeDefined();
    });
  });

  describe('serviceSearchQuerySchema', () => {
    it('validates search inputs', () => {
      const { error } = serviceSearchQuerySchema.validate({ q: 'cards', pricingType: 'FIXED', features: ['matte'] });
      expect(error).toBeUndefined();
    });

    it('rejects invalid price range', () => {
      const { error } = serviceSearchQuerySchema.validate({ minPrice: 50, maxPrice: 10 });
      expect(error).toBeDefined();
    });
  });

  describe('category schemas', () => {
    it('valid create category', () => {
      const { error } = createServiceCategorySchema.validate({ name: 'Cards', route: 'cards' });
      expect(error).toBeUndefined();
    });

    it('invalid route characters', () => {
      const { error } = createServiceCategorySchema.validate({ name: 'X', route: 'Bad Route' });
      expect(error).toBeDefined();
    });

    it('update category allows partial', () => {
      const { error } = updateServiceCategorySchema.validate({ name: 'New' });
      expect(error).toBeUndefined();
    });
  });

  describe('form field schemas', () => {
    it('valid create form field with options', () => {
      const { error } = createServiceFormFieldSchema.validate({
        serviceId: 'svc-1',
        name: 'size',
        label: 'Size',
        type: 'SELECT',
        options: [{ value: 'std', label: 'Standard', priceModifier: 0 }],
      });
      expect(error).toBeUndefined();
    });

    it('update form field allows partial', () => {
      const { error } = updateServiceFormFieldSchema.validate({ label: 'New' });
      expect(error).toBeUndefined();
    });

    it('valid create option', () => {
      const { error } = createServiceFormFieldOptionSchema.validate({ fieldId: 'fld-1', value: 'v', label: 'L' });
      expect(error).toBeUndefined();
    });

    it('update option allows partial', () => {
      const { error } = updateServiceFormFieldOptionSchema.validate({ label: 'New' });
      expect(error).toBeUndefined();
    });
  });

  describe('priceCalculationSchema', () => {
    it('validates price calc request', () => {
      const { error } = priceCalculationSchema.validate({ serviceId: 'svc-1', formData: {}, quantity: 2 });
      expect(error).toBeUndefined();
    });

    it('rejects missing formData', () => {
      const { error } = priceCalculationSchema.validate({ serviceId: 'svc-1' });
      expect(error).toBeDefined();
    });
  });
});
