import React from 'react';
import { Link } from 'react-router-dom';
import {
  <PERSON><PERSON><PERSON>cle,
  Clock,
  Shield,
  Award,
  ChevronDown,
  ChevronUp,
  Star,
  Printer,
  FileText,
  Users,
  Zap,
  ArrowLeft,
  Palette,
  Target,
  Eye,
  Layers,
  Lightbulb,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';

const PrintingDesign: React.FC = () => {
  const [openFaq, setOpenFaq] = React.useState<number | null>(null);
  const [portfolioScrollPosition, setPortfolioScrollPosition] = React.useState(0);
  const portfolioScrollRef = React.useRef<HTMLDivElement>(null);

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  const scrollPortfolio = (direction: 'left' | 'right') => {
    if (portfolioScrollRef.current) {
      const scrollAmount = 280; // Width of card + gap
      const currentScroll = portfolioScrollRef.current.scrollLeft;
      const newScroll = direction === 'left' 
        ? currentScroll - scrollAmount 
        : currentScroll + scrollAmount;
      
      portfolioScrollRef.current.scrollTo({
        left: newScroll,
        behavior: 'smooth'
      });
      setPortfolioScrollPosition(newScroll);
    }
  };

  const handlePortfolioScroll = () => {
    if (portfolioScrollRef.current) {
      setPortfolioScrollPosition(portfolioScrollRef.current.scrollLeft);
    }
  };
  const faqs = [
    {
      question: 'What printing design services do you offer?',
      answer:
        'We offer comprehensive printing design services including business cards, brochures, flyers, posters, banners, catalogs, and marketing materials. All designs are optimized for professional printing.',
    },
    {
      question: 'Do you design for both digital and print?',
      answer:
        'Yes! We create designs specifically optimized for print production, ensuring proper color profiles (CMYK), resolution (300 DPI), and bleed areas for professional printing results.',
    },
    {
      question: 'Can you work with my existing brand guidelines?',
      answer:
        'Absolutely! We can work within your existing brand guidelines, incorporating your colors, fonts, logos, and style preferences to maintain brand consistency across all materials.',
    },
    {
      question: 'What file formats will I receive?',
      answer:
        "You'll receive print-ready files in PDF format, along with editable source files (AI, PSD) and web-optimized versions (PNG, JPG) for digital use.",
    },
    {
      question: 'How do you ensure print quality?',
      answer:
        'We use industry-standard design practices including proper color management, high-resolution images, correct bleed and margin settings, and we provide print specifications to ensure optimal results.',
    },
    {
      question: 'Can you handle large format designs?',
      answer:
        'Yes! We design for all print formats from small business cards to large banners, posters, and trade show displays. We understand the technical requirements for each format.',
    },
  ];

  const portfolioSamples = [
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75',
      title: 'Corporate Brochure',
      description: 'Tri-fold brochure with professional layout and imagery',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75',
      title: 'Business Card Suite',
      description: 'Complete business card design with multiple variations',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75',
      title: 'Marketing Flyer',
      description: 'Eye-catching promotional flyer with bold graphics',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75',
      title: 'Trade Show Banner',
      description: 'Large format banner design for exhibition display',
    },
  ];

  const designProcess = [
    {
      step: '01',
      title: 'Brief & Requirements',
      description:
        'We gather detailed information about your project goals, target audience, and technical printing requirements.',
    },
    {
      step: '02',
      title: 'Concept Development',
      description:
        'Our designers create initial concepts and layouts that align with your brand and messaging objectives.',
    },
    {
      step: '03',
      title: 'Design Refinement',
      description:
        'We refine the chosen concept based on your feedback, ensuring every detail meets your expectations.',
    },
    {
      step: '04',
      title: 'Print-Ready Delivery',
      description:
        'You receive final files optimized for printing with proper specifications and technical requirements.',
    },
  ];

  const features = [
    {
      icon: Printer,
      title: 'Print Optimization',
      description:
        'Designs optimized specifically for professional printing with proper color profiles and resolution',
    },
    {
      icon: Palette,
      title: 'Brand Consistency',
      description:
        'Maintain consistent branding across all your printed marketing materials',
    },
    {
      icon: Target,
      title: 'Audience Focus',
      description:
        'Designs tailored to effectively communicate with your target audience',
    },
    {
      icon: Eye,
      title: 'Visual Impact',
      description:
        'Eye-catching designs that stand out and capture attention in print',
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Back Button */}
      <div className="bg-warm-cream py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link
            to="/design-services"
            className="flex items-center text-blue-600 hover:text-blue-700 transition-colors"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Design Studio
          </Link>
        </div>
      </div>

      {/* Hero Section */}

      <section className="bg-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900">
                Professional Printing Design
              </h1>
              <p className="text-xl mb-8 text-gray-600">
                From brochures to business cards, we create stunning print
                designs that captivate your audience and elevate your brand.
                Every design is optimized for professional printing results.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/contact"
                  className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center justify-center"
                >
                  Start Your Print Design
                </Link>
                <Link
                  to="#portfolio"
                  className="border-2 border-brand-orange text-brand-orange px-8 py-3 rounded-lg font-semibold hover:bg-brand-orange hover:text-white transition-colors inline-flex items-center justify-center"
                >
                  View Portfolio
                </Link>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75"
                alt="Professional Printing Design"
                className="rounded-lg shadow-2xl"
              />
              <div className="absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-xl">
                <div className="flex items-center space-x-3">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <Printer className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Print Ready</p>
                    <p className="text-sm text-gray-600">
                      Professional quality
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Gallery Section */}
      <section className="py-10 bg-warm-cream">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Custom Print Designs to Suit Your Needs
            </h2>
          </div>

          {/* Gallery Grid - 2 rows x 7 columns */}
          <div className="space-y-4">
            {/* First Row */}
            <div className="grid grid-cols-7 gap-4">
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75"
                  alt="Print design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75"
                  alt="Print design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75"
                  alt="Print design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75"
                  alt="Print design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1691933135035.jpg&w=1920&q=75"
                  alt="Print design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725932233225.jpg&w=1080&q=75"
                  alt="Print design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75"
                  alt="Print design sample"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* Second Row */}
            <div className="grid grid-cols-7 gap-4">
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733195102611.jpg&w=1080&q=75"
                  alt="Print design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1726835998298.jpg&w=1080&q=75"
                  alt="Print design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733797188503.jpg&w=1080&q=75"
                  alt="Print design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725923064812.jpg&w=1080&q=75"
                  alt="Print design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789523124.jpg&w=1080&q=75"
                  alt="Print design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733181112948.jpg&w=1080&q=75"
                  alt="Print design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75"
                  alt="Print design sample"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Design Process */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Print Design Process
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We follow a proven process to ensure your print materials are both
              visually stunning and technically perfect for production.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {designProcess.map((process, index) => (
              <div key={index} className="text-center">
                <div className="bg-blue-600 text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  {process.step}
                </div>
                <h3 className="font-semibold text-gray-900 mb-3">
                  {process.title}
                </h3>
                <p className="text-gray-600 text-sm">{process.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Print Design Matters
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Professional print design creates tangible connections with your
              audience and establishes credibility that digital alone cannot
              achieve.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-white p-6 rounded-lg shadow-lg text-center"
              >
                <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
      {/* Print Materials We Design */}
      <section className="py-20 bg-warm-cream">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Print Materials We Design
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We create designs for all types of printed materials, ensuring
              each piece effectively communicates your message.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-3">
                Business Materials
              </h3>
              <ul className="text-gray-600 text-sm space-y-2">
                <li>• Business Cards</li>
                <li>• Letterheads</li>
                <li>• Envelopes</li>
                <li>• Folders</li>
              </ul>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="bg-green-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <Target className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-3">
                Marketing Materials
              </h3>
              <ul className="text-gray-600 text-sm space-y-2">
                <li>• Brochures</li>
                <li>• Flyers</li>
                <li>• Postcards</li>
                <li>• Catalogs</li>
              </ul>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <Layers className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-3">Large Format</h3>
              <ul className="text-gray-600 text-sm space-y-2">
                <li>• Posters</li>
                <li>• Banners</li>
                <li>• Trade Show Displays</li>
                <li>• Signage</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="w-full py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-start">
            {/* Left Column - Client Love Content */}
            <div className="lg:col-span-1">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
                What our clients say ...
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                We're a global team of skilled designers united by a passion for design and a commitment to excellence.
              </p>
              
              {/* Statistics */}
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <Star className="h-8 w-8 text-yellow-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Print-Ready Designs</div>
                    <div className="text-gray-600">Professional designs optimized for high-quality printing.</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Lightbulb className="h-8 w-8 text-purple-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Brand Consistency</div>
                    <div className="text-gray-600">Cohesive designs that maintain your brand identity across all materials.</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Award className="h-8 w-8 text-orange-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Production Guidelines</div>
                    <div className="text-gray-600">Complete specifications and files ready for professional printing.</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Scrollable Portfolio */}
            <div className="lg:col-span-2">
              <div className="relative">
                {/* Left Navigation Button */}
                <button
                  onClick={() => scrollPortfolio('left')}
                  className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 ${
                    portfolioScrollPosition <= 0
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:bg-gray-50 hover:shadow-xl'
                  }`}
                  disabled={portfolioScrollPosition <= 0}
                >
                  <ChevronLeft className="h-5 w-5 text-gray-600" />
                </button>

                {/* Right Navigation Button */}
                <button
                  onClick={() => scrollPortfolio('right')}
                  className="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 hover:bg-gray-50 hover:shadow-xl"
                >
                  <ChevronRight className="h-5 w-5 text-gray-600" />
                </button>

                {/* Scrollable Container */}
                <div 
                  ref={portfolioScrollRef}
                  onScroll={handlePortfolioScroll}
                  className="overflow-x-auto whitespace-nowrap py-4 px-12 scrollbar-hide"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                  }}
                >
                <div className="flex space-x-6">
                  {portfolioSamples.map((sample, index) => (
                    <div
                      key={index}
                      className="inline-block w-64 bg-white rounded-lg shadow-lg overflow-hidden flex-shrink-0 hover:shadow-xl transition-shadow duration-300"
                    >
                      <img
                        src={sample.image}
                        alt={sample.title}
                        className="w-full h-48 object-cover"
                      />
                      <div className="p-6">
                        <h3 className="font-semibold text-gray-900 mb-2 whitespace-normal">
                          {sample.title}
                        </h3>
                        <p className="text-gray-600 text-sm mb-4 whitespace-normal">{sample.description}</p>
                        
                        {/* Star Rating */}
                        <div className="flex items-center mb-4">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className="h-4 w-4 text-yellow-400 fill-current"
                            />
                          ))}
                          <span className="text-sm text-gray-500 ml-2">5.0</span>
                        </div>
                        
                        {/* Reviewer Info */}
                        <div className="flex items-center space-x-3">
                          <img
                            src={sample.image}
                            alt="Client"
                            className="w-8 h-8 rounded-full object-cover"
                          />
                          <p className="text-sm font-medium text-gray-900 whitespace-normal">
                            Happy Client
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              </div>
            </div>
          </div>
        </div>
      </section>


      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600">
              Everything you need to know about our printing design services.
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg">
                <button
                  onClick={() => toggleFaq(index)}
                  className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
                >
                  <span className="font-semibold text-gray-900">
                    {faq.question}
                  </span>
                  {openFaq === index ? (
                    <ChevronUp className="h-5 w-5 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-500" />
                  )}
                </button>
                {openFaq === index && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-600">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4">
              Ready to create stunning print designs?
            </p>
            <Link
              to="/contact"
              className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center"
            >
              Start Your Print Design Project
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PrintingDesign;