import { User<PERSON><PERSON> } from '@prisma/client';
import { RegisterRequest, LoginRequest, ChangePasswordRequest, UpdateProfileRequest, AuthUser, TokenPair, ApiResponse } from '../types/auth';
export declare class AuthenticationModel {
    static createRegistrationData(request: RegisterRequest): RegistrationData;
    static createLoginData(request: LoginRequest): LoginData;
    static createProfileUpdateData(request: UpdateProfileRequest): ProfileUpdateData;
    static createPasswordChangeData(request: ChangePasswordRequest): PasswordChangeData;
}
export declare class RegistrationData {
    readonly email: string;
    readonly password: string;
    readonly firstName: string;
    readonly lastName: string;
    readonly phone?: string | undefined;
    constructor(email: string, password: string, firstName: string, lastName: string, phone?: string | undefined);
    private validate;
    private isValidEmail;
    private isValidPassword;
    private isValidPhone;
    getStorageData(): {
        email: string;
        firstName: string;
        lastName: string;
        phone: string | null;
    };
}
export declare class LoginData {
    readonly email: string;
    readonly password: string;
    constructor(email: string, password: string);
    private validate;
    getNormalizedEmail(): string;
}
export declare class ProfileUpdateData {
    readonly firstName?: string | undefined;
    readonly lastName?: string | undefined;
    readonly phone?: string | undefined;
    readonly avatar?: string | undefined;
    constructor(firstName?: string | undefined, lastName?: string | undefined, phone?: string | undefined, avatar?: string | undefined);
    private validate;
    private isValidName;
    private isValidPhone;
    private isValidUrl;
    hasUpdates(): boolean;
    getStorageData(): any;
}
export declare class PasswordChangeData {
    readonly currentPassword: string;
    readonly newPassword: string;
    constructor(currentPassword: string, newPassword: string);
    private validate;
    private isValidPassword;
}
export declare class UserProfile {
    readonly id: string;
    readonly email: string;
    readonly firstName: string;
    readonly lastName: string;
    readonly role: UserRole;
    readonly isActive: boolean;
    readonly isVerified: boolean;
    readonly avatar: string | null;
    readonly phone: string | null;
    readonly createdAt: Date;
    readonly updatedAt: Date;
    readonly lastLoginAt: Date | null;
    constructor(id: string, email: string, firstName: string, lastName: string, role: UserRole, isActive: boolean, isVerified: boolean, avatar: string | null, phone: string | null, createdAt: Date, updatedAt: Date, lastLoginAt: Date | null);
    static fromAuthUser(user: AuthUser): UserProfile;
    getDisplayName(): string;
    isProfileComplete(): boolean;
    getCompletionPercentage(): number;
    toJSON(): {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        role: import(".prisma/client").$Enums.UserRole;
        isActive: boolean;
        isVerified: boolean;
        avatar: string | null;
        phone: string | null;
        createdAt: Date;
        updatedAt: Date;
        lastLoginAt: Date | null;
        displayName: string;
        isProfileComplete: boolean;
        completionPercentage: number;
    };
}
export declare class AuthenticationResponse {
    readonly user: UserProfile;
    readonly tokens: TokenPair;
    constructor(user: UserProfile, tokens: TokenPair);
    toApiResponse(): ApiResponse<{
        user: UserProfile;
        tokens: TokenPair;
    }>;
}
export declare class SessionInfo {
    readonly id: string;
    readonly ipAddress: string;
    readonly userAgent: string;
    readonly createdAt: Date;
    readonly expiresAt: Date;
    readonly isActive: boolean;
    constructor(id: string, ipAddress: string, userAgent: string, createdAt: Date, expiresAt: Date, isActive: boolean);
    isCurrent(currentIpAddress: string, currentUserAgent: string): boolean;
    getDeviceInfo(): {
        browser: string;
        os: string;
        device: string;
    };
    getTimeUntilExpiration(): number;
    isExpired(): boolean;
    toJSON(): {
        id: string;
        ipAddress: string;
        createdAt: Date;
        expiresAt: Date;
        isActive: boolean;
        isExpired: boolean;
        timeUntilExpiration: number;
        deviceInfo: {
            browser: string;
            os: string;
            device: string;
        };
    };
}
declare const _default: {
    AuthenticationModel: typeof AuthenticationModel;
    RegistrationData: typeof RegistrationData;
    LoginData: typeof LoginData;
    ProfileUpdateData: typeof ProfileUpdateData;
    PasswordChangeData: typeof PasswordChangeData;
    UserProfile: typeof UserProfile;
    AuthenticationResponse: typeof AuthenticationResponse;
    SessionInfo: typeof SessionInfo;
};
export default _default;
//# sourceMappingURL=AuthModels.d.ts.map