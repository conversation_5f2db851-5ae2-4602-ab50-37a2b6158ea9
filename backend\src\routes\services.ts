import express from 'express';
import Jo<PERSON> from 'joi';
import { authenticate } from '../middleware/auth';
import { AuthenticatedRequest } from '../types/auth';
import { createLogger } from '../utils/logger';
import {
  categoryIdParamSchema,
  categoryRouteParamSchema,
  createServiceCategorySchema,
  createServiceFormFieldOptionSchema,
  createServiceFormFieldSchema,
  createServiceSchema,
  fieldIdParamSchema,
  popularServicesQuerySchema,
  priceCalculationSchema,
  pricingTypeParamSchema,
  serviceIdParamSchema,
  serviceListQuerySchema,
  serviceSearchQuerySchema,
  updateServiceCategorySchema,
  updateServiceFormFieldOptionSchema,
  updateServiceFormFieldSchema,
  updateServiceSchema,
  validate,
  validateParams,
  validateQuery,
} from '../validation/service';

// Import the layered architecture components
import { createServiceController } from '../controllers/ServiceController';
import { CompositeServiceRepository } from '../repositories/CompositeServiceRepository';
import { ServiceService } from '../services/ServiceService';

const router = express.Router();
const logger = createLogger('ServiceRoutes');

// Lazy initialization of service components
let serviceController: any = null;

function getServiceController() {
  if (!serviceController) {
    // Use test Prisma client in test environment, otherwise use database service
    let prisma;
    if (process.env.NODE_ENV === 'test' && (global as any).__PRISMA__) {
      prisma = (global as any).__PRISMA__;
    } else {
      const { databaseService } = require('../services');
      prisma = databaseService.getClient();
    }

    const serviceRepository = new CompositeServiceRepository(prisma);
    const serviceService = new ServiceService(serviceRepository);
    serviceController = createServiceController(serviceService);

    logger.info('Service controller initialized');
  }
  return serviceController;
}

// Middleware to check if user is admin (for admin-only routes)
const requireAdmin = (req: AuthenticatedRequest, res: any, next: any) => {
  if (req.user?.role !== 'ADMIN') {
    return res.forbidden('Admin access required');
  }
  next();
};

// Service routes
// =============

// Service list and search endpoints - MUST come before /:id routes
// GET /api/services - Get services list (public)
router.get('/', validateQuery(serviceListQuerySchema), (req, res, next) =>
  getServiceController().getServices(req, res, next)
);

// GET /api/services/search - Search services (public)
router.get('/search', validateQuery(serviceSearchQuerySchema), (req, res, next) =>
  getServiceController().searchServices(req, res, next)
);

// GET /api/services/popular - Get popular services (public)
router.get('/popular', validateQuery(popularServicesQuerySchema), (req, res, next) =>
  getServiceController().getPopularServices(req, res, next)
);

// GET /api/services/stats - Get service statistics (admin only)
router.get('/stats', authenticate, requireAdmin, (req, res, next) =>
  getServiceController().getServiceStats(req, res, next)
);

// GET /api/services/filter - Filter services by criteria (public)
router.get('/filter', validateQuery(serviceListQuerySchema), (req, res, next) =>
  getServiceController().getServices(req, res, next)
);

// Utility endpoints - MUST come before /:id routes
// GET /api/services/exists/:id - Check if service exists (admin only)
router.get('/exists/:id', authenticate, requireAdmin, validateParams(serviceIdParamSchema), (req, res, next) =>
  getServiceController().serviceExists(req, res, next)
);

// GET /api/services/count - Count total services (admin only)
router.get('/count', authenticate, requireAdmin, (req, res, next) =>
  getServiceController().countServices(req, res, next)
);

// Service by pricing type - MUST come before /:id routes
// GET /api/services/pricing/:pricingType - Get services by pricing type (public)
router.get('/pricing/:pricingType', validateParams(pricingTypeParamSchema), (req, res, next) =>
  getServiceController().getServicesByPricingType(req, res, next)
);

// POST /api/services - Create new service (admin only)
router.post('/', authenticate, requireAdmin, validate(createServiceSchema), (req, res, next) =>
  getServiceController().createService(req, res, next)
);

// Price calculation endpoint
// POST /api/services/calculate-price - Calculate service price (authenticated)
router.post('/calculate-price', authenticate, validate(priceCalculationSchema), (req, res, next) =>
  getServiceController().calculateServicePrice(req, res, next)
);

// Alias: POST /api/services/:id/calculate-price - Calculate service price using path param (authenticated)
router.post(
  '/:id/calculate-price',
  authenticate,
  validateParams(serviceIdParamSchema),
  validate(priceCalculationSchema.keys({ serviceId: Joi.forbidden() })),
  (req, res, next) => {
    // Propagate id from path to body to reuse controller logic
    req.body = { ...req.body, serviceId: (req.params as any).id };
    return getServiceController().calculateServicePrice(req, res, next);
  }
);

// Service category routes
// ======================

// Service categories - nested under /categories path
// GET /api/services/categories - Get service categories (public)
router.get('/categories', (req, res, next) => getServiceController().getServiceCategories(req, res, next));

// POST /api/services/categories - Create service category (admin only)
router.post('/categories', authenticate, requireAdmin, validate(createServiceCategorySchema), (req, res, next) =>
  getServiceController().createServiceCategory(req, res, next)
);

// Service categories by route - MUST come before /:id routes
// GET /api/services/categories/route/:route - Get category by route (public)
router.get('/categories/route/:route', validateParams(categoryRouteParamSchema), (req, res, next) =>
  getServiceController().getServiceCategoryByRoute(req, res, next)
);

// Convenience: GET /api/services/categories/route/:route/services - Services by category route (public)
router.get('/categories/route/:route/services', validateParams(categoryRouteParamSchema), (req, res, next) =>
  getServiceController().getServicesByCategoryRoute(req, res, next)
);

// Utility endpoints for categories
// GET /api/services/categories/exists/:id - Check if category exists (admin only)
router.get(
  '/categories/exists/:id',
  authenticate,
  requireAdmin,
  validateParams(categoryIdParamSchema),
  (req, res, next) => getServiceController().serviceCategoryExists(req, res, next)
);

// GET /api/services/categories/count/:categoryId - Count services by category (public)
router.get('/categories/count/:categoryId', validateParams(categoryIdParamSchema), (req, res, next) =>
  getServiceController().countServicesByCategory(req, res, next)
);

// Service categories by ID - parameterized routes
// GET /api/services/categories/:id - Get category by ID (public)
router.get('/categories/:id', validateParams(categoryIdParamSchema), (req, res, next) =>
  getServiceController().getServiceCategoryById(req, res, next)
);

// GET /api/services/categories/:categoryId/services - Get services by category (public)
router.get('/categories/:categoryId/services', validateParams(categoryIdParamSchema), (req, res, next) =>
  getServiceController().getServicesByCategory(req, res, next)
);

// PUT /api/services/categories/:id - Update category (admin only)
router.put(
  '/categories/:id',
  authenticate,
  requireAdmin,
  validateParams(categoryIdParamSchema),
  validate(updateServiceCategorySchema),
  (req, res, next) => getServiceController().updateServiceCategory(req, res, next)
);

// DELETE /api/services/categories/:id - Delete category (admin only)
router.delete('/categories/:id', authenticate, requireAdmin, validateParams(categoryIdParamSchema), (req, res, next) =>
  getServiceController().deleteServiceCategory(req, res, next)
);

// Service form field routes (nested under service)
// ================================================

// Service form fields by service ID
// GET /api/services/:serviceId/form-fields - Get service form fields (public)
router.get(
  '/:serviceId/form-fields',
  validateParams(Joi.object({ serviceId: Joi.string().required() })),
  (req, res, next) => getServiceController().getServiceFormFields(req, res, next)
);

// POST /api/services/:serviceId/form-fields - Add form field to service (admin only)
router.post(
  '/:serviceId/form-fields',
  authenticate,
  requireAdmin,
  validateParams(Joi.object({ serviceId: Joi.string().required() })),
  validate(createServiceFormFieldSchema),
  (req, res, next) => {
    // Ensure serviceId from URL matches the one in body
    req.body.serviceId = req.params.serviceId;
    return getServiceController().createServiceFormField(req, res, next);
  }
);

// Aliases to support nested semantics if the frontend prefers nested structure
// PUT /api/services/:serviceId/form-fields/:fieldId - Update form field (admin only)
router.put(
  '/:serviceId/form-fields/:fieldId',
  authenticate,
  requireAdmin,
  validateParams(Joi.object({ serviceId: Joi.string().required(), fieldId: Joi.string().required() })),
  validate(updateServiceFormFieldSchema),
  (req, res, next) => getServiceController().updateServiceFormField(req, res, next)
);

// DELETE /api/services/:serviceId/form-fields/:fieldId - Delete form field (admin only)
router.delete(
  '/:serviceId/form-fields/:fieldId',
  authenticate,
  requireAdmin,
  validateParams(Joi.object({ serviceId: Joi.string().required(), fieldId: Joi.string().required() })),
  (req, res, next) => getServiceController().deleteServiceFormField(req, res, next)
);

// Service form field by ID routes
// GET /api/services/form-fields/:fieldId - Get form field by ID (admin only)
router.get('/form-fields/:fieldId', authenticate, requireAdmin, validateParams(fieldIdParamSchema), (req, res, next) =>
  getServiceController().getServiceFormFieldById(req, res, next)
);

// PUT /api/services/form-fields/:fieldId - Update form field (admin only)
router.put(
  '/form-fields/:fieldId',
  authenticate,
  requireAdmin,
  validateParams(fieldIdParamSchema),
  validate(updateServiceFormFieldSchema),
  (req, res, next) => getServiceController().updateServiceFormField(req, res, next)
);

// DELETE /api/services/form-fields/:fieldId - Delete form field (admin only)
router.delete(
  '/form-fields/:fieldId',
  authenticate,
  requireAdmin,
  validateParams(fieldIdParamSchema),
  (req, res, next) => getServiceController().deleteServiceFormField(req, res, next)
);

// Service form field option routes
// ================================

// GET /api/services/form-fields/:fieldId/options - Get field options (public)
router.get('/form-fields/:fieldId/options', validateParams(fieldIdParamSchema), (req, res, next) =>
  getServiceController().getServiceFormFieldOptions(req, res, next)
);

// POST /api/services/form-fields/:fieldId/options - Add field option (admin only)
router.post(
  '/form-fields/:fieldId/options',
  authenticate,
  requireAdmin,
  validateParams(fieldIdParamSchema),
  validate(createServiceFormFieldOptionSchema),
  (req, res, next) => {
    // Ensure fieldId from URL matches the one in body
    req.body.fieldId = req.params.fieldId;
    return getServiceController().createServiceFormFieldOption(req, res, next);
  }
);

// PUT /api/services/form-fields/options/:optionId - Update field option (admin only)
router.put(
  '/form-fields/options/:optionId',
  authenticate,
  requireAdmin,
  validateParams(Joi.object({ optionId: Joi.string().required() })),
  validate(updateServiceFormFieldOptionSchema),
  (req, res, next) => getServiceController().updateServiceFormFieldOption(req, res, next)
);

// DELETE /api/services/form-fields/options/:optionId - Delete field option (admin only)
router.delete(
  '/form-fields/options/:optionId',
  authenticate,
  requireAdmin,
  validateParams(Joi.object({ optionId: Joi.string().required() })),
  (req, res, next) => getServiceController().deleteServiceFormFieldOption(req, res, next)
);

// Service CRUD routes (parameterized) - MUST come last to avoid conflicts
// =======================================================================

// GET /api/services/:id - Get service by ID (public)
router.get('/:id', validateParams(serviceIdParamSchema), (req, res, next) =>
  getServiceController().getServiceById(req, res, next)
);

// PUT /api/services/:id - Update service (admin only)
router.put(
  '/:id',
  authenticate,
  requireAdmin,
  validateParams(serviceIdParamSchema),
  validate(updateServiceSchema),
  (req, res, next) => getServiceController().updateService(req, res, next)
);

// DELETE /api/services/:id - Delete service (admin only)
router.delete('/:id', authenticate, requireAdmin, validateParams(serviceIdParamSchema), (req, res, next) =>
  getServiceController().deleteService(req, res, next)
);

export default router;
