import {Router} from 'express';
import {authenticate} from '../middleware/auth';
import {createLogger} from '../utils/logger';

const router = Router();
const logger = createLogger('Services');

/**
 * @swagger
 * /api/v1/services:
 *   get:
 *     summary: Get all services
 *     description: Retrieve a list of all available printing services
 *     tags: [Services]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of services retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       price:
 *                         type: number
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/', authenticate, async (req, res) => {
	try {
		logger.info('Fetching services', {
			userId: req.userId,
			requestId: res.getHeader('X-Request-ID'),
		});

		// TODO: Implement service fetching logic
		const services = [
			{
				id: '1',
				name: 'Business Cards',
				description: 'Professional business card printing',
				price: 29.99,
			},
			{
				id: '2',
				name: 'Banners',
				description: 'Large format banner printing',
				price: 149.99,
			},
		];

		res.status(200).json({
			success: true,
			data: services,
		});
	} catch (error) {
		logger.error('Error fetching services', error);
		res.status(500).json({
			success: false,
			error: 'Failed to fetch services',
		});
	}
});

/**
 * @swagger
 * /api/v1/services/{id}:
 *   get:
 *     summary: Get service by ID
 *     description: Retrieve a specific service by its ID
 *     tags: [Services]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Service ID
 *     responses:
 *       200:
 *         description: Service retrieved successfully
 *       404:
 *         description: Service not found
 *       401:
 *         description: Unauthorized
 */
router.get('/:id', authenticate, async (req, res) => {
	try {
		const {id} = req.params;

		logger.info('Fetching service by ID', {
			serviceId: id,
			userId: req.userId,
			requestId: res.getHeader('X-Request-ID'),
		});

		// TODO: Implement service fetching by ID logic
		const service = {
			id,
			name: 'Business Cards',
			description: 'Professional business card printing',
			price: 29.99,
			options: ['Standard', 'Premium', 'Custom'],
		};

		res.status(200).json({
			success: true,
			data: service,
		});
	} catch (error) {
		logger.error('Error fetching service by ID', error);
		res.status(500).json({
			success: false,
			error: 'Failed to fetch service',
		});
	}
});

export default router;
