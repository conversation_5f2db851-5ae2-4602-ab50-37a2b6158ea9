"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config({ path: '.env.test' });
beforeAll(async () => {
    process.env.NODE_ENV = 'test';
    process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || process.env.DATABASE_URL;
    const prisma = new client_1.PrismaClient();
    try {
        await prisma.$connect();
        console.log('✅ Test database connected successfully');
    }
    catch (error) {
        console.error('❌ Failed to connect to test database:', error);
        throw error;
    }
    finally {
        await prisma.$disconnect();
    }
});
afterAll(async () => {
    console.log('🧹 Test suite completed');
});
jest.setTimeout(30000);
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
beforeEach(() => {
    console.log = jest.fn();
    console.error = originalConsoleError;
});
afterEach(() => {
    console.log = originalConsoleLog;
});
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.JWT_EXPIRES_IN = '1h';
process.env.BCRYPT_ROUNDS = '10';
//# sourceMappingURL=setup.js.map