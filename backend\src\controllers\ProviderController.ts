import { Response } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { IProviderService } from '../services/ProviderService';
import { AuthenticatedRequest } from '../types/auth';
import {
  AddRatingRequest,
  CreateProviderRequest,
  CreateProviderServiceRequest,
  CreateServiceAreaRequest,
  UpdateProviderRequest,
  UpdateProviderServiceRequest,
  UpsertOperatingHoursRequest,
} from '../types/provider';
import { createLogger } from '../utils/logger';
import { RequestLogger } from '../utils/RequestLogger';
import { createPaginationMeta } from '../utils/responseWrapper';

export interface IProviderController {}

export class ProviderController {
  private logger = (createLogger && createLogger('ProviderController')) as ReturnType<typeof createLogger>;
  constructor(private providerService: IProviderService) {}

  listProviders = asyncHandler(
    RequestLogger.withLogging(this.logger, 'List providers', async (req: AuthenticatedRequest, res: Response) => {
      const result = await this.providerService.listProviders(req.query as any);
      const pagination = createPaginationMeta(result.pagination.page, result.pagination.limit, result.pagination.total);
      // Minimal FE-friendly projection: include zip codes array and joinedDate alias
      const providers = result.providers.map(p => ({
        id: p.id,
        businessName: p.businessName,
        isVerified: p.isVerified,
        isActive: p.isActive,
        averageRating: p.averageRating ?? 0,
        reviewCount: p.reviewCount ?? 0,
        serviceAreaZipCodes: p.serviceAreaZipCodes ?? [],
        joinedDate: (p.createdAt as unknown as string) || undefined,
      }));
      res.success({ providers }, undefined, undefined, pagination);
    })
  );

  getProviderById = asyncHandler(
    RequestLogger.withLogging(this.logger, 'Get provider detail', async (req: AuthenticatedRequest, res: Response) => {
      const provider = await this.providerService.getProviderById(req.params.id);
      res.success({ provider });
    })
  );

  createProvider = asyncHandler(
    RequestLogger.withLogging(this.logger, 'Create provider', async (req: AuthenticatedRequest, res: Response) => {
      const data: CreateProviderRequest = req.body;
      const provider = await this.providerService.createProvider(data);
      res.created({ provider }, 'Provider created successfully');
    })
  );

  updateProvider = asyncHandler(
    RequestLogger.withLogging(this.logger, 'Update provider', async (req: AuthenticatedRequest, res: Response) => {
      const update: UpdateProviderRequest = req.body;
      const provider = await this.providerService.updateProvider(req.params.id, update);
      res.success({ provider }, 'Provider updated successfully');
    })
  );

  deleteProvider = asyncHandler(
    RequestLogger.withLogging(this.logger, 'Delete provider', async (req: AuthenticatedRequest, res: Response) => {
      const deleted = await this.providerService.deleteProvider(req.params.id);
      res.success({ deleted }, 'Provider deleted successfully');
    })
  );

  // Provider services
  listProviderServices = asyncHandler(
    RequestLogger.withLogging(
      this.logger,
      'List provider services',
      async (req: AuthenticatedRequest, res: Response) => {
        const services = await this.providerService.listProviderServices(req.params.id);
        res.success({ services });
      }
    )
  );

  addProviderService = asyncHandler(
    RequestLogger.withLogging(this.logger, 'Add provider service', async (req: AuthenticatedRequest, res: Response) => {
      const data: CreateProviderServiceRequest = req.body;
      const service = await this.providerService.addProviderService(req.params.id, data);
      res.created({ service }, 'Provider service added successfully');
    })
  );

  updateProviderService = asyncHandler(
    RequestLogger.withLogging(
      this.logger,
      'Update provider service',
      async (req: AuthenticatedRequest, res: Response) => {
        const data: UpdateProviderServiceRequest = req.body;
        const updated = await this.providerService.updateProviderService(req.params.id, req.params.serviceId, data);
        res.success({ service: updated }, 'Provider service updated successfully');
      }
    )
  );

  removeProviderService = asyncHandler(
    RequestLogger.withLogging(
      this.logger,
      'Remove provider service',
      async (req: AuthenticatedRequest, res: Response) => {
        const removed = await this.providerService.removeProviderService(req.params.id, req.params.serviceId);
        res.success({ deleted: removed }, 'Provider service removed successfully');
      }
    )
  );

  // Operating hours
  getOperatingHours = asyncHandler(
    RequestLogger.withLogging(this.logger, 'Get operating hours', async (req: AuthenticatedRequest, res: Response) => {
      const hours = await this.providerService.getOperatingHours(req.params.id);
      res.success({ hours });
    })
  );

  upsertOperatingHours = asyncHandler(
    RequestLogger.withLogging(
      this.logger,
      'Upsert operating hours',
      async (req: AuthenticatedRequest, res: Response) => {
        const data: UpsertOperatingHoursRequest = req.body;
        const hours = await this.providerService.upsertOperatingHours(req.params.id, data);
        res.success({ hours }, 'Operating hours updated');
      }
    )
  );

  // Service areas
  listServiceAreas = asyncHandler(
    RequestLogger.withLogging(this.logger, 'List service areas', async (req: AuthenticatedRequest, res: Response) => {
      const areas = await this.providerService.listServiceAreas(req.params.id);
      res.success({ areas });
    })
  );

  addServiceArea = asyncHandler(
    RequestLogger.withLogging(this.logger, 'Add service area', async (req: AuthenticatedRequest, res: Response) => {
      const data: CreateServiceAreaRequest = req.body;
      const area = await this.providerService.addServiceArea(req.params.id, data);
      res.created({ area }, 'Service area added');
    })
  );

  removeServiceArea = asyncHandler(
    RequestLogger.withLogging(this.logger, 'Remove service area', async (req: AuthenticatedRequest, res: Response) => {
      const deleted = await this.providerService.removeServiceArea(req.params.id, req.params.areaId);
      res.success({ deleted }, 'Service area removed');
    })
  );

  // Ratings
  getRatings = asyncHandler(
    RequestLogger.withLogging(this.logger, 'Get rating display', async (req: AuthenticatedRequest, res: Response) => {
      const rating = await this.providerService.getRatingDisplay(req.params.id);
      res.success({ rating });
    })
  );

  addRating = asyncHandler(
    RequestLogger.withLogging(this.logger, 'Add rating', async (req: AuthenticatedRequest, res: Response) => {
      const data: AddRatingRequest = req.body;
      const rating = await this.providerService.addRating(req.params.id, data);
      res.created({ rating }, 'Rating added');
    })
  );
}

export const createProviderController = (providerService: IProviderService): ProviderController =>
  new ProviderController(providerService);

export default ProviderController;
