/**
 * AuthRepository Unit Tests
 * 
 * Comprehensive unit tests for the AuthRepository class covering:
 * - User CRUD operations
 * - Login attempt tracking and account locking
 * - Password reset token management
 * - Email verification token management
 * - Session management
 */

import { AuthRepository } from '../../../src/repositories/AuthRepository';
import { PrismaClient, UserRole, User } from '@prisma/client';
import { cleanUserData } from '../../setup';

describe('AuthRepository', () => {
  let authRepository: AuthRepository;
  let prisma: PrismaClient;

  const mockUser = {
    id: 'user123',
    email: '<EMAIL>',
    password: 'hashedPassword',
    firstName: 'Test',
    lastName: 'User',
    role: UserRole.CUSTOMER,
    isActive: true,
    isVerified: false,
    avatar: null,
    phone: '+**********',
    createdAt: new Date(),
    updatedAt: new Date(),
    lastLoginAt: null,
    loginAttempts: 0,
    lockedUntil: null,
    emailVerified: null,
    emailVerificationToken: null,
    emailVerificationTokenExpires: null,
    passwordResetToken: null,
    passwordResetTokenExpires: null
  };

  beforeAll(() => {
    prisma = global.__PRISMA__;
    authRepository = new AuthRepository(prisma);
  });

  beforeEach(async () => {
    await cleanUserData();
  });

  describe('User CRUD Operations', () => {
    describe('createUser', () => {
      it('should create a new user successfully', async () => {
        // Arrange
        const userData = {
          email: '<EMAIL>',
          password: 'hashedPassword',
          firstName: 'Test',
          lastName: 'User',
          phone: '+**********',
          role: UserRole.CUSTOMER,
          isActive: true,
          isVerified: false
        };

        // Act
        const result = await authRepository.createUser(userData);

        // Assert
        expect(result).toMatchObject({
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          phone: '+**********',
          role: UserRole.CUSTOMER,
          isActive: true,
          isVerified: false
        });
        expect(result.id).toBeDefined();
        expect(result.createdAt).toBeDefined();
        expect(result.updatedAt).toBeDefined();
      });

      it('should normalize email to lowercase', async () => {
        // Arrange
        const userData = {
          email: '<EMAIL>',
          password: 'hashedPassword',
          firstName: 'Test',
          lastName: 'User',
          role: UserRole.CUSTOMER,
          isActive: true,
          isVerified: false
        };

        // Act
        const result = await authRepository.createUser(userData);

        // Assert
        expect(result.email).toBe('<EMAIL>');
      });

      it('should trim whitespace from names', async () => {
        // Arrange
        const userData = {
          email: '<EMAIL>',
          password: 'hashedPassword',
          firstName: '  Test  ',
          lastName: '  User  ',
          role: UserRole.CUSTOMER,
          isActive: true,
          isVerified: false
        };

        // Act
        const result = await authRepository.createUser(userData);

        // Assert
        expect(result.firstName).toBe('Test');
        expect(result.lastName).toBe('User');
      });
    });

    describe('findUserByEmail', () => {
      beforeEach(async () => {
        await prisma.user.create({ data: mockUser });
      });

      it('should find user by email', async () => {
        // Act
        const result = await authRepository.findUserByEmail('<EMAIL>');

        // Assert
        expect(result).toMatchObject({
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User'
        });
      });

      it('should be case insensitive', async () => {
        // Act
        const result = await authRepository.findUserByEmail('<EMAIL>');

        // Assert
        expect(result).toMatchObject({
          email: '<EMAIL>'
        });
      });

      it('should return null for non-existent email', async () => {
        // Act
        const result = await authRepository.findUserByEmail('<EMAIL>');

        // Assert
        expect(result).toBeNull();
      });
    });

    describe('findUserById', () => {
      let userId: string;

      beforeEach(async () => {
        const user = await prisma.user.create({ data: mockUser });
        userId = user.id;
      });

      it('should find user by ID', async () => {
        // Act
        const result = await authRepository.findUserById(userId);

        // Assert
        expect(result).toMatchObject({
          id: userId,
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User'
        });
      });

      it('should return null for non-existent ID', async () => {
        // Act
        const result = await authRepository.findUserById('nonexistent-id');

        // Assert
        expect(result).toBeNull();
      });
    });

    describe('findUserByIdSafe', () => {
      let userId: string;

      beforeEach(async () => {
        const user = await prisma.user.create({ data: mockUser });
        userId = user.id;
      });

      it('should return user without sensitive fields', async () => {
        // Act
        const result = await authRepository.findUserByIdSafe(userId);

        // Assert
        expect(result).toMatchObject({
          id: userId,
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          isActive: true,
          isVerified: false
        });
        expect(result).not.toHaveProperty('password');
        expect(result).not.toHaveProperty('passwordResetToken');
        expect(result).not.toHaveProperty('emailVerificationToken');
      });
    });

    describe('updateUser', () => {
      let userId: string;

      beforeEach(async () => {
        const user = await prisma.user.create({ data: mockUser });
        userId = user.id;
      });

      it('should update user fields', async () => {
        // Arrange
        const updateData = {
          firstName: 'Updated',
          lastName: 'Name',
          phone: '+9876543210'
        };

        // Act
        const result = await authRepository.updateUser(userId, updateData);

        // Assert
        expect(result).toMatchObject({
          id: userId,
          firstName: 'Updated',
          lastName: 'Name',
          phone: '+9876543210'
        });
        expect(result.updatedAt.getTime()).toBeGreaterThan(result.createdAt.getTime());
      });

      it('should handle optional fields correctly', async () => {
        // Arrange
        const updateData = {
          phone: null,
          avatar: null
        };

        // Act
        const result = await authRepository.updateUser(userId, updateData);

        // Assert
        expect(result.phone).toBeNull();
        expect(result.avatar).toBeNull();
      });
    });

    describe('updateUserPassword', () => {
      let userId: string;

      beforeEach(async () => {
        const user = await prisma.user.create({ data: mockUser });
        userId = user.id;
      });

      it('should update user password', async () => {
        // Act
        await authRepository.updateUserPassword(userId, 'newHashedPassword');

        // Assert
        const updatedUser = await prisma.user.findUnique({ where: { id: userId } });
        expect(updatedUser?.password).toBe('newHashedPassword');
      });
    });
  });

  describe('Login Attempt Tracking', () => {
    let userId: string;

    beforeEach(async () => {
      const user = await prisma.user.create({ data: mockUser });
      userId = user.id;
    });

    describe('incrementLoginAttempts', () => {
      it('should increment login attempts', async () => {
        // Act
        await authRepository.incrementLoginAttempts(userId);

        // Assert
        const user = await prisma.user.findUnique({ where: { id: userId } });
        expect(user?.loginAttempts).toBe(1);
        expect(user?.lockedUntil).toBeNull();
      });

      it('should lock account after max attempts', async () => {
        // Arrange - Set attempts to 4 (one less than max)
        await prisma.user.update({
          where: { id: userId },
          data: { loginAttempts: 4 }
        });

        // Act
        await authRepository.incrementLoginAttempts(userId);

        // Assert
        const user = await prisma.user.findUnique({ where: { id: userId } });
        expect(user?.loginAttempts).toBe(5);
        expect(user?.lockedUntil).not.toBeNull();
        expect(user?.lockedUntil!.getTime()).toBeGreaterThan(Date.now());
      });
    });

    describe('resetLoginAttempts', () => {
      it('should reset login attempts and unlock account', async () => {
        // Arrange
        const lockUntil = new Date(Date.now() + 30 * 60 * 1000);
        await prisma.user.update({
          where: { id: userId },
          data: {
            loginAttempts: 5,
            lockedUntil: lockUntil
          }
        });

        // Act
        await authRepository.resetLoginAttempts(userId);

        // Assert
        const user = await prisma.user.findUnique({ where: { id: userId } });
        expect(user?.loginAttempts).toBe(0);
        expect(user?.lockedUntil).toBeNull();
        expect(user?.lastLoginAt).not.toBeNull();
      });
    });

    describe('isAccountLocked', () => {
      it('should return false for unlocked account', async () => {
        // Act
        const isLocked = await authRepository.isAccountLocked(userId);

        // Assert
        expect(isLocked).toBe(false);
      });

      it('should return true for locked account', async () => {
        // Arrange
        const lockUntil = new Date(Date.now() + 30 * 60 * 1000);
        await prisma.user.update({
          where: { id: userId },
          data: { lockedUntil: lockUntil }
        });

        // Act
        const isLocked = await authRepository.isAccountLocked(userId);

        // Assert
        expect(isLocked).toBe(true);
      });

      it('should auto-unlock expired locks', async () => {
        // Arrange
        const expiredLock = new Date(Date.now() - 1000); // 1 second ago
        await prisma.user.update({
          where: { id: userId },
          data: {
            lockedUntil: expiredLock,
            loginAttempts: 5
          }
        });

        // Act
        const isLocked = await authRepository.isAccountLocked(userId);

        // Assert
        expect(isLocked).toBe(false);
        
        // Verify auto-unlock
        const user = await prisma.user.findUnique({ where: { id: userId } });
        expect(user?.lockedUntil).toBeNull();
        expect(user?.loginAttempts).toBe(0);
      });
    });

    describe('getAccountLockInfo', () => {
      it('should return lock info for user', async () => {
        // Arrange
        await prisma.user.update({
          where: { id: userId },
          data: { loginAttempts: 3 }
        });

        // Act
        const lockInfo = await authRepository.getAccountLockInfo(userId);

        // Assert
        expect(lockInfo).toMatchObject({
          isLocked: false,
          attempts: 3,
          maxAttempts: 5,
          remainingAttempts: 2
        });
      });

      it('should return null for non-existent user', async () => {
        // Act
        const lockInfo = await authRepository.getAccountLockInfo('nonexistent');

        // Assert
        expect(lockInfo).toBeNull();
      });
    });
  });

  describe('Password Reset Token Management', () => {
    let userId: string;

    beforeEach(async () => {
      const user = await prisma.user.create({ data: mockUser });
      userId = user.id;
    });

    describe('setPasswordResetToken', () => {
      it('should set password reset token and expiry', async () => {
        // Arrange
        const token = 'resetToken123';
        const expiresAt = new Date(Date.now() + 60 * 60 * 1000);

        // Act
        await authRepository.setPasswordResetToken(userId, token, expiresAt);

        // Assert
        const user = await prisma.user.findUnique({ where: { id: userId } });
        expect(user?.passwordResetToken).toBe(token);
        expect(user?.passwordResetTokenExpires).toEqual(expiresAt);
      });
    });

    describe('findUserByPasswordResetToken', () => {
      it('should find user by valid reset token', async () => {
        // Arrange
        const token = 'resetToken123';
        const expiresAt = new Date(Date.now() + 60 * 60 * 1000);
        await prisma.user.update({
          where: { id: userId },
          data: {
            passwordResetToken: token,
            passwordResetTokenExpires: expiresAt
          }
        });

        // Act
        const result = await authRepository.findUserByPasswordResetToken(token);

        // Assert
        expect(result).toMatchObject({
          id: userId,
          email: '<EMAIL>'
        });
      });

      it('should not find user with expired token', async () => {
        // Arrange
        const token = 'expiredToken123';
        const expiredAt = new Date(Date.now() - 1000);
        await prisma.user.update({
          where: { id: userId },
          data: {
            passwordResetToken: token,
            passwordResetTokenExpires: expiredAt
          }
        });

        // Act
        const result = await authRepository.findUserByPasswordResetToken(token);

        // Assert
        expect(result).toBeNull();
      });
    });

    describe('clearPasswordResetToken', () => {
      it('should clear password reset token', async () => {
        // Arrange
        await prisma.user.update({
          where: { id: userId },
          data: {
            passwordResetToken: 'token123',
            passwordResetTokenExpires: new Date()
          }
        });

        // Act
        await authRepository.clearPasswordResetToken(userId);

        // Assert
        const user = await prisma.user.findUnique({ where: { id: userId } });
        expect(user?.passwordResetToken).toBeNull();
        expect(user?.passwordResetTokenExpires).toBeNull();
      });
    });
  });

  describe('Email Verification Token Management', () => {
    let userId: string;

    beforeEach(async () => {
      const user = await prisma.user.create({ data: mockUser });
      userId = user.id;
    });

    describe('setEmailVerificationToken', () => {
      it('should set email verification token and expiry', async () => {
        // Arrange
        const token = 'verifyToken123';
        const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000);

        // Act
        await authRepository.setEmailVerificationToken(userId, token, expiresAt);

        // Assert
        const user = await prisma.user.findUnique({ where: { id: userId } });
        expect(user?.emailVerificationToken).toBe(token);
        expect(user?.emailVerificationTokenExpires).toEqual(expiresAt);
      });
    });

    describe('findUserByEmailVerificationToken', () => {
      it('should find user by valid verification token', async () => {
        // Arrange
        const token = 'verifyToken123';
        const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000);
        await prisma.user.update({
          where: { id: userId },
          data: {
            emailVerificationToken: token,
            emailVerificationTokenExpires: expiresAt
          }
        });

        // Act
        const result = await authRepository.findUserByEmailVerificationToken(token);

        // Assert
        expect(result).toMatchObject({
          id: userId,
          email: '<EMAIL>'
        });
      });

      it('should not find user with expired token', async () => {
        // Arrange
        const token = 'expiredToken123';
        const expiredAt = new Date(Date.now() - 1000);
        await prisma.user.update({
          where: { id: userId },
          data: {
            emailVerificationToken: token,
            emailVerificationTokenExpires: expiredAt
          }
        });

        // Act
        const result = await authRepository.findUserByEmailVerificationToken(token);

        // Assert
        expect(result).toBeNull();
      });
    });

    describe('markEmailAsVerified', () => {
      it('should mark email as verified and clear token', async () => {
        // Arrange
        await prisma.user.update({
          where: { id: userId },
          data: {
            emailVerificationToken: 'token123',
            emailVerificationTokenExpires: new Date()
          }
        });

        // Act
        await authRepository.markEmailAsVerified(userId);

        // Assert
        const user = await prisma.user.findUnique({ where: { id: userId } });
        expect(user?.isVerified).toBe(true);
        expect(user?.emailVerified).not.toBeNull();
        expect(user?.emailVerificationToken).toBeNull();
        expect(user?.emailVerificationTokenExpires).toBeNull();
      });
    });

    describe('clearEmailVerificationToken', () => {
      it('should clear email verification token', async () => {
        // Arrange
        await prisma.user.update({
          where: { id: userId },
          data: {
            emailVerificationToken: 'token123',
            emailVerificationTokenExpires: new Date()
          }
        });

        // Act
        await authRepository.clearEmailVerificationToken(userId);

        // Assert
        const user = await prisma.user.findUnique({ where: { id: userId } });
        expect(user?.emailVerificationToken).toBeNull();
        expect(user?.emailVerificationTokenExpires).toBeNull();
      });
    });
  });

  describe('Session Management', () => {
    let userId: string;

    beforeEach(async () => {
      const user = await prisma.user.create({ data: mockUser });
      userId = user.id;
    });

    describe('createSession', () => {
      it('should create a new session', async () => {
        // Arrange
        const sessionData = {
          id: 'session123',
          userId: userId,
          refreshToken: 'refreshToken123',
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0 Test Browser'
        };

        // Act
        const result = await authRepository.createSession(sessionData);

        // Assert
        expect(result).toMatchObject({
          id: 'session123',
          userId: userId,
          refreshToken: 'refreshToken123',
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0 Test Browser',
          isActive: true
        });
      });

      it('should truncate long IP addresses and user agents', async () => {
        // Arrange
        const sessionData = {
          id: 'session123',
          userId: userId,
          refreshToken: 'refreshToken123',
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          ipAddress: '***********'.repeat(10), // Very long IP
          userAgent: 'Mozilla/5.0 Test Browser '.repeat(20) // Very long user agent
        };

        // Act
        const result = await authRepository.createSession(sessionData);

        // Assert
        expect(result.ipAddress.length).toBeLessThanOrEqual(45);
        expect(result.userAgent.length).toBeLessThanOrEqual(500);
      });
    });

    describe('findSessionByRefreshToken', () => {
      beforeEach(async () => {
        await prisma.user_sessions.create({
          data: {
            id: 'session123',
            userId: userId,
            refreshToken: 'refreshToken123',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            ipAddress: '***********',
            userAgent: 'Mozilla/5.0 Test Browser',
            isActive: true
          }
        });
      });

      it('should find session by refresh token', async () => {
        // Act
        const result = await authRepository.findSessionByRefreshToken('refreshToken123');

        // Assert
        expect(result).toMatchObject({
          id: 'session123',
          userId: userId,
          refreshToken: 'refreshToken123',
          isActive: true
        });
      });

      it('should return null for non-existent token', async () => {
        // Act
        const result = await authRepository.findSessionByRefreshToken('nonexistent');

        // Assert
        expect(result).toBeNull();
      });
    });

    describe('revokeSession', () => {
      beforeEach(async () => {
        await prisma.user_sessions.create({
          data: {
            id: 'session123',
            userId: userId,
            refreshToken: 'refreshToken123',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            ipAddress: '***********',
            userAgent: 'Mozilla/5.0 Test Browser',
            isActive: true
          }
        });
      });

      it('should revoke session by refresh token', async () => {
        // Act
        await authRepository.revokeSession('refreshToken123');

        // Assert
        const session = await prisma.user_sessions.findUnique({
          where: { refreshToken: 'refreshToken123' }
        });
        expect(session?.isActive).toBe(false);
        expect(session?.revokedAt).not.toBeNull();
      });
    });

    describe('revokeAllUserSessions', () => {
      beforeEach(async () => {
        // Create multiple sessions for the user
        await prisma.user_sessions.createMany({
          data: [
            {
              id: 'session1',
              userId: userId,
              refreshToken: 'token1',
              expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
              ipAddress: '***********',
              userAgent: 'Browser 1',
              isActive: true
            },
            {
              id: 'session2',
              userId: userId,
              refreshToken: 'token2',
              expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
              ipAddress: '***********',
              userAgent: 'Browser 2',
              isActive: true
            }
          ]
        });
      });

      it('should revoke all user sessions', async () => {
        // Act
        await authRepository.revokeAllUserSessions(userId);

        // Assert
        const sessions = await prisma.user_sessions.findMany({
          where: { userId: userId }
        });
        expect(sessions).toHaveLength(2);
        expect(sessions.every(s => !s.isActive)).toBe(true);
        expect(sessions.every(s => s.revokedAt !== null)).toBe(true);
      });
    });

    describe('getUserSessions', () => {
      beforeEach(async () => {
        // Create active and inactive sessions
        await prisma.user_sessions.createMany({
          data: [
            {
              id: 'active1',
              userId: userId,
              refreshToken: 'activeToken1',
              expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
              ipAddress: '***********',
              userAgent: 'Active Browser 1',
              isActive: true
            },
            {
              id: 'active2',
              userId: userId,
              refreshToken: 'activeToken2',
              expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
              ipAddress: '***********',
              userAgent: 'Active Browser 2',
              isActive: true
            },
            {
              id: 'inactive',
              userId: userId,
              refreshToken: 'inactiveToken',
              expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
              ipAddress: '***********',
              userAgent: 'Inactive Browser',
              isActive: false
            },
            {
              id: 'expired',
              userId: userId,
              refreshToken: 'expiredToken',
              expiresAt: new Date(Date.now() - 1000), // Expired
              ipAddress: '***********',
              userAgent: 'Expired Browser',
              isActive: true
            }
          ]
        });
      });

      it('should return only active, non-expired sessions', async () => {
        // Act
        const result = await authRepository.getUserSessions(userId);

        // Assert
        expect(result).toHaveLength(2);
        expect(result.every(s => s.isActive)).toBe(true);
        expect(result.map(s => s.id)).toEqual(expect.arrayContaining(['active1', 'active2']));
      });

      it('should order sessions by creation date (newest first)', async () => {
        // Act
        const result = await authRepository.getUserSessions(userId);

        // Assert
        expect(result).toHaveLength(2);
        // Check that results are ordered by createdAt desc
        expect(result[0].createdAt.getTime()).toBeGreaterThanOrEqual(result[1].createdAt.getTime());
      });
    });

    describe('revokeSpecificSession', () => {
      beforeEach(async () => {
        await prisma.user_sessions.create({
          data: {
            id: 'session123',
            userId: userId,
            refreshToken: 'refreshToken123',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            ipAddress: '***********',
            userAgent: 'Mozilla/5.0 Test Browser',
            isActive: true
          }
        });
      });

      it('should revoke specific session by ID', async () => {
        // Act
        await authRepository.revokeSpecificSession('session123', userId);

        // Assert
        const session = await prisma.user_sessions.findUnique({
          where: { id: 'session123' }
        });
        expect(session?.isActive).toBe(false);
        expect(session?.revokedAt).not.toBeNull();
      });

      it('should not affect other user sessions', async () => {
        // Arrange
        await prisma.user_sessions.create({
          data: {
            id: 'otherSession',
            userId: userId,
            refreshToken: 'otherToken',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            ipAddress: '***********',
            userAgent: 'Other Browser',
            isActive: true
          }
        });

        // Act
        await authRepository.revokeSpecificSession('session123', userId);

        // Assert
        const targetSession = await prisma.user_sessions.findUnique({
          where: { id: 'session123' }
        });
        const otherSession = await prisma.user_sessions.findUnique({
          where: { id: 'otherSession' }
        });
        
        expect(targetSession?.isActive).toBe(false);
        expect(otherSession?.isActive).toBe(true);
      });

      it('should only revoke sessions for the specified user', async () => {
        // Arrange
        const otherUser = await prisma.user.create({
          data: {
            ...mockUser,
            id: 'otherUser',
            email: '<EMAIL>'
          }
        });
        await prisma.user_sessions.create({
          data: {
            id: 'otherUserSession',
            userId: otherUser.id,
            refreshToken: 'otherUserToken',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            ipAddress: '***********',
            userAgent: 'Other User Browser',
            isActive: true
          }
        });

        // Act
        await authRepository.revokeSpecificSession('session123', userId);

        // Assert
        const userSession = await prisma.user_sessions.findUnique({
          where: { id: 'session123' }
        });
        const otherUserSession = await prisma.user_sessions.findUnique({
          where: { id: 'otherUserSession' }
        });
        
        expect(userSession?.isActive).toBe(false);
        expect(otherUserSession?.isActive).toBe(true);
      });
    });

    describe('cleanupExpiredSessions', () => {
      beforeEach(async () => {
        // Create expired and active sessions
        await prisma.user_sessions.createMany({
          data: [
            {
              id: 'expired1',
              userId: userId,
              refreshToken: 'expiredToken1',
              expiresAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
              ipAddress: '***********',
              userAgent: 'Expired Browser 1',
              isActive: true
            },
            {
              id: 'expired2',
              userId: userId,
              refreshToken: 'expiredToken2',
              expiresAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
              ipAddress: '***********',
              userAgent: 'Expired Browser 2',
              isActive: true
            },
            {
              id: 'active1',
              userId: userId,
              refreshToken: 'activeToken1',
              expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
              ipAddress: '***********',
              userAgent: 'Active Browser 1',
              isActive: true
            },
            {
              id: 'alreadyRevoked',
              userId: userId,
              refreshToken: 'revokedToken',
              expiresAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
              ipAddress: '***********',
              userAgent: 'Revoked Browser',
              isActive: false,
              revokedAt: new Date()
            }
          ]
        });
      });

      it('should deactivate expired sessions', async () => {
        // Act
        const cleanedCount = await authRepository.cleanupExpiredSessions();

        // Assert
        expect(cleanedCount).toBe(2); // Should clean 2 expired active sessions

        const sessions = await prisma.user_sessions.findMany({
          where: { userId: userId }
        });

        const expiredSessions = sessions.filter(s => s.id.startsWith('expired'));
        const activeSessions = sessions.filter(s => s.id.startsWith('active'));
        const revokedSessions = sessions.filter(s => s.id === 'alreadyRevoked');

        // Expired sessions should be deactivated
        expect(expiredSessions.every(s => !s.isActive)).toBe(true);
        expect(expiredSessions.every(s => s.revokedAt !== null)).toBe(true);

        // Active sessions should remain active
        expect(activeSessions.every(s => s.isActive)).toBe(true);

        // Already revoked sessions should remain revoked
        expect(revokedSessions[0].isActive).toBe(false);
      });

      it('should return correct count of cleaned sessions', async () => {
        // Act
        const cleanedCount = await authRepository.cleanupExpiredSessions();

        // Assert
        expect(cleanedCount).toBe(2);
      });

      it('should handle case with no expired sessions', async () => {
        // Arrange - Clean up all sessions first
        await prisma.user_sessions.deleteMany({ where: { userId: userId } });
        
        // Create only active sessions
        await prisma.user_sessions.create({
          data: {
            id: 'activeOnly',
            userId: userId,
            refreshToken: 'activeOnlyToken',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            ipAddress: '***********',
            userAgent: 'Active Browser',
            isActive: true
          }
        });

        // Act
        const cleanedCount = await authRepository.cleanupExpiredSessions();

        // Assert
        expect(cleanedCount).toBe(0);
        
        const session = await prisma.user_sessions.findUnique({
          where: { id: 'activeOnly' }
        });
        expect(session?.isActive).toBe(true);
      });
    });
  });
});