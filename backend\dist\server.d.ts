declare class DatabaseManager {
    private static instance;
    private isConnected;
    private constructor();
    static getInstance(): DatabaseManager;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    isConnectedToDatabase(): boolean;
}
declare class ServerManager {
    private server;
    private dbManager;
    constructor();
    start(): Promise<void>;
    stop(): Promise<void>;
    private logStartupInfo;
    private setupGracefulShutdown;
}
declare function startServer(): Promise<void>;
export { ServerManager, DatabaseManager, startServer };
//# sourceMappingURL=server.d.ts.map