declare class ServiceManager {
    private static instance;
    private isInitialized;
    private constructor();
    static getInstance(): ServiceManager;
    initialize(): Promise<void>;
    disconnect(): Promise<void>;
    getInitializedStatus(): boolean;
    healthCheck(): Promise<{
        database: boolean;
        redis: boolean;
    }>;
}
declare class ServerManager {
    private server;
    private serviceManager;
    constructor();
    start(): Promise<void>;
    stop(): Promise<void>;
    private logStartupInfo;
    private setupGracefulShutdown;
}
declare function startServer(): Promise<void>;
export { ServerManager, ServiceManager, startServer };
//# sourceMappingURL=server.d.ts.map