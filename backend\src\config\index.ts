// Re-export everything from the main configuration file
export {
	ConfigurationManager,
	AppConfig,
	DatabaseConfig,
	JWTConfig,
	ServerConfig,
	RedisConfig,
	AWSConfig,
	EmailConfig,
	LoggingConfig,
	SecurityConfig,
	GoogleOAuthConfig,
} from './config';

// Create and export singleton instance
import {ConfigurationManager} from './config';
export const config = ConfigurationManager.getInstance();

// Export configuration getters for backward compatibility
export const getConfig = () => config.getConfig();
export const getDatabaseConfig = () => config.getDatabaseConfig();
export const getJWTConfig = () => config.getJWTConfig();
export const getServerConfig = () => config.getServerConfig();
export const getRedisConfig = () => config.getRedisConfig();
export const getAWSConfig = () => config.getAWSConfig();
export const getEmailConfig = () => config.getEmailConfig();
export const getLoggingConfig = () => config.getLoggingConfig();
export const getSecurityConfig = () => config.getSecurityConfig();
export const getGoogleOAuthConfig = () => config.getGoogleOAuthConfig();
