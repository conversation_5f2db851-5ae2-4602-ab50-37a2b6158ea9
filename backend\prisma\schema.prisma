generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id             String           @id @default(cuid())
  email          String           @unique @db.VarChar(255)
  password       String?          @db.Var<PERSON>har(255)
  firstName      String           @db.VarChar(150)
  lastName       String           @db.VarChar(150)
  role           UserRole         @default(CUSTOMER)
  isActive       Boolean          @default(true)
  isVerified     Boolean          @default(false)
  emailVerified  DateTime?
  emailVerificationToken String? @db.VarChar(255)
  emailVerificationTokenExpires DateTime?
  passwordResetToken String? @db.VarChar(255)
  passwordResetTokenExpires DateTime?
  lastLoginAt    DateTime?
  loginAttempts  Int              @default(0)
  lockedUntil    DateTime?
  avatar         String?          @db.VarChar(500)
  phone          String?          @db.VarChar(20)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  admins         admins?
  orders         Order[]
  provider       Provider?
  user_addresses user_addresses[]
  user_sessions  user_sessions[]

  @@index([createdAt])
  @@index([email])
  @@index([isActive])
  @@index([role])
  @@map("users")
}

model Provider {
  id                       String                     @id @default(cuid())
  userId                   String                     @unique
  businessName             String                     @db.VarChar(255)
  description              String?
  email                    String?                    @db.VarChar(255)
  website                  String?                    @db.VarChar(500)
  phone                    String?                    @db.VarChar(20)
  isVerified               Boolean                    @default(false)
  isActive                 Boolean                    @default(true)
  createdAt                DateTime                   @default(now())
  updatedAt                DateTime                   @updatedAt
  orders                   Order[]
  provider_operating_hours provider_operating_hours[]
  provider_rating_displays provider_rating_displays?
  provider_service_areas   provider_service_areas[]
  services                 ProviderService[]
  user                     User                       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([businessName])
  @@index([isActive])
  @@index([isVerified])
  @@map("providers")
}

model ProviderService {
  id          String   @id @default(cuid())
  providerId  String
  serviceId   String
  price       Decimal  @db.Decimal(10, 2)
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  provider    Provider @relation(fields: [providerId], references: [id], onDelete: Cascade)
  service     Service  @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  @@unique([providerId, serviceId])
  @@index([isActive])
  @@index([providerId])
  @@index([serviceId])
  @@map("provider_services")
}

model Service {
  id                 String             @id @default(cuid())
  name               String             @db.VarChar(255)
  description        String
  detailedDesc       String?
  features           String[]           @default([])
  notes              String?
  categoryId         String
  image              String             @db.VarChar(500)
  basePrice          Decimal            @db.Decimal(10, 2)
  pricingType        ServicePricingType @default(FIXED)
  isActive           Boolean            @default(true)
  sortOrder          Int                @default(0)
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @updatedAt
  orders             Order[]
  providers          ProviderService[]
  formFields         ServiceFormField[]
  service_categories service_categories @relation(fields: [categoryId], references: [id])

  @@index([categoryId])
  @@index([isActive])
  @@index([pricingType])
  @@index([sortOrder])
  @@map("services")
}

model ServiceFormField {
  id                    String                  @id @default(cuid())
  serviceId             String
  name                  String                  @db.VarChar(100)
  label                 String                  @db.VarChar(255)
  type                  FormFieldType
  required              Boolean                 @default(false)
  placeholder           String?                 @db.VarChar(255)
  defaultValue          String?
  validation            Json?
  sortOrder             Int                     @default(0)
  isActive              Boolean                 @default(true)
  createdAt             DateTime                @default(now())
  service_field_options service_field_options[]
  service               Service                 @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  @@index([isActive])
  @@index([serviceId])
  @@index([sortOrder])
  @@map("service_form_fields")
}

model Order {
  id            String               @id @default(cuid())
  customerId    String
  providerId    String?
  serviceId     String
  status        OrderStatus          @default(PENDING)
  totalAmount   Decimal              @db.Decimal(10, 2)
  description   String?
  requirements  Json?
  dueDate       DateTime?
  completedAt   DateTime?
  createdAt     DateTime             @default(now())
  updatedAt     DateTime             @updatedAt
  files         OrderFile[]
  items         OrderItem[]
  statusHistory OrderStatusHistory[]
  customer      User                 @relation(fields: [customerId], references: [id])
  provider      Provider?            @relation(fields: [providerId], references: [id])
  services      Service              @relation(fields: [serviceId], references: [id])

  @@index([customerId, status])
  @@index([createdAt])
  @@index([customerId])
  @@index([providerId])
  @@index([serviceId])
  @@index([status])
  @@map("orders")
}

model OrderItem {
  id          String   @id @default(cuid())
  orderId     String
  name        String   @db.VarChar(255)
  description String?
  quantity    Int      @default(1)
  price       Decimal  @db.Decimal(10, 2)
  createdAt   DateTime @default(now())
  order       Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@map("order_items")
}

model OrderFile {
  id           String   @id @default(cuid())
  orderId      String
  filename     String   @db.VarChar(255)
  originalName String   @db.VarChar(255)
  mimeType     String   @db.VarChar(127)
  size         Int
  url          String   @db.VarChar(500)
  type         FileType
  isPublic     Boolean  @default(false)
  virusScanned Boolean  @default(false)
  accessToken  String?  @db.VarChar(255)
  uploadedBy   String?
  createdAt    DateTime @default(now())
  order        Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@index([type])
  @@index([uploadedBy])
  @@index([virusScanned])
  @@map("order_files")
}

model OrderStatusHistory {
  id        String      @id @default(cuid())
  orderId   String
  status    OrderStatus
  comment   String?
  changedBy String?
  createdAt DateTime    @default(now())
  order     Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([createdAt])
  @@index([orderId])
  @@index([status])
  @@map("order_status_history")
}

model admins {
  id          String    @id
  userId      String    @unique
  role        AdminRole @default(ADMIN)
  permissions Json?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime
  users       User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([isActive])
  @@index([role])
}

model gallery_images {
  id           String   @id
  filename     String   @db.VarChar(255)
  originalName String   @db.VarChar(255)
  mimeType     String   @db.VarChar(127)
  size         Int
  url          String   @db.VarChar(500)
  title        String?  @db.VarChar(255)
  description  String?
  category     String?  @db.VarChar(100)
  tags         String[]
  isActive     Boolean  @default(true)
  sortOrder    Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime

  @@index([category])
  @@index([isActive])
  @@index([sortOrder])
}

model provider_operating_hours {
  id         String   @id
  providerId String
  dayOfWeek  Int      @db.SmallInt
  openTime   String   @db.VarChar(5)
  closeTime  String   @db.VarChar(5)
  createdAt  DateTime @default(now())
  providers  Provider @relation(fields: [providerId], references: [id], onDelete: Cascade)

  @@unique([providerId, dayOfWeek])
  @@index([providerId])
}

model provider_rating_displays {
  id            String   @id
  providerId    String   @unique
  averageRating Float    @default(0)
  reviewCount   Int      @default(0)
  lastUpdated   DateTime @default(now())
  createdAt     DateTime @default(now())
  updatedAt     DateTime
  providers     Provider @relation(fields: [providerId], references: [id], onDelete: Cascade)

  @@index([averageRating])
  @@index([reviewCount])
}

model provider_service_areas {
  id            String   @id
  providerId    String
  streetAddress String   @db.VarChar(255)
  zipCode       String   @db.VarChar(20)
  city          String   @db.VarChar(100)
  state         String   @db.VarChar(50)
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  providers     Provider @relation(fields: [providerId], references: [id], onDelete: Cascade)

  @@index([city, state])
  @@index([providerId])
  @@index([zipCode])
}

model service_categories {
  id          String    @id
  name        String    @unique @db.VarChar(100)
  description String?
  icon        String?   @db.VarChar(100)
  route       String    @unique @db.VarChar(100)
  isActive    Boolean   @default(true)
  sortOrder   Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime
  services    Service[]

  @@index([isActive])
  @@index([sortOrder])
}

model service_field_options {
  id                  String           @id
  fieldId             String
  value               String           @db.VarChar(100)
  label               String           @db.VarChar(255)
  priceModifier       Decimal?         @db.Decimal(10, 2)
  sortOrder           Int              @default(0)
  isActive            Boolean          @default(true)
  createdAt           DateTime         @default(now())
  service_form_fields ServiceFormField @relation(fields: [fieldId], references: [id], onDelete: Cascade)

  @@index([fieldId])
  @@index([isActive])
}

model user_addresses {
  id            String      @id
  userId        String
  type          AddressType
  streetAddress String      @db.VarChar(255)
  city          String      @db.VarChar(100)
  state         String      @db.VarChar(50)
  zipCode       String      @db.VarChar(20)
  country       String      @default("US") @db.VarChar(3)
  isDefault     Boolean     @default(false)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime
  users         User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([zipCode])
}

model user_sessions {
  id           String    @id
  userId       String
  refreshToken String    @unique @db.VarChar(500)
  expiresAt    DateTime
  ipAddress    String    @db.VarChar(45)
  userAgent    String    @db.VarChar(500)
  isActive     Boolean   @default(true)
  revokedAt    DateTime?
  createdAt    DateTime  @default(now())
  users        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([expiresAt])
  @@index([isActive])
  @@index([userId])
}

enum FormFieldType {
  TEXT
  NUMBER
  SELECT
  CHECKBOX
  RADIO
  FILE
}

enum OrderStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  REVIEW
  COMPLETED
  CANCELLED
  REFUNDED
}

enum FileType {
  DESIGN
  REFERENCE
  PROOF
  FINAL
  OTHER
}

enum AddressType {
  BILLING
  SHIPPING
  BOTH
}

enum AdminRole {
  ADMIN
  SUPER_ADMIN
}

enum ServicePricingType {
  FIXED
  VARIABLE
  QUOTE
}

enum UserRole {
  CUSTOMER
  PROVIDER
  ADMIN
}
