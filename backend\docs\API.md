# PrintWeditt Backend API Documentation

## Table of Contents

- [Overview](#overview)
- [Unified Response Format](#unified-response-format)
- [Authentication](#authentication)
- [Core Services API](#core-services-api)
- [User Management](#user-management)
- [Error Handling](#error-handling)
- [Security Features](#security-features)
- [Request Tracking](#request-tracking)

## Overview

The PrintWeditt backend API provides comprehensive user management and authentication services built with Node.js, Express, TypeScript, and Prisma. The API follows REST conventions and includes robust security features with a unified, modern response format.

**Base URL**: `http://localhost:3001/api`

**Content Type**: `application/json`

**API Version**: `1.0.0`

## Unified Response Format

All API endpoints follow a consistent, modern response format that includes metadata, success status, structured error handling, and HATEOAS links for better API discoverability.

### Success Response Structure

All successful API responses follow this format:

```json
{
  "success": true,
  "data": {
    // Response data specific to the endpoint
  },
  "message": "Optional success message",
  "pagination": {
    // Only included for paginated responses
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false,
    "nextPage": 2,
    "prevPage": null
  },
  "links": [
    // HATEOAS links for API discoverability
    {
      "href": "/api/users",
      "rel": "collection",
      "method": "GET",
      "title": "Get all users"
    },
    {
      "href": "/api/users/123",
      "rel": "self",
      "method": "GET",
      "title": "Get user by ID"
    }
  ],
  "warnings": [
    // Optional warnings about the response
    "Some data was filtered due to permissions"
  ],
  "meta": {
    "timestamp": "2025-01-15T10:30:00.000Z",
    "requestId": "req_abc123",
    "correlationId": "corr_def456",
    "version": "1.0.0",
    "duration": 145,
    "path": "/api/users/123",
    "method": "GET",
    "endpoint": "/users/:id"
  }
}
```

### Error Response Structure

All error responses follow this format:

```json
{
  "success": false,
  "error": {
    "type": "ValidationError",
    "message": "Human-readable error message",
    "code": "VALIDATION_FAILED",
    "details": [
      {
        "field": "email",
        "code": "VALIDATION_ERROR",
        "message": "Email is required",
        "value": null,
        "suggestion": "Please provide a valid email address"
      }
    ],
    "retryAfter": 60,
    "helpUrl": "https://docs.printweditt.com/errors/validation"
  },
  "meta": {
    "timestamp": "2025-01-15T10:30:00.000Z",
    "requestId": "req_error_abc123",
    "correlationId": "corr_def456",
    "version": "1.0.0",
    "duration": 12,
    "path": "/api/users",
    "method": "POST",
    "endpoint": "/users"
  }
}
```

### Response Metadata

Every response includes comprehensive metadata with:

- **timestamp**: ISO 8601 timestamp of the response
- **requestId**: Unique identifier for request tracing
- **correlationId**: Correlation ID for distributed tracing
- **version**: API version
- **duration**: Request processing time in milliseconds
- **path**: The requested API path
- **method**: HTTP method used
- **endpoint**: The route pattern matched

### HATEOAS Links

The API includes HATEOAS (Hypermedia as the Engine of Application State) links for better API discoverability:

- **collection**: Link to the collection resource
- **self**: Link to the current resource
- **create**: Link to create a new resource
- **update**: Link to update the current resource
- **delete**: Link to delete the current resource
- **first**: Link to the first page (for paginated responses)
- **prev**: Link to the previous page
- **next**: Link to the next page
- **last**: Link to the last page

### Error Types

Standardized error types include:

- `ValidationError`: Input validation failed (400, 422)
- `AuthenticationError`: Authentication failed (401)
- `AuthorizationError`: Access denied (403)
- `NotFoundError`: Resource not found (404)
- `ConflictError`: Resource already exists (409)
- `RateLimitError`: Rate limit exceeded (429)
- `InternalServerError`: Server error (500)
- `ExternalServiceError`: External service error (502)
- `ServiceUnavailableError`: Service unavailable (503)

---

## Request Tracking

### Correlation IDs

Every request automatically receives a correlation ID for distributed tracing:

```bash
# Request with custom correlation ID
curl -H "X-Correlation-ID: my-custom-id" \
     -H "Authorization: Bearer token" \
     https://api.printweditt.com/users

# Response headers include correlation ID
X-Correlation-ID: my-custom-id
X-Request-ID: req_abc123
```

### Request Context

The API provides rich request context for debugging:

```json
{
  "meta": {
    "correlationId": "corr_def456",
    "requestId": "req_abc123",
    "duration": 145,
    "path": "/api/users/123",
    "method": "GET",
    "endpoint": "/users/:id"
  }
}
```

---

## Authentication

### Register User

Create a new user account.

**Endpoint**: `POST /auth/register`

**Rate Limit**: 5 requests per 15 minutes

**Request Body**:

```json
{
  "email": "<EMAIL>",
  "password": "StrongPassword123!",
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+**********"
}
```

**Response** (201 Created):

```json
{
  "success": true,
  "message": "Account created successfully",
  "data": {
    "user": {
      "id": "cuid123...",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "phone": "+**********",
      "role": "CUSTOMER",
      "isActive": true,
      "isVerified": false,
      "avatar": null,
      "createdAt": "2024-01-15T12:00:00.000Z",
      "updatedAt": "2024-01-15T12:00:00.000Z",
      "lastLoginAt": null
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
  },
  "links": [
    {
      "href": "/api/auth/profile",
      "rel": "self",
      "method": "GET",
      "title": "Get user profile"
    },
    {
      "href": "/api/auth/profile",
      "rel": "update",
      "method": "PUT",
      "title": "Update user profile"
    }
  ],
  "meta": {
    "timestamp": "2025-01-15T12:00:00.000Z",
    "requestId": "req_register_abc123",
    "correlationId": "corr_def456",
    "version": "1.0.0",
    "duration": 234,
    "path": "/api/auth/register",
    "method": "POST",
    "endpoint": "/auth/register"
  }
}
```

**Validation Requirements**:

- Email: Valid email format, max 255 characters
- Password: Min 8 characters, must contain uppercase, lowercase, number, and special character
- First/Last Name: Min 1 character, max 150 characters, letters/spaces/hyphens/apostrophes only
- Phone (optional): Valid international format

---

### Login User

Authenticate user and receive access tokens.

**Endpoint**: `POST /auth/login`

**Rate Limit**: 5 requests per 15 minutes

**Request Body**:

```json
{
  "email": "<EMAIL>",
  "password": "StrongPassword123!"
}
```

**Response** (200 OK):

```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "cuid123...",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "phone": "+**********",
      "role": "CUSTOMER",
      "isActive": true,
      "isVerified": true,
      "avatar": null,
      "createdAt": "2024-01-15T12:00:00.000Z",
      "updatedAt": "2024-01-15T12:00:00.000Z",
      "lastLoginAt": "2024-01-15T12:30:00.000Z"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
  },
  "links": [
    {
      "href": "/api/auth/profile",
      "rel": "self",
      "method": "GET",
      "title": "Get user profile"
    },
    {
      "href": "/api/auth/sessions",
      "rel": "sessions",
      "method": "GET",
      "title": "Get user sessions"
    }
  ],
  "meta": {
    "timestamp": "2025-01-15T12:30:00.000Z",
    "requestId": "req_login_def456",
    "correlationId": "corr_ghi789",
    "version": "1.0.0",
    "duration": 156,
    "path": "/api/auth/login",
    "method": "POST",
    "endpoint": "/auth/login"
  }
}
```

**Error Responses**:

- `401 Unauthorized`: Invalid credentials
- `423 Locked`: Account temporarily locked due to too many failed attempts

---

### Refresh Tokens

Refresh access token using refresh token.

**Endpoint**: `POST /auth/refresh`

**Request Body**:

```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response** (200 OK):

```json
{
  "success": true,
  "data": {
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
  },
  "message": "Tokens refreshed successfully",
  "meta": {
    "timestamp": "2025-01-15T12:45:00.000Z",
    "requestId": "req_refresh_ghi789",
    "correlationId": "corr_jkl012",
    "version": "1.0.0",
    "duration": 89,
    "path": "/api/auth/refresh",
    "method": "POST",
    "endpoint": "/auth/refresh"
  }
}
```

---

### Logout User

Invalidate refresh token and logout from current device.

**Endpoint**: `POST /auth/logout`

**Headers**: `Authorization: Bearer <access_token>`

**Request Body**:

```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response** (200 OK):

```json
{
  "success": true,
  "data": null,
  "message": "Logged out successfully",
  "meta": {
    "timestamp": "2025-01-15T13:00:00.000Z",
    "requestId": "req_logout_jkl012",
    "correlationId": "corr_mno345",
    "version": "1.0.0",
    "duration": 67,
    "path": "/api/auth/logout",
    "method": "POST",
    "endpoint": "/auth/logout"
  }
}
```

---

### Logout All Devices

Invalidate all refresh tokens and logout from all devices.

**Endpoint**: `POST /auth/logout-all`

**Headers**: `Authorization: Bearer <access_token>`

**Response** (200 OK):

```json
{
  "success": true,
  "data": null,
  "message": "Logged out from all devices",
  "meta": {
    "timestamp": "2025-01-15T13:05:00.000Z",
    "requestId": "req_logout_all_mno345",
    "correlationId": "corr_pqr678",
    "version": "1.0.0",
    "duration": 123,
    "path": "/api/auth/logout-all",
    "method": "POST",
    "endpoint": "/auth/logout-all"
  }
}
```

---

## User Management

### Get User Profile

Retrieve current user profile information.

**Endpoint**: `GET /auth/profile`

**Headers**: `Authorization: Bearer <access_token>`

**Response** (200 OK):

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "cuid123...",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "phone": "+**********",
      "role": "CUSTOMER",
      "isActive": true,
      "isVerified": true,
      "avatar": "https://example.com/avatar.jpg",
      "createdAt": "2024-01-15T12:00:00.000Z",
      "updatedAt": "2024-01-15T12:00:00.000Z",
      "lastLoginAt": "2024-01-15T12:30:00.000Z"
    }
  },
  "links": [
    {
      "href": "/api/auth/profile",
      "rel": "self",
      "method": "GET",
      "title": "Get user profile"
    },
    {
      "href": "/api/auth/profile",
      "rel": "update",
      "method": "PUT",
      "title": "Update user profile"
    }
  ],
  "meta": {
    "timestamp": "2025-01-15T13:10:00.000Z",
    "requestId": "req_profile_pqr678",
    "correlationId": "corr_stu901",
    "version": "1.0.0",
    "duration": 45,
    "path": "/api/auth/profile",
    "method": "GET",
    "endpoint": "/auth/profile"
  }
}
```

---

### Update User Profile

Update user profile information.

**Endpoint**: `PUT /auth/profile`

**Headers**: `Authorization: Bearer <access_token>`

**Request Body** (all fields optional):

```json
{
  "firstName": "John",
  "lastName": "Smith",
  "phone": "+**********",
  "avatar": "https://example.com/new-avatar.jpg"
}
```

**Response** (200 OK):

```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "user": {
      "id": "cuid123...",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Smith",
      "phone": "+**********",
      "role": "CUSTOMER",
      "isActive": true,
      "isVerified": true,
      "avatar": "https://example.com/new-avatar.jpg",
      "createdAt": "2024-01-15T12:00:00.000Z",
      "updatedAt": "2024-01-15T13:00:00.000Z",
      "lastLoginAt": "2024-01-15T12:30:00.000Z"
    }
  },
  "links": [
    {
      "href": "/api/auth/profile",
      "rel": "self",
      "method": "GET",
      "title": "Get user profile"
    },
    {
      "href": "/api/auth/profile",
      "rel": "update",
      "method": "PUT",
      "title": "Update user profile"
    }
  ],
  "meta": {
    "timestamp": "2025-01-15T13:00:00.000Z",
    "requestId": "req_update_profile_stu901",
    "correlationId": "corr_vwx234",
    "version": "1.0.0",
    "duration": 78,
    "path": "/api/auth/profile",
    "method": "PUT",
    "endpoint": "/auth/profile"
  }
}
```

**Validation Requirements**:

- At least one field must be provided
- Avatar: Valid URL format, max 500 characters

---

### Change Password

Change user password (requires current password).

**Endpoint**: `PUT /auth/change-password`

**Headers**: `Authorization: Bearer <access_token>`

**Request Body**:

```json
{
  "currentPassword": "CurrentPassword123!",
  "newPassword": "NewPassword123!"
}
```

**Response** (200 OK):

```json
{
  "success": true,
  "message": "Password changed successfully. Please log in again.",
  "data": null,
  "meta": {
    "timestamp": "2025-01-15T13:15:00.000Z",
    "requestId": "req_change_password_vwx234",
    "correlationId": "corr_yz567",
    "version": "1.0.0",
    "duration": 134,
    "path": "/api/auth/change-password",
    "method": "PUT",
    "endpoint": "/auth/change-password"
  }
}
```

**Note**: All existing sessions are invalidated for security.

---

## Password Reset

### Request Password Reset

Request a password reset email.

**Endpoint**: `POST /auth/password-reset/request`

**Rate Limit**: 5 requests per 15 minutes

**Request Body**:

```json
{
  "email": "<EMAIL>"
}
```

**Response** (200 OK):

```json
{
  "success": true,
  "message": "If an account with that email exists, a password reset link has been sent",
  "data": null,
  "meta": {
    "timestamp": "2025-01-15T13:20:00.000Z",
    "requestId": "req_password_reset_yz567",
    "correlationId": "corr_abc890",
    "version": "1.0.0",
    "duration": 89,
    "path": "/api/auth/password-reset/request",
    "method": "POST",
    "endpoint": "/auth/password-reset/request"
  }
}
```

**Note**: Always returns success for security (doesn't reveal if email exists).

---

### Confirm Password Reset

Reset password using token from email.

**Endpoint**: `POST /auth/password-reset/confirm`

**Rate Limit**: 5 requests per 15 minutes

**Request Body**:

```json
{
  "token": "secure-reset-token-from-email",
  "newPassword": "NewPassword123!"
}
```

**Response** (200 OK):

```json
{
  "success": true,
  "message": "Password has been reset successfully. Please log in with your new password.",
  "data": null,
  "meta": {
    "timestamp": "2025-01-15T13:25:00.000Z",
    "requestId": "req_password_confirm_abc890",
    "correlationId": "corr_def123",
    "version": "1.0.0",
    "duration": 156,
    "path": "/api/auth/password-reset/confirm",
    "method": "POST",
    "endpoint": "/auth/password-reset/confirm"
  }
}
```

**Note**: All existing sessions are invalidated for security.

---

## Email Verification

### Send Email Verification

Send verification email to current user.

**Endpoint**: `POST /auth/email/verify/send`

**Headers**: `Authorization: Bearer <access_token>`

**Response** (200 OK):

```json
{
  "success": true,
  "message": "Verification email has been sent to your email address",
  "data": null,
  "meta": {
    "timestamp": "2025-01-15T13:30:00.000Z",
    "requestId": "req_email_send_def123",
    "correlationId": "corr_ghi456",
    "version": "1.0.0",
    "duration": 67,
    "path": "/api/auth/email/verify/send",
    "method": "POST",
    "endpoint": "/auth/email/verify/send"
  }
}
```

---

### Verify Email

Verify email using token from email.

**Endpoint**: `POST /auth/email/verify/confirm`

**Request Body**:

```json
{
  "token": "secure-verification-token-from-email"
}
```

**Response** (200 OK):

```json
{
  "success": true,
  "message": "Email has been verified successfully. Welcome to PrintWeditt!",
  "data": null,
  "meta": {
    "timestamp": "2025-01-15T13:35:00.000Z",
    "requestId": "req_email_confirm_ghi456",
    "correlationId": "corr_jkl789",
    "version": "1.0.0",
    "duration": 89,
    "path": "/api/auth/email/verify/confirm",
    "method": "POST",
    "endpoint": "/auth/email/verify/confirm"
  }
}
```

---

## Session Management

### Get User Sessions

Retrieve all active sessions for the current user.

**Endpoint**: `GET /auth/sessions`

**Headers**: `Authorization: Bearer <access_token>`

**Response** (200 OK):

```json
{
  "success": true,
  "data": {
    "sessions": [
      {
        "id": "session_id_1",
        "ipAddress": "*************",
        "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "createdAt": "2024-01-15T12:00:00.000Z",
        "expiresAt": "2024-01-22T12:00:00.000Z",
        "isActive": true,
        "isCurrent": true
      },
      {
        "id": "session_id_2",
        "ipAddress": "********",
        "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X)",
        "createdAt": "2024-01-14T10:00:00.000Z",
        "expiresAt": "2024-01-21T10:00:00.000Z",
        "isActive": true,
        "isCurrent": false
      }
    ]
  },
  "links": [
    {
      "href": "/api/auth/sessions",
      "rel": "self",
      "method": "GET",
      "title": "Get user sessions"
    }
  ],
  "meta": {
    "timestamp": "2025-01-15T13:40:00.000Z",
    "requestId": "req_sessions_jkl789",
    "correlationId": "corr_mno012",
    "version": "1.0.0",
    "duration": 45,
    "path": "/api/auth/sessions",
    "method": "GET",
    "endpoint": "/auth/sessions"
  }
}
```

---

### Revoke Specific Session

Revoke a specific session (logout from specific device).

**Endpoint**: `DELETE /auth/sessions/:sessionId`

**Headers**: `Authorization: Bearer <access_token>`

**Response** (200 OK):

```json
{
  "success": true,
  "message": "Session revoked successfully",
  "data": null,
  "meta": {
    "timestamp": "2025-01-15T13:45:00.000Z",
    "requestId": "req_revoke_session_mno012",
    "correlationId": "corr_pqr345",
    "version": "1.0.0",
    "duration": 67,
    "path": "/api/auth/sessions/session_id_1",
    "method": "DELETE",
    "endpoint": "/auth/sessions/:sessionId"
  }
}
```

---

## Error Handling

### Standard Error Response Format

```json
{
  "success": false,
  "error": {
    "type": "AuthenticationError",
    "message": "Invalid credentials",
    "code": "AUTH_FAILED",
    "retryAfter": null,
    "helpUrl": "https://docs.printweditt.com/errors/authentication"
  },
  "meta": {
    "timestamp": "2025-01-15T12:00:00.000Z",
    "requestId": "req_error_abc123",
    "correlationId": "corr_def456",
    "version": "1.0.0",
    "duration": 23,
    "path": "/api/auth/login",
    "method": "POST",
    "endpoint": "/auth/login"
  }
}
```

### Common HTTP Status Codes

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `204 No Content`: Request successful, no content to return
- `400 Bad Request`: Invalid request data or validation errors
- `401 Unauthorized`: Authentication required or invalid credentials
- `403 Forbidden`: Valid authentication but insufficient permissions
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource already exists (e.g., email already registered)
- `422 Unprocessable Entity`: Request data validation failed
- `423 Locked`: Account temporarily locked
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

### Validation Error Response

```json
{
  "success": false,
  "error": {
    "type": "ValidationError",
    "message": "Validation failed",
    "code": "VALIDATION_FAILED",
    "details": [
      {
        "field": "email",
        "code": "VALIDATION_ERROR",
        "message": "Please provide a valid email address",
        "value": "invalid-email",
        "suggestion": "Use format: <EMAIL>"
      },
      {
        "field": "password",
        "code": "VALIDATION_ERROR",
        "message": "Password must be at least 8 characters long",
        "suggestion": "Include uppercase, lowercase, number, and special character"
      }
    ]
  },
  "meta": {
    "timestamp": "2025-01-15T12:00:00.000Z",
    "requestId": "req_validation_def456",
    "correlationId": "corr_ghi789",
    "version": "1.0.0",
    "duration": 45,
    "path": "/api/auth/register",
    "method": "POST",
    "endpoint": "/auth/register"
  }
}
```

### Rate Limit Error Response

```json
{
  "success": false,
  "error": {
    "type": "RateLimitError",
    "message": "Too many requests",
    "code": "RATE_LIMIT_EXCEEDED",
    "retryAfter": 60
  },
  "meta": {
    "timestamp": "2025-01-15T12:00:00.000Z",
    "requestId": "req_rate_limit_ghi789",
    "correlationId": "corr_jkl012",
    "version": "1.0.0",
    "duration": 12,
    "path": "/api/auth/login",
    "method": "POST",
    "endpoint": "/auth/login"
  }
}
```

---

## Security Features

### Authentication & Authorization

- **JWT Tokens**: Secure access tokens with 15-minute expiry
- **Refresh Tokens**: Long-lived tokens (7 days) for token renewal
- **Role-Based Access Control**: CUSTOMER, PROVIDER, ADMIN roles
- **Session Management**: Track and manage user sessions across devices

### Security Measures

- **Password Hashing**: bcrypt with configurable rounds (default: 12)
- **Rate Limiting**: Prevent brute force attacks
  - Authentication endpoints: 5 requests per 15 minutes
  - General API: 100 requests per 15 minutes
- **Account Locking**: Temporary account lock after 5 failed login attempts (30 minutes)
- **Input Validation**: Comprehensive validation using Joi schemas
- **Data Sanitization**: Remove sensitive data from responses
- **CORS**: Configurable cross-origin resource sharing
- **Helmet**: Security headers and protection

### Email Security

- **Token-Based Verification**: Secure email verification with expiring tokens
- **Password Reset**: Secure password reset flow with time-limited tokens
- **No Information Disclosure**: Password reset doesn't reveal if email exists

### Logging & Monitoring

- **Comprehensive Logging**: All authentication events logged with context
- **Security Events**: Track login attempts, password changes, etc.
- **Request Tracking**: Unique request IDs and correlation IDs for tracing
- **Performance Monitoring**: Request duration tracking

### Data Protection

- **Password Fields**: Never included in API responses
- **User Data Sanitization**: Sensitive fields automatically removed
- **Session Security**: Refresh tokens are securely stored and managed

---

## Environment Configuration

Required environment variables:

```env
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/printweditt_db"

# JWT
JWT_SECRET="your-super-secure-jwt-secret-key-at-least-32-characters"
JWT_REFRESH_SECRET="your-super-secure-refresh-secret-key-at-least-32-characters"
JWT_ACCESS_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Server
PORT=3001
NODE_ENV="development"
API_BASE_URL="http://localhost:3001"
FRONTEND_URL="http://localhost:5173"
API_VERSION="1.0.0"

# Email (for password reset and verification)
EMAIL_SERVICE="gmail"
EMAIL_HOST="smtp.gmail.com"
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER="<EMAIL>"
EMAIL_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"

# Security
BCRYPT_ROUNDS=12
CORS_ORIGIN="http://localhost:5173"
COOKIE_SECRET="your-super-secure-cookie-secret-at-least-32-characters"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_AUTH_MAX=5
```

---

## Testing Endpoints

You can test the API endpoints using tools like:

- cURL
- Postman
- Insomnia
- HTTPie

Example cURL for registration:

```bash
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -H "X-Correlation-ID: test-correlation-123" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPassword123!",
    "firstName": "Test",
    "lastName": "User",
    "phone": "+**********"
  }'
```

Example cURL for authenticated request:

```bash
curl -X GET http://localhost:3001/api/auth/profile \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN_HERE" \
  -H "X-Correlation-ID: test-correlation-456"
```

Example cURL with pagination:

```bash
curl -X GET "http://localhost:3001/api/users?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN_HERE" \
  -H "X-Correlation-ID: test-correlation-789"
```

---

## Best Practices

### Request Headers

- Always include `X-Correlation-ID` for distributed tracing
- Use `Authorization: Bearer <token>` for authenticated requests
- Set `Content-Type: application/json` for POST/PUT requests

### Error Handling

- Check the `success` field first
- Use the `error.code` for programmatic error handling
- Display `error.message` to users
- Use `error.details` for field-specific validation errors
- Check `error.retryAfter` for rate limit errors

### Pagination

- Use `pagination.hasNext` and `pagination.hasPrev` for navigation
- Use `pagination.nextPage` and `pagination.prevPage` for direct links
- Include query parameters in pagination links

### HATEOAS Links

- Use `links` array for API discovery
- Follow the `rel` values for navigation
- Use `href` for direct API calls
- Check `method` for HTTP method to use

### Performance

- Monitor `meta.duration` for performance tracking
- Use `meta.requestId` for debugging specific requests
- Use `meta.correlationId` for distributed tracing

---

## Core Services API

Base path: `/services`

### List Services

GET `/services`

Query params:

- `page` (number, default 1)
- `limit` (number, default 20)
- `search` (string)
- `categoryId` (string)
- `pricingType` (`FIXED` | `VARIABLE` | `QUOTE`)
- `isActive` (boolean)
- `sortBy` (`name` | `basePrice` | `createdAt` | `updatedAt` | `sortOrder`)
- `sortOrder` (`asc` | `desc`)
- `minPrice` (number)
- `maxPrice` (number)

### Search Services

GET `/services/search`

Query params:

- `q` (string)
- `category` (category route string, e.g., `business-cards`)
- `minPrice`, `maxPrice` (number)
- `pricingType` (`FIXED` | `VARIABLE` | `QUOTE`)
- `features` (string[])
- `sortBy` (`name` | `price` | `popularity` | `rating`)
- `sortOrder` (`asc` | `desc`)

### Get Service By ID

GET `/services/:id`

Returns full service detail with `formFields`, `category`, and `providers`.

### Create/Update/Delete Service (Admin)

- POST `/services` (admin)
- PUT `/services/:id` (admin)
- DELETE `/services/:id` (admin)

### Service Utilities

- GET `/services/pricing/:pricingType`
- GET `/services/popular`
- GET `/services/stats` (admin)
- GET `/services/exists/:id` (admin)
- GET `/services/count` (admin)

### Categories

- GET `/services/categories`
- GET `/services/categories/:id`
- GET `/services/categories/:categoryId/services`
- GET `/services/categories/route/:route`
- GET `/services/categories/route/:route/services`
- POST `/services/categories` (admin)
- PUT `/services/categories/:id` (admin)
- DELETE `/services/categories/:id` (admin)

### Form Fields

- GET `/services/:serviceId/form-fields`
- POST `/services/:serviceId/form-fields` (admin)
- GET `/services/form-fields/:fieldId` (admin)
- PUT `/services/form-fields/:fieldId` (admin)
- DELETE `/services/form-fields/:fieldId` (admin)
- Aliases: PUT `/services/:serviceId/form-fields/:fieldId`, DELETE `/services/:serviceId/form-fields/:fieldId`

### Form Field Options

- GET `/services/form-fields/:fieldId/options`
- POST `/services/form-fields/:fieldId/options` (admin)
- PUT `/services/form-fields/options/:optionId` (admin)
- DELETE `/services/form-fields/options/:optionId` (admin)

### Price Calculation

- POST `/services/calculate-price`
- POST `/services/:id/calculate-price` (alias)
