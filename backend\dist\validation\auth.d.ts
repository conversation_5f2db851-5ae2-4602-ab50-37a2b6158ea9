import Joi from 'joi';
export declare const registerSchema: Joi.ObjectSchema<any>;
export declare const loginSchema: Joi.ObjectSchema<any>;
export declare const refreshTokenSchema: Joi.ObjectSchema<any>;
export declare const passwordResetRequestSchema: Joi.ObjectSchema<any>;
export declare const passwordResetConfirmSchema: Joi.ObjectSchema<any>;
export declare const emailVerificationSchema: Joi.ObjectSchema<any>;
export declare const changePasswordSchema: Joi.ObjectSchema<any>;
export declare const updateProfileSchema: Joi.ObjectSchema<any>;
export declare const paginationSchema: Joi.ObjectSchema<any>;
export declare const revokeSessionSchema: Joi.ObjectSchema<any>;
export declare const validate: (schema: Joi.ObjectSchema) => (req: any, res: any, next: any) => any;
export declare const validateQuery: (schema: Joi.ObjectSchema) => (req: any, res: any, next: any) => any;
declare const _default: {
    registerSchema: Joi.ObjectSchema<any>;
    loginSchema: Joi.ObjectSchema<any>;
    refreshTokenSchema: Joi.ObjectSchema<any>;
    passwordResetRequestSchema: Joi.ObjectSchema<any>;
    passwordResetConfirmSchema: Joi.ObjectSchema<any>;
    emailVerificationSchema: Joi.ObjectSchema<any>;
    changePasswordSchema: Joi.ObjectSchema<any>;
    updateProfileSchema: Joi.ObjectSchema<any>;
    paginationSchema: Joi.ObjectSchema<any>;
    revokeSessionSchema: Joi.ObjectSchema<any>;
    validate: (schema: Joi.ObjectSchema) => (req: any, res: any, next: any) => any;
    validateQuery: (schema: Joi.ObjectSchema) => (req: any, res: any, next: any) => any;
};
export default _default;
//# sourceMappingURL=auth.d.ts.map