import { IServiceRepository } from '@/repositories/CompositeServiceRepository';
import { ServiceService } from '@/services/ServiceService';
import { ServicePricingType } from '@prisma/client';

jest.mock('@/utils/logger', () => ({
  createLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  }),
}));

describe('ServiceService - Core Services API (Week 1-2)', () => {
  let repo: jest.Mocked<IServiceRepository>;
  let service: ServiceService;

  beforeEach(() => {
    repo = {
      findServiceById: jest.fn(),
      createService: jest.fn(),
      updateService: jest.fn(),
      deleteService: jest.fn(),
      getServices: jest.fn(),
      searchServices: jest.fn(),
      getServicesByCategory: jest.fn(),
      getServiceStats: jest.fn(),
      getPopularServices: jest.fn(),
      getServicesByPricingType: jest.fn(),
      getServiceCategories: jest.fn(),
      findServiceCategoryById: jest.fn(),
      findServiceCategoryByRoute: jest.fn(),
      createServiceCategory: jest.fn(),
      updateServiceCategory: jest.fn(),
      deleteServiceCategory: jest.fn(),
      getServiceFormFields: jest.fn(),
      findServiceFormFieldById: jest.fn(),
      createServiceFormField: jest.fn(),
      updateServiceFormField: jest.fn(),
      deleteServiceFormField: jest.fn(),
      getServiceFormFieldOptions: jest.fn(),
      findServiceFormFieldOptionById: jest.fn(),
      createServiceFormFieldOption: jest.fn(),
      updateServiceFormFieldOption: jest.fn(),
      deleteServiceFormFieldOption: jest.fn(),
      serviceExists: jest.fn(),
      serviceCategoryExists: jest.fn(),
      countServices: jest.fn(),
      countServicesByCategory: jest.fn(),
      calculateServicePrice: jest.fn(),
    } as any;

    service = new ServiceService(repo);
  });

  it('getServices - validates and returns list', async () => {
    const query: any = { page: 1, limit: 10 };
    (repo.getServices as jest.Mock).mockResolvedValue({
      services: [],
      pagination: { page: 1, limit: 10, total: 0, totalPages: 0, hasNext: false, hasPrev: false },
    });
    const result = await service.getServices(query);
    expect(repo.getServices).toHaveBeenCalledWith(query);
    expect(result.services).toEqual([]);
  });

  it('createService - rejects when category does not exist', async () => {
    (repo.serviceCategoryExists as jest.Mock).mockResolvedValue(false);
    await expect(
      service.createService({
        name: 'A',
        description: 'B',
        categoryId: 'cat-1',
        image: 'https://img',
        basePrice: 10,
        pricingType: ServicePricingType.FIXED,
      } as any)
    ).rejects.toThrow('Service category does not exist');
  });

  it('updateServiceFormFieldOption - uses find by id', async () => {
    (repo.findServiceFormFieldOptionById as jest.Mock).mockResolvedValue({ id: 'opt-1', fieldId: 'fld-1' } as any);
    await service.updateServiceFormFieldOption('opt-1', { label: 'New' } as any);
    expect(repo.findServiceFormFieldOptionById).toHaveBeenCalledWith('opt-1');
    expect(repo.updateServiceFormFieldOption).toHaveBeenCalled();
  });

  // Additional coverage to increase confidence in ServiceService behavior

  it('getServiceById - throws when not found', async () => {
    (repo.findServiceById as jest.Mock).mockResolvedValue(null);
    await expect(service.getServiceById('svc-1')).rejects.toThrow('Service not found');
  });

  it('getServiceById - returns detail when found', async () => {
    const detail = {
      id: 'svc-1',
      name: 'S',
      description: 'D',
      categoryId: 'cat-1',
      image: 'https://img',
      basePrice: 10,
      pricingType: ServicePricingType.FIXED,
      isActive: true,
      sortOrder: 0,
      features: [],
      formFields: [],
      category: {
        id: 'cat-1',
        name: 'Cat',
        description: null,
        icon: null,
        route: 'cat',
        isActive: true,
        sortOrder: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;
    (repo.findServiceById as jest.Mock).mockResolvedValue(detail);
    const result = await service.getServiceById('svc-1');
    expect(repo.findServiceById).toHaveBeenCalledWith('svc-1', true);
    expect(result.id).toBe('svc-1');
  });

  it('updateService - throws when service not found', async () => {
    (repo.findServiceById as jest.Mock).mockResolvedValue(null);
    await expect(service.updateService('svc-1', {} as any)).rejects.toThrow('Service not found');
  });

  it('updateService - throws when updating to non-existent category', async () => {
    (repo.findServiceById as jest.Mock).mockResolvedValue({ id: 'svc-1', categoryId: 'old-cat' } as any);
    (repo.serviceCategoryExists as jest.Mock).mockResolvedValue(false);
    await expect(service.updateService('svc-1', { categoryId: 'new-cat' } as any)).rejects.toThrow(
      'Service category does not exist'
    );
  });

  it('deleteService - throws when service not found', async () => {
    (repo.findServiceById as jest.Mock).mockResolvedValue(null);
    await expect(service.deleteService('svc-1')).rejects.toThrow('Service not found');
  });

  it('deleteService - returns boolean from repository', async () => {
    (repo.findServiceById as jest.Mock).mockResolvedValue({ id: 'svc-1' } as any);
    (repo.deleteService as jest.Mock).mockResolvedValue(true);
    await expect(service.deleteService('svc-1')).resolves.toBe(true);
    (repo.deleteService as jest.Mock).mockResolvedValue(false);
    await expect(service.deleteService('svc-1')).resolves.toBe(false);
  });

  it('getServicesByCategory - throws when category not found', async () => {
    (repo.serviceCategoryExists as jest.Mock).mockResolvedValue(false);
    await expect(service.getServicesByCategory('cat-1')).rejects.toThrow('Service category not found');
  });

  it('getServiceCategoryById - throws when not found', async () => {
    (repo.findServiceCategoryById as jest.Mock).mockResolvedValue(null);
    await expect(service.getServiceCategoryById('cat-1')).rejects.toThrow('Service category not found');
  });

  it('getServiceCategoryByRoute - throws when not found', async () => {
    (repo.findServiceCategoryByRoute as jest.Mock).mockResolvedValue(null);
    await expect(service.getServiceCategoryByRoute('route')).rejects.toThrow('Service category not found');
  });

  it('createServiceCategory - throws when route already exists', async () => {
    (repo.findServiceCategoryByRoute as jest.Mock).mockResolvedValue({ id: 'existing' } as any);
    await expect(service.createServiceCategory({ name: 'N', route: 'r' } as any)).rejects.toThrow(
      'Service category with this route already exists'
    );
  });

  it('createServiceCategory - returns created category detail', async () => {
    (repo.findServiceCategoryByRoute as jest.Mock).mockResolvedValue(null);
    (repo.createServiceCategory as jest.Mock).mockResolvedValue({} as any);
    (repo.findServiceCategoryById as jest.Mock).mockResolvedValue({ id: 'cat-1' } as any);
    const result = await service.createServiceCategory({ name: 'N', route: 'r' } as any);
    expect(result.id).toBe('cat-1');
  });

  it('deleteServiceCategory - throws when category has services', async () => {
    (repo.findServiceCategoryById as jest.Mock).mockResolvedValue({ id: 'cat-1' } as any);
    (repo.countServicesByCategory as jest.Mock).mockResolvedValue(2);
    await expect(service.deleteServiceCategory('cat-1')).rejects.toThrow(
      'Cannot delete service category that has associated services'
    );
  });

  it('calculateServicePrice - throws when service not found', async () => {
    (repo.serviceExists as jest.Mock).mockResolvedValue(false);
    await expect(service.calculateServicePrice({ serviceId: 'svc-1', formData: {} } as any)).rejects.toThrow(
      'Service not found'
    );
  });

  it('getServiceFormFieldById - throws when not found', async () => {
    (repo.findServiceFormFieldById as jest.Mock).mockResolvedValue(null);
    await expect(service.getServiceFormFieldById('fld-1')).rejects.toThrow('Service form field not found');
  });

  it('createServiceFormField - throws when service not found', async () => {
    (repo.serviceExists as jest.Mock).mockResolvedValue(false);
    await expect(
      service.createServiceFormField({ serviceId: 'svc-1', name: 'n', label: 'l', type: 'TEXT' } as any)
    ).rejects.toThrow('Service not found');
  });

  it('updateServiceFormField - throws when field not found', async () => {
    (repo.findServiceFormFieldById as jest.Mock).mockResolvedValue(null);
    await expect(service.updateServiceFormField('fld-1', {} as any)).rejects.toThrow('Service form field not found');
  });

  it('updateServiceFormFieldOption - throws when option not found', async () => {
    (repo.findServiceFormFieldOptionById as jest.Mock).mockResolvedValue(null);
    await expect(service.updateServiceFormFieldOption('opt-1', {} as any)).rejects.toThrow(
      'Service form field option not found'
    );
  });

  it('getServiceFormFieldOptions - throws when field not found', async () => {
    (repo.findServiceFormFieldById as jest.Mock).mockResolvedValue(null);
    await expect(service.getServiceFormFieldOptions('fld-1')).rejects.toThrow('Service form field not found');
  });

  it('createServiceFormFieldOption - throws when field not found', async () => {
    (repo.findServiceFormFieldById as jest.Mock).mockResolvedValue(null);
    await expect(
      service.createServiceFormFieldOption({ fieldId: 'fld-1', value: 'v', label: 'L' } as any)
    ).rejects.toThrow('Service form field not found');
  });
});
