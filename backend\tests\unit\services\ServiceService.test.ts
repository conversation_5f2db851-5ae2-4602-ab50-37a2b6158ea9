import { IServiceRepository } from '@/repositories/CompositeServiceRepository';
import { ServiceService } from '@/services/ServiceService';
import { ServicePricingType } from '@prisma/client';

jest.mock('@/utils/logger', () => ({
  createLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  }),
}));

describe('ServiceService - Core Services API (Week 1-2)', () => {
  let repo: jest.Mocked<IServiceRepository>;
  let service: ServiceService;

  beforeEach(() => {
    repo = {
      findServiceById: jest.fn(),
      createService: jest.fn(),
      updateService: jest.fn(),
      deleteService: jest.fn(),
      getServices: jest.fn(),
      searchServices: jest.fn(),
      getServicesByCategory: jest.fn(),
      getServiceStats: jest.fn(),
      getPopularServices: jest.fn(),
      getServicesByPricingType: jest.fn(),
      getServiceCategories: jest.fn(),
      findServiceCategoryById: jest.fn(),
      findServiceCategoryByRoute: jest.fn(),
      createServiceCategory: jest.fn(),
      updateServiceCategory: jest.fn(),
      deleteServiceCategory: jest.fn(),
      getServiceFormFields: jest.fn(),
      findServiceFormFieldById: jest.fn(),
      createServiceFormField: jest.fn(),
      updateServiceFormField: jest.fn(),
      deleteServiceFormField: jest.fn(),
      getServiceFormFieldOptions: jest.fn(),
      findServiceFormFieldOptionById: jest.fn(),
      createServiceFormFieldOption: jest.fn(),
      updateServiceFormFieldOption: jest.fn(),
      deleteServiceFormFieldOption: jest.fn(),
      serviceExists: jest.fn(),
      serviceCategoryExists: jest.fn(),
      countServices: jest.fn(),
      countServicesByCategory: jest.fn(),
      calculateServicePrice: jest.fn(),
    } as any;

    service = new ServiceService(repo);
  });

  it('getServices - validates and returns list', async () => {
    const query: any = { page: 1, limit: 10 };
    (repo.getServices as jest.Mock).mockResolvedValue({
      services: [],
      pagination: { page: 1, limit: 10, total: 0, totalPages: 0, hasNext: false, hasPrev: false },
    });
    const result = await service.getServices(query);
    expect(repo.getServices).toHaveBeenCalledWith(query);
    expect(result.services).toEqual([]);
  });

  it('createService - rejects when category does not exist', async () => {
    (repo.serviceCategoryExists as jest.Mock).mockResolvedValue(false);
    await expect(
      service.createService({
        name: 'A',
        description: 'B',
        categoryId: 'cat-1',
        image: 'https://img',
        basePrice: 10,
        pricingType: ServicePricingType.FIXED,
      } as any)
    ).rejects.toThrow('Service category does not exist');
  });

  it('updateServiceFormFieldOption - uses find by id', async () => {
    (repo.findServiceFormFieldOptionById as jest.Mock).mockResolvedValue({ id: 'opt-1', fieldId: 'fld-1' } as any);
    await service.updateServiceFormFieldOption('opt-1', { label: 'New' } as any);
    expect(repo.findServiceFormFieldOptionById).toHaveBeenCalledWith('opt-1');
    expect(repo.updateServiceFormFieldOption).toHaveBeenCalled();
  });
});
