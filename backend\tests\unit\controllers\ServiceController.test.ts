import { ServiceController } from '@/controllers/ServiceController';
import { IServiceService } from '@/services/ServiceService';
import { AuthenticatedRequest } from '@/types/auth';
import { Response } from 'express';

jest.mock('@/middleware/errorHandler', () => ({
  asyncHandler: (fn: any) => fn,
}));

jest.mock('@/utils/logger', () => ({
  createLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  }),
}));

jest.mock('@/utils/responseWrapper', () => ({
  createPaginationMeta: jest.fn().mockReturnValue({
    currentPage: 1,
    totalPages: 1,
    hasNext: false,
    hasPrev: false,
  }),
}));

describe('ServiceController', () => {
  let controller: ServiceController;
  let service: jest.Mocked<IServiceService>;
  let res: Partial<Response> & { success: any; created: any };
  let req: Partial<AuthenticatedRequest>;

  beforeEach(() => {
    service = {
      // CRUD
      getServiceById: jest.fn(),
      createService: jest.fn(),
      updateService: jest.fn(),
      deleteService: jest.fn(),
      // list/search
      getServices: jest.fn(),
      searchServices: jest.fn(),
      getServicesByCategory: jest.fn(),
      getPopularServices: jest.fn(),
      getServicesByPricingType: jest.fn(),
      // stats
      getServiceStats: jest.fn(),
      // categories
      getServiceCategories: jest.fn(),
      getServiceCategoryById: jest.fn(),
      getServiceCategoryByRoute: jest.fn(),
      createServiceCategory: jest.fn(),
      updateServiceCategory: jest.fn(),
      deleteServiceCategory: jest.fn(),
      // form fields
      getServiceFormFields: jest.fn(),
      getServiceFormFieldById: jest.fn(),
      createServiceFormField: jest.fn(),
      updateServiceFormField: jest.fn(),
      deleteServiceFormField: jest.fn(),
      // options
      getServiceFormFieldOptions: jest.fn(),
      createServiceFormFieldOption: jest.fn(),
      updateServiceFormFieldOption: jest.fn(),
      deleteServiceFormFieldOption: jest.fn(),
      // price
      calculateServicePrice: jest.fn(),
      // utils
      serviceExists: jest.fn(),
      serviceCategoryExists: jest.fn(),
      countServices: jest.fn(),
      countServicesByCategory: jest.fn(),
    } as any;

    res = {
      getHeader: jest.fn().mockReturnValue('request-id-123'),
      success: jest.fn(),
      created: jest.fn(),
    } as any;

    req = {
      userId: 'user-1',
      params: {},
      query: {},
      body: {},
    } as any;

    controller = new ServiceController(service);
    jest.clearAllMocks();
  });

  it('getServices - returns list with pagination meta', async () => {
    const query = { page: '1', limit: '10' } as any;
    req.query = query;
    (service.getServices as jest.Mock).mockResolvedValue({
      services: [{ id: 's1' }],
      pagination: { page: 1, limit: 10, total: 1, totalPages: 1, hasNext: false, hasPrev: false },
    });

    await controller.getServices(req as AuthenticatedRequest, res as Response, jest.fn());

    expect(service.getServices).toHaveBeenCalledWith(query);
    expect(res.success).toHaveBeenCalled();
    const call = (res.success as jest.Mock).mock.calls[0];
    expect(call[0]).toEqual(expect.objectContaining({ services: [{ id: 's1' }] }));
  });

  it('getServiceById - returns service', async () => {
    req.params = { id: 'svc-1' } as any;
    (service.getServiceById as jest.Mock).mockResolvedValue({ id: 'svc-1' });
    await controller.getServiceById(req as AuthenticatedRequest, res as Response, jest.fn());
    expect(service.getServiceById).toHaveBeenCalledWith('svc-1');
    expect(res.success).toHaveBeenCalledWith({ service: { id: 'svc-1' } });
  });

  it('createService - returns created', async () => {
    const body = { name: 'N' } as any;
    req.body = body;
    (service.createService as jest.Mock).mockResolvedValue({ id: 'svc-1' });
    await controller.createService(req as AuthenticatedRequest, res as Response, jest.fn());
    expect(service.createService).toHaveBeenCalledWith(body);
    expect(res.created).toHaveBeenCalledWith({ service: { id: 'svc-1' } }, 'Service created successfully');
  });

  it('getServiceCategories - returns list', async () => {
    (service.getServiceCategories as jest.Mock).mockResolvedValue([{ id: 'cat-1' }]);
    await controller.getServiceCategories(req as AuthenticatedRequest, res as Response, jest.fn());
    expect(res.success).toHaveBeenCalledWith({ categories: [{ id: 'cat-1' }] });
  });

  it('createServiceCategory - returns created', async () => {
    const category = { name: 'Cat', route: 'cat' } as any;
    req.body = category;
    (service.createServiceCategory as jest.Mock).mockResolvedValue({ id: 'cat-1' });
    await controller.createServiceCategory(req as AuthenticatedRequest, res as Response, jest.fn());
    expect(service.createServiceCategory).toHaveBeenCalledWith(category);
    expect(res.created).toHaveBeenCalledWith({ category: { id: 'cat-1' } }, 'Service category created successfully');
  });

  it('getServiceFormFields - returns list', async () => {
    req.params = { serviceId: 'svc-1' } as any;
    (service.getServiceFormFields as jest.Mock).mockResolvedValue([{ id: 'fld-1' }]);
    await controller.getServiceFormFields(req as AuthenticatedRequest, res as Response, jest.fn());
    expect(service.getServiceFormFields).toHaveBeenCalledWith('svc-1');
    expect(res.success).toHaveBeenCalledWith({ formFields: [{ id: 'fld-1' }] });
  });

  it('createServiceFormField - returns created', async () => {
    const field = { serviceId: 'svc-1', name: 'n', label: 'l', type: 'TEXT' } as any;
    req.body = field;
    (service.createServiceFormField as jest.Mock).mockResolvedValue({ id: 'fld-1' });
    await controller.createServiceFormField(req as AuthenticatedRequest, res as Response, jest.fn());
    expect(service.createServiceFormField).toHaveBeenCalledWith(field);
    expect(res.created).toHaveBeenCalledWith({ formField: { id: 'fld-1' } }, 'Service form field created successfully');
  });

  it('getServiceFormFieldOptions - returns list', async () => {
    req.params = { fieldId: 'fld-1' } as any;
    (service.getServiceFormFieldOptions as jest.Mock).mockResolvedValue([{ id: 'opt-1' }]);
    await controller.getServiceFormFieldOptions(req as AuthenticatedRequest, res as Response, jest.fn());
    expect(service.getServiceFormFieldOptions).toHaveBeenCalledWith('fld-1');
    expect(res.success).toHaveBeenCalledWith({ options: [{ id: 'opt-1' }] });
  });

  it('createServiceFormFieldOption - returns created', async () => {
    const option = { fieldId: 'fld-1', value: 'v', label: 'L' } as any;
    req.body = option;
    (service.createServiceFormFieldOption as jest.Mock).mockResolvedValue({ id: 'opt-1' });
    await controller.createServiceFormFieldOption(req as AuthenticatedRequest, res as Response, jest.fn());
    expect(service.createServiceFormFieldOption).toHaveBeenCalledWith(option);
    expect(res.created).toHaveBeenCalledWith(
      { option: { id: 'opt-1' } },
      'Service form field option created successfully'
    );
  });

  it('calculateServicePrice - returns calculation', async () => {
    const body = { serviceId: 'svc-1', formData: {}, quantity: 2 } as any;
    req.body = body;
    (service.calculateServicePrice as jest.Mock).mockResolvedValue({ total: 100 });
    await controller.calculateServicePrice(req as AuthenticatedRequest, res as Response, jest.fn());
    expect(service.calculateServicePrice).toHaveBeenCalledWith(body);
    expect(res.success).toHaveBeenCalledWith({ priceCalculation: { total: 100 } });
  });

  it('serviceExists + countServices - returns values', async () => {
    req.params = { id: 'svc-1' } as any;
    (service.serviceExists as jest.Mock).mockResolvedValue(true);
    await controller.serviceExists(req as AuthenticatedRequest, res as Response, jest.fn());
    expect(res.success).toHaveBeenCalledWith({ exists: true });

    (service.countServices as jest.Mock).mockResolvedValue(5);
    await controller.countServices(req as AuthenticatedRequest, res as Response, jest.fn());
    expect(res.success).toHaveBeenCalledWith({ count: 5 });
  });

  it('popular + stats - returns values', async () => {
    req.query = { limit: '3' } as any;
    (service.getPopularServices as jest.Mock).mockResolvedValue([{ id: 's1' }]);
    await controller.getPopularServices(req as AuthenticatedRequest, res as Response, jest.fn());
    expect(service.getPopularServices).toHaveBeenCalledWith(3);
    expect(res.success).toHaveBeenCalledWith({ services: [{ id: 's1' }] });

    (service.getServiceStats as jest.Mock).mockResolvedValue({ total: 1, active: 1 } as any);
    await controller.getServiceStats(req as AuthenticatedRequest, res as Response, jest.fn());
    expect(res.success).toHaveBeenCalledWith({ stats: { total: 1, active: 1 } });
  });
});
