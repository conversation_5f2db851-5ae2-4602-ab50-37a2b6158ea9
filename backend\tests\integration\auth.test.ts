/**
 * Auth Integration Tests
 *
 * Integration tests for authentication endpoints covering:
 * - User registration and login flow
 * - Password reset workflow
 * - Email verification process
 * - Session management
 * - Token refresh functionality
 * - Error handling and validation
 */

import { PrismaClient, UserRole } from '@prisma/client';
import { Express } from 'express';
import request from 'supertest';
import { cleanUserData } from '../setup';

// Mock the email service to avoid sending real emails during tests
jest.mock('../../src/services/EmailService', () => {
  return {
    NodemailerEmailService: jest.fn().mockImplementation(() => ({
      sendPasswordResetEmail: jest.fn().mockResolvedValue(true),
      sendEmailVerification: jest.fn().mockResolvedValue(true),
      sendWelcomeEmail: jest.fn().mockResolvedValue(true),
      verifyConnection: jest.fn().mockResolvedValue(true),
    })),
  };
});

describe('Auth Integration Tests', () => {
  let app: Express;
  let prisma: PrismaClient;

  const testUser = {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    firstName: 'Integration',
    lastName: 'Test',
    phone: '+1234567890',
  };

  beforeAll(async () => {
    // Import the app after mocking
    const appModule = await import('../../src/app');
    app = appModule.default;
    prisma = global.__PRISMA__;
  });

  beforeEach(async () => {
    await cleanUserData();
  });

  describe('POST /api/auth/register', () => {
    it('should successfully register a new user', async () => {
      const response = await request(app).post('/api/auth/register').send(testUser);

      // Debug: Log response for investigation
      if (response.status !== 201) {
        console.log('❌ Registration failed');
        console.log('Status:', response.status);
        console.log('Body:', JSON.stringify(response.body, null, 2));
        console.log('Text:', response.text);
      }

      expect(response.status).toBe(201);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Account created successfully',
        data: {
          user: {
            email: testUser.email,
            firstName: testUser.firstName,
            lastName: testUser.lastName,
            role: UserRole.CUSTOMER,
            isActive: true,
            isVerified: false,
          },
          tokens: {
            accessToken: expect.any(String),
            refreshToken: expect.any(String),
          },
        },
        meta: {
          timestamp: expect.any(String),
          version: expect.any(String),
          path: '/api/auth/register',
        },
      });

      // Verify user was created in database
      const user = await prisma.user.findUnique({
        where: { email: testUser.email },
      });
      expect(user).toBeTruthy();
      expect(user?.email).toBe(testUser.email);
    });

    it('should reject registration with existing email', async () => {
      // Create user first
      await request(app).post('/api/auth/register').send(testUser);

      // Try to register again with same email
      const response = await request(app).post('/api/auth/register').send(testUser).expect(409);

      expect(response.body).toMatchObject({
        success: false,
        error: {
          type: 'ConflictError',
          message: 'Email already registered',
        },
        meta: {
          timestamp: expect.any(String),
          version: expect.any(String),
          path: '/api/auth/register',
        },
      });
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: 'invalid-email',
          password: '123', // Too short
          firstName: '',
          lastName: '',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.type).toBe('ValidationError');
      expect(response.body.meta).toMatchObject({
        timestamp: expect.any(String),
        version: expect.any(String),
        path: '/api/auth/register',
      });
    });

    it('should validate email format', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          ...testUser,
          email: 'invalid-email-format',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.type).toBe('ValidationError');
    });

    it('should validate password strength', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          ...testUser,
          password: 'weak',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.type).toBe('ValidationError');
    });
  });

  describe('POST /api/auth/login', () => {
    it('should successfully login with valid credentials', async () => {
      // Register a user first
      await request(app).post('/api/auth/register').send(testUser);

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password,
        })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Login successful',
        data: {
          user: {
            email: testUser.email,
            firstName: testUser.firstName,
            isActive: true,
          },
          tokens: {
            accessToken: expect.any(String),
            refreshToken: expect.any(String),
          },
        },
        meta: {
          timestamp: expect.any(String),
          version: expect.any(String),
          path: '/api/auth/login',
        },
      });

      // Verify refresh token cookie is set
      expect(response.headers['set-cookie']).toBeDefined();
      const cookies = Array.isArray(response.headers['set-cookie'])
        ? response.headers['set-cookie']
        : [response.headers['set-cookie']];
      const refreshCookie = cookies.find((cookie: string) => cookie.startsWith('refreshToken='));
      expect(refreshCookie).toBeDefined();
    });

    it('should reject login with invalid email', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: testUser.password,
        })
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        error: {
          type: 'AuthenticationError',
          message: 'Invalid credentials',
        },
        meta: {
          timestamp: expect.any(String),
          version: expect.any(String),
          path: '/api/auth/login',
        },
      });
    });

    it('should reject login with invalid password', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'WrongPassword123!',
        })
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        error: {
          type: 'AuthenticationError',
          message: 'Invalid credentials',
        },
        meta: {
          timestamp: expect.any(String),
          version: expect.any(String),
          path: '/api/auth/login',
        },
      });
    });

    it('should handle account lockout after multiple failed attempts', async () => {
      // Register a user first for account lockout testing
      await request(app).post('/api/auth/register').send(testUser);

      // Make multiple failed login attempts
      for (let i = 0; i < 5; i++) {
        await request(app).post('/api/auth/login').send({
          email: testUser.email,
          password: 'WrongPassword123!',
        });
      }

      // Next attempt should be locked
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password,
        })
        .expect(401);

      expect(response.body.error.message).toContain('Account is locked');
      expect(response.body.error.type).toBe('AuthenticationError');
    });

    it('should validate login request format', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: 'invalid-email',
          password: '',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.type).toBe('ValidationError');
    });
  });

  describe('POST /api/auth/refresh', () => {
    let refreshToken: string;

    beforeEach(async () => {
      // Register and login to get refresh token
      const registerResponse = await request(app).post('/api/auth/register').send(testUser);

      if (registerResponse.status !== 201) {
        console.log('❌ Registration failed in refresh test setup');
        console.log('Status:', registerResponse.status);
        console.log('Body:', JSON.stringify(registerResponse.body, null, 2));
        throw new Error('Registration failed in refresh test setup');
      }

      const loginResponse = await request(app).post('/api/auth/login').send({
        email: testUser.email,
        password: testUser.password,
      });

      if (loginResponse.status !== 200) {
        console.log('❌ Login failed in refresh test setup');
        console.log('Status:', loginResponse.status);
        console.log('Body:', JSON.stringify(loginResponse.body, null, 2));
        throw new Error('Login failed in refresh test setup');
      }

      // Debug: Log the response structure
      console.log('✅ Login successful in refresh test setup');
      console.log('Login response body:', JSON.stringify(loginResponse.body, null, 2));

      refreshToken = loginResponse.body.data.tokens.refreshToken;
    });

    it('should refresh tokens with valid refresh token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .set('Cookie', `refreshToken=${refreshToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Tokens refreshed successfully',
        data: {
          tokens: {
            accessToken: expect.any(String),
          },
        },
        meta: {
          timestamp: expect.any(String),
          version: expect.any(String),
          path: '/api/auth/refresh',
        },
      });

      // Verify refresh token is set as HttpOnly cookie, not in response body
      expect(response.body.data.tokens).not.toHaveProperty('refreshToken');
      expect(response.headers['set-cookie']).toBeDefined();
      const cookies = Array.isArray(response.headers['set-cookie'])
        ? response.headers['set-cookie']
        : [response.headers['set-cookie']];
      const refreshCookie = cookies.find(
        (cookie: string) => cookie.includes('refreshToken=') && cookie.includes('HttpOnly')
      );
      expect(refreshCookie).toBeDefined();
    });

    it('should reject refresh with invalid token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .set('Cookie', 'refreshToken=invalid-token')
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        error: {
          type: 'AuthenticationError',
          message: 'Invalid refresh token',
        },
        meta: {
          timestamp: expect.any(String),
          version: expect.any(String),
          path: '/api/auth/refresh',
        },
      });
    });

    it('should reject refresh without token', async () => {
      const response = await request(app).post('/api/auth/refresh').expect(401);

      expect(response.body).toMatchObject({
        success: false,
        error: {
          type: 'AuthenticationError',
          message:
            'Refresh token must be provided via secure HttpOnly cookie. Body-based refresh tokens are not supported for security reasons.',
        },
        meta: {
          timestamp: expect.any(String),
          version: expect.any(String),
          path: '/api/auth/refresh',
        },
      });
    });

    // 2025 Security: Test body-based refresh token rejection
    it('should reject refresh token sent in request body', async () => {
      const response = await request(app).post('/api/auth/refresh').send({ refreshToken: 'some-token' }).expect(401);

      expect(response.body).toMatchObject({
        success: false,
        error: {
          type: 'AuthenticationError',
          message:
            'Refresh tokens in request body are no longer supported. Please ensure your client sends refresh tokens via HttpOnly cookies only.',
        },
        meta: {
          timestamp: expect.any(String),
          version: expect.any(String),
          path: '/api/auth/refresh',
        },
      });
    });

    // 2025 Security: Test that refresh token is not returned in response body
    it('should not return refresh token in response body for security', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .set('Cookie', `refreshToken=${refreshToken}`)
        .expect(200);

      expect(response.body.data.tokens).toHaveProperty('accessToken');
      expect(response.body.data.tokens).not.toHaveProperty('refreshToken');

      // Verify refresh token is set as HttpOnly cookie
      expect(response.headers['set-cookie']).toBeDefined();
      const cookies = Array.isArray(response.headers['set-cookie'])
        ? response.headers['set-cookie']
        : [response.headers['set-cookie']];
      const refreshCookie = cookies.find(
        (cookie: string) => cookie.includes('refreshToken=') && cookie.includes('HttpOnly')
      );
      expect(refreshCookie).toBeDefined();
    });

    // 2025 Security: Test middleware integration
    it('should enforce cookie-only refresh tokens through middleware', async () => {
      // Test without any token
      const response1 = await request(app).post('/api/auth/refresh').expect(401);
      expect(response1.body.error.message).toContain('Refresh token must be provided via secure HttpOnly cookie');

      // Test with body token only
      const response2 = await request(app).post('/api/auth/refresh').send({ refreshToken: 'body-token' }).expect(401);
      expect(response2.body.error.message).toContain('Refresh tokens in request body are no longer supported');

      // Test with cookie token (should work)
      const response3 = await request(app)
        .post('/api/auth/refresh')
        .set('Cookie', `refreshToken=${refreshToken}`)
        .expect(200);
      expect(response3.body.success).toBe(true);
    });
  });

  describe('POST /api/auth/logout', () => {
    let accessToken: string;

    beforeEach(async () => {
      // Register and login to get access token
      await request(app).post('/api/auth/register').send(testUser);

      const loginResponse = await request(app).post('/api/auth/login').send({
        email: testUser.email,
        password: testUser.password,
      });

      accessToken = loginResponse.body.data.tokens.accessToken;
    });

    it('should successfully logout user', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Logout successful',
        meta: {
          timestamp: expect.any(String),
          version: expect.any(String),
          path: '/api/auth/logout',
        },
      });

      // Verify refresh token cookie is cleared
      expect(response.headers['set-cookie']).toBeDefined();
      const cookies = Array.isArray(response.headers['set-cookie'])
        ? response.headers['set-cookie']
        : [response.headers['set-cookie']];
      const refreshCookie = cookies.find((cookie: string) => cookie.includes('refreshToken=;'));
      expect(refreshCookie).toBeDefined();
    });

    it('should require authentication for logout', async () => {
      const response = await request(app).post('/api/auth/logout').expect(401);

      expect(response.body).toMatchObject({
        success: false,
        error: {
          type: 'AuthenticationError',
          message: 'Authentication required',
        },
        meta: {
          timestamp: expect.any(String),
          version: expect.any(String),
          path: '/api/auth/logout',
        },
      });
    });
  });

  describe('Password Reset Flow', () => {
    beforeEach(async () => {
      // Register a user for password reset tests
      await request(app).post('/api/auth/register').send(testUser);
    });

    describe('POST /api/auth/password-reset/request', () => {
      it('should successfully request password reset', async () => {
        const response = await request(app)
          .post('/api/auth/password-reset/request')
          .send({ email: testUser.email })
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'If the email exists, a password reset link has been sent',
          meta: {
            timestamp: expect.any(String),
            version: expect.any(String),
            path: '/api/auth/password-reset/request',
          },
        });

        // Verify reset token was set in database
        const user = await prisma.user.findUnique({
          where: { email: testUser.email },
        });
        expect(user?.passwordResetToken).toBeTruthy();
        expect(user?.passwordResetTokenExpires).toBeTruthy();
      });

      it('should not reveal if email does not exist', async () => {
        const response = await request(app)
          .post('/api/auth/password-reset/request')
          .send({ email: '<EMAIL>' })
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'If the email exists, a password reset link has been sent',
          meta: {
            timestamp: expect.any(String),
            version: expect.any(String),
            path: '/api/auth/password-reset/request',
          },
        });
      });

      it('should validate email format', async () => {
        const response = await request(app)
          .post('/api/auth/password-reset/request')
          .send({ email: 'invalid-email' })
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error.type).toBe('ValidationError');
      });

      it('should apply rate limiting', async () => {
        // Make multiple requests quickly
        const requests = Array(6)
          .fill(null)
          .map(() => request(app).post('/api/auth/password-reset/request').send({ email: testUser.email }));

        const responses = await Promise.all(requests);

        // Some requests should be rate limited
        const rateLimitedResponses = responses.filter(r => r.status === 429);
        expect(rateLimitedResponses.length).toBeGreaterThan(0);
      });
    });

    describe('POST /api/auth/password-reset/confirm', () => {
      let resetToken: string;

      beforeEach(async () => {
        // Request password reset to get token
        await request(app).post('/api/auth/password-reset/request').send({ email: testUser.email });

        // Get the reset token from database
        const user = await prisma.user.findUnique({
          where: { email: testUser.email },
        });
        resetToken = user?.passwordResetToken || '';
      });

      it('should successfully reset password with valid token', async () => {
        const newPassword = 'NewPassword123!';

        const response = await request(app)
          .post('/api/auth/password-reset/confirm')
          .send({
            token: resetToken,
            newPassword: newPassword,
          })
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Password has been reset successfully',
          meta: {
            timestamp: expect.any(String),
            version: expect.any(String),
            path: '/api/auth/password-reset/confirm',
          },
        });

        // Verify user can login with new password
        const loginResponse = await request(app)
          .post('/api/auth/login')
          .send({
            email: testUser.email,
            password: newPassword,
          })
          .expect(200);

        expect(loginResponse.body.success).toBe(true);

        // Verify reset token was cleared
        const user = await prisma.user.findUnique({
          where: { email: testUser.email },
        });
        expect(user?.passwordResetToken).toBeNull();
      });

      it('should reject invalid reset token', async () => {
        const response = await request(app)
          .post('/api/auth/password-reset/confirm')
          .send({
            token: 'invalid-token',
            newPassword: 'NewPassword123!',
          })
          .expect(401);

        expect(response.body).toMatchObject({
          success: false,
          error: {
            type: 'AuthenticationError',
            message: 'Invalid or expired reset token',
          },
          meta: {
            timestamp: expect.any(String),
            version: expect.any(String),
            path: '/api/auth/password-reset/confirm',
          },
        });
      });

      it('should validate new password strength', async () => {
        const response = await request(app)
          .post('/api/auth/password-reset/confirm')
          .send({
            token: resetToken,
            newPassword: 'weak',
          })
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error.type).toBe('ValidationError');
      });
    });
  });

  describe('Email Verification Flow', () => {
    let accessToken: string;
    let userId: string;

    beforeEach(async () => {
      // Register a user for email verification tests
      const registerResponse = await request(app).post('/api/auth/register').send(testUser);

      accessToken = registerResponse.body.data.tokens.accessToken;
      userId = registerResponse.body.data.user.id;
    });

    describe('POST /api/auth/email/verify/send', () => {
      it('should send verification email for authenticated user', async () => {
        const response = await request(app)
          .post('/api/auth/email/verify/send')
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Verification email sent',
          meta: {
            timestamp: expect.any(String),
            version: expect.any(String),
            path: '/api/auth/email/verify/send',
          },
        });

        // Verify verification token was set in database
        const user = await prisma.user.findUnique({
          where: { id: userId },
        });
        expect(user?.emailVerificationToken).toBeTruthy();
        expect(user?.emailVerificationTokenExpires).toBeTruthy();
      });

      it('should require authentication', async () => {
        const response = await request(app).post('/api/auth/email/verify/send').expect(401);

        expect(response.body).toMatchObject({
          success: false,
          error: {
            type: 'AuthenticationError',
            message: 'Authentication required',
          },
          meta: {
            timestamp: expect.any(String),
            version: expect.any(String),
            path: '/api/auth/email/verify/send',
          },
        });
      });
    });

    describe('POST /api/auth/email/verify/confirm', () => {
      let verificationToken: string;

      beforeEach(async () => {
        // Send verification email to get token
        await request(app).post('/api/auth/email/verify/send').set('Authorization', `Bearer ${accessToken}`);

        // Get the verification token from database
        const user = await prisma.user.findUnique({
          where: { id: userId },
        });
        verificationToken = user?.emailVerificationToken || '';
      });

      it('should successfully verify email with valid token', async () => {
        const response = await request(app)
          .post('/api/auth/email/verify/confirm')
          .send({ token: verificationToken })
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Email verified successfully',
          meta: {
            timestamp: expect.any(String),
            version: expect.any(String),
            path: '/api/auth/email/verify/confirm',
          },
        });

        // Verify user is marked as verified
        const user = await prisma.user.findUnique({
          where: { id: userId },
        });
        expect(user?.isVerified).toBe(true);
        expect(user?.emailVerified).toBeTruthy();
        expect(user?.emailVerificationToken).toBeNull();
      });

      it('should reject invalid verification token', async () => {
        const response = await request(app)
          .post('/api/auth/email/verify/confirm')
          .send({ token: 'invalid-token' })
          .expect(401);

        expect(response.body).toMatchObject({
          success: false,
          error: {
            type: 'AuthenticationError',
            message: 'Invalid or expired verification token',
          },
          meta: {
            timestamp: expect.any(String),
            version: expect.any(String),
            path: '/api/auth/email/verify/confirm',
          },
        });
      });
    });
  });

  describe('Profile Management', () => {
    let accessToken: string;
    let userId: string;

    beforeEach(async () => {
      // Register and login to get access token
      const registerResponse = await request(app).post('/api/auth/register').send(testUser);

      accessToken = registerResponse.body.data.tokens.accessToken;
      userId = registerResponse.body.data.user.id;
    });

    describe('GET /api/auth/profile', () => {
      it('should return user profile for authenticated user', async () => {
        const response = await request(app)
          .get('/api/auth/profile')
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          data: {
            id: userId,
            email: testUser.email,
            firstName: testUser.firstName,
            lastName: testUser.lastName,
            isActive: true,
            isVerified: false,
          },
          meta: {
            timestamp: expect.any(String),
            version: expect.any(String),
            path: '/api/auth/profile',
          },
        });

        // Should not include sensitive fields
        expect(response.body.data.password).toBeUndefined();
        expect(response.body.data.passwordResetToken).toBeUndefined();
      });

      it('should require authentication', async () => {
        const response = await request(app).get('/api/auth/profile').expect(401);

        expect(response.body).toMatchObject({
          success: false,
          error: {
            type: 'AuthenticationError',
            message: 'Authentication required',
          },
          meta: {
            timestamp: expect.any(String),
            version: expect.any(String),
            path: '/api/auth/profile',
          },
        });
      });
    });

    describe('PUT /api/auth/profile', () => {
      it('should update user profile successfully', async () => {
        const updateData = {
          firstName: 'Updated',
          lastName: 'Name',
          phone: '+9876543210',
        };

        const response = await request(app)
          .put('/api/auth/profile')
          .set('Authorization', `Bearer ${accessToken}`)
          .send(updateData)
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Profile updated successfully',
          data: {
            firstName: 'Updated',
            lastName: 'Name',
            phone: '+9876543210',
          },
          meta: {
            timestamp: expect.any(String),
            version: expect.any(String),
            path: '/api/auth/profile',
          },
        });

        // Verify changes in database
        const user = await prisma.user.findUnique({
          where: { id: userId },
        });
        expect(user?.firstName).toBe('Updated');
        expect(user?.lastName).toBe('Name');
        expect(user?.phone).toBe('+9876543210');
      });

      it('should validate profile update data', async () => {
        const response = await request(app)
          .put('/api/auth/profile')
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            firstName: '', // Empty string should be invalid
            email: '<EMAIL>', // Email changes not allowed
          })
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error.type).toBe('ValidationError');
      });

      it('should require authentication', async () => {
        const response = await request(app).put('/api/auth/profile').send({ firstName: 'Updated' }).expect(401);

        expect(response.body).toMatchObject({
          success: false,
          error: {
            type: 'AuthenticationError',
            message: 'Authentication required',
          },
          meta: {
            timestamp: expect.any(String),
            version: expect.any(String),
            path: '/api/auth/profile',
          },
        });
      });
    });

    describe('PUT /api/auth/change-password', () => {
      it('should change password with valid current password', async () => {
        const response = await request(app)
          .put('/api/auth/change-password')
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            currentPassword: testUser.password,
            newPassword: 'NewPassword123!',
          })
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          message: 'Password changed successfully',
          meta: {
            timestamp: expect.any(String),
            version: expect.any(String),
            path: '/api/auth/change-password',
          },
        });

        // Verify user can login with new password
        const loginResponse = await request(app)
          .post('/api/auth/login')
          .send({
            email: testUser.email,
            password: 'NewPassword123!',
          })
          .expect(200);

        expect(loginResponse.body.success).toBe(true);
      });

      it('should reject change with invalid current password', async () => {
        const response = await request(app)
          .put('/api/auth/change-password')
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            currentPassword: 'WrongPassword123!',
            newPassword: 'NewPassword123!',
          })
          .expect(401);

        expect(response.body).toMatchObject({
          success: false,
          error: {
            type: 'AuthenticationError',
            message: 'Current password is incorrect',
          },
          meta: {
            timestamp: expect.any(String),
            version: expect.any(String),
            path: '/api/auth/change-password',
          },
        });
      });

      it('should validate new password strength', async () => {
        const response = await request(app)
          .put('/api/auth/change-password')
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            currentPassword: testUser.password,
            newPassword: 'weak',
          })
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error.type).toBe('ValidationError');
      });

      it('should require authentication', async () => {
        const response = await request(app)
          .put('/api/auth/change-password')
          .send({
            currentPassword: testUser.password,
            newPassword: 'NewPassword123!',
          })
          .expect(401);

        expect(response.body).toMatchObject({
          success: false,
          error: {
            type: 'AuthenticationError',
            message: 'Authentication required',
          },
          meta: {
            timestamp: expect.any(String),
            version: expect.any(String),
            path: '/api/auth/change-password',
          },
        });
      });
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits on auth endpoints', async () => {
      // Test rate limiting on login endpoint
      const promises = Array(10)
        .fill(null)
        .map(() =>
          request(app).post('/api/auth/login').send({
            email: testUser.email,
            password: testUser.password,
          })
        );

      const responses = await Promise.all(promises);
      const rateLimitedResponses = responses.filter(r => r.status === 429);

      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // Temporarily disconnect from database
      await prisma.$disconnect();

      const response = await request(app).post('/api/auth/register').send(testUser).expect(500);

      expect(response.body).toMatchObject({
        success: false,
        error: {
          type: 'InternalServerError',
          message: 'Internal server error',
        },
        meta: {
          timestamp: expect.any(String),
          version: expect.any(String),
          path: '/api/auth/register',
        },
      });

      // Reconnect for other tests
      await prisma.$connect();
    });

    it('should return consistent error format', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password',
        })
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('meta');
      expect(response.body.meta).toHaveProperty('timestamp');
      expect(response.body.meta).toHaveProperty('version');
    });
  });
});
