import {Router} from 'express';
import authRoutes from './auth';

const router = Router();

// API versioning middleware
router.use('/v1', (req, res, next) => {
	req.apiVersion = 'v1';
	next();
});

// Mount routes
router.use('/v1/auth', authRoutes);

// Health check route (moved from main index.ts for better organization)
router.get('/health', async (req, res) => {
	const startTime = Date.now();

	try {
		// Check database connection
		const {PrismaClient} = await import('@prisma/client');
		const prisma = new PrismaClient();
		await prisma.$queryRaw`SELECT 1`;
		await prisma.$disconnect();

		const dbStatus = 'healthy';
		const responseTime = Date.now() - startTime;

		// Get system information
		const systemInfo = {
			uptime: process.uptime(),
			memory: process.memoryUsage(),
			cpu: process.cpuUsage(),
		};

		res.status(200).json({
			status: 'OK',
			timestamp: new Date().toISOString(),
			environment: process.env.NODE_ENV || 'development',
			version: process.env.npm_package_version || '1.0.0',
			services: {
				database: dbStatus,
				api: 'healthy',
			},
			responseTime: `${responseTime}ms`,
			system: systemInfo,
		});
	} catch (error) {
		res.status(503).json({
			status: 'ERROR',
			timestamp: new Date().toISOString(),
			environment: process.env.NODE_ENV || 'development',
			version: process.env.npm_package_version || '1.0.0',
			services: {
				database: 'unhealthy',
				api: 'healthy',
			},
			error: 'Database connection failed',
		});
	}
});

// Metrics route
router.get('/metrics', (req, res) => {
	const metrics = {
		timestamp: new Date().toISOString(),
		uptime: process.uptime(),
		memory: process.memoryUsage(),
		cpu: process.cpuUsage(),
		environment: process.env.NODE_ENV || 'development',
		version: process.env.npm_package_version || '1.0.0',
	};

	res.status(200).json(metrics);
});

export default router;
