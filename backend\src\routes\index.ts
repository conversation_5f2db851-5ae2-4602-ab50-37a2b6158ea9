import { Router } from 'express';
import { createLogger } from '../utils/logger';

// Import route modules
import authRoutes from './auth';
import documentationRoutes from './documentation';
import providerRoutes from './providers';
import serviceRoutes from './services';
import userRoutes from './user';

const logger = createLogger('Routes');
const router = Router();

/**
 * Route Registration
 *
 * This file centralizes all route registration for the application.
 * Add new route modules here as they are developed.
 */

// Authentication routes
router.use('/auth', authRoutes);

// User management routes
router.use('/users', userRoutes);

// Service management routes (Phase 1: Core Services API)
router.use('/services', serviceRoutes);

// Provider management routes (Phase 2)
router.use('/providers', providerRoutes);

// API Documentation routes
router.use('/docs', documentationRoutes);

// TODO: Add more route modules as they are developed
// router.use('/providers', providerRoutes);
// router.use('/orders', orderRoutes);
// router.use('/files', fileRoutes);
// router.use('/gallery', galleryRoutes);
// router.use('/admin', adminRoutes);

logger.info('Routes registered successfully', {
  authRoutes: '/auth',
  userRoutes: '/users',
  serviceRoutes: '/services',
  providerRoutes: '/providers',
  documentationRoutes: '/docs',
  // Add more routes as they are implemented
});

export default router;
