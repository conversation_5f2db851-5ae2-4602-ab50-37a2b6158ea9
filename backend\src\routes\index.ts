import {Router} from 'express';
import {createLogger} from '../utils/logger';

// Import route modules
import authRoutes from './auth';

const logger = createLogger('Routes');
const router = Router();

/**
 * Route Registration
 *
 * This file centralizes all route registration for the application.
 * Add new route modules here as they are developed.
 */

// Authentication routes
router.use('/auth', authRoutes);

// TODO: Add more route modules as they are developed
// router.use('/users', userRoutes);
// router.use('/services', serviceRoutes);
// router.use('/orders', orderRoutes);
// router.use('/files', fileRoutes);
// router.use('/admin', adminRoutes);

logger.info('Routes registered successfully', {
	authRoutes: '/auth',
	// Add more routes as they are implemented
});

export default router;
