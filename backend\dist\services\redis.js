"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisService = exports.redisService = void 0;
const redis_1 = require("redis");
const config_1 = require("../config");
const logger_1 = require("../utils/logger");
const logger = (0, logger_1.createLogger)('Redis');
class RedisService {
    static instance;
    client;
    isConnected = false;
    isInitialized = false;
    constructor() {
        this.client = (0, redis_1.createClient)();
    }
    static getInstance() {
        if (!RedisService.instance) {
            RedisService.instance = new RedisService();
        }
        return RedisService.instance;
    }
    async initialize() {
        if (this.isInitialized) {
            logger.warn('Redis service already initialized');
            return;
        }
        try {
            const redisConfig = config_1.config.getRedisConfig();
            logger.info('Initializing Redis connection', {
                url: redisConfig.url.replace(/\/\/.*@/, '//***:***@'),
                db: redisConfig.db,
                keyPrefix: redisConfig.keyPrefix,
            });
            this.client = (0, redis_1.createClient)({
                url: redisConfig.url,
                password: redisConfig.password,
                database: redisConfig.db,
            });
            this.client.on('connect', () => {
                logger.info('Redis client connecting...');
            });
            this.client.on('ready', () => {
                logger.info('Redis client ready');
                this.isConnected = true;
            });
            this.client.on('error', (error) => {
                logger.error('Redis client error', error);
                this.isConnected = false;
            });
            this.client.on('end', () => {
                logger.info('Redis client disconnected');
                this.isConnected = false;
            });
            await this.client.connect();
            this.isInitialized = true;
            logger.info('Redis connection established successfully');
        }
        catch (error) {
            logger.error('Failed to initialize Redis connection', error);
            throw error;
        }
    }
    getClient() {
        if (!this.isInitialized) {
            throw new Error('Redis service not initialized. Call initialize() first.');
        }
        return this.client;
    }
    isConnectedToRedis() {
        return this.isConnected;
    }
    async disconnect() {
        if (!this.isConnected) {
            logger.warn('Redis already disconnected');
            return;
        }
        try {
            await this.client.quit();
            this.isConnected = false;
            this.isInitialized = false;
            logger.info('Redis disconnected successfully');
        }
        catch (error) {
            logger.error('Error disconnecting from Redis', error);
            throw error;
        }
    }
    async healthCheck() {
        try {
            await this.client.ping();
            return true;
        }
        catch (error) {
            logger.error('Redis health check failed', error);
            return false;
        }
    }
    getConfig() {
        return config_1.config.getRedisConfig();
    }
}
exports.RedisService = RedisService;
exports.redisService = RedisService.getInstance();
//# sourceMappingURL=redis.js.map