import { z } from 'zod';
declare const DatabaseConfigSchema: z.ZodObject<{
    url: z.ZodString;
    maxConnections: z.ZodDefault<z.ZodNumber>;
    idleTimeout: z.ZodDefault<z.ZodNumber>;
    connectionTimeout: z.ZodDefault<z.ZodNumber>;
}, z.core.$strip>;
declare const JWTConfigSchema: z.ZodObject<{
    secret: z.ZodString;
    refreshSecret: z.ZodString;
    accessExpiresIn: z.ZodDefault<z.ZodString>;
    refreshExpiresIn: z.ZodDefault<z.ZodString>;
    issuer: z.ZodDefault<z.ZodString>;
    audience: z.ZodDefault<z.ZodString>;
}, z.core.$strip>;
declare const ServerConfigSchema: z.ZodObject<{
    port: z.ZodDefault<z.ZodNumber>;
    host: z.<PERSON>od<PERSON>efault<z.ZodString>;
    nodeEnv: z.<PERSON>efault<z.ZodEnum<{
        development: "development";
        production: "production";
        test: "test";
    }>>;
    apiBaseUrl: z.ZodString;
    frontendUrl: z.ZodString;
    corsOrigin: z.ZodString;
}, z.core.$strip>;
declare const RedisConfigSchema: z.ZodObject<{
    url: z.ZodString;
    password: z.ZodOptional<z.ZodString>;
    db: z.ZodDefault<z.ZodNumber>;
    keyPrefix: z.ZodDefault<z.ZodString>;
    retryDelayOnFailover: z.ZodDefault<z.ZodNumber>;
    maxRetriesPerRequest: z.ZodDefault<z.ZodNumber>;
}, z.core.$strip>;
declare const AWSConfigSchema: z.ZodObject<{
    accessKeyId: z.ZodString;
    secretAccessKey: z.ZodString;
    region: z.ZodDefault<z.ZodString>;
    s3Bucket: z.ZodString;
    cloudfrontUrl: z.ZodOptional<z.ZodString>;
}, z.core.$strip>;
declare const EmailConfigSchema: z.ZodObject<{
    service: z.ZodDefault<z.ZodString>;
    host: z.ZodDefault<z.ZodString>;
    port: z.ZodDefault<z.ZodNumber>;
    secure: z.ZodDefault<z.ZodBoolean>;
    user: z.ZodString;
    password: z.ZodString;
    from: z.ZodString;
}, z.core.$strip>;
declare const LoggingConfigSchema: z.ZodObject<{
    level: z.ZodDefault<z.ZodEnum<{
        error: "error";
        warn: "warn";
        info: "info";
        debug: "debug";
    }>>;
    file: z.ZodOptional<z.ZodString>;
    enableConsole: z.ZodDefault<z.ZodBoolean>;
    enableFile: z.ZodDefault<z.ZodBoolean>;
    maxFileSize: z.ZodDefault<z.ZodNumber>;
    maxFiles: z.ZodDefault<z.ZodNumber>;
    format: z.ZodDefault<z.ZodEnum<{
        json: "json";
        simple: "simple";
    }>>;
}, z.core.$strip>;
declare const SecurityConfigSchema: z.ZodObject<{
    bcryptRounds: z.ZodDefault<z.ZodNumber>;
    cookieSecret: z.ZodString;
    rateLimitWindowMs: z.ZodDefault<z.ZodNumber>;
    rateLimitMaxRequests: z.ZodDefault<z.ZodNumber>;
    rateLimitAuthMax: z.ZodDefault<z.ZodNumber>;
    maxFileSize: z.ZodDefault<z.ZodNumber>;
    allowedFileTypes: z.ZodDefault<z.ZodArray<z.ZodString>>;
}, z.core.$strip>;
declare const GoogleOAuthConfigSchema: z.ZodObject<{
    clientId: z.ZodString;
    clientSecret: z.ZodString;
    callbackUrl: z.ZodString;
}, z.core.$strip>;
declare const AppConfigSchema: z.ZodObject<{
    database: z.ZodObject<{
        url: z.ZodString;
        maxConnections: z.ZodDefault<z.ZodNumber>;
        idleTimeout: z.ZodDefault<z.ZodNumber>;
        connectionTimeout: z.ZodDefault<z.ZodNumber>;
    }, z.core.$strip>;
    jwt: z.ZodObject<{
        secret: z.ZodString;
        refreshSecret: z.ZodString;
        accessExpiresIn: z.ZodDefault<z.ZodString>;
        refreshExpiresIn: z.ZodDefault<z.ZodString>;
        issuer: z.ZodDefault<z.ZodString>;
        audience: z.ZodDefault<z.ZodString>;
    }, z.core.$strip>;
    server: z.ZodObject<{
        port: z.ZodDefault<z.ZodNumber>;
        host: z.ZodDefault<z.ZodString>;
        nodeEnv: z.ZodDefault<z.ZodEnum<{
            development: "development";
            production: "production";
            test: "test";
        }>>;
        apiBaseUrl: z.ZodString;
        frontendUrl: z.ZodString;
        corsOrigin: z.ZodString;
    }, z.core.$strip>;
    redis: z.ZodObject<{
        url: z.ZodString;
        password: z.ZodOptional<z.ZodString>;
        db: z.ZodDefault<z.ZodNumber>;
        keyPrefix: z.ZodDefault<z.ZodString>;
        retryDelayOnFailover: z.ZodDefault<z.ZodNumber>;
        maxRetriesPerRequest: z.ZodDefault<z.ZodNumber>;
    }, z.core.$strip>;
    aws: z.ZodObject<{
        accessKeyId: z.ZodString;
        secretAccessKey: z.ZodString;
        region: z.ZodDefault<z.ZodString>;
        s3Bucket: z.ZodString;
        cloudfrontUrl: z.ZodOptional<z.ZodString>;
    }, z.core.$strip>;
    email: z.ZodObject<{
        service: z.ZodDefault<z.ZodString>;
        host: z.ZodDefault<z.ZodString>;
        port: z.ZodDefault<z.ZodNumber>;
        secure: z.ZodDefault<z.ZodBoolean>;
        user: z.ZodString;
        password: z.ZodString;
        from: z.ZodString;
    }, z.core.$strip>;
    logging: z.ZodObject<{
        level: z.ZodDefault<z.ZodEnum<{
            error: "error";
            warn: "warn";
            info: "info";
            debug: "debug";
        }>>;
        file: z.ZodOptional<z.ZodString>;
        enableConsole: z.ZodDefault<z.ZodBoolean>;
        enableFile: z.ZodDefault<z.ZodBoolean>;
        maxFileSize: z.ZodDefault<z.ZodNumber>;
        maxFiles: z.ZodDefault<z.ZodNumber>;
        format: z.ZodDefault<z.ZodEnum<{
            json: "json";
            simple: "simple";
        }>>;
    }, z.core.$strip>;
    security: z.ZodObject<{
        bcryptRounds: z.ZodDefault<z.ZodNumber>;
        cookieSecret: z.ZodString;
        rateLimitWindowMs: z.ZodDefault<z.ZodNumber>;
        rateLimitMaxRequests: z.ZodDefault<z.ZodNumber>;
        rateLimitAuthMax: z.ZodDefault<z.ZodNumber>;
        maxFileSize: z.ZodDefault<z.ZodNumber>;
        allowedFileTypes: z.ZodDefault<z.ZodArray<z.ZodString>>;
    }, z.core.$strip>;
    googleOAuth: z.ZodObject<{
        clientId: z.ZodString;
        clientSecret: z.ZodString;
        callbackUrl: z.ZodString;
    }, z.core.$strip>;
    enableSwagger: z.ZodDefault<z.ZodBoolean>;
    enableLogging: z.ZodDefault<z.ZodBoolean>;
    enableCors: z.ZodDefault<z.ZodBoolean>;
}, z.core.$strip>;
export type AppConfig = z.infer<typeof AppConfigSchema>;
export type DatabaseConfig = z.infer<typeof DatabaseConfigSchema>;
export type JWTConfig = z.infer<typeof JWTConfigSchema>;
export type ServerConfig = z.infer<typeof ServerConfigSchema>;
export type RedisConfig = z.infer<typeof RedisConfigSchema>;
export type AWSConfig = z.infer<typeof AWSConfigSchema>;
export type EmailConfig = z.infer<typeof EmailConfigSchema>;
export type LoggingConfig = z.infer<typeof LoggingConfigSchema>;
export type SecurityConfig = z.infer<typeof SecurityConfigSchema>;
export type GoogleOAuthConfig = z.infer<typeof GoogleOAuthConfigSchema>;
export declare class ConfigurationManager {
    private config;
    private isInitialized;
    constructor();
    private loadConfiguration;
    getConfig(): AppConfig;
    getDatabaseConfig(): DatabaseConfig;
    getJWTConfig(): JWTConfig;
    getServerConfig(): ServerConfig;
    getRedisConfig(): RedisConfig;
    getAWSConfig(): AWSConfig;
    getEmailConfig(): EmailConfig;
    getLoggingConfig(): LoggingConfig;
    getSecurityConfig(): SecurityConfig;
    getGoogleOAuthConfig(): GoogleOAuthConfig;
    isDevelopment(): boolean;
    isProduction(): boolean;
    isTest(): boolean;
    reload(): void;
}
export {};
//# sourceMappingURL=config.d.ts.map