import { Response } from 'express';
import { IAuthService } from '../services/AuthService';
export interface IAuthController {
    register: any;
    login: any;
    refreshTokens: any;
    logout: any;
    logoutAllDevices: any;
    getProfile: any;
    updateProfile: any;
    changePassword: any;
    getUserSessions: any;
    revokeSession: any;
}
export declare class AuthController implements IAuthController {
    private authService;
    constructor(authService: IAuthService);
    register: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
    login: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
    refreshTokens: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
    logout: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
    logoutAllDevices: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
    getProfile: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
    updateProfile: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
    changePassword: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
    getUserSessions: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
    revokeSession: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
    private createRequestContext;
}
export declare const createAuthController: (authService: IAuthService) => AuthController;
export default AuthController;
//# sourceMappingURL=AuthController.d.ts.map