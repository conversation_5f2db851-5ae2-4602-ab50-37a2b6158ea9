import { NextFunction, Request, Response } from 'express';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { ValidationError as JoiValidationError } from 'joi';
import {
  AppError,
  AuthenticationError,
  AuthorizationError,
  ValidationError,
  NotFoundError,
  ConflictError,
  TooManyRequestsError,
  errorHandler,
  asyncHandler,
  notFoundHandler,
} from '@/middleware/errorHandler';

// Mock logger
jest.mock('@/utils/logger', () => ({
  createLogger: jest.fn().mockReturnValue({
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    debug: jest.fn(),
  }),
}));

// Mock sanitization utilities
jest.mock('@/utils/sanitization', () => ({
  sanitizeHeaders: jest.fn().mockImplementation((headers) => ({ sanitized: true, ...headers })),
  sanitizeRequestBody: jest.fn().mockImplementation((body) => ({ sanitized: true, ...body })),
}));

// Mock response wrapper
jest.mock('@/utils/responseWrapper', () => ({
  createErrorResponse: jest.fn().mockImplementation((message, statusCode, name) => ({
    success: false,
    message,
    statusCode,
    error: { name },
    timestamp: new Date().toISOString(),
  })),
  HttpStatus: {
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    CONFLICT: 409,
    TOO_MANY_REQUESTS: 429,
    INTERNAL_SERVER_ERROR: 500,
  },
}));

describe('ErrorHandler Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockRequest = {
      path: '/test',
      method: 'GET',
      ip: '***********',
      headers: { 'user-agent': 'test-agent' },
      body: { test: 'data' },
      query: { param: 'value' },
      params: { id: '123' },
      get: jest.fn().mockReturnValue('test-agent'),
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      getHeader: jest.fn().mockReturnValue('request-id-123'),
    };

    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('Custom Error Classes', () => {
    describe('AppError', () => {
      it('should create AppError with correct properties', () => {
        const error = new AppError('Test error', 400);

        expect(error.name).toBe('AppError');
        expect(error.message).toBe('Test error');
        expect(error.statusCode).toBe(400);
        expect(error.isOperational).toBe(true);
        expect(error.stack).toBeDefined();
      });
    });

    describe('AuthenticationError', () => {
      it('should create AuthenticationError with default message', () => {
        const error = new AuthenticationError();

        expect(error.name).toBe('AuthenticationError');
        expect(error.message).toBe('Authentication failed');
        expect(error.statusCode).toBe(401);
        expect(error.isOperational).toBe(true);
      });

      it('should create AuthenticationError with custom message', () => {
        const error = new AuthenticationError('Custom auth error');

        expect(error.message).toBe('Custom auth error');
        expect(error.statusCode).toBe(401);
      });
    });

    describe('AuthorizationError', () => {
      it('should create AuthorizationError with default message', () => {
        const error = new AuthorizationError();

        expect(error.name).toBe('AuthorizationError');
        expect(error.message).toBe('Access denied');
        expect(error.statusCode).toBe(403);
      });

      it('should create AuthorizationError with custom message', () => {
        const error = new AuthorizationError('Insufficient permissions');

        expect(error.message).toBe('Insufficient permissions');
        expect(error.statusCode).toBe(403);
      });
    });

    describe('ValidationError', () => {
      it('should create ValidationError with default message', () => {
        const error = new ValidationError();

        expect(error.name).toBe('ValidationError');
        expect(error.message).toBe('Validation failed');
        expect(error.statusCode).toBe(400);
        expect(error.validationErrors).toEqual([]);
      });

      it('should create ValidationError with validation errors', () => {
        const validationErrors = [
          { field: 'email', message: 'Email is required' },
          { field: 'password', message: 'Password too short' },
        ];
        const error = new ValidationError('Custom validation error', validationErrors);

        expect(error.message).toBe('Custom validation error');
        expect(error.validationErrors).toEqual(validationErrors);
      });
    });

    describe('NotFoundError', () => {
      it('should create NotFoundError with default message', () => {
        const error = new NotFoundError();

        expect(error.name).toBe('NotFoundError');
        expect(error.message).toBe('Resource not found');
        expect(error.statusCode).toBe(404);
      });
    });

    describe('ConflictError', () => {
      it('should create ConflictError with default message', () => {
        const error = new ConflictError();

        expect(error.name).toBe('ConflictError');
        expect(error.message).toBe('Resource already exists');
        expect(error.statusCode).toBe(409);
      });
    });

    describe('TooManyRequestsError', () => {
      it('should create TooManyRequestsError with default message', () => {
        const error = new TooManyRequestsError();

        expect(error.name).toBe('TooManyRequestsError');
        expect(error.message).toBe('Too many requests');
        expect(error.statusCode).toBe(429);
      });
    });
  });

  describe('errorHandler', () => {
    it('should handle AppError correctly', () => {
      const error = new AppError('Custom app error', 422);

      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(422);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          message: 'Custom app error',
          statusCode: 422,
        })
      );
    });

    it('should handle ValidationError with validation details', () => {
      const validationErrors = [
        { field: 'email', message: 'Email is required' },
      ];
      const error = new ValidationError('Validation failed', validationErrors);

      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Validation failed',
        })
      );
    });

    it('should handle PrismaClientKnownRequestError P2002 (unique constraint)', () => {
      const error = new PrismaClientKnownRequestError('Unique constraint failed', {
        code: 'P2002',
        clientVersion: '4.0.0',
        meta: { target: ['email'] },
      });

      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(409);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Email already registered',
        })
      );
    });

    it('should handle PrismaClientKnownRequestError P2025 (record not found)', () => {
      const error = new PrismaClientKnownRequestError('Record not found', {
        code: 'P2025',
        clientVersion: '4.0.0',
      });

      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Record not found',
        })
      );
    });

    it('should handle PrismaClientKnownRequestError P2003 (foreign key constraint)', () => {
      const error = new PrismaClientKnownRequestError('Foreign key constraint failed', {
        code: 'P2003',
        clientVersion: '4.0.0',
      });

      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid reference',
        })
      );
    });

    it('should handle unknown Prisma errors', () => {
      const error = new PrismaClientKnownRequestError('Unknown database error', {
        code: 'P9999',
        clientVersion: '4.0.0',
      });

      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Database error',
        })
      );
    });

    it('should handle Joi ValidationError', () => {
      const mockJoiError = {
        name: 'ValidationError',
        message: 'Validation error',
        details: [
          {
            path: ['email'],
            message: 'Email is required',
            context: { value: '' },
          },
          {
            path: ['password', 'length'],
            message: 'Password must be at least 8 characters',
            context: { value: '123' },
          },
        ],
      } as JoiValidationError;

      errorHandler(mockJoiError, mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Validation failed',
        })
      );
    });

    it('should handle JsonWebTokenError', () => {
      const error = new Error('Invalid token');
      error.name = 'JsonWebTokenError';

      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid token',
        })
      );
    });

    it('should handle TokenExpiredError', () => {
      const error = new Error('Token expired');
      error.name = 'TokenExpiredError';

      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Token expired',
        })
      );
    });

    it('should handle JSON SyntaxError', () => {
      const error = new SyntaxError('Unexpected token in JSON');
      (error as any).body = true; // Simulate body parsing error

      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid JSON format',
        })
      );
    });

    it('should handle generic errors as 500', () => {
      const error = new Error('Something went wrong');

      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Internal server error',
        })
      );
    });

    it('should include stack trace in development environment', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const error = new Error('Test error');
      error.stack = 'Error stack trace';

      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.objectContaining({
            stack: 'Error stack trace',
          }),
        })
      );

      process.env.NODE_ENV = originalEnv;
    });

    it('should not include stack trace in production environment', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const error = new Error('Test error');
      error.stack = 'Error stack trace';

      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.not.objectContaining({
          stack: expect.anything(),
        })
      );

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('asyncHandler', () => {
    it('should handle successful async function', async () => {
      const successfulHandler = asyncHandler(async (req, res, next) => {
        res.json({ success: true });
      });

      await successfulHandler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith({ success: true });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should catch and pass errors to next', async () => {
      const error = new Error('Async error');
      const failingHandler = asyncHandler(async (req, res, next) => {
        throw error;
      });

      await failingHandler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should handle rejected promises', async () => {
      const error = new Error('Promise rejection');
      const rejectingHandler = asyncHandler(async (req, res, next) => {
        return Promise.reject(error);
      });

      await rejectingHandler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('notFoundHandler', () => {
    it('should create NotFoundError for unmatched routes', () => {
      mockRequest.originalUrl = '/non-existent-route';

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'NotFoundError',
          message: 'Route /non-existent-route not found',
          statusCode: 404,
        })
      );
    });
  });
});