# Test Environment Configuration
NODE_ENV=test

# Test Database Configuration
DATABASE_URL="postgresql://printweditt_user:printweditt_password@localhost:5432/printweditt_db?schema=public"

# Test JWT Configuration
JWT_SECRET="test-jwt-secret-key-that-is-at-least-32-characters-long-for-testing-only"
JWT_REFRESH_SECRET="test-refresh-secret-key-that-is-at-least-32-characters-long-for-testing-only"
JWT_ACCESS_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Test Server Configuration
PORT=3002
API_BASE_URL="http://localhost:3002"
FRONTEND_URL="http://localhost:5173"

# Test Email Configuration (use fake SMTP for testing)
EMAIL_SERVICE="gmail"
EMAIL_HOST="smtp.gmail.com"
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER="<EMAIL>"
EMAIL_PASSWORD="test-password"
EMAIL_FROM="<EMAIL>"

# Test Security Configuration
BCRYPT_ROUNDS=4
CORS_ORIGIN="http://localhost:5173"
COOKIE_SECRET="test-cookie-secret-key-that-is-at-least-32-characters-long-for-testing-only"

# Test Rate Limiting Configuration (more lenient for testing)
RATE_LIMIT_WINDOW_MS=60000  # 1 minute
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_AUTH_MAX=100

# Test File Upload Configuration
MAX_FILE_SIZE=1048576  # 1MB for testing
ALLOWED_FILE_TYPES="jpg,jpeg,png,pdf"

# Test Logging Configuration
LOG_LEVEL="error"  # Reduce noise during testing
ENABLE_LOGGING=false
ENABLE_CONSOLE=false
