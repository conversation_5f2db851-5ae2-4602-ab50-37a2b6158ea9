{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AACA,4DAA+E;AAG/E,4CAA+C;AAC/C,wDAA6E;AAG7E,MAAa,QAAS,SAAQ,KAAK;IAC1B,UAAU,CAAS;IACnB,aAAa,CAAU;IAE9B,YAAY,OAAe,EAAE,UAAkB;QAC7C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAVD,4BAUC;AAGD,MAAa,mBAAoB,SAAQ,QAAQ;IAC/C,YAAY,UAAkB,uBAAuB;QACnD,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACtB,CAAC;CACF;AAJD,kDAIC;AAED,MAAa,kBAAmB,SAAQ,QAAQ;IAC9C,YAAY,UAAkB,eAAe;QAC3C,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACtB,CAAC;CACF;AAJD,gDAIC;AAED,MAAa,eAAgB,SAAQ,QAAQ;IACpC,gBAAgB,CAAQ;IAE/B,YAAY,UAAkB,mBAAmB,EAAE,mBAA0B,EAAE;QAC7E,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAC3C,CAAC;CACF;AAPD,0CAOC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,UAAkB,oBAAoB;QAChD,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACtB,CAAC;CACF;AAJD,sCAIC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,UAAkB,yBAAyB;QACrD,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACtB,CAAC;CACF;AAJD,sCAIC;AAED,MAAa,oBAAqB,SAAQ,QAAQ;IAChD,YAAY,UAAkB,mBAAmB;QAC/C,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACtB,CAAC;CACF;AAJD,oDAIC;AAGM,MAAM,YAAY,GAAG,CAC1B,KAAY,EACZ,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,MAAM,MAAM,GAAG,IAAA,qBAAY,EAAC,cAAc,CAAC,CAAC;IAC5C,IAAI,UAAU,GAAG,GAAG,CAAC;IACrB,IAAI,OAAO,GAAG,uBAAuB,CAAC;IACtC,IAAI,OAAO,GAAQ,SAAS,CAAC;IAG7B,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;QAC9B,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QAC9B,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAExB,IAAI,KAAK,YAAY,eAAe,EAAE,CAAC;YACrC,OAAO,GAAG,EAAE,gBAAgB,EAAE,KAAK,CAAC,gBAAgB,EAAE,CAAC;QACzD,CAAC;IACH,CAAC;SAAM,IAAI,KAAK,YAAY,uCAA6B,EAAE,CAAC;QAE1D,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,OAAO;gBACV,UAAU,GAAG,GAAG,CAAC;gBACjB,OAAO,GAAG,yBAAyB,CAAC;gBACpC,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,EAAE,MAAkB,CAAC;gBAC9C,IAAI,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC9B,OAAO,GAAG,qCAAqC,CAAC;gBAClD,CAAC;gBACD,MAAM;YACR,KAAK,OAAO;gBACV,UAAU,GAAG,GAAG,CAAC;gBACjB,OAAO,GAAG,kBAAkB,CAAC;gBAC7B,MAAM;YACR,KAAK,OAAO;gBACV,UAAU,GAAG,GAAG,CAAC;gBACjB,OAAO,GAAG,mBAAmB,CAAC;gBAC9B,MAAM;YACR;gBACE,UAAU,GAAG,GAAG,CAAC;gBACjB,OAAO,GAAG,gBAAgB,CAAC;QAC/B,CAAC;IACH,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QAE5C,MAAM,QAAQ,GAAG,KAA2B,CAAC;QAC7C,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,mBAAmB,CAAC;QAC9B,OAAO,GAAG;YACR,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAChD,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;gBAC5B,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,KAAK,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK;aAC7B,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC9C,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,eAAe,CAAC;IAC5B,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC9C,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,eAAe,CAAC;IAC5B,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,IAAI,MAAM,IAAI,KAAK,EAAE,CAAC;QAC3D,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,qBAAqB,CAAC;IAClC,CAAC;IAGD,MAAM,YAAY,GAAG;QACnB,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,UAAU;QACV,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;QAChC,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC;QACxC,MAAM,EAAG,GAAW,CAAC,MAAM;QAC3B,OAAO,EAAE,IAAA,8BAAe,EAAC,GAAG,CAAC,OAAO,CAAC;QACrC,IAAI,EAAE,IAAA,kCAAmB,EAAC,GAAG,CAAC,IAAI,CAAC;QACnC,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,MAAM,EAAE,GAAG,CAAC,MAAM;KACnB,CAAC;IAEF,IAAI,UAAU,IAAI,GAAG,EAAE,CAAC;QAEtB,MAAM,CAAC,KAAK,CAAC,iBAAiB,OAAO,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;IAChE,CAAC;SAAM,IAAI,UAAU,IAAI,GAAG,EAAE,CAAC;QAE7B,MAAM,CAAC,IAAI,CAAC,iBAAiB,OAAO,EAAE,EAAE;YACtC,GAAG,YAAY;YACf,YAAY,EAAE,KAAK,CAAC,OAAO;YAC3B,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;SACvE,CAAC,CAAC;IACL,CAAC;IAGD,MAAM,aAAa,GAAkB;QACnC,KAAK,EAAE,KAAK,CAAC,IAAI,IAAI,OAAO;QAC5B,OAAO;QACP,UAAU;QACV,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,IAAI,EAAE,GAAG,CAAC,IAAI;KACf,CAAC;IAGF,IAAI,OAAO,EAAE,CAAC;QACX,aAAqB,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3C,CAAC;IAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;QACxD,aAAqB,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAC7C,CAAC;IAED,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7C,CAAC,CAAC;AAjHW,QAAA,YAAY,gBAiHvB;AAKK,MAAM,YAAY,GAAG,CAC1B,EAAqE,EACrE,EAAE;IACF,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AANW,QAAA,YAAY,gBAMvB;AAGK,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACjF,MAAM,KAAK,GAAG,IAAI,aAAa,CAAC,SAAS,GAAG,CAAC,WAAW,YAAY,CAAC,CAAC;IACtE,IAAI,CAAC,KAAK,CAAC,CAAC;AACd,CAAC,CAAC;AAHW,QAAA,eAAe,mBAG1B;AAEF,kBAAe;IACb,QAAQ;IACR,mBAAmB;IACnB,kBAAkB;IAClB,eAAe;IACf,aAAa;IACb,aAAa;IACb,oBAAoB;IACpB,YAAY,EAAZ,oBAAY;IACZ,YAAY,EAAZ,oBAAY;IACZ,eAAe,EAAf,uBAAe;CAChB,CAAC"}