import { PrismaClient } from '@prisma/client';
import {
  CreateProviderRequest,
  CreateProviderServiceRequest,
  CreateServiceAreaRequest,
  ProviderDetail,
  ProviderListQuery,
  ProviderListResponse,
  ProviderOperatingHour,
  ProviderRatingDisplay,
  ProviderServiceArea,
  ProviderServiceSummary,
  ProviderSummary,
  UpdateProviderRequest,
  UpdateProviderServiceRequest,
  UpsertOperatingHoursRequest,
} from '../types/provider';
import { generateId } from '../utils/appHelpers';

export interface IProviderRepository {
  // Provider CRUD
  createProvider(data: CreateProviderRequest): Promise<ProviderDetail>;
  updateProvider(id: string, data: UpdateProviderRequest): Promise<ProviderDetail>;
  deleteProvider(id: string): Promise<boolean>;
  findById(id: string): Promise<ProviderDetail | null>;
  listProviders(query: ProviderListQuery): Promise<ProviderListResponse>;
  findByUserId(userId: string): Promise<ProviderDetail | null>;

  // Provider services
  listProviderServices(providerId: string): Promise<ProviderServiceSummary[]>;
  addProviderService(providerId: string, req: CreateProviderServiceRequest): Promise<ProviderServiceSummary>;
  updateProviderService(
    providerId: string,
    serviceId: string,
    data: UpdateProviderServiceRequest
  ): Promise<ProviderServiceSummary>;
  removeProviderService(providerId: string, serviceId: string): Promise<boolean>;

  // Operating hours
  getOperatingHours(providerId: string): Promise<ProviderOperatingHour[]>;
  upsertOperatingHours(providerId: string, req: UpsertOperatingHoursRequest): Promise<ProviderOperatingHour[]>;

  // Service areas
  listServiceAreas(providerId: string): Promise<ProviderServiceArea[]>;
  addServiceArea(providerId: string, req: CreateServiceAreaRequest): Promise<ProviderServiceArea>;
  removeServiceArea(providerId: string, areaId: string): Promise<boolean>;

  // Ratings
  getRatingDisplay(providerId: string): Promise<ProviderRatingDisplay | null>;
  incrementRating(providerId: string, rating: number): Promise<ProviderRatingDisplay>;
}

export class ProviderRepository implements IProviderRepository {
  constructor(private prisma: PrismaClient) {}

  private mapProviderDetail(p: any): ProviderDetail {
    return {
      id: p.id,
      userId: p.userId,
      businessName: p.businessName,
      description: p.description ?? null,
      email: p.email ?? null,
      website: p.website ?? null,
      phone: p.phone ?? null,
      isActive: p.isActive,
      isVerified: p.isVerified,
      averageRating: p.provider_rating_displays?.averageRating ?? 0,
      reviewCount: p.provider_rating_displays?.reviewCount ?? 0,
      services: (p.services || []).map((s: any) => ({
        id: s.id,
        providerId: s.providerId,
        serviceId: s.serviceId,
        price: Number(s.price),
        description: s.description ?? null,
        isActive: s.isActive,
        createdAt: s.createdAt,
        updatedAt: s.updatedAt,
      })),
      operatingHours: (p.provider_operating_hours || []).map((h: any) => ({
        id: h.id,
        providerId: h.providerId,
        dayOfWeek: h.dayOfWeek,
        openTime: h.openTime,
        closeTime: h.closeTime,
        createdAt: h.createdAt,
      })),
      serviceAreas: (p.provider_service_areas || []).map((a: any) => ({
        id: a.id,
        providerId: a.providerId,
        streetAddress: a.streetAddress,
        zipCode: a.zipCode,
        city: a.city,
        state: a.state,
        isActive: a.isActive,
        createdAt: a.createdAt,
      })),
      createdAt: p.createdAt,
      updatedAt: p.updatedAt,
    };
  }

  async createProvider(data: CreateProviderRequest): Promise<ProviderDetail> {
    const created = await this.prisma.provider.create({
      data: {
        userId: data.userId,
        businessName: data.businessName.trim(),
        description: data.description?.trim() || null,
        email: data.email?.trim() || null,
        website: data.website?.trim() || null,
        phone: data.phone?.trim() || null,
      },
      include: {
        provider_rating_displays: true,
        services: true,
        provider_operating_hours: true,
        provider_service_areas: true,
      },
    });
    return this.mapProviderDetail(created);
  }

  async updateProvider(id: string, data: UpdateProviderRequest): Promise<ProviderDetail> {
    const updated = await this.prisma.provider.update({
      where: { id },
      data: {
        businessName: data.businessName?.trim(),
        description: data.description?.trim() ?? undefined,
        email: data.email?.trim() ?? undefined,
        website: data.website?.trim() ?? undefined,
        phone: data.phone?.trim() ?? undefined,
        isActive: data.isActive,
        isVerified: data.isVerified,
      },
      include: {
        provider_rating_displays: true,
        services: true,
        provider_operating_hours: true,
        provider_service_areas: true,
      },
    });
    return this.mapProviderDetail(updated);
  }

  async deleteProvider(id: string): Promise<boolean> {
    await this.prisma.provider.delete({ where: { id } });
    return true;
  }

  async findById(id: string): Promise<ProviderDetail | null> {
    const found = await this.prisma.provider.findUnique({
      where: { id },
      include: {
        provider_rating_displays: true,
        services: true,
        provider_operating_hours: true,
        provider_service_areas: true,
      },
    });
    return found ? this.mapProviderDetail(found) : null;
  }

  async findByUserId(userId: string): Promise<ProviderDetail | null> {
    const found = await this.prisma.provider.findUnique({
      where: { userId },
      include: {
        provider_rating_displays: true,
        services: true,
        provider_operating_hours: true,
        provider_service_areas: true,
      },
    });
    return found ? this.mapProviderDetail(found) : null;
  }

  async listProviders(query: ProviderListQuery): Promise<ProviderListResponse> {
    const page = query.page ?? 1;
    const limit = query.limit ?? 20;
    const where: any = {};
    if (query.search) where.businessName = { contains: query.search, mode: 'insensitive' };
    if (query.isActive !== undefined) where.isActive = query.isActive;
    if (query.isVerified !== undefined) where.isVerified = query.isVerified;

    // service filter
    if (query.serviceId) {
      where.services = { some: { serviceId: query.serviceId, isActive: true } };
    }

    // location filter (basic by zipCode)
    if (query.zipCode) {
      where.provider_service_areas = { some: { zipCode: query.zipCode, isActive: true } };
    }

    const [total, items] = await this.prisma.$transaction([
      this.prisma.provider.count({ where }),
      this.prisma.provider.findMany({
        where,
        orderBy: { businessName: 'asc' },
        skip: (page - 1) * limit,
        take: limit,
        include: { provider_rating_displays: true },
      }),
    ]);

    const providers: ProviderSummary[] = items.map(p => ({
      id: p.id,
      userId: p.userId,
      businessName: p.businessName,
      isActive: p.isActive,
      isVerified: p.isVerified,
      averageRating: p.provider_rating_displays?.averageRating ?? 0,
      reviewCount: p.provider_rating_displays?.reviewCount ?? 0,
      serviceAreaZipCodes: [],
      createdAt: p.createdAt,
      updatedAt: p.updatedAt,
    }));

    const totalPages = Math.ceil(total / limit) || 1;
    return {
      providers,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  // Provider services
  async listProviderServices(providerId: string): Promise<ProviderServiceSummary[]> {
    const list = await this.prisma.providerService.findMany({ where: { providerId } });
    return list.map(s => ({
      id: s.id,
      providerId: s.providerId,
      serviceId: s.serviceId,
      price: Number(s.price),
      description: s.description ?? null,
      isActive: s.isActive,
      createdAt: s.createdAt,
      updatedAt: s.updatedAt,
    }));
  }

  async addProviderService(providerId: string, req: CreateProviderServiceRequest): Promise<ProviderServiceSummary> {
    const created = await this.prisma.providerService.create({
      data: {
        providerId,
        serviceId: req.serviceId,
        price: req.price,
        description: req.description ?? null,
        isActive: req.isActive ?? true,
      },
    });
    return {
      id: created.id,
      providerId: created.providerId,
      serviceId: created.serviceId,
      price: Number(created.price),
      description: created.description ?? null,
      isActive: created.isActive,
      createdAt: created.createdAt,
      updatedAt: created.updatedAt,
    };
  }

  async updateProviderService(
    providerId: string,
    serviceId: string,
    data: UpdateProviderServiceRequest
  ): Promise<ProviderServiceSummary> {
    const updated = await this.prisma.providerService.update({
      where: { providerId_serviceId: { providerId, serviceId } },
      data: {
        price: data.price ?? undefined,
        description: data.description ?? undefined,
        isActive: data.isActive ?? undefined,
      },
    });
    return {
      id: updated.id,
      providerId: updated.providerId,
      serviceId: updated.serviceId,
      price: Number(updated.price),
      description: updated.description ?? null,
      isActive: updated.isActive,
      createdAt: updated.createdAt,
      updatedAt: updated.updatedAt,
    };
  }

  async removeProviderService(providerId: string, serviceId: string): Promise<boolean> {
    await this.prisma.providerService.delete({ where: { providerId_serviceId: { providerId, serviceId } } });
    return true;
  }

  // Operating hours
  async getOperatingHours(providerId: string): Promise<ProviderOperatingHour[]> {
    const hours = await this.prisma.provider_operating_hours.findMany({
      where: { providerId },
      orderBy: { dayOfWeek: 'asc' },
    });
    return hours.map(h => ({
      id: h.id,
      providerId: h.providerId,
      dayOfWeek: h.dayOfWeek,
      openTime: h.openTime,
      closeTime: h.closeTime,
      createdAt: h.createdAt,
    }));
  }

  async upsertOperatingHours(providerId: string, req: UpsertOperatingHoursRequest): Promise<ProviderOperatingHour[]> {
    // Replace strategy: delete existing then create provided set
    await this.prisma.provider_operating_hours.deleteMany({ where: { providerId } });
    await this.prisma.$transaction(
      req.hours.map(h =>
        this.prisma.provider_operating_hours.create({
          data: {
            id: generateId(),
            providerId,
            dayOfWeek: h.dayOfWeek,
            openTime: h.openTime,
            closeTime: h.closeTime,
          },
        })
      )
    );
    return this.getOperatingHours(providerId);
  }

  // Service areas
  async listServiceAreas(providerId: string): Promise<ProviderServiceArea[]> {
    const areas = await this.prisma.provider_service_areas.findMany({ where: { providerId } });
    return areas.map(a => ({
      id: a.id,
      providerId: a.providerId,
      streetAddress: a.streetAddress,
      zipCode: a.zipCode,
      city: a.city,
      state: a.state,
      isActive: a.isActive,
      createdAt: a.createdAt,
    }));
  }

  async addServiceArea(providerId: string, req: CreateServiceAreaRequest): Promise<ProviderServiceArea> {
    const created = await this.prisma.provider_service_areas.create({
      data: {
        id: generateId(),
        providerId,
        streetAddress: req.streetAddress,
        zipCode: req.zipCode,
        city: req.city,
        state: req.state,
        isActive: req.isActive ?? true,
      },
    });
    return {
      id: created.id,
      providerId: created.providerId,
      streetAddress: created.streetAddress,
      zipCode: created.zipCode,
      city: created.city,
      state: created.state,
      isActive: created.isActive,
      createdAt: created.createdAt,
    };
  }

  async removeServiceArea(providerId: string, areaId: string): Promise<boolean> {
    await this.prisma.provider_service_areas.delete({ where: { id: areaId } });
    return true;
  }

  // Ratings
  async getRatingDisplay(providerId: string): Promise<ProviderRatingDisplay | null> {
    const r = await this.prisma.provider_rating_displays.findUnique({ where: { providerId } });
    return r
      ? {
          providerId: r.providerId,
          averageRating: r.averageRating,
          reviewCount: r.reviewCount,
          lastUpdated: r.lastUpdated,
        }
      : null;
  }

  async incrementRating(providerId: string, rating: number): Promise<ProviderRatingDisplay> {
    const existing = await this.prisma.provider_rating_displays.findUnique({ where: { providerId } });
    if (!existing) {
      const created = await this.prisma.provider_rating_displays.create({
        data: { id: generateId(), providerId, averageRating: rating, reviewCount: 1, updatedAt: new Date() },
      });
      return {
        providerId,
        averageRating: created.averageRating,
        reviewCount: created.reviewCount,
        lastUpdated: created.lastUpdated,
      };
    }
    const totalRating = existing.averageRating * existing.reviewCount + rating;
    const newCount = existing.reviewCount + 1;
    const newAvg = totalRating / newCount;
    const updated = await this.prisma.provider_rating_displays.update({
      where: { providerId },
      data: { averageRating: newAvg, reviewCount: newCount, updatedAt: new Date() },
    });
    return {
      providerId,
      averageRating: updated.averageRating,
      reviewCount: updated.reviewCount,
      lastUpdated: updated.lastUpdated,
    };
  }
}
