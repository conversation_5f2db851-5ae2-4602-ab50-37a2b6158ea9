import {PrismaClient} from '@prisma/client';
import {createLogger} from './logger';

const logger = createLogger('StartupValidation');

export interface ValidationResult {
	success: boolean;
	errors: string[];
	warnings: string[];
}

export async function validateStartup(): Promise<ValidationResult> {
	const result: ValidationResult = {
		success: true,
		errors: [],
		warnings: [],
	};

	// 1. Validate environment variables
	validateEnvironmentVariables(result);

	// 2. Validate database connection
	await validateDatabaseConnection(result);

	// 3. Validate file system permissions
	validateFileSystemPermissions(result);

	// 4. Validate port availability
	await validatePortAvailability(result);

	return result;
}

function validateEnvironmentVariables(result: ValidationResult): void {
	const requiredVars = [
		'JWT_SECRET',
		'JWT_REFRESH_SECRET',
		'DATABASE_URL',
		'NODE_ENV',
	];

	const missingVars = requiredVars.filter((varName) => !process.env[varName]);

	if (missingVars.length > 0) {
		result.success = false;
		result.errors.push(
			`Missing required environment variables: ${missingVars.join(', ')}`
		);
	}

	// Check for weak JWT secrets
	if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
		result.warnings.push(
			'JWT_SECRET should be at least 32 characters long for security'
		);
	}

	if (
		process.env.JWT_REFRESH_SECRET &&
		process.env.JWT_REFRESH_SECRET.length < 32
	) {
		result.warnings.push(
			'JWT_REFRESH_SECRET should be at least 32 characters long for security'
		);
	}

	// Check for development environment warnings
	if (process.env.NODE_ENV === 'production') {
		if (
			process.env.JWT_SECRET?.includes('development') ||
			process.env.JWT_REFRESH_SECRET?.includes('development')
		) {
			result.warnings.push(
				'Using development JWT secrets in production environment'
			);
		}
	}
}

async function validateDatabaseConnection(
	result: ValidationResult
): Promise<void> {
	try {
		const prisma = new PrismaClient();
		await prisma.$connect();
		await prisma.$disconnect();
		logger.info('Database connection validation successful');
	} catch (error) {
		result.success = false;
		result.errors.push(
			`Database connection failed: ${
				error instanceof Error ? error.message : 'Unknown error'
			}`
		);
		logger.error('Database connection validation failed', error);
	}
}

function validateFileSystemPermissions(result: ValidationResult): void {
	try {
		const fs = require('fs');
		const path = require('path');
		const logsDir = path.join(process.cwd(), 'logs');

		// Test if we can create/write to logs directory
		if (!fs.existsSync(logsDir)) {
			fs.mkdirSync(logsDir, {recursive: true});
		}

		// Test write permission
		const testFile = path.join(logsDir, 'test-write-permission.tmp');
		fs.writeFileSync(testFile, 'test');
		fs.unlinkSync(testFile);

		logger.info('File system permissions validation successful');
	} catch (error) {
		result.warnings.push(
			`File system permissions issue: ${
				error instanceof Error ? error.message : 'Unknown error'
			}`
		);
		logger.warn('File system permissions validation failed', error);
	}
}

async function validatePortAvailability(
	result: ValidationResult
): Promise<void> {
	try {
		const net = require('net');
		const port = parseInt(process.env.PORT || '3000', 10);

		return new Promise((resolve) => {
			const server = net.createServer();

			server.listen(port, () => {
				server.close();
				logger.info(`Port ${port} availability validation successful`);
				resolve();
			});

			server.on('error', (error: any) => {
				if (error.code === 'EADDRINUSE') {
					result.success = false;
					result.errors.push(`Port ${port} is already in use`);
				} else {
					result.warnings.push(`Port validation issue: ${error.message}`);
				}
				resolve();
			});
		});
	} catch (error) {
		result.warnings.push(
			`Port validation failed: ${
				error instanceof Error ? error.message : 'Unknown error'
			}`
		);
	}
}

export function logValidationResult(result: ValidationResult): void {
	if (result.errors.length > 0) {
		logger.error('Startup validation failed with errors:');
		result.errors.forEach((error) => logger.error(`❌ ${error}`));
	}

	if (result.warnings.length > 0) {
		logger.warn('Startup validation completed with warnings:');
		result.warnings.forEach((warning) => logger.warn(`⚠️  ${warning}`));
	}

	if (result.success) {
		logger.info('✅ Startup validation completed successfully');
	}
}
