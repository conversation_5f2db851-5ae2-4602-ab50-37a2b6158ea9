{
  // JavaScript settings
  "javascript.preferences.includePackageJsonAutoImports": "on",
  "javascript.suggest.autoImports": true,
  "javascript.updateImportsOnFileMove.enabled": "always",
  "javascript.preferences.importModuleSpecifier": "relative",
  "javascript.preferences.quoteStyle": "single",
  "javascript.format.enable": true,
  "javascript.format.insertSpaceAfterCommaDelimiter": true,
  "javascript.format.insertSpaceAfterSemicolonInForStatements": true,
  "javascript.format.insertSpaceBeforeAndAfterBinaryOperators": true,
  "javascript.format.insertSpaceAfterKeywordsInControlFlowStatements": true,
  "javascript.format.insertSpaceAfterFunctionKeywordForAnonymousFunctions": true,
  "javascript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis": false,
  "javascript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets": false,
  "javascript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": true,
  "javascript.format.insertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces": false,
  "javascript.format.insertSpaceAfterOpeningAndBeforeClosingJsxExpressionBraces": false,
  "javascript.format.placeOpenBraceOnNewLineForFunctions": false,
  "javascript.format.placeOpenBraceOnNewLineForControlBlocks": false,

  // Editor settings
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.formatOnType": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.rulers": [80, 120],
  "editor.wordWrap": "bounded",
  "editor.wordWrapColumn": 120,
  "editor.minimap.enabled": true,
  "editor.minimap.showSlider": "always",
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": true,
  "editor.guides.indentation": true,
  "editor.suggestSelection": "first",
  "editor.acceptSuggestionOnCommitCharacter": false,
  "editor.acceptSuggestionOnEnter": "on",
  "editor.snippetSuggestions": "top",
  "editor.wordBasedSuggestions": "off",
  "editor.parameterHints.enabled": true,
  "editor.hover.enabled": true,
  "editor.quickSuggestionsDelay": 10,
  "editor.suggest.showKeywords": false,
  "editor.suggest.showSnippets": true,
  "editor.suggest.showUsers": false,
  "editor.suggest.showFunctions": true,
  "editor.suggest.showVariables": true,
  "editor.suggest.showClasses": true,
  "editor.suggest.showModules": true,
  "editor.suggest.showProperties": true,
  "editor.suggest.showEvents": true,
  "editor.suggest.showOperators": true,
  "editor.suggest.showUnits": true,
  "editor.suggest.showValues": true,
  "editor.suggest.showConstants": true,
  "editor.suggest.showEnums": true,
  "editor.suggest.showEnumMembers": true,
  "editor.suggest.showWords": true,
  "editor.suggest.showColors": true,
  "editor.suggest.showFiles": true,
  "editor.suggest.showReferences": true,
  "editor.suggest.showCustomcolors": true,
  "editor.suggest.showFolders": true,
  "editor.suggest.showTypeParameters": true,

  // File associations
  "files.associations": {
    "*.prisma": "prisma",
    "*.env": "dotenv",
    "*.env.local": "dotenv",
    "*.env.development": "dotenv",
    "*.env.production": "dotenv",
    "*.env.test": "dotenv"
  },

  // File settings
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/*.log": true,
    "**/coverage": true,
    "**/.nyc_output": true
  },
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  "files.eol": "\n",
  "files.encoding": "utf8",
  "files.autoSave": "onFocusChange",
  "files.autoSaveDelay": 1000,

  // Search settings

  // Terminal settings
  "terminal.integrated.defaultProfile.windows": "PowerShell",
  "terminal.integrated.profiles.windows": {
    "PowerShell": {
      "source": "PowerShell",
      "icon": "terminal-powershell"
    },
    "Command Prompt": {
      "path": ["${env:windir}\\Sysnative\\cmd.exe", "${env:windir}\\System32\\cmd.exe"],
      "args": [],
      "icon": "terminal-cmd"
    },
    "Git Bash": {
      "source": "Git Bash"
    }
  },
  "terminal.integrated.cwd": "${workspaceFolder}",
  "terminal.integrated.env.windows": {
    "CHOKIDAR_USEPOLLING": "1"
  },

  // Git settings
  "git.enableSmartCommit": true,
  "git.confirmSync": false,
  "git.autofetch": true,
  "git.autofetchPeriod": 180,
  "git.ignoreLimitWarning": true,
  "git.ignoreSubmodules": true,
  "git.showPushSuccessNotification": true,
  "git.enableCommitSigning": false,

  // ESLint settings
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"],
  "eslint.format.enable": true,
  "eslint.workingDirectories": ["./backend", "./frontend"],

  // Prettier settings
  "prettier.singleQuote": true,
  "prettier.trailingComma": "es5",
  "prettier.printWidth": 120,
  "prettier.tabWidth": 2,
  "prettier.useTabs": false,
  "prettier.semi": true,
  "prettier.bracketSpacing": true,
  "prettier.bracketSameLine": false,
  "prettier.arrowParens": "avoid",
  "prettier.endOfLine": "lf",
  "prettier.quoteProps": "as-needed",
  "prettier.jsxSingleQuote": true,
  "prettier.proseWrap": "preserve",

  // Debug settings
  "debug.console.fontSize": 14,
  "debug.console.lineHeight": 20,
  "debug.inlineValues": "auto",
  "debug.showBreakpointsInOverviewRuler": true,
  "debug.showInlineBreakpointCandidates": true,

  // Extensions settings
  "prisma.format.enable": true,
  "prisma.validate.enable": true,
  "prisma.completion.enable": true,
  "prisma.hover.enable": true,
  "prisma.goToDefinition.enable": true,
  "prisma.findReferences.enable": true,
  "prisma.rename.enable": true,
  "prisma.syntaxHighlighting.enable": true,

  // Auto-save and format settings
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit",
      "source.organizeImports": "explicit"
    }
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit",
      "source.organizeImports": "explicit"
    }
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.wordWrap": "on"
  },
  "[prisma]": {
    "editor.defaultFormatter": "Prisma.prisma",
    "editor.formatOnSave": true
  },

  // Workspace-specific settings
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.preferences.quoteStyle": "single",
  "typescript.format.enable": true,
  "typescript.format.insertSpaceAfterCommaDelimiter": true,
  "typescript.format.insertSpaceAfterSemicolonInForStatements": true,
  "typescript.format.insertSpaceBeforeAndAfterBinaryOperators": true,
  "typescript.format.insertSpaceAfterKeywordsInControlFlowStatements": true,
  "typescript.format.insertSpaceAfterFunctionKeywordForAnonymousFunctions": true,
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis": false,
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets": false,
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": true,
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces": false,
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingJsxExpressionBraces": false,
  "typescript.format.placeOpenBraceOnNewLineForFunctions": false,
  "typescript.format.placeOpenBraceOnNewLineForControlBlocks": false,

  // Performance settings
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/dist/**": true,
    "**/build/**": true,
    "**/.git/**": true,
    "**/coverage/**": true,
    "**/.nyc_output/**": true,
    "**/tmp/**": true,
    "**/temp/**": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.git": true,
    "**/coverage": true,
    "**/.nyc_output": true,
    "**/*.log": true,
    "**/tmp": true,
    "**/temp": true
  },

  // Emmet settings
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html",
    "javascript": "html",
    "javascriptreact": "html"
  },
  "emmet.triggerExpansionOnTab": true,
  "emmet.showAbbreviationSuggestions": true,
  "emmet.showExpandedAbbreviation": "always",
  "emmet.showSuggestionsAsSnippets": true,

  // IntelliSense settings
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": true
  }
}
