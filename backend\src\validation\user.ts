import { UserRole } from '@prisma/client';
import <PERSON><PERSON> from 'joi';

// Reuse existing validation schemas from auth.ts
import { emailSchema, nameSchema, passwordSchema, phoneSchema } from './auth';

// User creation validation schema
export const createUserSchema = Joi.object({
  email: emailSchema,
  password: passwordSchema,
  firstName: nameSchema.messages({
    'any.required': 'First name is required',
  }),
  lastName: nameSchema.messages({
    'any.required': 'Last name is required',
  }),
  phone: phoneSchema,
  role: Joi.string()
    .valid(...Object.values(UserRole))
    .required()
    .messages({
      'any.only': 'Role must be one of: CUSTOMER, PROVIDER, ADMIN',
      'any.required': 'Role is required',
    }),
  isActive: Joi.boolean().default(true),
  isVerified: Joi.boolean().default(false),
});

// User update validation schema
export const updateUserSchema = Joi.object({
  firstName: nameSchema.optional(),
  lastName: nameSchema.optional(),
  email: emailSchema.optional(),
  phone: phoneSchema.optional(),
  avatar: Joi.string().uri().max(500).optional().messages({
    'string.uri': 'Avatar must be a valid URL',
    'string.max': 'Avatar URL must not exceed 500 characters',
  }),
  role: Joi.string()
    .valid(...Object.values(UserRole))
    .optional()
    .messages({
      'any.only': 'Role must be one of: CUSTOMER, PROVIDER, ADMIN',
    }),
  isActive: Joi.boolean().optional(),
  isVerified: Joi.boolean().optional(),
});

// User list query validation schema
export const userListQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1).messages({
    'number.base': 'Page must be a number',
    'number.integer': 'Page must be an integer',
    'number.min': 'Page must be at least 1',
  }),
  limit: Joi.number().integer().min(1).max(100).default(20).messages({
    'number.base': 'Limit must be a number',
    'number.integer': 'Limit must be an integer',
    'number.min': 'Limit must be at least 1',
    'number.max': 'Limit must not exceed 100',
  }),
  search: Joi.string().max(255).trim().optional().allow('').messages({
    'string.max': 'Search term must not exceed 255 characters',
  }),
  role: Joi.string()
    .valid(...Object.values(UserRole))
    .optional()
    .messages({
      'any.only': 'Role must be one of: CUSTOMER, PROVIDER, ADMIN',
    }),
  isActive: Joi.boolean().optional(),
  isVerified: Joi.boolean().optional(),
  sortBy: Joi.string().valid('createdAt', 'updatedAt', 'firstName', 'lastName', 'email').default('createdAt').messages({
    'any.only': 'Sort by must be one of: createdAt, updatedAt, firstName, lastName, email',
  }),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc').messages({
    'any.only': 'Sort order must be either asc or desc',
  }),
});

// User ID parameter validation schema
export const userIdParamSchema = Joi.object({
  id: Joi.string().required().messages({
    'any.required': 'User ID is required',
  }),
});

// Bulk user operations validation schema
export const bulkUserOperationSchema = Joi.object({
  userIds: Joi.array().items(Joi.string()).min(1).max(100).required().messages({
    'array.min': 'At least one user ID is required',
    'array.max': 'Cannot process more than 100 users at once',
    'any.required': 'User IDs are required',
  }),
  action: Joi.string().valid('activate', 'deactivate', 'verify', 'unverify', 'delete').required().messages({
    'any.only': 'Action must be one of: activate, deactivate, verify, unverify, delete',
    'any.required': 'Action is required',
  }),
});

// Export validation middleware
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
      }));

      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: 'Invalid request data',
        validationErrors,
      });
    }

    req.body = value;
    next();
  };
};

// Query validation middleware
export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
      }));

      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: 'Invalid query parameters',
        validationErrors,
      });
    }

    req.query = value;
    next();
  };
};

// Parameter validation middleware
export const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.params, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
      }));

      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: 'Invalid path parameters',
        validationErrors,
      });
    }

    req.params = value;
    next();
  };
};
