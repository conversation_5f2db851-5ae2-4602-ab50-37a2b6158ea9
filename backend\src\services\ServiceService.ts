import { ServicePricingType } from '@prisma/client';
import { ConflictError, NotFoundError, ValidationError } from '../middleware/errorHandler';
import { IServiceRepository } from '../repositories/CompositeServiceRepository';
import {
  CreateServiceCategoryData,
  CreateServiceCategoryRequest,
  CreateServiceData,
  CreateServiceFormFieldData,
  CreateServiceFormFieldOptionData,
  CreateServiceFormFieldOptionRequest,
  CreateServiceFormFieldRequest,
  CreateServiceRequest,
  PriceCalculationRequest,
  PriceCalculationResponse,
  ServiceCategoryDetail,
  ServiceCategorySummary,
  ServiceDetail,
  ServiceFormFieldDetail,
  ServiceFormFieldOptionDetail,
  ServiceListQuery,
  ServiceListResponse,
  ServiceSearchQuery,
  ServiceSearchResult,
  ServiceStats,
  ServiceSummary,
  UpdateServiceCategoryData,
  UpdateServiceCategoryRequest,
  UpdateServiceData,
  UpdateServiceFormFieldData,
  UpdateServiceFormFieldOptionData,
  UpdateServiceFormFieldOptionRequest,
  UpdateServiceFormFieldRequest,
  UpdateServiceRequest,
} from '../types/service';
import { generateId } from '../utils/appHelpers';
import { createLogger } from '../utils/logger';
import { ValidationUtils } from '../utils/ValidationUtils';

// Service interface for dependency inversion
export interface IServiceService {
  // Service CRUD operations
  getServiceById(id: string): Promise<ServiceDetail>;
  createService(serviceData: CreateServiceRequest): Promise<ServiceDetail>;
  updateService(id: string, data: UpdateServiceRequest): Promise<ServiceDetail>;
  deleteService(id: string): Promise<boolean>;

  // Service list and search operations
  getServices(query: ServiceListQuery): Promise<ServiceListResponse>;
  searchServices(query: ServiceSearchQuery): Promise<ServiceSearchResult>;
  getServicesByCategory(categoryId: string): Promise<ServiceSummary[]>;
  getPopularServices(limit?: number): Promise<ServiceSummary[]>;
  getServicesByPricingType(pricingType: ServicePricingType): Promise<ServiceSummary[]>;

  // Service statistics and analytics
  getServiceStats(): Promise<ServiceStats>;

  // Service category operations
  getServiceCategories(): Promise<ServiceCategorySummary[]>;
  getServiceCategoryById(id: string): Promise<ServiceCategoryDetail>;
  getServiceCategoryByRoute(route: string): Promise<ServiceCategoryDetail>;
  createServiceCategory(categoryData: CreateServiceCategoryRequest): Promise<ServiceCategoryDetail>;
  updateServiceCategory(id: string, data: UpdateServiceCategoryRequest): Promise<ServiceCategoryDetail>;
  deleteServiceCategory(id: string): Promise<boolean>;

  // Service form field operations
  getServiceFormFields(serviceId: string): Promise<ServiceFormFieldDetail[]>;
  getServiceFormFieldById(id: string): Promise<ServiceFormFieldDetail>;
  createServiceFormField(fieldData: CreateServiceFormFieldRequest): Promise<ServiceFormFieldDetail>;
  updateServiceFormField(id: string, data: UpdateServiceFormFieldRequest): Promise<ServiceFormFieldDetail>;
  deleteServiceFormField(id: string): Promise<boolean>;

  // Service form field option operations
  getServiceFormFieldOptions(fieldId: string): Promise<ServiceFormFieldOptionDetail[]>;
  createServiceFormFieldOption(optionData: CreateServiceFormFieldOptionRequest): Promise<ServiceFormFieldOptionDetail>;
  updateServiceFormFieldOption(
    id: string,
    data: UpdateServiceFormFieldOptionRequest
  ): Promise<ServiceFormFieldOptionDetail>;
  deleteServiceFormFieldOption(id: string): Promise<boolean>;

  // Price calculation
  calculateServicePrice(request: PriceCalculationRequest): Promise<PriceCalculationResponse>;

  // Utility operations
  serviceExists(id: string): Promise<boolean>;
  serviceCategoryExists(id: string): Promise<boolean>;
  countServices(): Promise<number>;
  countServicesByCategory(categoryId: string): Promise<number>;
}

// Service implementation
export class ServiceService implements IServiceService {
  private logger =
    (createLogger && createLogger('ServiceService')) ||
    ({
      info: () => undefined,
      error: () => undefined,
      warn: () => undefined,
      debug: () => undefined,
    } as unknown as ReturnType<typeof createLogger>);

  constructor(private serviceRepository: IServiceRepository) {
    this.logger.info('ServiceService initialized');
  }

  // Service CRUD operations
  async getServiceById(id: string): Promise<ServiceDetail> {
    this.logger.info('Getting service by ID', { serviceId: id });

    const service = await this.serviceRepository.findServiceById(id, true);
    if (!service) {
      throw new NotFoundError('Service not found');
    }

    this.logger.info('Service retrieved successfully', { serviceId: id, serviceName: service.name });
    return service;
  }

  async createService(serviceData: CreateServiceRequest): Promise<ServiceDetail> {
    this.logger.info('Creating new service', { serviceName: serviceData.name, categoryId: serviceData.categoryId });

    // Validate category exists
    const categoryExists = await this.serviceRepository.serviceCategoryExists(serviceData.categoryId);
    if (!categoryExists) {
      throw new ValidationError('Service category does not exist');
    }

    // Validate business rules
    ValidationUtils.validateServiceData(serviceData);

    // Prepare data for storage
    const storageData: CreateServiceData = {
      id: generateId(),
      name: serviceData.name.trim(),
      description: serviceData.description.trim(),
      detailedDesc: serviceData.detailedDesc?.trim() || null,
      features: serviceData.features || [],
      notes: serviceData.notes?.trim() || null,
      categoryId: serviceData.categoryId,
      image: serviceData.image.trim(),
      basePrice: serviceData.basePrice,
      pricingType: serviceData.pricingType || ServicePricingType.FIXED,
      isActive: serviceData.isActive !== undefined ? serviceData.isActive : true,
      sortOrder: serviceData.sortOrder || 0,
    };

    // Create service
    const createdService = await this.serviceRepository.createService(storageData);

    // Return full service details
    const serviceDetail = await this.serviceRepository.findServiceById(createdService.id, true);

    this.logger.info('Service created successfully', {
      serviceId: createdService.id,
      serviceName: createdService.name,
      categoryId: createdService.categoryId,
    });

    return serviceDetail!;
  }

  async updateService(id: string, data: UpdateServiceRequest): Promise<ServiceDetail> {
    this.logger.info('Updating service', { serviceId: id, updatedFields: Object.keys(data) });

    // Check if service exists
    const existingService = await this.serviceRepository.findServiceById(id, false);
    if (!existingService) {
      throw new NotFoundError('Service not found');
    }

    // Validate category exists if being updated
    if (data.categoryId && data.categoryId !== existingService.categoryId) {
      const categoryExists = await this.serviceRepository.serviceCategoryExists(data.categoryId);
      if (!categoryExists) {
        throw new ValidationError('Service category does not exist');
      }
    }

    // Validate business rules for updated data
    if (Object.keys(data).length > 0) {
      ValidationUtils.validateServiceUpdateData(data);
    }

    // Prepare data for storage
    const updateData: UpdateServiceData = {};

    if (data.name !== undefined) updateData.name = data.name.trim();
    if (data.description !== undefined) updateData.description = data.description.trim();
    if (data.detailedDesc !== undefined) updateData.detailedDesc = data.detailedDesc?.trim() || null;
    if (data.features !== undefined) updateData.features = data.features;
    if (data.notes !== undefined) updateData.notes = data.notes?.trim() || null;
    if (data.categoryId !== undefined) updateData.categoryId = data.categoryId;
    if (data.image !== undefined) updateData.image = data.image.trim();
    if (data.basePrice !== undefined) updateData.basePrice = data.basePrice;
    if (data.pricingType !== undefined) updateData.pricingType = data.pricingType;
    if (data.isActive !== undefined) updateData.isActive = data.isActive;
    if (data.sortOrder !== undefined) updateData.sortOrder = data.sortOrder;

    // Update service
    await this.serviceRepository.updateService(id, updateData);

    // Return updated service details
    const updatedService = await this.serviceRepository.findServiceById(id, true);

    this.logger.info('Service updated successfully', { serviceId: id });
    return updatedService!;
  }

  async deleteService(id: string): Promise<boolean> {
    this.logger.info('Deleting service', { serviceId: id });

    // Check if service exists
    const existingService = await this.serviceRepository.findServiceById(id, false);
    if (!existingService) {
      throw new NotFoundError('Service not found');
    }

    const deleted = await this.serviceRepository.deleteService(id);

    if (deleted) {
      this.logger.info('Service deleted successfully', { serviceId: id });
    } else {
      this.logger.error('Failed to delete service', { serviceId: id });
    }

    return deleted;
  }

  // Service list and search operations
  async getServices(query: ServiceListQuery): Promise<ServiceListResponse> {
    this.logger.info('Getting services list', { query });

    // Validate query parameters
    ValidationUtils.validateServiceListQuery(query);

    const result = await this.serviceRepository.getServices(query);

    this.logger.info('Services list retrieved successfully', {
      totalServices: result.pagination.total,
      page: result.pagination.page,
      limit: result.pagination.limit,
    });

    return result;
  }

  async searchServices(query: ServiceSearchQuery): Promise<ServiceSearchResult> {
    this.logger.info('Searching services', { query });

    // Validate search query parameters
    ValidationUtils.validateServiceSearchQuery(query);

    const result = await this.serviceRepository.searchServices(query);

    this.logger.info('Service search completed', {
      searchQuery: query.q,
      resultsCount: result.total,
    });

    return result;
  }

  async getServicesByCategory(categoryId: string): Promise<ServiceSummary[]> {
    this.logger.info('Getting services by category', { categoryId });

    // Validate category exists
    const categoryExists = await this.serviceRepository.serviceCategoryExists(categoryId);
    if (!categoryExists) {
      throw new NotFoundError('Service category not found');
    }

    const services = await this.serviceRepository.getServicesByCategory(categoryId);

    this.logger.info('Services by category retrieved successfully', {
      categoryId,
      count: services.length,
    });

    return services;
  }

  async getPopularServices(limit: number = 10): Promise<ServiceSummary[]> {
    this.logger.info('Getting popular services', { limit });

    ValidationUtils.validatePopularServicesLimit(limit);

    const services = await this.serviceRepository.getPopularServices(limit);

    this.logger.info('Popular services retrieved successfully', { count: services.length });

    return services;
  }

  async getServicesByPricingType(pricingType: ServicePricingType): Promise<ServiceSummary[]> {
    this.logger.info('Getting services by pricing type', { pricingType });

    const services = await this.serviceRepository.getServicesByPricingType(pricingType);

    this.logger.info('Services by pricing type retrieved successfully', {
      pricingType,
      count: services.length,
    });

    return services;
  }

  // Service statistics
  async getServiceStats(): Promise<ServiceStats> {
    this.logger.info('Getting service statistics');

    const stats = await this.serviceRepository.getServiceStats();

    this.logger.info('Service statistics retrieved successfully', {
      totalServices: stats.total,
      activeServices: stats.active,
      averageBasePrice: stats.averageBasePrice,
    });

    return stats;
  }

  // Service category operations
  async getServiceCategories(): Promise<ServiceCategorySummary[]> {
    this.logger.info('Getting service categories');

    const categories = await this.serviceRepository.getServiceCategories();

    this.logger.info('Service categories retrieved successfully', { count: categories.length });

    return categories;
  }

  async getServiceCategoryById(id: string): Promise<ServiceCategoryDetail> {
    this.logger.info('Getting service category by ID', { categoryId: id });

    const category = await this.serviceRepository.findServiceCategoryById(id);
    if (!category) {
      throw new NotFoundError('Service category not found');
    }

    this.logger.info('Service category retrieved successfully', { categoryId: id, categoryName: category.name });
    return category;
  }

  async getServiceCategoryByRoute(route: string): Promise<ServiceCategoryDetail> {
    this.logger.info('Getting service category by route', { route });

    const category = await this.serviceRepository.findServiceCategoryByRoute(route);
    if (!category) {
      throw new NotFoundError('Service category not found');
    }

    this.logger.info('Service category retrieved successfully', { route, categoryName: category.name });
    return category;
  }

  async createServiceCategory(categoryData: CreateServiceCategoryRequest): Promise<ServiceCategoryDetail> {
    this.logger.info('Creating new service category', { categoryName: categoryData.name, route: categoryData.route });

    // Validate category data
    ValidationUtils.validateServiceCategoryData(categoryData);

    // Check if route is unique
    const existingCategory = await this.serviceRepository.findServiceCategoryByRoute(categoryData.route);
    if (existingCategory) {
      throw new ConflictError('Service category with this route already exists');
    }

    // Prepare data for storage
    const storageData: CreateServiceCategoryData = {
      id: generateId(),
      name: categoryData.name.trim(),
      description: categoryData.description?.trim() || null,
      icon: categoryData.icon?.trim() || null,
      route: categoryData.route.trim(),
      isActive: categoryData.isActive !== undefined ? categoryData.isActive : true,
      sortOrder: categoryData.sortOrder || 0,
    };

    // Create category
    await this.serviceRepository.createServiceCategory(storageData);

    // Return full category details
    const categoryDetail = await this.serviceRepository.findServiceCategoryById(storageData.id);

    this.logger.info('Service category created successfully', {
      categoryId: storageData.id,
      categoryName: storageData.name,
    });

    return categoryDetail!;
  }

  async updateServiceCategory(id: string, data: UpdateServiceCategoryRequest): Promise<ServiceCategoryDetail> {
    this.logger.info('Updating service category', { categoryId: id, updatedFields: Object.keys(data) });

    // Check if category exists
    const existingCategory = await this.serviceRepository.findServiceCategoryById(id);
    if (!existingCategory) {
      throw new NotFoundError('Service category not found');
    }

    // Check if route is unique if being updated
    if (data.route && data.route !== existingCategory.route) {
      const routeExists = await this.serviceRepository.findServiceCategoryByRoute(data.route);
      if (routeExists) {
        throw new ConflictError('Service category with this route already exists');
      }
    }

    // Validate updated data
    if (Object.keys(data).length > 0) {
      ValidationUtils.validateServiceCategoryUpdateData(data);
    }

    // Prepare data for storage
    const updateData: UpdateServiceCategoryData = {};

    if (data.name !== undefined) updateData.name = data.name.trim();
    if (data.description !== undefined) updateData.description = data.description?.trim() || null;
    if (data.icon !== undefined) updateData.icon = data.icon?.trim() || null;
    if (data.route !== undefined) updateData.route = data.route.trim();
    if (data.isActive !== undefined) updateData.isActive = data.isActive;
    if (data.sortOrder !== undefined) updateData.sortOrder = data.sortOrder;

    // Update category
    await this.serviceRepository.updateServiceCategory(id, updateData);

    // Return updated category details
    const updatedCategory = await this.serviceRepository.findServiceCategoryById(id);

    this.logger.info('Service category updated successfully', { categoryId: id });
    return updatedCategory!;
  }

  async deleteServiceCategory(id: string): Promise<boolean> {
    this.logger.info('Deleting service category', { categoryId: id });

    // Check if category exists
    const existingCategory = await this.serviceRepository.findServiceCategoryById(id);
    if (!existingCategory) {
      throw new NotFoundError('Service category not found');
    }

    // Check if category has services
    const serviceCount = await this.serviceRepository.countServicesByCategory(id);
    if (serviceCount > 0) {
      throw new ConflictError('Cannot delete service category that has associated services');
    }

    const deleted = await this.serviceRepository.deleteServiceCategory(id);

    if (deleted) {
      this.logger.info('Service category deleted successfully', { categoryId: id });
    } else {
      this.logger.error('Failed to delete service category', { categoryId: id });
    }

    return deleted;
  }

  // Service form field operations
  async getServiceFormFields(serviceId: string): Promise<ServiceFormFieldDetail[]> {
    this.logger.info('Getting service form fields', { serviceId });

    // Validate service exists
    const serviceExists = await this.serviceRepository.serviceExists(serviceId);
    if (!serviceExists) {
      throw new NotFoundError('Service not found');
    }

    const fields = await this.serviceRepository.getServiceFormFields(serviceId);

    this.logger.info('Service form fields retrieved successfully', {
      serviceId,
      count: fields.length,
    });

    return fields;
  }

  async getServiceFormFieldById(id: string): Promise<ServiceFormFieldDetail> {
    this.logger.info('Getting service form field by ID', { fieldId: id });

    const field = await this.serviceRepository.findServiceFormFieldById(id);
    if (!field) {
      throw new NotFoundError('Service form field not found');
    }

    this.logger.info('Service form field retrieved successfully', { fieldId: id, fieldName: field.name });
    return field;
  }

  async createServiceFormField(fieldData: CreateServiceFormFieldRequest): Promise<ServiceFormFieldDetail> {
    this.logger.info('Creating service form field', { serviceId: fieldData.serviceId, fieldName: fieldData.name });

    // Validate service exists
    const serviceExists = await this.serviceRepository.serviceExists(fieldData.serviceId);
    if (!serviceExists) {
      throw new NotFoundError('Service not found');
    }

    // Validate field data
    ValidationUtils.validateServiceFormFieldData(fieldData);

    // Prepare data for storage
    const storageData: CreateServiceFormFieldData = {
      id: generateId(),
      serviceId: fieldData.serviceId,
      name: fieldData.name.trim(),
      label: fieldData.label.trim(),
      type: fieldData.type,
      required: fieldData.required !== undefined ? fieldData.required : false,
      placeholder: fieldData.placeholder?.trim() || null,
      defaultValue: fieldData.defaultValue?.trim() || null,
      validation: fieldData.validation || null,
      sortOrder: fieldData.sortOrder || 0,
      isActive: fieldData.isActive !== undefined ? fieldData.isActive : true,
    };

    // Create field
    await this.serviceRepository.createServiceFormField(storageData);

    // Create options if provided
    if (fieldData.options && fieldData.options.length > 0) {
      for (const optionData of fieldData.options) {
        const optionStorageData: CreateServiceFormFieldOptionData = {
          id: generateId(),
          fieldId: storageData.id,
          value: optionData.value.trim(),
          label: optionData.label.trim(),
          priceModifier: optionData.priceModifier || null,
          sortOrder: optionData.sortOrder || 0,
          isActive: optionData.isActive !== undefined ? optionData.isActive : true,
        };
        await this.serviceRepository.createServiceFormFieldOption(optionStorageData);
      }
    }

    // Return full field details
    const fieldDetail = await this.serviceRepository.findServiceFormFieldById(storageData.id);

    this.logger.info('Service form field created successfully', {
      fieldId: storageData.id,
      fieldName: storageData.name,
    });

    return fieldDetail!;
  }

  async updateServiceFormField(id: string, data: UpdateServiceFormFieldRequest): Promise<ServiceFormFieldDetail> {
    this.logger.info('Updating service form field', { fieldId: id, updatedFields: Object.keys(data) });

    // Check if field exists
    const existingField = await this.serviceRepository.findServiceFormFieldById(id);
    if (!existingField) {
      throw new NotFoundError('Service form field not found');
    }

    // Validate updated data
    if (Object.keys(data).length > 0) {
      ValidationUtils.validateServiceFormFieldUpdateData(data);
    }

    // Prepare data for storage
    const updateData: UpdateServiceFormFieldData = {};

    if (data.name !== undefined) updateData.name = data.name.trim();
    if (data.label !== undefined) updateData.label = data.label.trim();
    if (data.type !== undefined) updateData.type = data.type;
    if (data.required !== undefined) updateData.required = data.required;
    if (data.placeholder !== undefined) updateData.placeholder = data.placeholder?.trim() || null;
    if (data.defaultValue !== undefined) updateData.defaultValue = data.defaultValue?.trim() || null;
    if (data.validation !== undefined) updateData.validation = data.validation;
    if (data.sortOrder !== undefined) updateData.sortOrder = data.sortOrder;
    if (data.isActive !== undefined) updateData.isActive = data.isActive;

    // Update field
    await this.serviceRepository.updateServiceFormField(id, updateData);

    // Return updated field details
    const updatedField = await this.serviceRepository.findServiceFormFieldById(id);

    this.logger.info('Service form field updated successfully', { fieldId: id });
    return updatedField!;
  }

  async deleteServiceFormField(id: string): Promise<boolean> {
    this.logger.info('Deleting service form field', { fieldId: id });

    // Check if field exists
    const existingField = await this.serviceRepository.findServiceFormFieldById(id);
    if (!existingField) {
      throw new NotFoundError('Service form field not found');
    }

    const deleted = await this.serviceRepository.deleteServiceFormField(id);

    if (deleted) {
      this.logger.info('Service form field deleted successfully', { fieldId: id });
    } else {
      this.logger.error('Failed to delete service form field', { fieldId: id });
    }

    return deleted;
  }

  // Service form field option operations
  async getServiceFormFieldOptions(fieldId: string): Promise<ServiceFormFieldOptionDetail[]> {
    this.logger.info('Getting service form field options', { fieldId });

    // Validate field exists
    const fieldExists = await this.serviceRepository.findServiceFormFieldById(fieldId);
    if (!fieldExists) {
      throw new NotFoundError('Service form field not found');
    }

    const options = await this.serviceRepository.getServiceFormFieldOptions(fieldId);

    this.logger.info('Service form field options retrieved successfully', {
      fieldId,
      count: options.length,
    });

    return options;
  }

  async createServiceFormFieldOption(
    optionData: CreateServiceFormFieldOptionRequest
  ): Promise<ServiceFormFieldOptionDetail> {
    this.logger.info('Creating service form field option', {
      fieldId: optionData.fieldId,
      optionValue: optionData.value,
    });

    // Validate field exists
    const fieldExists = await this.serviceRepository.findServiceFormFieldById(optionData.fieldId);
    if (!fieldExists) {
      throw new NotFoundError('Service form field not found');
    }

    // Validate option data
    ValidationUtils.validateServiceFormFieldOptionData(optionData);

    // Prepare data for storage
    const storageData: CreateServiceFormFieldOptionData = {
      id: generateId(),
      fieldId: optionData.fieldId,
      value: optionData.value.trim(),
      label: optionData.label.trim(),
      priceModifier: optionData.priceModifier || null,
      sortOrder: optionData.sortOrder || 0,
      isActive: optionData.isActive !== undefined ? optionData.isActive : true,
    };

    // Create option
    await this.serviceRepository.createServiceFormFieldOption(storageData);

    // Return option details
    const optionDetail = await this.serviceRepository.getServiceFormFieldOptions(optionData.fieldId);
    const createdOption = optionDetail.find(opt => opt.id === storageData.id);

    this.logger.info('Service form field option created successfully', {
      optionId: storageData.id,
      optionValue: storageData.value,
    });

    return createdOption!;
  }

  async updateServiceFormFieldOption(
    id: string,
    data: UpdateServiceFormFieldOptionRequest
  ): Promise<ServiceFormFieldOptionDetail> {
    this.logger.info('Updating service form field option', { optionId: id, updatedFields: Object.keys(data) });

    // Check if option exists
    const existingOption = await this.serviceRepository.findServiceFormFieldOptionById(id);
    if (!existingOption) {
      throw new NotFoundError('Service form field option not found');
    }

    // Validate updated data
    if (Object.keys(data).length > 0) {
      ValidationUtils.validateServiceFormFieldOptionUpdateData(data);
    }

    // Prepare data for storage
    const updateData: UpdateServiceFormFieldOptionData = {};

    if (data.value !== undefined) updateData.value = data.value.trim();
    if (data.label !== undefined) updateData.label = data.label.trim();
    if (data.priceModifier !== undefined) updateData.priceModifier = data.priceModifier;
    if (data.sortOrder !== undefined) updateData.sortOrder = data.sortOrder;
    if (data.isActive !== undefined) updateData.isActive = data.isActive;

    // Update option
    await this.serviceRepository.updateServiceFormFieldOption(id, updateData);

    // Return updated option details
    const updatedOption = await this.serviceRepository.findServiceFormFieldOptionById(id);

    this.logger.info('Service form field option updated successfully', { optionId: id });
    return updatedOption!;
  }

  async deleteServiceFormFieldOption(id: string): Promise<boolean> {
    this.logger.info('Deleting service form field option', { optionId: id });

    const deleted = await this.serviceRepository.deleteServiceFormFieldOption(id);

    if (deleted) {
      this.logger.info('Service form field option deleted successfully', { optionId: id });
    } else {
      this.logger.error('Failed to delete service form field option', { optionId: id });
    }

    return deleted;
  }

  // Price calculation
  async calculateServicePrice(request: PriceCalculationRequest): Promise<PriceCalculationResponse> {
    this.logger.info('Calculating service price', { serviceId: request.serviceId, providerId: request.providerId });

    // Validate service exists
    const serviceExists = await this.serviceRepository.serviceExists(request.serviceId);
    if (!serviceExists) {
      throw new NotFoundError('Service not found');
    }

    // Validate request data
    ValidationUtils.validatePriceCalculationRequest(request);

    const result = await this.serviceRepository.calculateServicePrice(request);

    this.logger.info('Service price calculated successfully', {
      serviceId: request.serviceId,
      total: result.total,
      quantity: result.quantity,
    });

    return result;
  }

  // Utility operations
  async serviceExists(id: string): Promise<boolean> {
    return this.serviceRepository.serviceExists(id);
  }

  async serviceCategoryExists(id: string): Promise<boolean> {
    return this.serviceRepository.serviceCategoryExists(id);
  }

  async countServices(): Promise<number> {
    return this.serviceRepository.countServices();
  }

  async countServicesByCategory(categoryId: string): Promise<number> {
    return this.serviceRepository.countServicesByCategory(categoryId);
  }
}
