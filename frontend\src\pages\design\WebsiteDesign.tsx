import React from 'react';
import { Link } from 'react-router-dom';
import { 
  <PERSON><PERSON><PERSON>cle, 
  Clock, 
  Shield, 
  Award,
  ChevronDown,
  ChevronUp,
  Star,
  Monitor,
  Smartphone,
  Globe,
  Zap,
  ArrowLeft,
  Code,
  Palette,
  Search,
  ShoppingCart,
  Lightbulb,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

const WebsiteDesign: React.FC = () => {
  const [openFaq, setOpenFaq] = React.useState<number | null>(null);
  const [portfolioScrollPosition, setPortfolioScrollPosition] = React.useState(0);
  const portfolioScrollRef = React.useRef<HTMLDivElement>(null);

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  const scrollPortfolio = (direction: 'left' | 'right') => {
    if (portfolioScrollRef.current) {
      const scrollAmount = 280; // Width of card + gap
      const currentScroll = portfolioScrollRef.current.scrollLeft;
      const newScroll = direction === 'left' 
        ? currentScroll - scrollAmount 
        : currentScroll + scrollAmount;
      
      portfolioScrollRef.current.scrollTo({
        left: newScroll,
        behavior: 'smooth'
      });
      setPortfolioScrollPosition(newScroll);
    }
  };

  const handlePortfolioScroll = () => {
    if (portfolioScrollRef.current) {
      setPortfolioScrollPosition(portfolioScrollRef.current.scrollLeft);
    }
  };

  const faqs = [
    {
      question: "What's included in a website design package?",
      answer: "Our website design packages include custom design mockups, responsive layouts, up to 5-10 pages, contact forms, SEO optimization, mobile optimization, and basic content management system setup."
    },
    {
      question: "How long does it take to design a website?",
      answer: "Website design typically takes 2-4 weeks depending on complexity. Simple business websites take 2 weeks, while e-commerce or complex sites may take 3-4 weeks including revisions."
    },
    {
      question: "Will my website be mobile-friendly?",
      answer: "Absolutely! All our websites are designed with a mobile-first approach, ensuring they look and function perfectly on smartphones, tablets, and desktop computers."
    },
    {
      question: "Do you provide website hosting and domain services?",
      answer: "While we focus on design, we can recommend reliable hosting providers and help you set up your domain. We also provide guidance on hosting requirements for your specific website."
    },
    {
      question: "Can you redesign my existing website?",
      answer: "Yes! We offer website redesign services to modernize your existing site, improve user experience, and ensure it meets current web standards and best practices."
    },
    {
      question: "Will I be able to update my website content myself?",
      answer: "Yes, we build websites with user-friendly content management systems that allow you to easily update text, images, and basic content without technical knowledge."
    }
  ];

  const portfolioSamples = [
    {
      image: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75",
      title: "E-commerce Store",
      description: "Modern online store with shopping cart and payment integration"
    },
    {
      image: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75",
      title: "Restaurant Website",
      description: "Elegant restaurant site with menu and reservation system"
    },
    {
      image: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75",
      title: "Corporate Website",
      description: "Professional business website with service showcase"
    },
    {
      image: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75",
      title: "Portfolio Website",
      description: "Creative portfolio site for artist and designer"
    }
  ];

  const designProcess = [
    {
      step: "01",
      title: "Discovery & Planning",
      description: "We analyze your business goals, target audience, and competitors to create a strategic foundation for your website."
    },
    {
      step: "02",
      title: "Design & Mockups",
      description: "Our designers create visual mockups and wireframes that showcase the layout, style, and user experience."
    },
    {
      step: "03",
      title: "Development & Testing",
      description: "We build your website using modern technologies and test it across all devices and browsers."
    },
    {
      step: "04",
      title: "Launch & Support",
      description: "We launch your website and provide training and ongoing support to ensure your success."
    }
  ];

  const features = [
    {
      icon: Monitor,
      title: "Responsive Design",
      description: "Websites that look perfect on all devices and screen sizes"
    },
    {
      icon: Zap,
      title: "Fast Loading",
      description: "Optimized for speed to provide the best user experience"
    },
    {
      icon: Search,
      title: "SEO Optimized",
      description: "Built with search engine optimization best practices"
    },
    {
      icon: Shield,
      title: "Secure & Reliable",
      description: "Modern security features and reliable hosting recommendations"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Back Button */}
      <div className="bg-warm-cream py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link
            to="/design-services"
            className="flex items-center text-blue-600 hover:text-blue-700 transition-colors"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Design Studio
          </Link>
        </div>
      </div>

      {/* Hero Section */}
      <section className="bg-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900">
                Professional Website Design
              </h1>
              <p className="text-xl mb-8 text-gray-600">
                Bring your business online with a responsive and visually appealing website that reflects your brand and engages your customers. Modern, fast, and optimized for success.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/contact"
                  className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center justify-center"
                >
                  Start Your Website Project
                </Link>
                <Link
                  to="#portfolio"
                  className="border-2 border-brand-orange text-brand-orange px-8 py-3 rounded-lg font-semibold hover:bg-brand-orange hover:text-white transition-colors inline-flex items-center justify-center"
                >
                  View Portfolio
                </Link>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1726835998298.jpg&w=1080&q=75"
                alt="Professional Website Design"
                className="rounded-lg shadow-2xl"
              />
              <div className="absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-xl">
                <div className="flex items-center space-x-3">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <Globe className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Modern Design</p>
                    <p className="text-sm text-gray-600">Responsive & fast</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-warm-cream">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Your Website Matters
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              In today's digital world, your website is often the first interaction customers have with your business. Make it count with professional design.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Design Process */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Website Design Process
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We follow a proven process to ensure your website meets your business goals and provides an excellent user experience.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {designProcess.map((process, index) => (
              <div key={index} className="text-center">
                <div className="bg-blue-600 text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  {process.step}
                </div>
                <h3 className="font-semibold text-gray-900 mb-3">{process.title}</h3>
                <p className="text-gray-600 text-sm">{process.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>


      {/* Pricing Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Website Design Packages
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Choose the package that best fits your business needs and budget.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Basic Package */}
            <div className="bg-white border-2 border-gray-200 rounded-lg p-8 text-center">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Basic Website</h3>
              <div className="text-4xl font-bold text-blue-600 mb-6">$399</div>
              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Up to 5 pages</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Responsive design</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Contact form</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Basic SEO setup</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">2-3 week delivery</span>
                </li>
                <li className="pt-3 border-t border-gray-200">
                  <div className="text-sm font-medium text-gray-900 mb-2">Add-on Services:</div>
                </li>
                <li className="flex items-center">
                  <span className="text-blue-600 mr-3">+</span>
                  <span className="text-gray-600 text-sm">Hosting (monthly): $10-40</span>
                </li>
                <li className="flex items-center">
                  <span className="text-blue-600 mr-3">+</span>
                  <span className="text-gray-600 text-sm">Payment Integration: +$250</span>
                </li>
                <li className="flex items-center">
                  <span className="text-blue-600 mr-3">+</span>
                  <span className="text-gray-600 text-sm">Advanced SEO: +$500</span>
                </li>
                <li className="flex items-center">
                  <span className="text-blue-600 mr-3">+</span>
                  <span className="text-gray-600 text-sm">Blog Functionality: +$500</span>
                </li>
              </ul>
              <button className="w-full bg-brand-orange text-white py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors">
                Get Started
              </button>
            </div>

            {/* Professional Package */}
            <div className="bg-white border-2 border-blue-600 rounded-lg p-8 text-center relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Professional Website</h3>
              <div className="text-4xl font-bold text-blue-600 mb-6">$1,599</div>
              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Up to 10 pages</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Custom design & branding</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Content management system</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                   <span className="text-gray-700">Advanced SEO optimization (included)</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">3-4 week delivery</span>
                </li>
                <li className="pt-3 border-t border-gray-200">
                  <span className="text-sm text-gray-600 italic">Additional features available upon request</span>
                </li>
              </ul>
              <button className="w-full bg-brand-orange text-white py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors">
                Get Started
              </button>
            </div>

            {/* E-commerce Package */}
            <div className="bg-white border-2 border-gray-200 rounded-lg p-8 text-center">
              <h3 className="text-xl font-bold text-gray-900 mb-4">E-commerce Website</h3>
              <div className="text-4xl font-bold text-blue-600 mb-6">$2,499</div>
              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Unlimited pages</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Online store setup</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                   <span className="text-gray-700">Payment integration (included)</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Inventory management</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">4-6 week delivery</span>
                </li>
                <li className="pt-3 border-t border-gray-200">
                  <span className="text-sm text-gray-600 italic">Advanced features and customizations available</span>
                </li>
              </ul>
              <button className="w-full bg-brand-orange text-white py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors">
                Get Started
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="w-full py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-start">
            {/* Left Column - Client Love Content */}
            <div className="lg:col-span-1">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
                What our clients say ...
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                We're a global team of skilled designers united by a passion for design and a commitment to excellence.
              </p>
              
              {/* Statistics */}
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <Star className="h-8 w-8 text-yellow-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Responsive Design</div>
                    <div className="text-gray-600">Websites that work perfectly on all devices and screen sizes.</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Lightbulb className="h-8 w-8 text-purple-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">SEO Optimized</div>
                    <div className="text-gray-600">Built with search engine optimization best practices.</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Award className="h-8 w-8 text-orange-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Modern Technology</div>
                    <div className="text-gray-600">Fast, secure, and built with the latest web technologies.</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Scrollable Portfolio */}
            <div className="lg:col-span-2">
              <div className="relative">
                {/* Left Navigation Button */}
                <button
                  onClick={() => scrollPortfolio('left')}
                  className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 ${
                    portfolioScrollPosition <= 0
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:bg-gray-50 hover:shadow-xl'
                  }`}
                  disabled={portfolioScrollPosition <= 0}
                >
                  <ChevronLeft className="h-5 w-5 text-gray-600" />
                </button>

                {/* Right Navigation Button */}
                <button
                  onClick={() => scrollPortfolio('right')}
                  className="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 hover:bg-gray-50 hover:shadow-xl"
                >
                  <ChevronRight className="h-5 w-5 text-gray-600" />
                </button>

                {/* Scrollable Container */}
                <div 
                  ref={portfolioScrollRef}
                  onScroll={handlePortfolioScroll}
                  className="overflow-x-auto whitespace-nowrap py-4 px-12 scrollbar-hide"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                  }}
                >
                <div className="flex space-x-6">
                  {portfolioSamples.map((sample, index) => (
                    <div
                      key={index}
                      className="inline-block w-64 bg-white rounded-lg shadow-lg overflow-hidden flex-shrink-0 hover:shadow-xl transition-shadow duration-300"
                    >
                      <img
                        src={sample.image}
                        alt={sample.title}
                        className="w-full h-48 object-cover"
                      />
                      <div className="p-6">
                        <h3 className="font-semibold text-gray-900 mb-2 whitespace-normal">
                          {sample.title}
                        </h3>
                        <p className="text-gray-600 text-sm mb-4 whitespace-normal">{sample.description}</p>
                        
                        {/* Star Rating */}
                        <div className="flex items-center mb-4">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className="h-4 w-4 text-yellow-400 fill-current"
                            />
                          ))}
                          <span className="text-sm text-gray-500 ml-2">5.0</span>
                        </div>
                        
                        {/* Reviewer Info */}
                        <div className="flex items-center space-x-3">
                          <img
                            src={sample.image}
                            alt="Client"
                            className="w-8 h-8 rounded-full object-cover"
                          />
                          <p className="text-sm font-medium text-gray-900 whitespace-normal">
                            Happy Client
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-warm-cream">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600">
              Everything you need to know about our website design services.
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg">
                <button
                  onClick={() => toggleFaq(index)}
                  className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
                >
                  <span className="font-semibold text-gray-900">{faq.question}</span>
                  {openFaq === index ? (
                    <ChevronUp className="h-5 w-5 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-500" />
                  )}
                </button>
                {openFaq === index && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-600">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4">Ready to create your perfect website?</p>
            <Link
              to="/contact"
              className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center"
            >
              Start Your Website Project
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default WebsiteDesign;