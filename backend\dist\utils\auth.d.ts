import { TokenPair, SecurityEvent } from '../types/auth';
export declare const hashPassword: (password: string) => Promise<string>;
export declare const verifyPassword: (password: string, hash: string) => Promise<boolean>;
export declare const generateSecureToken: (length?: number) => string;
export declare const generateVerificationToken: () => {
    token: string;
    expires: Date;
};
export declare const generatePasswordResetToken: () => {
    token: string;
    expires: Date;
};
export declare const generateTokenPair: (userId: string, email: string, role: any, ipAddress: string, userAgent: string) => Promise<TokenPair>;
export declare const revokeRefreshToken: (refreshToken: string) => Promise<void>;
export declare const revokeAllUserSessions: (userId: string) => Promise<void>;
export declare const isRefreshTokenValid: (refreshToken: string) => Promise<boolean>;
export declare const cleanupExpiredSessions: () => Promise<number>;
export declare const incrementLoginAttempts: (userId: string) => Promise<void>;
export declare const resetLoginAttempts: (userId: string) => Promise<void>;
export declare const isAccountLocked: (userId: string) => Promise<boolean>;
export declare const getAccountLockInfo: (userId: string) => Promise<{
    isLocked: boolean;
    lockExpires: Date | null;
    attempts: number;
    maxAttempts: number;
    remainingAttempts: number;
} | null>;
export declare const logSecurityEvent: (event: Omit<SecurityEvent, "timestamp">) => Promise<void>;
export declare const getClientIP: (req: any) => string;
export declare const getUserAgent: (req: any) => string;
export declare const sanitizeUser: (user: any) => any;
export declare const createApiResponse: (success: boolean, data?: any, message?: string) => {
    success: boolean;
    data: any;
    message: string | undefined;
    timestamp: string;
};
declare const _default: {
    hashPassword: (password: string) => Promise<string>;
    verifyPassword: (password: string, hash: string) => Promise<boolean>;
    generateSecureToken: (length?: number) => string;
    generateVerificationToken: () => {
        token: string;
        expires: Date;
    };
    generatePasswordResetToken: () => {
        token: string;
        expires: Date;
    };
    generateTokenPair: (userId: string, email: string, role: any, ipAddress: string, userAgent: string) => Promise<TokenPair>;
    revokeRefreshToken: (refreshToken: string) => Promise<void>;
    revokeAllUserSessions: (userId: string) => Promise<void>;
    isRefreshTokenValid: (refreshToken: string) => Promise<boolean>;
    cleanupExpiredSessions: () => Promise<number>;
    incrementLoginAttempts: (userId: string) => Promise<void>;
    resetLoginAttempts: (userId: string) => Promise<void>;
    isAccountLocked: (userId: string) => Promise<boolean>;
    getAccountLockInfo: (userId: string) => Promise<{
        isLocked: boolean;
        lockExpires: Date | null;
        attempts: number;
        maxAttempts: number;
        remainingAttempts: number;
    } | null>;
    logSecurityEvent: (event: Omit<SecurityEvent, "timestamp">) => Promise<void>;
    getClientIP: (req: any) => string;
    getUserAgent: (req: any) => string;
    sanitizeUser: (user: any) => any;
    createApiResponse: (success: boolean, data?: any, message?: string) => {
        success: boolean;
        data: any;
        message: string | undefined;
        timestamp: string;
    };
};
export default _default;
//# sourceMappingURL=auth.d.ts.map