import { EmailService, NodemailerEmailService } from '@/services/EmailService';
import nodemailer from 'nodemailer';

// Mock nodemailer
jest.mock('nodemailer');

jest.mock('@/utils/logger', () => ({
  createLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
  }),
}));

describe('EmailService', () => {
  let emailService: EmailService;
  let mockTransporter: jest.Mocked<nodemailer.Transporter>;
  let mockEmailConfig: any;

  beforeEach(() => {
    mockTransporter = {
      sendMail: jest.fn(),
      verify: jest.fn(),
    } as any;

    (nodemailer.createTransport as jest.Mock).mockReturnValue(mockTransporter);

    mockEmailConfig = {
      service: 'gmail',
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      user: '<EMAIL>',
      password: 'testpassword',
      from: 'PrintWeditt <<EMAIL>>',
    };

    // Mock environment variables
    process.env.FRONTEND_URL = 'https://example.com';

    jest.clearAllMocks();

    // Initialize email service after mocks are set up
    emailService = new NodemailerEmailService(mockEmailConfig);
  });

  afterEach(() => {
    delete process.env.FRONTEND_URL;
  });

  describe('constructor', () => {
    it('should initialize email service with correct configuration', () => {
      expect(nodemailer.createTransport).toHaveBeenCalledWith({
        service: 'gmail',
        host: 'smtp.gmail.com',
        port: 587,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: 'testpassword',
        },
        tls: {
          rejectUnauthorized: false,
        },
      });
    });
  });

  describe('sendPasswordResetEmail', () => {
    it('should successfully send password reset email', async () => {
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-message-id' });

      await emailService.sendPasswordResetEmail('<EMAIL>', 'reset-token-123', 'John');

      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: 'PrintWeditt <<EMAIL>>',
        to: '<EMAIL>',
        subject: 'Password Reset Request - PrintWeditt',
        html: expect.stringContaining('Hello John'),
        text: expect.stringContaining('Hello John'),
      });

      const callArgs = mockTransporter.sendMail.mock.calls[0][0];
      expect(callArgs.html).toContain('https://example.com/reset-password?token=reset-token-123');
      expect(callArgs.text).toContain('https://example.com/reset-password?token=reset-token-123');
    });

    it('should throw error when email sending fails', async () => {
      const error = new Error('SMTP error');
      mockTransporter.sendMail.mockRejectedValue(error);

      await expect(emailService.sendPasswordResetEmail('<EMAIL>', 'reset-token-123', 'John')).rejects.toThrow(
        'Failed to send password reset email'
      );
    });

    it('should include security notice in password reset email', async () => {
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-message-id' });

      await emailService.sendPasswordResetEmail('<EMAIL>', 'reset-token-123', 'John');

      const callArgs = mockTransporter.sendMail.mock.calls[0][0];
      expect(callArgs.html).toContain('This link will expire in 1 hour');
      expect(callArgs.html).toContain("If you didn't request this reset");
      expect(callArgs.text).toContain('This link will expire in 1 hour');
      expect(callArgs.text).toContain("If you didn't request this reset");
    });

    it('should include reset token preview in HTML email', async () => {
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-message-id' });

      await emailService.sendPasswordResetEmail('<EMAIL>', 'reset-token-123456789', 'John');

      const callArgs = mockTransporter.sendMail.mock.calls[0][0];
      expect(callArgs.html).toContain('Reset Token: reset-to...');
    });
  });

  describe('sendEmailVerification', () => {
    it('should successfully send email verification', async () => {
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-message-id' });

      await emailService.sendEmailVerification('<EMAIL>', 'verify-token-123', 'Jane');

      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: 'PrintWeditt <<EMAIL>>',
        to: '<EMAIL>',
        subject: 'Email Verification - PrintWeditt',
        html: expect.stringContaining('Hello Jane'),
        text: expect.stringContaining('Hello Jane'),
      });

      const callArgs = mockTransporter.sendMail.mock.calls[0][0];
      expect(callArgs.html).toContain('https://example.com/verify-email?token=verify-token-123');
      expect(callArgs.text).toContain('https://example.com/verify-email?token=verify-token-123');
    });

    it('should throw error when email sending fails', async () => {
      const error = new Error('SMTP error');
      mockTransporter.sendMail.mockRejectedValue(error);

      await expect(emailService.sendEmailVerification('<EMAIL>', 'verify-token-123', 'Jane')).rejects.toThrow(
        'Failed to send email verification'
      );
    });

    it('should include verification instructions in email', async () => {
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-message-id' });

      await emailService.sendEmailVerification('<EMAIL>', 'verify-token-123', 'Jane');

      const callArgs = mockTransporter.sendMail.mock.calls[0][0];
      expect(callArgs.html).toContain('This verification link will expire in 24 hours');
      expect(callArgs.html).toContain("You won't be able to access all features");
      expect(callArgs.text).toContain('This verification link will expire in 24 hours');
      expect(callArgs.text).toContain("You won't be able to access all features");
    });

    it('should include verification token preview in HTML email', async () => {
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-message-id' });

      await emailService.sendEmailVerification('<EMAIL>', 'verify-token-123456789', 'Jane');

      const callArgs = mockTransporter.sendMail.mock.calls[0][0];
      expect(callArgs.html).toContain('Verification Token: verify-t...');
    });
  });

  describe('sendWelcomeEmail', () => {
    it('should successfully send welcome email', async () => {
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-message-id' });

      await emailService.sendWelcomeEmail('<EMAIL>', 'Bob');

      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: 'PrintWeditt <<EMAIL>>',
        to: '<EMAIL>',
        subject: 'Welcome to PrintWeditt!',
        html: expect.stringContaining('Hello Bob'),
        text: expect.stringContaining('Hello Bob'),
      });

      const callArgs = mockTransporter.sendMail.mock.calls[0][0];
      expect(callArgs.html).toContain('https://example.com/login');
      expect(callArgs.text).toContain('https://example.com/login');
    });

    it('should not throw error when welcome email sending fails', async () => {
      const error = new Error('SMTP error');
      mockTransporter.sendMail.mockRejectedValue(error);

      // Should not throw - welcome email failure is not critical
      await expect(emailService.sendWelcomeEmail('<EMAIL>', 'Bob')).resolves.not.toThrow();
    });

    it('should include platform features in welcome email', async () => {
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-message-id' });

      await emailService.sendWelcomeEmail('<EMAIL>', 'Bob');

      const callArgs = mockTransporter.sendMail.mock.calls[0][0];
      expect(callArgs.html).toContain('Browse our wide range of printing services');
      expect(callArgs.html).toContain('Upload and manage your design files');
      expect(callArgs.html).toContain('Get quotes from trusted printing providers');
      expect(callArgs.html).toContain('Track your orders in real-time');
      expect(callArgs.html).toContain('Build your professional printing network');
    });
  });

  describe('verifyConnection', () => {
    it('should return true when connection is successful', async () => {
      mockTransporter.verify.mockResolvedValue(true);

      const result = await (emailService as NodemailerEmailService).verifyConnection();

      expect(mockTransporter.verify).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should return false when connection fails', async () => {
      const error = new Error('Connection failed');
      mockTransporter.verify.mockRejectedValue(error);

      const result = await (emailService as NodemailerEmailService).verifyConnection();

      expect(mockTransporter.verify).toHaveBeenCalled();
      expect(result).toBe(false);
    });
  });

  describe('HTML email generation', () => {
    it('should generate proper HTML structure for password reset email', async () => {
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-message-id' });

      await emailService.sendPasswordResetEmail('<EMAIL>', 'reset-token-123', 'John');

      const callArgs = mockTransporter.sendMail.mock.calls[0][0];
      const html = callArgs.html;

      expect(html).toContain('<!DOCTYPE html>');
      expect(html).toContain('<html>');
      expect(html).toContain('<head>');
      expect(html).toContain('<body>');
      expect(html).toContain('class="header"');
      expect(html).toContain('class="content"');
      expect(html).toContain('class="button"');
      expect(html).toContain('class="warning"');
      expect(html).toContain('class="footer"');
    });

    it('should generate proper HTML structure for email verification', async () => {
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-message-id' });

      await emailService.sendEmailVerification('<EMAIL>', 'verify-token-123', 'Jane');

      const callArgs = mockTransporter.sendMail.mock.calls[0][0];
      const html = callArgs.html;

      expect(html).toContain('<!DOCTYPE html>');
      expect(html).toContain('class="header"');
      expect(html).toContain('class="content"');
      expect(html).toContain('class="button"');
      expect(html).toContain('class="info"');
      expect(html).toContain('class="footer"');
    });

    it('should generate proper HTML structure for welcome email', async () => {
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-message-id' });

      await emailService.sendWelcomeEmail('<EMAIL>', 'Bob');

      const callArgs = mockTransporter.sendMail.mock.calls[0][0];
      const html = callArgs.html;

      expect(html).toContain('<!DOCTYPE html>');
      expect(html).toContain('class="header"');
      expect(html).toContain('class="content"');
      expect(html).toContain('class="button"');
      expect(html).toContain('class="features"');
      expect(html).toContain('class="footer"');
    });
  });

  describe('Text email generation', () => {
    it('should generate proper text content for password reset email', async () => {
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-message-id' });

      await emailService.sendPasswordResetEmail('<EMAIL>', 'reset-token-123', 'John');

      const callArgs = mockTransporter.sendMail.mock.calls[0][0];
      const text = callArgs.text;

      expect(text).toContain('Hello John,');
      expect(text).toContain('We received a request to reset your password');
      expect(text).toContain('https://example.com/reset-password?token=reset-token-123');
      expect(text).toContain('Security Notice:');
      expect(text).toContain('This link will expire in 1 hour');
      expect(text).toContain('Best regards,');
      expect(text).toContain('The PrintWeditt Team');
    });

    it('should generate proper text content for email verification', async () => {
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-message-id' });

      await emailService.sendEmailVerification('<EMAIL>', 'verify-token-123', 'Jane');

      const callArgs = mockTransporter.sendMail.mock.calls[0][0];
      const text = callArgs.text;

      expect(text).toContain('Hello Jane,');
      expect(text).toContain('Thank you for registering with PrintWeditt!');
      expect(text).toContain('https://example.com/verify-email?token=verify-token-123');
      expect(text).toContain('Important:');
      expect(text).toContain('This verification link will expire in 24 hours');
      expect(text).toContain('Welcome to PrintWeditt!');
    });

    it('should generate proper text content for welcome email', async () => {
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-message-id' });

      await emailService.sendWelcomeEmail('<EMAIL>', 'Bob');

      const callArgs = mockTransporter.sendMail.mock.calls[0][0];
      const text = callArgs.text;

      expect(text).toContain('Hello Bob,');
      expect(text).toContain('Welcome to PrintWeditt!');
      expect(text).toContain('What you can do now:');
      expect(text).toContain('Browse our wide range of printing services');
      expect(text).toContain('Get started: https://example.com/login');
      expect(text).toContain('Happy printing!');
    });
  });
});
