/**
 * AuthService Unit Tests
 * 
 * Comprehensive unit tests for the AuthService class covering:
 * - User registration and authentication
 * - Password management and reset functionality
 * - Email verification
 * - Session management
 * - Security features
 */

import { AuthService } from '../../../src/services/AuthService';
import { IAuthRepository } from '../../../src/repositories/AuthRepository';
import { EmailService } from '../../../src/services/EmailService';
import { SecurityEventType } from '../../../src/types/auth';
import { AuthenticationError, ConflictError, NotFoundError } from '../../../src/middleware/errorHandler';
import { UserRole } from '@prisma/client';
import * as authUtils from '../../../src/utils/auth';

// Mock dependencies
jest.mock('../../../src/utils/auth');
jest.mock('../../../src/utils/logger');

describe('AuthService', () => {
  let authService: AuthService;
  let mockAuthRepository: jest.Mocked<IAuthRepository>;
  let mockEmailService: jest.Mocked<EmailService>;
  
  const mockJWTConfig = {
    secret: 'test-secret',
    refreshSecret: 'test-refresh-secret',
    accessExpiresIn: '15m',
    refreshExpiresIn: '7d',
    issuer: 'test-issuer',
    audience: 'test-audience'
  };
  
  const mockSecurityConfig = {
    bcryptRounds: 12,
    cookieSecret: 'test-cookie-secret',
    rateLimitWindowMs: 900000,
    rateLimitMaxRequests: 100,
    rateLimitAuthMax: 5,
    maxFileSize: 52428800,
    allowedFileTypes: ['jpg', 'jpeg', 'png', 'webp', 'pdf', 'ai', 'psd']
  };

  const mockContext = {
    ipAddress: '***********',
    userAgent: 'Mozilla/5.0 Test Browser'
  };

  const mockUser = {
    id: 'user123',
    email: '<EMAIL>',
    password: 'hashedPassword',
    firstName: 'Test',
    lastName: 'User',
    role: UserRole.CUSTOMER,
    isActive: true,
    isVerified: false,
    avatar: null,
    phone: '+1234567890',
    createdAt: new Date(),
    updatedAt: new Date(),
    lastLoginAt: null,
    loginAttempts: 0,
    lockedUntil: null,
    emailVerified: null,
    emailVerificationToken: null,
    emailVerificationTokenExpires: null,
    passwordResetToken: null,
    passwordResetTokenExpires: null
  };

  beforeEach(() => {
    // Create mocked repository
    mockAuthRepository = {
      findUserByEmail: jest.fn(),
      findUserById: jest.fn(),
      findUserByIdSafe: jest.fn(),
      createUser: jest.fn(),
      updateUser: jest.fn(),
      updateUserPassword: jest.fn(),
      incrementLoginAttempts: jest.fn(),
      resetLoginAttempts: jest.fn(),
      isAccountLocked: jest.fn(),
      getAccountLockInfo: jest.fn(),
      findUserByPasswordResetToken: jest.fn(),
      setPasswordResetToken: jest.fn(),
      clearPasswordResetToken: jest.fn(),
      findUserByEmailVerificationToken: jest.fn(),
      setEmailVerificationToken: jest.fn(),
      markEmailAsVerified: jest.fn(),
      clearEmailVerificationToken: jest.fn(),
      createSession: jest.fn(),
      findSessionByRefreshToken: jest.fn(),
      revokeSession: jest.fn(),
      revokeAllUserSessions: jest.fn(),
      getUserSessions: jest.fn(),
      revokeSpecificSession: jest.fn(),
      cleanupExpiredSessions: jest.fn()
    };

    // Create mocked email service
    mockEmailService = {
      sendPasswordResetEmail: jest.fn(),
      sendEmailVerification: jest.fn(),
      sendWelcomeEmail: jest.fn(),
      verifyConnection: jest.fn()
    } as any;

    // Create service instance
    authService = new AuthService(
      mockAuthRepository,
      mockEmailService,
      mockJWTConfig,
      mockSecurityConfig
    );

    // Mock utility functions
    (authUtils.hashPassword as jest.Mock).mockResolvedValue('hashedPassword');
    (authUtils.verifyPassword as jest.Mock).mockResolvedValue(true);
    (authUtils.generateSecureToken as jest.Mock).mockReturnValue('secureToken123');
    (authUtils.sanitizeUser as jest.Mock).mockImplementation((user) => ({ ...user, password: undefined }));
    (authUtils.logSecurityEvent as jest.Mock).mockResolvedValue(undefined);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    const registerData = {
      email: '<EMAIL>',
      password: 'Password123!',
      firstName: 'Test',
      lastName: 'User',
      phone: '+1234567890'
    };

    it('should successfully register a new user', async () => {
      // Arrange
      mockAuthRepository.findUserByEmail.mockResolvedValue(null);
      mockAuthRepository.createUser.mockResolvedValue(mockUser);
      mockAuthRepository.createSession.mockResolvedValue({} as any);

      // Act
      const result = await authService.register(registerData, mockContext);

      // Assert
      expect(result).toHaveProperty('user');
      expect(result).toHaveProperty('tokens');
      expect(mockAuthRepository.findUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockAuthRepository.createUser).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'hashedPassword',
        firstName: 'Test',
        lastName: 'User',
        phone: '+1234567890',
        role: UserRole.CUSTOMER,
        isActive: true,
        isVerified: false
      });
      expect(authUtils.hashPassword).toHaveBeenCalledWith('Password123!', 12);
      expect(authUtils.logSecurityEvent).toHaveBeenCalledWith({
        type: SecurityEventType.LOGIN_SUCCESS,
        userId: mockUser.id,
        email: mockUser.email,
        ipAddress: mockContext.ipAddress,
        userAgent: mockContext.userAgent,
        metadata: { action: 'registration' }
      });
    });

    it('should throw ConflictError if email already exists', async () => {
      // Arrange
      mockAuthRepository.findUserByEmail.mockResolvedValue(mockUser);

      // Act & Assert
      await expect(authService.register(registerData, mockContext))
        .rejects.toThrow(ConflictError);
      
      expect(mockAuthRepository.findUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockAuthRepository.createUser).not.toHaveBeenCalled();
      expect(authUtils.logSecurityEvent).toHaveBeenCalledWith({
        type: SecurityEventType.LOGIN_FAILURE,
        email: '<EMAIL>',
        ipAddress: mockContext.ipAddress,
        userAgent: mockContext.userAgent,
        metadata: { reason: 'Email already registered' }
      });
    });
  });

  describe('login', () => {
    const loginData = {
      email: '<EMAIL>',
      password: 'Password123!'
    };

    it('should successfully login with valid credentials', async () => {
      // Arrange
      mockAuthRepository.findUserByEmail.mockResolvedValue(mockUser);
      mockAuthRepository.isAccountLocked.mockResolvedValue(false);
      mockAuthRepository.createSession.mockResolvedValue({} as any);
      (authUtils.verifyPassword as jest.Mock).mockResolvedValue(true);

      // Act
      const result = await authService.login(loginData, mockContext);

      // Assert
      expect(result).toHaveProperty('user');
      expect(result).toHaveProperty('tokens');
      expect(mockAuthRepository.findUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockAuthRepository.isAccountLocked).toHaveBeenCalledWith(mockUser.id);
      expect(authUtils.verifyPassword).toHaveBeenCalledWith('Password123!', 'hashedPassword');
      expect(mockAuthRepository.resetLoginAttempts).toHaveBeenCalledWith(mockUser.id);
      expect(authUtils.logSecurityEvent).toHaveBeenCalledWith({
        type: SecurityEventType.LOGIN_SUCCESS,
        userId: mockUser.id,
        email: mockUser.email,
        ipAddress: mockContext.ipAddress,
        userAgent: mockContext.userAgent,
        metadata: { action: 'login' }
      });
    });

    it('should throw AuthenticationError for non-existent user', async () => {
      // Arrange
      mockAuthRepository.findUserByEmail.mockResolvedValue(null);

      // Act & Assert
      await expect(authService.login(loginData, mockContext))
        .rejects.toThrow(AuthenticationError);
      
      expect(authUtils.logSecurityEvent).toHaveBeenCalledWith({
        type: SecurityEventType.LOGIN_FAILURE,
        email: '<EMAIL>',
        ipAddress: mockContext.ipAddress,
        userAgent: mockContext.userAgent,
        metadata: { reason: 'User not found' }
      });
    });

    it('should throw AuthenticationError for locked account', async () => {
      // Arrange
      mockAuthRepository.findUserByEmail.mockResolvedValue(mockUser);
      mockAuthRepository.isAccountLocked.mockResolvedValue(true);
      mockAuthRepository.getAccountLockInfo.mockResolvedValue({
        isLocked: true,
        lockExpires: new Date(),
        attempts: 5,
        maxAttempts: 5,
        remainingAttempts: 0
      });

      // Act & Assert
      await expect(authService.login(loginData, mockContext))
        .rejects.toThrow(AuthenticationError);
      
      expect(mockAuthRepository.isAccountLocked).toHaveBeenCalledWith(mockUser.id);
      expect(authUtils.logSecurityEvent).toHaveBeenCalledWith({
        type: SecurityEventType.LOGIN_FAILURE,
        userId: mockUser.id,
        email: mockUser.email,
        ipAddress: mockContext.ipAddress,
        userAgent: mockContext.userAgent,
        metadata: { reason: 'Account locked', lockInfo: expect.any(Object) }
      });
    });

    it('should throw AuthenticationError for invalid password', async () => {
      // Arrange
      mockAuthRepository.findUserByEmail.mockResolvedValue(mockUser);
      mockAuthRepository.isAccountLocked.mockResolvedValue(false);
      (authUtils.verifyPassword as jest.Mock).mockResolvedValue(false);

      // Act & Assert
      await expect(authService.login(loginData, mockContext))
        .rejects.toThrow(AuthenticationError);
      
      expect(mockAuthRepository.incrementLoginAttempts).toHaveBeenCalledWith(mockUser.id);
      expect(authUtils.logSecurityEvent).toHaveBeenCalledWith({
        type: SecurityEventType.LOGIN_FAILURE,
        userId: mockUser.id,
        email: mockUser.email,
        ipAddress: mockContext.ipAddress,
        userAgent: mockContext.userAgent,
        metadata: { reason: 'Invalid password' }
      });
    });

    it('should throw AuthenticationError for inactive user', async () => {
      // Arrange
      const inactiveUser = { ...mockUser, isActive: false };
      mockAuthRepository.findUserByEmail.mockResolvedValue(inactiveUser);
      mockAuthRepository.isAccountLocked.mockResolvedValue(false);

      // Act & Assert
      await expect(authService.login(loginData, mockContext))
        .rejects.toThrow(AuthenticationError);
      
      expect(authUtils.logSecurityEvent).toHaveBeenCalledWith({
        type: SecurityEventType.LOGIN_FAILURE,
        userId: inactiveUser.id,
        email: inactiveUser.email,
        ipAddress: mockContext.ipAddress,
        userAgent: mockContext.userAgent,
        metadata: { reason: 'Account deactivated' }
      });
    });
  });

  describe('requestPasswordReset', () => {
    it('should send password reset email for existing user', async () => {
      // Arrange
      mockAuthRepository.findUserByEmail.mockResolvedValue(mockUser);
      (authUtils.generateSecureToken as jest.Mock).mockReturnValue('resetToken123');

      // Act
      await authService.requestPasswordReset('<EMAIL>', mockContext);

      // Assert
      expect(mockAuthRepository.findUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockAuthRepository.setPasswordResetToken).toHaveBeenCalledWith(
        mockUser.id,
        'resetToken123',
        expect.any(Date)
      );
      expect(mockEmailService.sendPasswordResetEmail).toHaveBeenCalledWith(
        mockUser.email,
        'resetToken123',
        mockUser.firstName
      );
      expect(authUtils.logSecurityEvent).toHaveBeenCalledWith({
        type: SecurityEventType.PASSWORD_RESET_REQUEST,
        userId: mockUser.id,
        email: mockUser.email,
        ipAddress: mockContext.ipAddress,
        userAgent: mockContext.userAgent
      });
    });

    it('should silently succeed for non-existent user (security)', async () => {
      // Arrange
      mockAuthRepository.findUserByEmail.mockResolvedValue(null);

      // Act
      await authService.requestPasswordReset('<EMAIL>', mockContext);

      // Assert
      expect(mockAuthRepository.findUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockAuthRepository.setPasswordResetToken).not.toHaveBeenCalled();
      expect(mockEmailService.sendPasswordResetEmail).not.toHaveBeenCalled();
    });

    it('should silently succeed for inactive user (security)', async () => {
      // Arrange
      const inactiveUser = { ...mockUser, isActive: false };
      mockAuthRepository.findUserByEmail.mockResolvedValue(inactiveUser);

      // Act
      await authService.requestPasswordReset('<EMAIL>', mockContext);

      // Assert
      expect(mockAuthRepository.setPasswordResetToken).not.toHaveBeenCalled();
      expect(mockEmailService.sendPasswordResetEmail).not.toHaveBeenCalled();
    });
  });

  describe('resetPassword', () => {
    it('should successfully reset password with valid token', async () => {
      // Arrange
      mockAuthRepository.findUserByPasswordResetToken.mockResolvedValue(mockUser);
      (authUtils.hashPassword as jest.Mock).mockResolvedValue('newHashedPassword');

      // Act
      await authService.resetPassword('resetToken123', 'NewPassword123!', mockContext);

      // Assert
      expect(mockAuthRepository.findUserByPasswordResetToken).toHaveBeenCalledWith('resetToken123');
      expect(authUtils.hashPassword).toHaveBeenCalledWith('NewPassword123!', 12);
      expect(mockAuthRepository.updateUserPassword).toHaveBeenCalledWith(mockUser.id, 'newHashedPassword');
      expect(mockAuthRepository.clearPasswordResetToken).toHaveBeenCalledWith(mockUser.id);
      expect(mockAuthRepository.revokeAllUserSessions).toHaveBeenCalledWith(mockUser.id);
      expect(authUtils.logSecurityEvent).toHaveBeenCalledWith({
        type: SecurityEventType.PASSWORD_RESET_COMPLETE,
        userId: mockUser.id,
        email: mockUser.email,
        ipAddress: mockContext.ipAddress,
        userAgent: mockContext.userAgent
      });
    });

    it('should throw AuthenticationError for invalid token', async () => {
      // Arrange
      mockAuthRepository.findUserByPasswordResetToken.mockResolvedValue(null);

      // Act & Assert
      await expect(authService.resetPassword('invalidToken', 'NewPassword123!', mockContext))
        .rejects.toThrow(AuthenticationError);
      
      expect(mockAuthRepository.updateUserPassword).not.toHaveBeenCalled();
      expect(mockAuthRepository.clearPasswordResetToken).not.toHaveBeenCalled();
    });
  });

  describe('sendEmailVerification', () => {
    it('should send verification email for unverified user', async () => {
      // Arrange
      mockAuthRepository.findUserById.mockResolvedValue(mockUser);
      (authUtils.generateSecureToken as jest.Mock).mockReturnValue('verifyToken123');

      // Act
      await authService.sendEmailVerification(mockUser.id, mockContext);

      // Assert
      expect(mockAuthRepository.findUserById).toHaveBeenCalledWith(mockUser.id);
      expect(mockAuthRepository.setEmailVerificationToken).toHaveBeenCalledWith(
        mockUser.id,
        'verifyToken123',
        expect.any(Date)
      );
      expect(mockEmailService.sendEmailVerification).toHaveBeenCalledWith(
        mockUser.email,
        'verifyToken123',
        mockUser.firstName
      );
      expect(authUtils.logSecurityEvent).toHaveBeenCalledWith({
        type: SecurityEventType.EMAIL_VERIFICATION_SENT,
        userId: mockUser.id,
        email: mockUser.email,
        ipAddress: mockContext.ipAddress,
        userAgent: mockContext.userAgent
      });
    });

    it('should not send email for already verified user', async () => {
      // Arrange
      const verifiedUser = { ...mockUser, isVerified: true };
      mockAuthRepository.findUserById.mockResolvedValue(verifiedUser);

      // Act
      await authService.sendEmailVerification(mockUser.id, mockContext);

      // Assert
      expect(mockAuthRepository.setEmailVerificationToken).not.toHaveBeenCalled();
      expect(mockEmailService.sendEmailVerification).not.toHaveBeenCalled();
    });

    it('should throw NotFoundError for non-existent user', async () => {
      // Arrange
      mockAuthRepository.findUserById.mockResolvedValue(null);

      // Act & Assert
      await expect(authService.sendEmailVerification('nonexistent', mockContext))
        .rejects.toThrow(NotFoundError);
    });
  });

  describe('verifyEmail', () => {
    it('should successfully verify email with valid token', async () => {
      // Arrange
      mockAuthRepository.findUserByEmailVerificationToken.mockResolvedValue(mockUser);

      // Act
      await authService.verifyEmail('verifyToken123', mockContext);

      // Assert
      expect(mockAuthRepository.findUserByEmailVerificationToken).toHaveBeenCalledWith('verifyToken123');
      expect(mockAuthRepository.markEmailAsVerified).toHaveBeenCalledWith(mockUser.id);
      expect(mockEmailService.sendWelcomeEmail).toHaveBeenCalledWith(mockUser.email, mockUser.firstName);
      expect(authUtils.logSecurityEvent).toHaveBeenCalledWith({
        type: SecurityEventType.EMAIL_VERIFIED,
        userId: mockUser.id,
        email: mockUser.email,
        ipAddress: mockContext.ipAddress,
        userAgent: mockContext.userAgent
      });
    });

    it('should throw AuthenticationError for invalid token', async () => {
      // Arrange
      mockAuthRepository.findUserByEmailVerificationToken.mockResolvedValue(null);

      // Act & Assert
      await expect(authService.verifyEmail('invalidToken', mockContext))
        .rejects.toThrow(AuthenticationError);
      
      expect(mockAuthRepository.markEmailAsVerified).not.toHaveBeenCalled();
      expect(mockEmailService.sendWelcomeEmail).not.toHaveBeenCalled();
    });
  });

  describe('changePassword', () => {
    const changePasswordData = {
      currentPassword: 'CurrentPassword123!',
      newPassword: 'NewPassword123!'
    };

    it('should successfully change password with valid current password', async () => {
      // Arrange
      mockAuthRepository.findUserById.mockResolvedValue(mockUser);
      (authUtils.verifyPassword as jest.Mock).mockResolvedValue(true);
      (authUtils.hashPassword as jest.Mock).mockResolvedValue('newHashedPassword');

      // Act
      await authService.changePassword(mockUser.id, changePasswordData, mockContext);

      // Assert
      expect(mockAuthRepository.findUserById).toHaveBeenCalledWith(mockUser.id);
      expect(authUtils.verifyPassword).toHaveBeenCalledWith('CurrentPassword123!', 'hashedPassword');
      expect(authUtils.hashPassword).toHaveBeenCalledWith('NewPassword123!', 12);
      expect(mockAuthRepository.updateUserPassword).toHaveBeenCalledWith(mockUser.id, 'newHashedPassword');
      expect(mockAuthRepository.revokeAllUserSessions).toHaveBeenCalledWith(mockUser.id);
      expect(authUtils.logSecurityEvent).toHaveBeenCalledWith({
        type: SecurityEventType.PASSWORD_CHANGE,
        userId: mockUser.id,
        email: mockUser.email,
        ipAddress: mockContext.ipAddress,
        userAgent: mockContext.userAgent
      });
    });

    it('should throw AuthenticationError for invalid current password', async () => {
      // Arrange
      mockAuthRepository.findUserById.mockResolvedValue(mockUser);
      (authUtils.verifyPassword as jest.Mock).mockResolvedValue(false);

      // Act & Assert
      await expect(authService.changePassword(mockUser.id, changePasswordData, mockContext))
        .rejects.toThrow(AuthenticationError);
      
      expect(mockAuthRepository.updateUserPassword).not.toHaveBeenCalled();
      expect(mockAuthRepository.revokeAllUserSessions).not.toHaveBeenCalled();
    });

    it('should throw AuthenticationError for user without password', async () => {
      // Arrange
      const userWithoutPassword = { ...mockUser, password: null };
      mockAuthRepository.findUserById.mockResolvedValue(userWithoutPassword);

      // Act & Assert
      await expect(authService.changePassword(mockUser.id, changePasswordData, mockContext))
        .rejects.toThrow(AuthenticationError);
    });
  });

  describe('getProfile', () => {
    it('should return user profile for existing user', async () => {
      // Arrange
      const sanitizedUser = { ...mockUser, password: undefined };
      mockAuthRepository.findUserByIdSafe.mockResolvedValue(sanitizedUser as any);

      // Act
      const result = await authService.getProfile(mockUser.id);

      // Assert
      expect(result).toEqual(sanitizedUser);
      expect(mockAuthRepository.findUserByIdSafe).toHaveBeenCalledWith(mockUser.id);
    });

    it('should throw NotFoundError for non-existent user', async () => {
      // Arrange
      mockAuthRepository.findUserByIdSafe.mockResolvedValue(null);

      // Act & Assert
      await expect(authService.getProfile('nonexistent'))
        .rejects.toThrow(NotFoundError);
    });
  });

  describe('updateProfile', () => {
    const updateData = {
      firstName: 'Updated',
      lastName: 'Name',
      phone: '+9876543210',
      avatar: 'https://example.com/avatar.jpg'
    };

    it('should successfully update user profile', async () => {
      // Arrange
      const updatedUser = { ...mockUser, ...updateData };
      mockAuthRepository.updateUser.mockResolvedValue(updatedUser);
      (authUtils.sanitizeUser as jest.Mock).mockReturnValue({ ...updatedUser, password: undefined });

      // Act
      const result = await authService.updateProfile(mockUser.id, updateData);

      // Assert
      expect(result).toEqual({ ...updatedUser, password: undefined });
      expect(mockAuthRepository.updateUser).toHaveBeenCalledWith(mockUser.id, updateData);
      expect(authUtils.sanitizeUser).toHaveBeenCalledWith(updatedUser);
    });
  });
});