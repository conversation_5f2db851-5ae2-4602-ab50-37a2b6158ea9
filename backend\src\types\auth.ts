import { Request } from 'express';
import { User, UserRole } from '@prisma/client';

// JWT payload interface
export interface JWTPayload {
  userId: string;
  email: string;
  role: UserRole;
  iat?: number;
  exp?: number;
}

// Token pair interface
export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

// Registration request body
export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

// Login request body
export interface LoginRequest {
  email: string;
  password: string;
}

// Refresh token request body
export interface RefreshTokenRequest {
  refreshToken: string;
}

// Password reset request
export interface PasswordResetRequest {
  email: string;
}

// Password reset confirm request
export interface PasswordResetConfirmRequest {
  token: string;
  newPassword: string;
}

// Email verification request
export interface EmailVerificationRequest {
  token: string;
}

// Change password request
export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// User profile update request
export interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  phone?: string;
  avatar?: string;
}

// User for authenticated requests (without sensitive fields)
export interface AuthUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  isActive: boolean;
  isVerified: boolean;
  avatar: string | null;
  phone: string | null;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt: Date | null;
}

// Extended Request interface with authenticated user
export interface AuthenticatedRequest extends Request {
  user?: AuthUser;
  userId?: string;
}

// Authentication response
export interface AuthResponse {
  user: Omit<User, 'password'>;
  tokens: TokenPair;
}

// Session info
export interface SessionInfo {
  id: string;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  expiresAt: Date;
  isActive: boolean;
}

// API Response wrapper
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Error response
export interface ErrorResponse {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
  path: string;
}

// Validation error details
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

// Login attempt tracking
export interface LoginAttempt {
  email: string;
  ipAddress: string;
  userAgent: string;
  success: boolean;
  timestamp: Date;
  failureReason?: string;
}

// Account lock status
export interface AccountLockStatus {
  isLocked: boolean;
  lockExpires?: Date;
  attempts: number;
  maxAttempts: number;
}

// Security event types
export enum SecurityEventType {
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  PASSWORD_RESET = 'PASSWORD_RESET',
  EMAIL_VERIFICATION = 'EMAIL_VERIFICATION',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  ACCOUNT_UNLOCKED = 'ACCOUNT_UNLOCKED',
  TOKEN_REFRESH = 'TOKEN_REFRESH',
  LOGOUT = 'LOGOUT'
}

// Security event log
export interface SecurityEvent {
  type: SecurityEventType;
  userId?: string;
  email?: string;
  ipAddress: string;
  userAgent: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

// All types are exported individually above