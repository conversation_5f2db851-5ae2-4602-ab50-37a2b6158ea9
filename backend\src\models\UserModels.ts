import { UserR<PERSON>, User } from '@prisma/client';
import { 
  CreateUserRequest, 
  UpdateUserRequest, 
  UserListQuery, 
  UserListResponse, 
  UserSummary, 
  UserDetail, 
  UserStats,
  BulkUserOperation,
  CreateUserData,
  UpdateUserData
} from '../types/user';

// User management domain models with business logic
export class UserManagementModel {
  // User creation model
  static createUserData(request: CreateUserRequest): UserCreationData {
    return new UserCreationData(
      request.email,
      request.password,
      request.firstName,
      request.lastName,
      request.phone,
      request.role,
      request.isActive,
      request.isVerified
    );
  }

  // User update model
  static createUserUpdateData(request: UpdateUserRequest): UserUpdateData {
    return new UserUpdateData(
      request.email,
      request.firstName,
      request.lastName,
      request.phone,
      request.role,
      request.isActive,
      request.isVerified,
      request.password
    );
  }

  // User list query model
  static createUserListQuery(query: User<PERSON>istQuery): UserListQueryData {
    return new UserListQueryData(
      query.page,
      query.limit,
      query.search,
      query.role,
      query.isActive,
      query.isVerified,
      query.sortBy,
      query.sortOrder
    );
  }

  // Bulk operation model
  static createBulkOperationData(operation: BulkUserOperation): BulkOperationData {
    return new BulkOperationData(operation.userIds, operation.updates);
  }
}

// User creation data model with validation
export class UserCreationData {
  constructor(
    public readonly email: string,
    public readonly password: string,
    public readonly firstName: string,
    public readonly lastName: string,
    public readonly phone?: string,
    public readonly role?: UserRole,
    public readonly isActive?: boolean,
    public readonly isVerified?: boolean
  ) {
    this.validate();
  }

  private validate(): void {
    if (!this.email || !this.isValidEmail(this.email)) {
      throw new Error('Valid email is required');
    }
    
    if (!this.password || !this.isValidPassword(this.password)) {
      throw new Error('Password must meet security requirements');
    }
    
    if (!this.firstName?.trim()) {
      throw new Error('First name is required');
    }
    
    if (!this.lastName?.trim()) {
      throw new Error('Last name is required');
    }
    
    if (this.phone && !this.isValidPhone(this.phone)) {
      throw new Error('Invalid phone number format');
    }

    if (this.role && !this.isValidRole(this.role)) {
      throw new Error('Invalid user role');
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 255;
  }

  private isValidPassword(password: string): boolean {
    // Password must be 8-128 chars with uppercase, lowercase, number, and special char
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,128}$/;
    return passwordRegex.test(password);
  }

  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone) && phone.length <= 20;
  }

  private isValidRole(role: UserRole): boolean {
    return Object.values(UserRole).includes(role);
  }

  // Get sanitized data for storage
  getStorageData(): CreateUserData {
    return {
      email: this.email.toLowerCase().trim(),
      password: this.password, // Note: Should be hashed before storage
      firstName: this.firstName.trim(),
      lastName: this.lastName.trim(),
      phone: this.phone?.trim() || null,
      role: this.role || UserRole.CUSTOMER,
      isActive: this.isActive ?? true,
      isVerified: this.isVerified ?? false,
    };
  }

  // Get normalized email for comparison
  getNormalizedEmail(): string {
    return this.email.toLowerCase().trim();
  }

  // Check if user data is complete
  isComplete(): boolean {
    return !!(this.email && this.password && this.firstName && this.lastName);
  }
}

// User update data model with validation
export class UserUpdateData {
  constructor(
    public readonly email?: string,
    public readonly firstName?: string,
    public readonly lastName?: string,
    public readonly phone?: string,
    public readonly role?: UserRole,
    public readonly isActive?: boolean,
    public readonly isVerified?: boolean,
    public readonly password?: string
  ) {
    this.validate();
  }

  private validate(): void {
    if (this.email && !this.isValidEmail(this.email)) {
      throw new Error('Invalid email format');
    }
    
    if (this.firstName && !this.isValidName(this.firstName)) {
      throw new Error('Invalid first name format');
    }
    
    if (this.lastName && !this.isValidName(this.lastName)) {
      throw new Error('Invalid last name format');
    }
    
    if (this.phone && !this.isValidPhone(this.phone)) {
      throw new Error('Invalid phone number format');
    }

    if (this.role && !this.isValidRole(this.role)) {
      throw new Error('Invalid user role');
    }

    if (this.password && !this.isValidPassword(this.password)) {
      throw new Error('Password must meet security requirements');
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 255;
  }

  private isValidName(name: string): boolean {
    return name.trim().length >= 1 && name.trim().length <= 150;
  }

  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone) && phone.length <= 20;
  }

  private isValidRole(role: UserRole): boolean {
    return Object.values(UserRole).includes(role);
  }

  private isValidPassword(password: string): boolean {
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,128}$/;
    return passwordRegex.test(password);
  }

  // Check if there are any updates
  hasUpdates(): boolean {
    return !!(this.email || this.firstName || this.lastName || this.phone || 
              this.role !== undefined || this.isActive !== undefined || 
              this.isVerified !== undefined || this.password);
  }

  // Get sanitized data for storage
  getStorageData(): UpdateUserData {
    const data: UpdateUserData = {};
    
    if (this.email) data.email = this.email.toLowerCase().trim();
    if (this.firstName) data.firstName = this.firstName.trim();
    if (this.lastName) data.lastName = this.lastName.trim();
    if (this.phone) data.phone = this.phone.trim();
    if (this.role !== undefined) data.role = this.role;
    if (this.isActive !== undefined) data.isActive = this.isActive;
    if (this.isVerified !== undefined) data.isVerified = this.isVerified;
    if (this.password) data.password = this.password; // Note: Should be hashed before storage

    return data;
  }

  // Get normalized email for comparison
  getNormalizedEmail(): string | undefined {
    return this.email ? this.email.toLowerCase().trim() : undefined;
  }
}

// User list query data model with validation
export class UserListQueryData {
  constructor(
    public readonly page?: number,
    public readonly limit?: number,
    public readonly search?: string,
    public readonly role?: UserRole,
    public readonly isActive?: boolean,
    public readonly isVerified?: boolean,
    public readonly sortBy?: string,
    public readonly sortOrder?: 'asc' | 'desc'
  ) {
    this.validate();
  }

  private validate(): void {
    if (this.page !== undefined && (this.page < 1 || !Number.isInteger(this.page))) {
      throw new Error('Page must be a positive integer');
    }
    
    if (this.limit !== undefined && (this.limit < 1 || this.limit > 100 || !Number.isInteger(this.limit))) {
      throw new Error('Limit must be between 1 and 100');
    }
    
    if (this.search && this.search.length > 255) {
      throw new Error('Search term too long');
    }

    if (this.role && !this.isValidRole(this.role)) {
      throw new Error('Invalid user role');
    }

    if (this.sortBy && !this.isValidSortBy(this.sortBy)) {
      throw new Error('Invalid sort field');
    }

    if (this.sortOrder && !this.isValidSortOrder(this.sortOrder)) {
      throw new Error('Invalid sort order');
    }
  }

  private isValidRole(role: UserRole): boolean {
    return Object.values(UserRole).includes(role);
  }

  private isValidSortBy(sortBy: string): boolean {
    const validSortFields = ['createdAt', 'updatedAt', 'firstName', 'lastName', 'email'];
    return validSortFields.includes(sortBy);
  }

  private isValidSortOrder(sortOrder: string): boolean {
    return sortOrder === 'asc' || sortOrder === 'desc';
  }

  // Get query parameters for database
  getQueryParams() {
    return {
      page: this.page || 1,
      limit: this.limit || 20,
      search: this.search?.trim(),
      role: this.role,
      isActive: this.isActive,
      isVerified: this.isVerified,
      sortBy: this.sortBy || 'createdAt',
      sortOrder: this.sortOrder || 'desc'
    };
  }

  // Calculate skip value for pagination
  getSkip(): number {
    const page = this.page || 1;
    const limit = this.limit || 20;
    return (page - 1) * limit;
  }

  // Check if query has filters
  hasFilters(): boolean {
    return !!(this.search || this.role !== undefined || 
              this.isActive !== undefined || this.isVerified !== undefined);
  }
}

// Bulk operation data model with validation
export class BulkOperationData {
  constructor(
    public readonly userIds: string[],
    public readonly updates?: Partial<UpdateUserRequest>
  ) {
    this.validate();
  }

  private validate(): void {
    if (!this.userIds || this.userIds.length === 0) {
      throw new Error('User IDs are required');
    }

    if (this.userIds.length > 100) {
      throw new Error('Cannot process more than 100 users at once');
    }

    if (this.userIds.some(id => !id || typeof id !== 'string')) {
      throw new Error('All user IDs must be valid strings');
    }

    if (this.updates && Object.keys(this.updates).length === 0) {
      throw new Error('Updates object cannot be empty');
    }
  }

  // Get unique user IDs
  getUniqueUserIds(): string[] {
    return [...new Set(this.userIds)];
  }

  // Check if operation has updates
  hasUpdates(): boolean {
    return !!(this.updates && Object.keys(this.updates).length > 0);
  }

  // Get operation size
  getOperationSize(): number {
    return this.userIds.length;
  }
}

// User profile model with business logic
export class UserProfileModel {
  constructor(
    public readonly id: string,
    public readonly email: string,
    public readonly firstName: string,
    public readonly lastName: string,
    public readonly role: UserRole,
    public readonly isActive: boolean,
    public readonly isVerified: boolean,
    public readonly avatar: string | null,
    public readonly phone: string | null,
    public readonly createdAt: Date,
    public readonly updatedAt: Date,
    public readonly lastLoginAt: Date | null,
    public readonly emailVerified: Date | null,
    public readonly loginAttempts: number,
    public readonly lockedUntil: Date | null
  ) {}

  // Create from User entity
  static fromUser(user: User): UserProfileModel {
    return new UserProfileModel(
      user.id,
      user.email,
      user.firstName,
      user.lastName,
      user.role,
      user.isActive,
      user.isVerified,
      user.avatar,
      user.phone,
      user.createdAt,
      user.updatedAt,
      user.lastLoginAt,
      user.emailVerified,
      user.loginAttempts,
      user.lockedUntil
    );
  }

  // Get display name
  getDisplayName(): string {
    return `${this.firstName} ${this.lastName}`.trim();
  }

  // Check if profile is complete
  isProfileComplete(): boolean {
    return !!(this.firstName && this.lastName && this.email);
  }

  // Get profile completion percentage
  getCompletionPercentage(): number {
    const fields = [
      this.firstName,
      this.lastName,
      this.email,
      this.phone,
      this.avatar
    ];
    
    const completedFields = fields.filter(field => !!field).length;
    return Math.round((completedFields / fields.length) * 100);
  }

  // Check if user is locked
  isLocked(): boolean {
    return !!(this.lockedUntil && this.lockedUntil > new Date());
  }

  // Check if user can login
  canLogin(): boolean {
    return this.isActive && !this.isLocked();
  }

  // Check if user is admin
  isAdmin(): boolean {
    return this.role === UserRole.ADMIN;
  }

  // Check if user is provider
  isProvider(): boolean {
    return this.role === UserRole.PROVIDER;
  }

  // Check if user is customer
  isCustomer(): boolean {
    return this.role === UserRole.CUSTOMER;
  }

  // Convert to UserSummary
  toUserSummary(): UserSummary {
    return {
      id: this.id,
      email: this.email,
      firstName: this.firstName,
      lastName: this.lastName,
      role: this.role,
      isActive: this.isActive,
      isVerified: this.isVerified,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  // Convert to UserDetail
  toUserDetail(): UserDetail {
    return {
      ...this.toUserSummary(),
      phone: this.phone || undefined,
      lastLoginAt: this.lastLoginAt || undefined,
      avatar: this.avatar || undefined,
      emailVerified: this.emailVerified || undefined,
      loginAttempts: this.loginAttempts,
      lockedUntil: this.lockedUntil || undefined
    };
  }

  // Convert to JSON
  toJSON() {
    return {
      id: this.id,
      email: this.email,
      firstName: this.firstName,
      lastName: this.lastName,
      role: this.role,
      isActive: this.isActive,
      isVerified: this.isVerified,
      avatar: this.avatar,
      phone: this.phone,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      lastLoginAt: this.lastLoginAt,
      emailVerified: this.emailVerified,
      loginAttempts: this.loginAttempts,
      lockedUntil: this.lockedUntil,
      displayName: this.getDisplayName(),
      profileComplete: this.isProfileComplete(),
      completionPercentage: this.getCompletionPercentage(),
      isLocked: this.isLocked(),
      canLogin: this.canLogin(),
      isAdmin: this.isAdmin(),
      isProvider: this.isProvider(),
      isCustomer: this.isCustomer()
    };
  }
}

// User statistics model
export class UserStatisticsModel {
  constructor(
    public readonly total: number,
    public readonly totalUsers: number,
    public readonly active: number,
    public readonly activeUsers: number,
    public readonly inactive: number,
    public readonly verified: number,
    public readonly verifiedUsers: number,
    public readonly unverified: number,
    public readonly byRole: Record<UserRole, number>,
    public readonly usersByRole: Record<UserRole, number>,
    public readonly recentRegistrations: number,
    public readonly averageRegistrationsPerDay: number,
    public readonly newUsersThisMonth: number
  ) {}

  // Calculate growth rate
  getGrowthRate(): number {
    if (this.total === 0) return 0;
    return Math.round((this.recentRegistrations / this.total) * 100);
  }

  // Get most common role
  getMostCommonRole(): UserRole {
    let maxCount = 0;
    let mostCommonRole: UserRole = UserRole.CUSTOMER;
    
    Object.entries(this.byRole).forEach(([role, count]) => {
      if (count > maxCount) {
        maxCount = count;
        mostCommonRole = role as UserRole;
      }
    });
    
    return mostCommonRole;
  }

  // Get verification rate
  getVerificationRate(): number {
    if (this.total === 0) return 0;
    return Math.round((this.verified / this.total) * 100);
  }

  // Get active user rate
  getActiveUserRate(): number {
    if (this.total === 0) return 0;
    return Math.round((this.active / this.total) * 100);
  }

  // Convert to UserStats
  toUserStats(): UserStats {
    return {
      total: this.total,
      totalUsers: this.totalUsers,
      active: this.active,
      activeUsers: this.activeUsers,
      inactive: this.inactive,
      verified: this.verified,
      verifiedUsers: this.verifiedUsers,
      unverified: this.unverified,
      byRole: this.byRole,
      usersByRole: this.usersByRole,
      recentRegistrations: this.recentRegistrations,
      averageRegistrationsPerDay: this.averageRegistrationsPerDay,
      newUsersThisMonth: this.newUsersThisMonth
    };
  }

  // Convert to JSON with calculated metrics
  toJSON() {
    return {
      ...this.toUserStats(),
      growthRate: this.getGrowthRate(),
      mostCommonRole: this.getMostCommonRole(),
      verificationRate: this.getVerificationRate(),
      activeUserRate: this.getActiveUserRate()
    };
  }
} 