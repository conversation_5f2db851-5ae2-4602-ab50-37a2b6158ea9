import { Request, Response, NextFunction } from 'express';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { ValidationError as JoiValidationError } from 'joi';
import { ErrorResponse } from '../types/auth';
import { createLogger } from '../utils/logger';
import { sanitizeRequestBody, sanitizeHeaders } from '../utils/sanitization';

// Custom error class
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;
    Error.captureStackTrace(this, this.constructor);
  }
}

// Authentication specific errors
export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied') {
    super(message, 403);
  }
}

export class ValidationError extends AppError {
  public validationErrors: any[];

  constructor(message: string = 'Validation failed', validationErrors: any[] = []) {
    super(message, 400);
    this.validationErrors = validationErrors;
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404);
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource already exists') {
    super(message, 409);
  }
}

export class TooManyRequestsError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429);
  }
}

// Error handler middleware
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const logger = createLogger('ErrorHandler');
  let statusCode = 500;
  let message = 'Internal server error';
  let details: any = undefined;

  // Handle different types of errors
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
    
    if (error instanceof ValidationError) {
      details = { validationErrors: error.validationErrors };
    }
  } else if (error instanceof PrismaClientKnownRequestError) {
    // Handle Prisma errors
    switch (error.code) {
      case 'P2002':
        statusCode = 409;
        message = 'Resource already exists';
        const target = error.meta?.target as string[];
        if (target?.includes('email')) {
          message = 'Email address is already registered';
        }
        break;
      case 'P2025':
        statusCode = 404;
        message = 'Record not found';
        break;
      case 'P2003':
        statusCode = 400;
        message = 'Invalid reference';
        break;
      default:
        statusCode = 500;
        message = 'Database error';
    }
  } else if (error.name === 'ValidationError') {
    // Handle Joi validation errors
    const joiError = error as JoiValidationError;
    statusCode = 400;
    message = 'Validation failed';
    details = {
      validationErrors: joiError.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }))
    };
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
  } else if (error.name === 'SyntaxError' && 'body' in error) {
    statusCode = 400;
    message = 'Invalid JSON format';
  }

  // Log error with appropriate level and context
  const errorContext = {
    name: error.name,
    statusCode,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    requestId: res.getHeader('X-Request-ID'),
    userId: (req as any).userId,
    headers: sanitizeHeaders(req.headers),
    body: sanitizeRequestBody(req.body),
    query: req.query,
    params: req.params
  };

  if (statusCode >= 500) {
    // Server errors - log as error
    logger.error(`Server error: ${message}`, error, errorContext);
  } else if (statusCode >= 400) {
    // Client errors - log as warning
    logger.warn(`Client error: ${message}`, { 
      ...errorContext,
      errorMessage: error.message,
      stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined
    });
  }

  // Build error response
  const errorResponse: ErrorResponse = {
    error: error.name || 'Error',
    message,
    statusCode,
    timestamp: new Date().toISOString(),
    path: req.path
  };

  // Add validation errors if present
  if (details) {
    (errorResponse as any).details = details;
  }

  // Add stack trace in development
  if (process.env.NODE_ENV !== 'production' && error.stack) {
    (errorResponse as any).stack = error.stack;
  }

  res.status(statusCode).json(errorResponse);
};

// Note: sanitizeRequestBody now imported from utils/sanitization.ts

// Async error wrapper
export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 404 error handler
export const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};

export default {
  AppError,
  AuthenticationError,
  AuthorizationError,
  ValidationError,
  NotFoundError,
  ConflictError,
  TooManyRequestsError,
  errorHandler,
  asyncHandler,
  notFoundHandler
};