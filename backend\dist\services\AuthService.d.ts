import { IAuthRepository, SessionData, AccountLockInfo } from '../repositories/AuthRepository';
import { AuthUser, TokenPair, RegisterRequest, LoginRequest, ChangePasswordRequest, UpdateProfileRequest } from '../types/auth';
export interface IAuthService {
    register(data: RegisterRequest, context: RequestContext): Promise<AuthResult>;
    login(data: LoginRequest, context: RequestContext): Promise<AuthResult>;
    refreshTokens(refreshToken: string, context: RequestContext): Promise<TokenResult>;
    logout(refreshToken: string | undefined, user: AuthUser, context: RequestContext): Promise<void>;
    logoutAllDevices(user: AuthUser, context: RequestContext): Promise<void>;
    getProfile(userId: string): Promise<AuthUser>;
    updateProfile(userId: string, data: UpdateProfileRequest): Promise<AuthUser>;
    changePassword(userId: string, data: ChangePasswordRequest, context: RequestContext): Promise<void>;
    getUserSessions(userId: string): Promise<SessionData[]>;
    revokeSession(sessionId: string, userId: string): Promise<void>;
    isAccountLocked(userId: string): Promise<boolean>;
    getAccountLockInfo(userId: string): Promise<AccountLockInfo | null>;
}
export interface RequestContext {
    ipAddress: string;
    userAgent: string;
}
export interface AuthResult {
    user: AuthUser;
    tokens: TokenPair;
}
export interface TokenResult {
    tokens: TokenPair;
}
export declare class AuthService implements IAuthService {
    private authRepository;
    constructor(authRepository: IAuthRepository);
    register(data: RegisterRequest, context: RequestContext): Promise<AuthResult>;
    login(data: LoginRequest, context: RequestContext): Promise<AuthResult>;
    refreshTokens(refreshToken: string, context: RequestContext): Promise<TokenResult>;
    logout(refreshToken: string | undefined, user: AuthUser, context: RequestContext): Promise<void>;
    logoutAllDevices(user: AuthUser, context: RequestContext): Promise<void>;
    getProfile(userId: string): Promise<AuthUser>;
    updateProfile(userId: string, data: UpdateProfileRequest): Promise<AuthUser>;
    changePassword(userId: string, data: ChangePasswordRequest, context: RequestContext): Promise<void>;
    getUserSessions(userId: string): Promise<SessionData[]>;
    revokeSession(sessionId: string, userId: string): Promise<void>;
    isAccountLocked(userId: string): Promise<boolean>;
    getAccountLockInfo(userId: string): Promise<AccountLockInfo | null>;
    private generateTokenPair;
    private logSecurityEvent;
}
export default AuthService;
//# sourceMappingURL=AuthService.d.ts.map