import React from 'react';
import { Link } from 'react-router-dom';

interface CategoryCardHomeProps {
  categoryName: string;
  imageUrl: string;
  route: string;
  price: string;
}

const CategoryCardHome: React.FC<CategoryCardHomeProps> = ({
  categoryName,
  imageUrl,
  route,
  price
}) => {
  return (
    <Link to={route} className="block group">
      <div className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
        {/* Category Badge */}
        <div className="relative">
          <div className="absolute top-3 left-3 z-10">
            <span className="bg-orange-500 text-white px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wide">
              Browse Category
            </span>
          </div>
          
          {/* Category Image */}
          <div className="aspect-square overflow-hidden">
            <img
              src={imageUrl}
              alt={categoryName}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
          </div>
        </div>
        
        {/* Category Info */}
        <div className="p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {categoryName}
          </h3>
          <p className="text-sm text-gray-600">
            Starting at <span className="font-semibold">{price}</span>
          </p>
        </div>
      </div>
    </Link>
  );
};

export default CategoryCardHome;