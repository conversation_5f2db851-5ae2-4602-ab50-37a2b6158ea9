import { ServiceCategoryRepository } from '@/repositories/ServiceCategoryRepository';

const mockPrisma = {
  service_categories: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  },
  service: {
    count: jest.fn(),
  },
} as any;

describe('ServiceCategoryRepository', () => {
  let repo: ServiceCategoryRepository;

  beforeEach(() => {
    repo = new ServiceCategoryRepository(mockPrisma);
    jest.clearAllMocks();
  });

  it('getServiceCategories - queries active categories with counts', async () => {
    mockPrisma.service_categories.findMany.mockResolvedValue([
      { id: 'cat-1', name: 'Cat', route: 'cat', isActive: true, sortOrder: 0, _count: { services: 2 } },
    ]);
    const result = await repo.getServiceCategories();
    expect(mockPrisma.service_categories.findMany).toHaveBeenCalledWith({
      where: { isActive: true },
      include: { _count: { select: { services: true } } },
      orderBy: { sortOrder: 'asc' },
    });
    expect(result[0]).toEqual(expect.objectContaining({ id: 'cat-1', serviceCount: 2 }));
  });

  it('findServiceCategoryById - returns detail with services', async () => {
    mockPrisma.service_categories.findUnique.mockResolvedValue({
      id: 'cat-1',
      name: 'Cat',
      route: 'cat',
      isActive: true,
      sortOrder: 0,
      services: [],
    });
    const result = await repo.findServiceCategoryById('cat-1');
    expect(mockPrisma.service_categories.findUnique).toHaveBeenCalledWith({
      where: { id: 'cat-1' },
      include: {
        services: {
          where: { isActive: true },
          include: {
            service_categories: { select: { name: true } },
            providers: { where: { isActive: true }, select: { id: true } },
          },
          orderBy: { sortOrder: 'asc' },
        },
      },
    });
    expect(result?.id).toBe('cat-1');
  });

  it('create/update/delete category - delegates to prisma and returns boolean on delete', async () => {
    const data = { id: 'cat-1', name: 'Cat', route: 'cat', isActive: true, sortOrder: 0 } as any;
    const expected = {
      data: expect.objectContaining({
        id: 'cat-1',
        name: 'Cat',
        route: 'cat',
        isActive: true,
        sortOrder: 0,
      }),
    };
    mockPrisma.service_categories.create.mockResolvedValue({ id: 'cat-1' });
    await repo.createServiceCategory(data);
    expect(mockPrisma.service_categories.create).toHaveBeenCalledWith(expected);

    mockPrisma.service_categories.update.mockResolvedValue({});
    await repo.updateServiceCategory('cat-1', { name: 'New' });
    expect(mockPrisma.service_categories.update).toHaveBeenCalledWith({
      where: { id: 'cat-1' },
      data: { name: 'New' },
    });

    mockPrisma.service_categories.delete.mockResolvedValue({});
    await expect(repo.deleteServiceCategory('cat-1')).resolves.toBe(true);
    mockPrisma.service_categories.delete.mockRejectedValue(new Error('fk'));
    await expect(repo.deleteServiceCategory('cat-1')).resolves.toBe(false);
  });

  it('serviceCategoryExists and countServicesByCategory', async () => {
    mockPrisma.service_categories.count.mockResolvedValue(1);
    await expect(repo.serviceCategoryExists('cat-1')).resolves.toBe(true);
    mockPrisma.service.count.mockResolvedValue(3);
    await expect(repo.countServicesByCategory('cat-1')).resolves.toBe(3);
  });
});
