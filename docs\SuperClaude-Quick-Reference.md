# 🚀 SuperClaude Quick Reference: SRP & DRY Refactoring

## 📋 **Essential Commands**

### **SuperClaude Core Commands**

```bash
# Main refactoring operations
/sc:refactor analyze [file_path]              # Analyze file for refactoring opportunities
/sc:refactor extract-repository [class] --domain=[domain]  # Extract domain repository
/sc:refactor extract-service [class] --business-domain=[domain]  # Extract business service
/sc:refactor optimize-controller [class] --apply-decorators  # Optimize controller with decorators
/sc:refactor consolidate-types [file] --eliminate-duplicates  # Consolidate type definitions
/sc:refactor complete --target=[goal] --strategy=[strategy]  # Complete refactoring

# Implementation commands
/sc:implement [component] [type]              # Implement new components
/sc:implement unit-tests [component]          # Generate unit tests
/sc:implement integration-tests generation    # Generate integration tests
```

### **Custom SRP/DRY Analysis Commands**

```bash
# SRP Analysis
/srp_analyze [file_path]                      # Analyze SRP violations
/srp_analyze backend/src/repositories/ServiceRepository.ts

# DRY Analysis
/dry_analyze [pattern_type]                   # Identify code duplication
/dry_analyze logging_patterns
/dry_analyze validation_logic
/dry_analyze data_mapping
/dry_analyze type_definitions
```

### **Validation Commands**

```bash
# Validate refactoring results
/validate_refactoring srp_compliance          # SRP compliance validation
/validate_refactoring dry_compliance          # DRY compliance validation
/validate_refactoring performance             # Performance validation
```

## 🎯 **Phase-by-Phase Commands**

### **Phase 1: Analysis (Day 1)**

```bash
# SuperClaude refactoring analysis
/sc:refactor analyze backend/src/repositories/ServiceRepository.ts
/sc:refactor analyze backend/src/services/ServiceService.ts
/sc:refactor analyze backend/src/controllers/ServiceController.ts

# Custom SRP/DRY analysis
/srp_analyze backend/src/repositories/ServiceRepository.ts
/srp_analyze backend/src/services/ServiceService.ts
/srp_analyze backend/src/controllers/ServiceController.ts
/dry_analyze logging_patterns
/dry_analyze validation_logic
/dry_analyze data_mapping
/dry_analyze type_definitions

# Create refactoring plan
/sc:refactor plan --target=srp-dry-compliance --strategy=incremental
```

### **Phase 2: Core Refactoring (Days 2-4)**

```bash
# Extract repositories
/sc:refactor extract-repository ServiceRepository --domain=service
/sc:refactor extract-repository ServiceRepository --domain=category
/sc:refactor extract-repository ServiceRepository --domain=form-field

# Extract services
/sc:refactor extract-service ServiceService --business-domain=service
/sc:refactor extract-service ServiceService --business-domain=category
/sc:refactor extract-service ServiceService --business-domain=form-field

# Optimize controllers
/sc:refactor optimize-controller ServiceController --apply-decorators
```

### **Phase 3: Implementation (Days 5-6)**

```bash
# Create utility classes
/sc:implement ServiceMapper utility class
/sc:implement ValidationUtils utility class
/sc:implement LoggingDecorator utility class
/sc:implement ResponseUtils utility class

# Create specialized services
/sc:implement ServicePriceCalculator extraction
/sc:implement ServiceAnalyticsService extraction
```

### **Phase 4: Type System (Day 7)**

```bash
# Consolidate types
/sc:refactor consolidate-types service.ts --eliminate-duplicates
/sc:refactor consolidate-types service.ts --create-base-types
/sc:implement TypeComposition patterns
/sc:implement StrictTyping implementation
```

### **Phase 5: Testing (Days 8-10)**

```bash
# Generate tests
/sc:implement unit-tests repository
/sc:implement unit-tests service
/sc:implement unit-tests controller
/sc:implement integration-tests generation

# Validate results
/validate_refactoring srp_compliance
/validate_refactoring dry_compliance
/validate_refactoring performance
```

## 🚀 **Quick Start Commands**

### **One-Command Refactoring**

```bash
# Complete refactoring in one command
/sc:refactor complete --target=srp-dry-compliance --strategy=incremental
```

### **Phase-Based Refactoring**

```bash
# Phase 1: Analysis
/sc:refactor analyze --all-files

# Phase 2: Core refactoring
/sc:refactor extract-all --strategy=domain-driven

# Phase 3: Implementation
/sc:implement all-utilities

# Phase 4: Type system
/sc:refactor consolidate-all-types

# Phase 5: Testing
/sc:implement all-tests
```

---

**💡 Pro Tip**: Use `/sc:help [command]` to get detailed help for any command.

**📖 For detailed instructions, see the full implementation guide: `docs/SuperClaude-Implementation-Guide.md`**
