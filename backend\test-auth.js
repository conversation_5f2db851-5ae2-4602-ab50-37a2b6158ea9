const fetch = require('node-fetch');

// Simple test to verify authentication endpoints
async function testAuth() {
  const baseUrl = 'http://localhost:3001';
  
  try {
    // Test health endpoint
    console.log('Testing health endpoint...');
    const healthResponse = await fetch(`${baseUrl}/health`);
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData);
    
    // Test registration
    console.log('\nTesting user registration...');
    const registerData = {
      email: '<EMAIL>',
      password: 'Test123!@#',
      firstName: 'Test',
      lastName: 'User',
      phone: '+**********'
    };
    
    const registerResponse = await fetch(`${baseUrl}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(registerData)
    });
    
    if (registerResponse.ok) {
      const registerResult = await registerResponse.json();
      console.log('✅ Registration successful:', registerResult.message);
      console.log('   User ID:', registerResult.data.user.id);
      console.log('   Access Token:', registerResult.data.tokens.accessToken ? 'Generated' : 'Missing');
      
      // Test login
      console.log('\nTesting user login...');
      const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: registerData.email,
          password: registerData.password
        })
      });
      
      if (loginResponse.ok) {
        const loginResult = await loginResponse.json();
        console.log('✅ Login successful:', loginResult.message);
        
        // Test protected endpoint
        console.log('\nTesting protected endpoint...');
        const meResponse = await fetch(`${baseUrl}/api/auth/me`, {
          headers: {
            'Authorization': `Bearer ${loginResult.data.tokens.accessToken}`
          }
        });
        
        if (meResponse.ok) {
          const meResult = await meResponse.json();
          console.log('✅ Protected endpoint access:', meResult.data.user.email);
        } else {
          console.log('❌ Failed to access protected endpoint');
        }
      } else {
        const loginError = await loginResponse.json();
        console.log('❌ Login failed:', loginError);
      }
    } else {
      const registerError = await registerResponse.json();
      console.log('❌ Registration failed:', registerError);
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
    console.log('\n💡 Make sure the server is running: npm start');
  }
}

testAuth();