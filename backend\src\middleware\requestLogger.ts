import {Request, Response, NextFunction} from 'express';
import {httpLogger, createLogger} from '../utils/logger';
import {AuthenticatedRequest} from '../types/auth';

// Performance timing middleware
export interface TimedRequest extends Request {
	startTime?: number;
}

// Request context for logging
interface RequestContext {
	requestId: string;
	method: string;
	url: string;
	userAgent?: string;
	ip: string;
	userId?: string;
	userEmail?: string;
	startTime: number;
}

// Generate unique request ID
function generateRequestId(): string {
	return `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}

// Get client IP address
function getClientIP(req: Request): string {
	return (
		(req.headers['x-forwarded-for'] as string) ||
		(req.headers['x-real-ip'] as string) ||
		req.connection?.remoteAddress ||
		req.socket?.remoteAddress ||
		req.ip ||
		'unknown'
	);
}

// Enhanced request logging middleware
export const requestLogger = (
	req: TimedRequest & AuthenticatedRequest,
	res: Response,
	next: NextFunction
): void => {
	const startTime = Date.now();
	req.startTime = startTime;

	const requestId = generateRequestId();
	const context: RequestContext = {
		requestId,
		method: req.method,
		url: req.originalUrl || req.url,
		userAgent: req.headers['user-agent'],
		ip: getClientIP(req),
		userId: req.userId,
		userEmail: (req as any).user?.email,
		startTime,
	};

	// Add request ID to response headers for debugging
	res.setHeader('X-Request-ID', requestId);

	// Log incoming request
	httpLogger.info('Incoming request', {
		requestId: context.requestId,
		method: context.method,
		url: context.url,
		userAgent: context.userAgent,
		ip: context.ip,
		userId: context.userId,
		userEmail: context.userEmail,
		headers: req.headers,
		query: req.query,
		body: sanitizeRequestBody(req.body),
	});

	// Override res.end to log response
	const originalEnd = res.end;
	res.end = function (chunk?: any, encoding?: any): Response {
		const duration = Date.now() - startTime;
		const responseSize =
			res.get('content-length') || (chunk ? Buffer.byteLength(chunk) : 0);

		// Log response
		const logLevel = res.statusCode >= 400 ? 'warn' : 'info';
		httpLogger[logLevel]('Request completed', {
			requestId: context.requestId,
			method: context.method,
			url: context.url,
			statusCode: res.statusCode,
			duration,
			responseSize,
			userId: context.userId,
			userEmail: context.userEmail,
			ip: context.ip,
			userAgent: context.userAgent,
		});

		// Log slow requests
		if (duration > 1000) {
			httpLogger.warn('Slow request detected', {
				requestId: context.requestId,
				method: context.method,
				url: context.url,
				duration,
				statusCode: res.statusCode,
				userId: context.userId,
			});
		}

		// Call original end function
		return originalEnd.call(this, chunk, encoding);
	};

	next();
};

// Sanitize request body for logging (remove sensitive data)
function sanitizeRequestBody(body: any): any {
	if (!body || typeof body !== 'object') {
		return body;
	}

	const sensitiveFields = [
		'password',
		'currentPassword',
		'newPassword',
		'confirmPassword',
		'token',
		'refreshToken',
		'accessToken',
		'apiKey',
		'secret',
		'creditCard',
		'ssn',
		'socialSecurityNumber',
	];

	const sanitized = {...body};

	sensitiveFields.forEach((field) => {
		if (field in sanitized) {
			sanitized[field] = '[REDACTED]';
		}
	});

	return sanitized;
}

// Middleware to log specific events
export const logEvent = (
	eventName: string,
	getData?: (req: Request) => any
) => {
	return (req: Request, res: Response, next: NextFunction): void => {
		const logger = createLogger('Event');
		const eventData = getData ? getData(req) : {};

		logger.info(`Event: ${eventName}`, {
			event: eventName,
			requestId: res.getHeader('X-Request-ID'),
			userId: (req as AuthenticatedRequest).userId,
			ip: getClientIP(req),
			userAgent: req.headers['user-agent'],
			...eventData,
		});

		next();
	};
};

// Middleware for authentication events
export const logAuthEvent = (
	eventType:
		| 'LOGIN'
		| 'LOGOUT'
		| 'REGISTER'
		| 'PASSWORD_CHANGE'
		| 'TOKEN_REFRESH'
) => {
	return (
		req: AuthenticatedRequest,
		res: Response,
		next: NextFunction
	): void => {
		const logger = createLogger('AuthEvent');

		// Log the event after the request is processed
		const originalEnd = res.end;
		res.end = function (chunk?: any, encoding?: any): Response {
			const isSuccess = res.statusCode >= 200 && res.statusCode < 300;

			logger.auth(
				`${eventType}_${isSuccess ? 'SUCCESS' : 'FAILURE'}`,
				req.userId,
				{
					eventType,
					success: isSuccess,
					statusCode: res.statusCode,
					requestId: res.getHeader('X-Request-ID'),
					ip: getClientIP(req),
					userAgent: req.headers['user-agent'],
					email: req.body?.email || (req as any).user?.email,
				}
			);

			return originalEnd.call(this, chunk, encoding);
		};

		next();
	};
};

// Error logging middleware (should be used after error handler)
export const logError = (
	err: Error,
	req: Request,
	res: Response,
	next: NextFunction
): void => {
	const logger = createLogger('Error');

	logger.error('Request error', err, {
		requestId: res.getHeader('X-Request-ID'),
		method: req.method,
		url: req.originalUrl || req.url,
		statusCode: res.statusCode,
		userId: (req as AuthenticatedRequest).userId,
		ip: getClientIP(req),
		userAgent: req.headers['user-agent'],
		body: sanitizeRequestBody(req.body),
		query: req.query,
		params: req.params,
	});

	next(err);
};

// Performance monitoring middleware
export const performanceLogger = (
	req: TimedRequest,
	res: Response,
	next: NextFunction
): void => {
	const startTime = Date.now();
	req.startTime = startTime;

	const originalEnd = res.end;
	res.end = function (chunk?: any, encoding?: any): Response {
		const duration = Date.now() - startTime;
		const performanceLogger = createLogger('Performance');

		// Log performance metrics
		performanceLogger.performance('Request performance', duration, {
			requestId: res.getHeader('X-Request-ID'),
			method: req.method,
			url: req.originalUrl || req.url,
			statusCode: res.statusCode,
			responseSize:
				res.get('content-length') || (chunk ? Buffer.byteLength(chunk) : 0),
		});

		return originalEnd.call(this, chunk, encoding);
	};

	next();
};

export default {
	requestLogger,
	logEvent,
	logAuthEvent,
	logError,
	performanceLogger,
};
