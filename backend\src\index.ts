import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import compression from 'compression';
import morgan from 'morgan';
import cookieParser from 'cookie-parser';
import {PrismaClient} from '@prisma/client';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

// Import type extensions
import './types/express';

// Import configuration system
import {config} from './config';
import {createLogger} from './utils/logger';

// Import routes
import authRoutes from './routes/auth';
import {errorHandler} from './middleware/errorHandler';
import {requestLogger} from './middleware/requestLogger';

const app = express();
const prisma = new PrismaClient();
const logger = createLogger('Server');

// Get configuration
const serverConfig = config.getServerConfig();
const securityConfig = config.getSecurityConfig();
const loggingConfig = config.getLoggingConfig();

// Swagger/OpenAPI configuration
const swaggerOptions = {
	definition: {
		openapi: '3.0.0',
		info: {
			title: 'PrintWeditt API',
			version: '1.0.0',
			description: 'PrintWeditt Backend API Documentation',
			contact: {
				name: 'PrintWeditt Team',
				email: '<EMAIL>',
			},
		},
		servers: [
			{
				url: serverConfig.apiBaseUrl,
				description: 'Development server',
			},
		],
		components: {
			securitySchemes: {
				bearerAuth: {
					type: 'http',
					scheme: 'bearer',
					bearerFormat: 'JWT',
				},
			},
		},
		security: [
			{
				bearerAuth: [],
			},
		],
	},
	apis: ['./src/routes/*.ts', './src/types/*.ts'], // Path to the API docs
};

const swaggerSpec = swaggerJsdoc(swaggerOptions);

// Security middleware
app.use(
	helmet({
		crossOriginResourcePolicy: {policy: 'cross-origin'},
		contentSecurityPolicy: {
			directives: {
				defaultSrc: ["'self'"],
				styleSrc: ["'self'", "'unsafe-inline'"],
				scriptSrc: ["'self'"],
				imgSrc: ["'self'", 'data:', 'https:'],
			},
		},
	})
);

// Rate limiting with configuration
const limiter = rateLimit({
	windowMs: securityConfig.rateLimitWindowMs,
	max: securityConfig.rateLimitMaxRequests,
	message: 'Too many requests from this IP, please try again later.',
	standardHeaders: true,
	legacyHeaders: false,
});

app.use(limiter);

// CORS configuration
app.use(
	cors({
		origin: serverConfig.corsOrigin,
		credentials: true,
		methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
		allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
	})
);

// Body parsing middleware
app.use(compression());
app.use(express.json({limit: `${securityConfig.maxFileSize}mb`}));
app.use(
	express.urlencoded({extended: true, limit: `${securityConfig.maxFileSize}mb`})
);
app.use(cookieParser(securityConfig.cookieSecret));

// Logging middleware
if (loggingConfig.enableConsole) {
	if (serverConfig.nodeEnv !== 'production') {
		app.use(morgan('dev'));
	} else {
		app.use(morgan('combined'));
	}
}

// Request logging middleware
app.use(requestLogger);

// API Documentation (only in development and staging)
if (serverConfig.nodeEnv !== 'production') {
	app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
	app.get('/api-docs.json', (req, res) => {
		res.setHeader('Content-Type', 'application/json');
		res.send(swaggerSpec);
	});
}

// Enhanced health check endpoint
app.get('/health', async (req, res) => {
	const startTime = Date.now();

	try {
		// Check database connection
		await prisma.$queryRaw`SELECT 1`;
		const dbStatus = 'healthy';

		// Calculate response time
		const responseTime = Date.now() - startTime;

		// Get system information
		const systemInfo = {
			uptime: process.uptime(),
			memory: process.memoryUsage(),
			cpu: process.cpuUsage(),
		};

		res.status(200).json({
			status: 'OK',
			timestamp: new Date().toISOString(),
			environment: serverConfig.nodeEnv,
			version: process.env.npm_package_version || '1.0.0',
			services: {
				database: dbStatus,
				api: 'healthy',
			},
			responseTime: `${responseTime}ms`,
			system: systemInfo,
		});
	} catch (error) {
		logger.error('Health check failed', error);
		res.status(503).json({
			status: 'ERROR',
			timestamp: new Date().toISOString(),
			environment: serverConfig.nodeEnv,
			version: process.env.npm_package_version || '1.0.0',
			services: {
				database: 'unhealthy',
				api: 'healthy',
			},
			error: 'Database connection failed',
		});
	}
});

// Metrics endpoint for monitoring
app.get('/metrics', (req, res) => {
	const metrics = {
		timestamp: new Date().toISOString(),
		uptime: process.uptime(),
		memory: process.memoryUsage(),
		cpu: process.cpuUsage(),
		environment: serverConfig.nodeEnv,
		version: process.env.npm_package_version || '1.0.0',
	};

	res.status(200).json(metrics);
});

// API versioning middleware
app.use('/api/v1', (req, res, next) => {
	req.apiVersion = 'v1';
	next();
});

// API routes
app.use('/api/v1/auth', authRoutes);

// 404 handler
app.use('*', (req, res) => {
	res.status(404).json({
		error: 'Not Found',
		message: `Route ${req.originalUrl} not found`,
		apiVersion: req.apiVersion || 'unknown',
	});
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Database connection test with enhanced error handling
async function connectDB() {
	try {
		const dbConfig = config.getDatabaseConfig();

		// Test database connection
		await prisma.$connect();

		// Test with a simple query
		await prisma.$queryRaw`SELECT 1 as test`;

		logger.info('Database connected successfully', {
			url: dbConfig.url.replace(/\/\/.*@/, '//***:***@'), // Mask credentials
			maxConnections: dbConfig.maxConnections,
			idleTimeout: dbConfig.idleTimeout,
		});

		return true;
	} catch (error) {
		logger.error('Database connection failed', error);
		return false;
	}
}

// Enhanced graceful shutdown
async function gracefulShutdown(signal: string) {
	logger.info(`Received ${signal}. Starting graceful shutdown...`);

	try {
		// Close database connection
		await prisma.$disconnect();
		logger.info('Database connection closed');

		// Close server
		process.exit(0);
	} catch (error) {
		logger.error('Error during graceful shutdown', error);
		process.exit(1);
	}
}

// Graceful shutdown handlers
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

// Unhandled promise rejection handler
process.on('unhandledRejection', (reason, promise) => {
	logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
	process.exit(1);
});

// Uncaught exception handler
process.on('uncaughtException', (error) => {
	logger.error('Uncaught Exception:', error);
	process.exit(1);
});

// Start server with enhanced error handling
async function startServer() {
	try {
		logger.info('Starting PrintWeditt server...', {
			environment: serverConfig.nodeEnv,
			port: serverConfig.port,
			host: serverConfig.host,
		});

		// Connect to database
		const dbConnected = await connectDB();
		if (!dbConnected) {
			throw new Error('Database connection failed');
		}

		// Start HTTP server
		const server = app.listen(serverConfig.port, serverConfig.host, () => {
			logger.info('Server started successfully', {
				port: serverConfig.port,
				host: serverConfig.host,
				environment: serverConfig.nodeEnv,
				apiBaseUrl: serverConfig.apiBaseUrl,
				frontendUrl: serverConfig.frontendUrl,
				healthCheck: `http://${serverConfig.host}:${serverConfig.port}/health`,
				metrics: `http://${serverConfig.host}:${serverConfig.port}/metrics`,
				docs:
					serverConfig.nodeEnv !== 'production'
						? `http://${serverConfig.host}:${serverConfig.port}/api-docs`
						: 'disabled in production',
			});
		});

		// Handle server errors
		server.on('error', (error) => {
			logger.error('Server error:', error);
			process.exit(1);
		});

		return server;
	} catch (error) {
		logger.error('Failed to start server', error);
		process.exit(1);
	}
}

// Initialize server
startServer().catch((error) => {
	logger.error('Failed to start server', error);
	process.exit(1);
});

export default app;
