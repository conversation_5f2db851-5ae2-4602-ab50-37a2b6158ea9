/**
 * PrintWedittV1 Backend Server Entry Point
 *
 * This file serves as the main entry point for the application.
 * It imports the server startup logic and starts the application.
 *
 * For development and production, this file can be used directly.
 * For testing, the server components can be imported individually.
 */

import {startServer} from './server';
import {createLogger} from './utils/logger';

const logger = createLogger('Index');

// Start the server
startServer().catch((error) => {
	logger.error('Failed to start server from index.ts', error);
	process.exit(1);
});

// Export the app for testing purposes
export {default as app} from './app';
export {ServerManager, ServiceManager, startServer} from './server';
export {databaseService, redisService} from './services';
