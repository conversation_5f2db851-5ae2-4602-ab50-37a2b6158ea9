import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import compression from 'compression';
import morgan from 'morgan';
import cookieParser from 'cookie-parser';
import {PrismaClient} from '@prisma/client';

// Import configuration system
import {config} from './config';
import {createLogger} from './utils/logger';

// Import routes
import authRoutes from './routes/auth';
import {errorHandler} from './middleware/errorHandler';

const app = express();
const prisma = new PrismaClient();
const logger = createLogger('Server');

// Get configuration
const serverConfig = config.getServerConfig();
const securityConfig = config.getSecurityConfig();
const loggingConfig = config.getLoggingConfig();

// Security middleware
app.use(
	helmet({
		crossOriginResourcePolicy: {policy: 'cross-origin'},
		contentSecurityPolicy: {
			directives: {
				defaultSrc: ["'self'"],
				styleSrc: ["'self'", "'unsafe-inline'"],
				scriptSrc: ["'self'"],
				imgSrc: ["'self'", 'data:', 'https:'],
			},
		},
	})
);

// Rate limiting with configuration
const limiter = rateLimit({
	windowMs: securityConfig.rateLimitWindowMs,
	max: securityConfig.rateLimitMaxRequests,
	message: 'Too many requests from this IP, please try again later.',
	standardHeaders: true,
	legacyHeaders: false,
});

app.use(limiter);

// CORS configuration
app.use(
	cors({
		origin: serverConfig.corsOrigin,
		credentials: true,
		methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
		allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
	})
);

// Body parsing middleware
app.use(compression());
app.use(express.json({limit: `${securityConfig.maxFileSize}mb`}));
app.use(
	express.urlencoded({extended: true, limit: `${securityConfig.maxFileSize}mb`})
);
app.use(cookieParser(securityConfig.cookieSecret));

// Logging middleware
if (loggingConfig.enableConsole) {
	if (serverConfig.nodeEnv !== 'production') {
		app.use(morgan('dev'));
	} else {
		app.use(morgan('combined'));
	}
}

// Health check endpoint
app.get('/health', (req, res) => {
	res.status(200).json({
		status: 'OK',
		timestamp: new Date().toISOString(),
		uptime: process.uptime(),
		environment: serverConfig.nodeEnv,
		version: process.env.npm_package_version || '1.0.0',
	});
});

// API routes
app.use('/api/auth', authRoutes);

// 404 handler
app.use('*', (req, res) => {
	res.status(404).json({
		error: 'Not Found',
		message: `Route ${req.originalUrl} not found`,
	});
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Database connection test
async function connectDB() {
	try {
		const dbConfig = config.getDatabaseConfig();
		await prisma.$connect();
		logger.info('Database connected successfully', {
			url: dbConfig.url.replace(/\/\/.*@/, '//***:***@'), // Mask credentials
			maxConnections: dbConfig.maxConnections,
			idleTimeout: dbConfig.idleTimeout,
		});
	} catch (error) {
		logger.error('Database connection failed', error);
		process.exit(1);
	}
}

// Graceful shutdown
process.on('SIGINT', async () => {
	logger.info('Shutting down gracefully...');
	await prisma.$disconnect();
	process.exit(0);
});

process.on('SIGTERM', async () => {
	logger.info('Shutting down gracefully...');
	await prisma.$disconnect();
	process.exit(0);
});

// Start server
async function startServer() {
	try {
		// Connect to database
		await connectDB();

		// Start HTTP server
		app.listen(serverConfig.port, serverConfig.host, () => {
			logger.info('Server started successfully', {
				port: serverConfig.port,
				host: serverConfig.host,
				environment: serverConfig.nodeEnv,
				apiBaseUrl: serverConfig.apiBaseUrl,
				frontendUrl: serverConfig.frontendUrl,
				healthCheck: `http://${serverConfig.host}:${serverConfig.port}/health`,
			});
		});
	} catch (error) {
		logger.error('Failed to start server', error);
		process.exit(1);
	}
}

startServer().catch((error) => {
	logger.error('Failed to start server', error);
	process.exit(1);
});

export default app;
