{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "🚀 Launch Backend (Development)",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/backend/src/index.ts",
      "outFiles": ["${workspaceFolder}/backend/dist/**/*.js"],
      "sourceMaps": true,
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development",
        "TS_NODE_PROJECT": "${workspaceFolder}/backend/tsconfig.json"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "restart": true,
      "protocol": "inspector"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "🔧 Launch Backend (Production Build)",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/backend/dist/index.js",
      "outFiles": ["${workspaceFolder}/backend/dist/**/*.js"],
      "sourceMaps": true,
      "env": {
        "NODE_ENV": "production"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "preLaunchTask": "npm: build"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "🧪 Debug Tests",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/backend/node_modules/jest/bin/jest.js",
      "args": ["--runInBand", "--no-cache", "--detectOpenHandles"],
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "NODE_ENV": "test"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "sourceMaps": true
    },
    {
      "type": "node",
      "request": "launch",
      "name": "🔍 Debug Specific Test File",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/backend/node_modules/jest/bin/jest.js",
      "args": ["--runInBand", "--no-cache", "--detectOpenHandles", "${file}"],
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "NODE_ENV": "test"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "sourceMaps": true
    },
    {
      "type": "node",
      "request": "launch",
      "name": "🧪 Debug Tests (npm)",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/backend/node_modules/.bin/npm",
      "args": ["run", "test"],
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "NODE_ENV": "test"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "sourceMaps": true
    },
    {
      "type": "node",
      "request": "launch",
      "name": "🧪 Debug Tests (Unit Only)",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/backend/node_modules/jest/bin/jest.js",
      "args": ["--runInBand", "--no-cache", "--testPathPattern=unit"],
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "NODE_ENV": "test"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "sourceMaps": true
    },
    {
      "type": "node",
      "request": "launch",
      "name": "🧪 Debug Tests (Integration Only)",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/backend/node_modules/jest/bin/jest.js",
      "args": ["--runInBand", "--no-cache", "--testPathPattern=integration"],
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "NODE_ENV": "test"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "sourceMaps": true
    },
    {
      "type": "node",
      "request": "launch",
      "name": "📊 Debug with Database",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/backend/src/index.ts",
      "outFiles": ["${workspaceFolder}/backend/dist/**/*.js"],
      "sourceMaps": true,
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development",
        "TS_NODE_PROJECT": "${workspaceFolder}/backend/tsconfig.json",
        "DATABASE_URL": "postgresql://postgres:password@localhost:5432/printweditt_dev"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "restart": true,
      "protocol": "inspector"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "🐛 Debug with Hot Reload",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/backend/node_modules/.bin/nodemon",
      "args": ["--exec", "node --inspect -r ts-node/register src/index.ts"],
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "NODE_ENV": "development",
        "TS_NODE_PROJECT": "${workspaceFolder}/backend/tsconfig.json"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "restart": true,
      "protocol": "inspector"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "🔐 Debug Auth Routes",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/backend/src/index.ts",
      "outFiles": ["${workspaceFolder}/backend/dist/**/*.js"],
      "sourceMaps": true,
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development",
        "TS_NODE_PROJECT": "${workspaceFolder}/backend/tsconfig.json",
        "DEBUG": "auth:*"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "restart": true,
      "protocol": "inspector"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "📧 Debug Email Service",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/backend/src/index.ts",
      "outFiles": ["${workspaceFolder}/backend/dist/**/*.js"],
      "sourceMaps": true,
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development",
        "TS_NODE_PROJECT": "${workspaceFolder}/backend/tsconfig.json",
        "DEBUG": "email:*"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "restart": true,
      "protocol": "inspector"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "🗄️ Debug Database Operations",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/backend/src/index.ts",
      "outFiles": ["${workspaceFolder}/backend/dist/**/*.js"],
      "sourceMaps": true,
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development",
        "TS_NODE_PROJECT": "${workspaceFolder}/backend/tsconfig.json",
        "DEBUG": "prisma:*"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "restart": true,
      "protocol": "inspector"
    },
    {
      "type": "node",
      "request": "attach",
      "name": "🔗 Attach to Running Process",
      "port": 9229,
      "skipFiles": ["<node_internals>/**"],
      "sourceMaps": true,
      "outFiles": ["${workspaceFolder}/backend/dist/**/*.js"],
      "restart": true
    },
    {
      "type": "node",
      "request": "launch",
      "name": "🚀 Launch Frontend (Development)",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/frontend/node_modules/.bin/vite",
      "args": ["--port", "3000"],
      "cwd": "${workspaceFolder}/frontend",
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "🐳 Debug Docker Container",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/backend/src/index.ts",
      "outFiles": ["${workspaceFolder}/backend/dist/**/*.js"],
      "sourceMaps": true,
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development",
        "TS_NODE_PROJECT": "${workspaceFolder}/backend/tsconfig.json",
        "DATABASE_URL": "********************************************/printweditt_dev",
        "REDIS_URL": "redis://redis:6379"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "restart": true,
      "protocol": "inspector"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "⚡ Quick Start (No Build)",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/backend/src/index.ts",
      "outFiles": ["${workspaceFolder}/backend/dist/**/*.js"],
      "sourceMaps": true,
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development",
        "TS_NODE_PROJECT": "${workspaceFolder}/backend/tsconfig.json"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "restart": true,
      "protocol": "inspector"
    }
  ],
  "compounds": [
    {
      "name": "🚀 Full Stack Debug (Backend + Frontend)",
      "configurations": ["🚀 Launch Backend (Development)", "🚀 Launch Frontend (Development)"],
      "stopAll": true
    },
    {
      "name": "🧪 Debug Backend + Tests",
      "configurations": ["🚀 Launch Backend (Development)", "🧪 Debug Tests"],
      "stopAll": true
    }
  ]
}
