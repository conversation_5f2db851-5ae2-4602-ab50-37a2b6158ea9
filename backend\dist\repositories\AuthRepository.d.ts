import { PrismaClient, User, UserRole, user_sessions } from '@prisma/client';
import { AuthUser } from '../types/auth';
export interface IAuthRepository {
    findUserByEmail(email: string): Promise<User | null>;
    findUserById(id: string): Promise<User | null>;
    findUserByIdSafe(id: string): Promise<AuthUser | null>;
    createUser(userData: CreateUserData): Promise<User>;
    updateUser(id: string, data: UpdateUserData): Promise<User>;
    updateUserPassword(id: string, hashedPassword: string): Promise<void>;
    incrementLoginAttempts(userId: string): Promise<void>;
    resetLoginAttempts(userId: string): Promise<void>;
    isAccountLocked(userId: string): Promise<boolean>;
    getAccountLockInfo(userId: string): Promise<AccountLockInfo | null>;
    findUserByPasswordResetToken(token: string): Promise<User | null>;
    setPasswordResetToken(userId: string, token: string, expiresAt: Date): Promise<void>;
    clearPasswordResetToken(userId: string): Promise<void>;
    findUserByEmailVerificationToken(token: string): Promise<User | null>;
    setEmailVerificationToken(userId: string, token: string, expiresAt: Date): Promise<void>;
    markEmailAsVerified(userId: string): Promise<void>;
    clearEmailVerificationToken(userId: string): Promise<void>;
    createSession(sessionData: CreateSessionData): Promise<user_sessions>;
    findSessionByRefreshToken(refreshToken: string): Promise<user_sessions | null>;
    revokeSession(refreshToken: string): Promise<void>;
    revokeAllUserSessions(userId: string): Promise<void>;
    getUserSessions(userId: string): Promise<SessionData[]>;
    revokeSpecificSession(sessionId: string, userId: string): Promise<boolean>;
    cleanupExpiredSessions(): Promise<number>;
}
export interface CreateUserData {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string | null;
    role?: UserRole;
    isActive?: boolean;
    isVerified?: boolean;
}
export interface UpdateUserData {
    firstName?: string;
    lastName?: string;
    phone?: string | null;
    avatar?: string | null;
    emailVerified?: Date | null;
    emailVerificationToken?: string | null;
    emailVerificationTokenExpires?: Date | null;
    passwordResetToken?: string | null;
    passwordResetTokenExpires?: Date | null;
    lastLoginAt?: Date;
    loginAttempts?: number;
    lockedUntil?: Date | null;
}
export interface CreateSessionData {
    id: string;
    userId: string;
    refreshToken: string;
    expiresAt: Date;
    ipAddress: string;
    userAgent: string;
}
export interface SessionData {
    id: string;
    ipAddress: string;
    userAgent: string;
    createdAt: Date;
    expiresAt: Date;
    isActive: boolean;
}
export interface AccountLockInfo {
    isLocked: boolean;
    lockExpires?: Date | null;
    attempts: number;
    maxAttempts: number;
    remainingAttempts: number;
}
export declare class AuthRepository implements IAuthRepository {
    private prisma;
    constructor(prisma: PrismaClient);
    findUserByEmail(email: string): Promise<User | null>;
    findUserById(id: string): Promise<User | null>;
    findUserByIdSafe(id: string): Promise<AuthUser | null>;
    createUser(userData: CreateUserData): Promise<User>;
    updateUser(id: string, data: UpdateUserData): Promise<User>;
    updateUserPassword(id: string, hashedPassword: string): Promise<void>;
    incrementLoginAttempts(userId: string): Promise<void>;
    resetLoginAttempts(userId: string): Promise<void>;
    isAccountLocked(userId: string): Promise<boolean>;
    getAccountLockInfo(userId: string): Promise<AccountLockInfo | null>;
    findUserByPasswordResetToken(token: string): Promise<User | null>;
    setPasswordResetToken(userId: string, token: string, expiresAt: Date): Promise<void>;
    clearPasswordResetToken(userId: string): Promise<void>;
    findUserByEmailVerificationToken(token: string): Promise<User | null>;
    setEmailVerificationToken(userId: string, token: string, expiresAt: Date): Promise<void>;
    markEmailAsVerified(userId: string): Promise<void>;
    clearEmailVerificationToken(userId: string): Promise<void>;
    createSession(sessionData: CreateSessionData): Promise<user_sessions>;
    findSessionByRefreshToken(refreshToken: string): Promise<user_sessions | null>;
    revokeSession(refreshToken: string): Promise<void>;
    revokeAllUserSessions(userId: string): Promise<void>;
    getUserSessions(userId: string): Promise<SessionData[]>;
    revokeSpecificSession(sessionId: string, userId: string): Promise<boolean>;
    cleanupExpiredSessions(): Promise<number>;
}
export default AuthRepository;
//# sourceMappingURL=AuthRepository.d.ts.map