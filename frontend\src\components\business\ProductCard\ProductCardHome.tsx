import React from 'react';
import { Link } from 'react-router-dom';

interface ProductCardHomeProps {
  productName: string;
  imageUrl: string;
  price: string;
  route: string;
}

const ProductCardHome: React.FC<ProductCardHomeProps> = ({
  productName,
  imageUrl,
  price,
  route,
}) => {
  return (
    <Link to={route} className="block group">
      <div className="bg-none rounded-xl overflow-hidden shadow-none hover:shadow-lg transition-shadow duration-300">
        {/* Product Image */}
        <div className="aspect-square overflow-hidden bg-gray-100">
          <img
            src={imageUrl}
            alt={productName}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </div>

        {/* Product Info */}
        <div className="p-4 text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {productName}
          </h3>
          <p className="text-sm text-gray-600">
            Starting at{' '}
            <span className="font-semibold text-gray-900">{price}</span>
          </p>
        </div>
      </div>
    </Link>
  );
};

export default ProductCardHome;
