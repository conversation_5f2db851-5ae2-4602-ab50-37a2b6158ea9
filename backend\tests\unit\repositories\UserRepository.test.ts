import { PrismaClient, UserRole } from '@prisma/client';
import { UserRepository } from '@/repositories/UserRepository';
import { CreateUserData, UpdateUserData, UserListQuery } from '@/types/user';

// Mock Prisma Client with proper Jest mock typing
const mockPrisma = {
  user: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
    updateMany: jest.fn(),
    deleteMany: jest.fn(),
    groupBy: jest.fn(),
  },
} as any;

describe('UserRepository', () => {
  let userRepository: UserRepository;

  beforeEach(() => {
    userRepository = new UserRepository(mockPrisma);
    jest.clearAllMocks();
  });

  describe('findUserById', () => {
    it('should find user by ID', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser as any);

      const result = await userRepository.findUserById('user-123');

      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'user-123' },
      });
      expect(result).toEqual(mockUser);
    });

    it('should return null when user not found', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(null);

      const result = await userRepository.findUserById('non-existent');

      expect(result).toBeNull();
    });
  });

  describe('findUserByEmail', () => {
    it('should find user by email (case insensitive)', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser as any);

      const result = await userRepository.findUserByEmail('<EMAIL>');

      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
      expect(result).toEqual(mockUser);
    });

    it('should return null when user not found', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(null);

      const result = await userRepository.findUserByEmail('<EMAIL>');

      expect(result).toBeNull();
    });
  });

  describe('createUser', () => {
    it('should create a new user', async () => {
      const userData: CreateUserData = {
        email: '<EMAIL>',
        password: 'hashedpassword123',
        firstName: 'John',
        lastName: 'Doe',
        role: 'customer' as UserRole,
        isActive: true,
        isVerified: false,
      };

      const createdUser = {
        id: 'user-123',
        ...userData,
        email: '<EMAIL>',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.user.create.mockResolvedValue(createdUser as any);

      const result = await userRepository.createUser(userData);

      expect(mockPrisma.user.create).toHaveBeenCalledWith({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword123',
          firstName: 'John',
          lastName: 'Doe',
          phone: undefined,
          role: 'customer',
          isActive: true,
          isVerified: false,
        },
      });
      expect(result).toEqual(createdUser);
    });

    it('should convert email to lowercase when creating user', async () => {
      const userData: CreateUserData = {
        email: '<EMAIL>',
        password: 'hashedpassword123',
        firstName: 'John',
        lastName: 'Doe',
        role: 'customer' as UserRole,
        isActive: true,
        isVerified: false,
      };

      mockPrisma.user.create.mockResolvedValue({} as any);

      await userRepository.createUser(userData);

      expect(mockPrisma.user.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          email: '<EMAIL>',
        }),
      });
    });
  });

  describe('updateUser', () => {
    it('should update user successfully', async () => {
      const updateData: UpdateUserData = {
        firstName: 'Jane',
        lastName: 'Smith',
      };

      const updatedUser = {
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
      };

      mockPrisma.user.update.mockResolvedValue(updatedUser as any);

      const result = await userRepository.updateUser('user-123', updateData);

      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        data: updateData,
      });
      expect(result).toEqual(updatedUser);
    });

    it('should convert email to lowercase when updating', async () => {
      const updateData: UpdateUserData = {
        email: '<EMAIL>',
      };

      mockPrisma.user.update.mockResolvedValue({} as any);

      await userRepository.updateUser('user-123', updateData);

      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        data: {
          email: '<EMAIL>',
        },
      });
    });
  });

  describe('deleteUser', () => {
    it('should delete user successfully', async () => {
      mockPrisma.user.delete.mockResolvedValue({} as any);

      const result = await userRepository.deleteUser('user-123');

      expect(mockPrisma.user.delete).toHaveBeenCalledWith({
        where: { id: 'user-123' },
      });
      expect(result).toBe(true);
    });

    it('should return false when deletion fails', async () => {
      mockPrisma.user.delete.mockRejectedValue(new Error('User not found'));

      const result = await userRepository.deleteUser('user-123');

      expect(result).toBe(false);
    });
  });

  describe('getUsers', () => {
    it('should get users with pagination', async () => {
      const query: UserListQuery = {
        page: 1,
        limit: 10,
      };

      const mockUsers = [
        { id: 'user-1', email: '<EMAIL>', firstName: 'User', lastName: 'One' },
        { id: 'user-2', email: '<EMAIL>', firstName: 'User', lastName: 'Two' },
      ];

      mockPrisma.user.count.mockResolvedValue(50);
      mockPrisma.user.findMany.mockResolvedValue(mockUsers as any);

      const result = await userRepository.getUsers(query);

      expect(mockPrisma.user.count).toHaveBeenCalledWith({ where: {} });
      expect(mockPrisma.user.findMany).toHaveBeenCalledWith({
        where: {},
        select: expect.objectContaining({
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          isActive: true,
          isVerified: true,
        }),
        orderBy: { createdAt: 'desc' },
        skip: 0,
        take: 10,
      });

      expect(result).toEqual({
        users: mockUsers,
        pagination: {
          page: 1,
          limit: 10,
          total: 50,
          totalPages: 5,
          hasNext: true,
          hasPrev: false,
        },
      });
    });

    it('should apply search filters', async () => {
      const query: UserListQuery = {
        page: 1,
        limit: 10,
        search: 'john',
      };

      mockPrisma.user.count.mockResolvedValue(2);
      mockPrisma.user.findMany.mockResolvedValue([]);

      await userRepository.getUsers(query);

      expect(mockPrisma.user.count).toHaveBeenCalledWith({
        where: {
          OR: [
            { firstName: { contains: 'john', mode: 'insensitive' } },
            { lastName: { contains: 'john', mode: 'insensitive' } },
            { email: { contains: 'john', mode: 'insensitive' } },
          ],
        },
      });
    });

    it('should apply role filter', async () => {
      const query: UserListQuery = {
        page: 1,
        limit: 10,
        role: 'admin' as UserRole,
      };

      mockPrisma.user.count.mockResolvedValue(5);
      mockPrisma.user.findMany.mockResolvedValue([]);

      await userRepository.getUsers(query);

      expect(mockPrisma.user.count).toHaveBeenCalledWith({
        where: { role: 'admin' },
      });
    });

    it('should apply active filter', async () => {
      const query: UserListQuery = {
        page: 1,
        limit: 10,
        isActive: true,
      };

      mockPrisma.user.count.mockResolvedValue(40);
      mockPrisma.user.findMany.mockResolvedValue([]);

      await userRepository.getUsers(query);

      expect(mockPrisma.user.count).toHaveBeenCalledWith({
        where: { isActive: true },
      });
    });

    it('should apply verified filter', async () => {
      const query: UserListQuery = {
        page: 1,
        limit: 10,
        isVerified: false,
      };

      mockPrisma.user.count.mockResolvedValue(10);
      mockPrisma.user.findMany.mockResolvedValue([]);

      await userRepository.getUsers(query);

      expect(mockPrisma.user.count).toHaveBeenCalledWith({
        where: { isVerified: false },
      });
    });

    it('should apply custom sorting', async () => {
      const query: UserListQuery = {
        page: 1,
        limit: 10,
        sortBy: 'email',
        sortOrder: 'asc',
      };

      mockPrisma.user.count.mockResolvedValue(10);
      mockPrisma.user.findMany.mockResolvedValue([]);

      await userRepository.getUsers(query);

      expect(mockPrisma.user.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBy: { email: 'asc' },
        })
      );
    });
  });

  describe('searchUsers', () => {
    it('should search users by term', async () => {
      const mockUsers = [
        { id: 'user-1', firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
      ];

      mockPrisma.user.findMany.mockResolvedValue(mockUsers as any);

      const result = await userRepository.searchUsers('john', 5);

      expect(mockPrisma.user.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { firstName: { contains: 'john', mode: 'insensitive' } },
            { lastName: { contains: 'john', mode: 'insensitive' } },
            { email: { contains: 'john', mode: 'insensitive' } },
          ],
        },
        select: expect.any(Object),
        take: 5,
      });

      expect(result).toEqual(mockUsers);
    });

    it('should use default limit when not provided', async () => {
      mockPrisma.user.findMany.mockResolvedValue([]);

      await userRepository.searchUsers('test');

      expect(mockPrisma.user.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          take: 10,
        })
      );
    });
  });

  describe('getUserStats', () => {
    it('should get comprehensive user statistics', async () => {
      // Mock all the parallel Promise.all calls
      mockPrisma.user.count
        .mockResolvedValueOnce(100) // totalUsers
        .mockResolvedValueOnce(85)  // activeUsers
        .mockResolvedValueOnce(75)  // verifiedUsers
        .mockResolvedValueOnce(15)  // recentRegistrations
        .mockResolvedValueOnce(8);  // newUsersThisMonth

      mockPrisma.user.groupBy.mockResolvedValue([
        { role: 'CUSTOMER' as UserRole, _count: { role: 80 } },
        { role: 'PROVIDER' as UserRole, _count: { role: 15 } },
        { role: 'ADMIN' as UserRole, _count: { role: 5 } },
      ]);

      const result = await userRepository.getUserStats();

      expect(result).toEqual({
        total: 100,
        totalUsers: 100,
        active: 85,
        activeUsers: 85,
        inactive: 15,
        verified: 75,
        verifiedUsers: 75,
        unverified: 25,
        byRole: {
          CUSTOMER: 80,
          PROVIDER: 15,
          ADMIN: 5,
        },
        usersByRole: {
          CUSTOMER: 80,
          PROVIDER: 15,
          ADMIN: 5,
        },
        recentRegistrations: 15,
        averageRegistrationsPerDay: 1, // Math.round(15/30)
        newUsersThisMonth: 8,
      });
    });

    it('should handle empty role groups', async () => {
      mockPrisma.user.count
        .mockResolvedValueOnce(10)
        .mockResolvedValueOnce(8)
        .mockResolvedValueOnce(6)
        .mockResolvedValueOnce(2)
        .mockResolvedValueOnce(1);

      mockPrisma.user.groupBy.mockResolvedValue([]);

      const result = await userRepository.getUserStats();

      expect(result.byRole).toEqual({
        CUSTOMER: 0,
        PROVIDER: 0,
        ADMIN: 0,
      });
    });
  });

  describe('getUsersByRole', () => {
    it('should get users by specific role', async () => {
      const mockUsers = [
        { id: 'admin-1', role: 'admin', email: '<EMAIL>' },
        { id: 'admin-2', role: 'admin', email: '<EMAIL>' },
      ];

      mockPrisma.user.findMany.mockResolvedValue(mockUsers as any);

      const result = await userRepository.getUsersByRole('admin' as UserRole);

      expect(mockPrisma.user.findMany).toHaveBeenCalledWith({
        where: { role: 'admin' },
        select: expect.objectContaining({
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
        }),
      });

      expect(result).toEqual(mockUsers);
    });
  });

  describe('getActiveUsers', () => {
    it('should get active users', async () => {
      const mockUsers = [
        { id: 'user-1', isActive: true, email: '<EMAIL>' },
        { id: 'user-2', isActive: true, email: '<EMAIL>' },
      ];

      mockPrisma.user.findMany.mockResolvedValue(mockUsers as any);

      const result = await userRepository.getActiveUsers();

      expect(mockPrisma.user.findMany).toHaveBeenCalledWith({
        where: { isActive: true },
        select: expect.any(Object),
      });

      expect(result).toEqual(mockUsers);
    });
  });

  describe('getVerifiedUsers', () => {
    it('should get verified users', async () => {
      const mockUsers = [
        { id: 'user-1', isVerified: true, email: '<EMAIL>' },
        { id: 'user-2', isVerified: true, email: '<EMAIL>' },
      ];

      mockPrisma.user.findMany.mockResolvedValue(mockUsers as any);

      const result = await userRepository.getVerifiedUsers();

      expect(mockPrisma.user.findMany).toHaveBeenCalledWith({
        where: { isVerified: true },
        select: expect.any(Object),
      });

      expect(result).toEqual(mockUsers);
    });
  });

  describe('bulkUpdateUsers', () => {
    it('should bulk update users', async () => {
      const userIds = ['user-1', 'user-2', 'user-3'];
      const updates = { isActive: false };

      mockPrisma.user.updateMany.mockResolvedValue({ count: 3 });

      const result = await userRepository.bulkUpdateUsers(userIds, updates);

      expect(mockPrisma.user.updateMany).toHaveBeenCalledWith({
        where: { id: { in: userIds } },
        data: updates,
      });

      expect(result).toBe(3);
    });
  });

  describe('bulkDeleteUsers', () => {
    it('should bulk delete users', async () => {
      const userIds = ['user-1', 'user-2'];

      mockPrisma.user.deleteMany.mockResolvedValue({ count: 2 });

      const result = await userRepository.bulkDeleteUsers(userIds);

      expect(mockPrisma.user.deleteMany).toHaveBeenCalledWith({
        where: { id: { in: userIds } },
      });

      expect(result).toBe(2);
    });
  });

  describe('userExists', () => {
    it('should return true when user exists', async () => {
      mockPrisma.user.count.mockResolvedValue(1);

      const result = await userRepository.userExists('<EMAIL>');

      expect(mockPrisma.user.count).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });

      expect(result).toBe(true);
    });

    it('should return false when user does not exist', async () => {
      mockPrisma.user.count.mockResolvedValue(0);

      const result = await userRepository.userExists('<EMAIL>');

      expect(result).toBe(false);
    });

    it('should convert email to lowercase when checking existence', async () => {
      mockPrisma.user.count.mockResolvedValue(0);

      await userRepository.userExists('<EMAIL>');

      expect(mockPrisma.user.count).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
    });
  });

  describe('countUsers', () => {
    it('should count all users', async () => {
      mockPrisma.user.count.mockResolvedValue(150);

      const result = await userRepository.countUsers();

      expect(mockPrisma.user.count).toHaveBeenCalledWith();
      expect(result).toBe(150);
    });
  });

  describe('countUsersByRole', () => {
    it('should count users by role', async () => {
      mockPrisma.user.count.mockResolvedValue(25);

      const result = await userRepository.countUsersByRole('admin' as UserRole);

      expect(mockPrisma.user.count).toHaveBeenCalledWith({
        where: { role: 'admin' },
      });

      expect(result).toBe(25);
    });
  });
});