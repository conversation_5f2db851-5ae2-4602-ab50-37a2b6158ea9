"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAuthController = exports.AuthController = void 0;
const AuthModels_1 = require("../models/AuthModels");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../utils/auth");
class AuthController {
    authService;
    constructor(authService) {
        this.authService = authService;
    }
    register = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const registrationData = AuthModels_1.AuthenticationModel.createRegistrationData(req.body);
        const context = this.createRequestContext(req);
        const result = await this.authService.register(req.body, context);
        const userProfile = AuthModels_1.UserProfile.fromAuthUser(result.user);
        const authResponse = new AuthModels_1.AuthenticationResponse(userProfile, result.tokens);
        res.status(201).json((0, auth_1.createApiResponse)(true, {
            user: userProfile.toJSON(),
            tokens: result.tokens
        }, 'Account created successfully'));
    });
    login = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const loginData = AuthModels_1.AuthenticationModel.createLoginData(req.body);
        const context = this.createRequestContext(req);
        const result = await this.authService.login(req.body, context);
        const userProfile = AuthModels_1.UserProfile.fromAuthUser(result.user);
        res.json((0, auth_1.createApiResponse)(true, {
            user: userProfile.toJSON(),
            tokens: result.tokens
        }, 'Login successful'));
    });
    refreshTokens = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const { refreshToken } = req.body;
        const context = this.createRequestContext(req);
        const result = await this.authService.refreshTokens(refreshToken, context);
        res.json((0, auth_1.createApiResponse)(true, result, 'Tokens refreshed successfully'));
    });
    logout = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const refreshToken = req.body.refreshToken;
        const context = this.createRequestContext(req);
        const userId = req.userId;
        const user = await this.authService.getProfile(userId);
        await this.authService.logout(refreshToken, user, context);
        res.json((0, auth_1.createApiResponse)(true, null, 'Logged out successfully'));
    });
    logoutAllDevices = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const context = this.createRequestContext(req);
        const userId = req.userId;
        const user = await this.authService.getProfile(userId);
        await this.authService.logoutAllDevices(user, context);
        res.json((0, auth_1.createApiResponse)(true, null, 'Logged out from all devices'));
    });
    getProfile = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const userId = req.userId || req.user?.id;
        if (!userId) {
            throw new errorHandler_1.AuthenticationError('User ID not found');
        }
        const user = await this.authService.getProfile(userId);
        const userProfile = AuthModels_1.UserProfile.fromAuthUser(user);
        res.json((0, auth_1.createApiResponse)(true, { user: userProfile.toJSON() }));
    });
    updateProfile = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const profileData = AuthModels_1.AuthenticationModel.createProfileUpdateData(req.body);
        const userId = req.userId;
        const updatedUser = await this.authService.updateProfile(userId, req.body);
        const userProfile = AuthModels_1.UserProfile.fromAuthUser(updatedUser);
        res.json((0, auth_1.createApiResponse)(true, {
            user: userProfile.toJSON()
        }, 'Profile updated successfully'));
    });
    changePassword = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const passwordData = AuthModels_1.AuthenticationModel.createPasswordChangeData(req.body);
        const context = this.createRequestContext(req);
        const userId = req.userId;
        await this.authService.changePassword(userId, req.body, context);
        res.json((0, auth_1.createApiResponse)(true, null, 'Password changed successfully. Please log in again.'));
    });
    getUserSessions = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const userId = req.userId;
        const sessions = await this.authService.getUserSessions(userId);
        const context = this.createRequestContext(req);
        const sessionInfos = sessions.map(session => {
            const sessionInfo = new AuthModels_1.SessionInfo(session.id, session.ipAddress, session.userAgent, session.createdAt, session.expiresAt, session.isActive);
            return {
                ...sessionInfo.toJSON(),
                isCurrent: sessionInfo.isCurrent(context.ipAddress, context.userAgent)
            };
        });
        res.json((0, auth_1.createApiResponse)(true, { sessions: sessionInfos }));
    });
    revokeSession = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const { sessionId } = req.params;
        const userId = req.userId;
        await this.authService.revokeSession(sessionId, userId);
        res.json((0, auth_1.createApiResponse)(true, null, 'Session revoked successfully'));
    });
    createRequestContext(req) {
        return {
            ipAddress: (0, auth_1.getClientIP)(req),
            userAgent: (0, auth_1.getUserAgent)(req)
        };
    }
}
exports.AuthController = AuthController;
const createAuthController = (authService) => {
    return new AuthController(authService);
};
exports.createAuthController = createAuthController;
exports.default = AuthController;
//# sourceMappingURL=AuthController.js.map