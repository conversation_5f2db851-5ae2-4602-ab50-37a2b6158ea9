"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAuthController = exports.AuthController = void 0;
const AuthModels_1 = require("../models/AuthModels");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../utils/auth");
const logger_1 = require("../utils/logger");
class AuthController {
    authService;
    logger = (0, logger_1.createLogger)('AuthController');
    constructor(authService) {
        this.authService = authService;
        this.logger.info('AuthController initialized');
    }
    register = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const startTime = Date.now();
        const requestId = res.getHeader('X-Request-ID');
        this.logger.info('Registration request received', {
            requestId,
            email: req.body.email,
            ipAddress: (0, auth_1.getClientIP)(req),
            userAgent: (0, auth_1.getUserAgent)(req),
        });
        try {
            const registrationData = AuthModels_1.AuthenticationModel.createRegistrationData(req.body);
            const context = this.createRequestContext(req);
            const result = await this.authService.register(req.body, context);
            const userProfile = AuthModels_1.UserProfile.fromAuthUser(result.user);
            const authResponse = new AuthModels_1.AuthenticationResponse(userProfile, result.tokens);
            const duration = Date.now() - startTime;
            this.logger.info('Registration completed successfully', {
                requestId,
                userId: result.user.id,
                email: result.user.email,
                duration,
            });
            res.status(201).json((0, auth_1.createApiResponse)(true, {
                user: userProfile.toJSON(),
                tokens: result.tokens,
            }, 'Account created successfully'));
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Registration failed', error, {
                requestId,
                email: req.body.email,
                duration,
            });
            throw error;
        }
    });
    login = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const startTime = Date.now();
        const requestId = res.getHeader('X-Request-ID');
        this.logger.info('Login request received', {
            requestId,
            email: req.body.email,
            ipAddress: (0, auth_1.getClientIP)(req),
            userAgent: (0, auth_1.getUserAgent)(req),
        });
        try {
            const loginData = AuthModels_1.AuthenticationModel.createLoginData(req.body);
            const context = this.createRequestContext(req);
            const result = await this.authService.login(req.body, context);
            const userProfile = AuthModels_1.UserProfile.fromAuthUser(result.user);
            const duration = Date.now() - startTime;
            this.logger.info('Login completed successfully', {
                requestId,
                userId: result.user.id,
                email: result.user.email,
                duration,
            });
            res.json((0, auth_1.createApiResponse)(true, {
                user: userProfile.toJSON(),
                tokens: result.tokens,
            }, 'Login successful'));
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Login failed', error, {
                requestId,
                email: req.body.email,
                duration,
            });
            throw error;
        }
    });
    refreshTokens = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const startTime = Date.now();
        const requestId = res.getHeader('X-Request-ID');
        this.logger.info('Token refresh request received', {
            requestId,
            ipAddress: (0, auth_1.getClientIP)(req),
            userAgent: (0, auth_1.getUserAgent)(req),
        });
        try {
            const { refreshToken } = req.body;
            const context = this.createRequestContext(req);
            const result = await this.authService.refreshTokens(refreshToken, context);
            const duration = Date.now() - startTime;
            this.logger.info('Token refresh completed successfully', {
                requestId,
                duration,
            });
            res.json((0, auth_1.createApiResponse)(true, result, 'Tokens refreshed successfully'));
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Token refresh failed', error, {
                requestId,
                duration,
            });
            throw error;
        }
    });
    logout = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const startTime = Date.now();
        const requestId = res.getHeader('X-Request-ID');
        const userId = req.userId;
        this.logger.info('Logout request received', {
            requestId,
            userId,
            ipAddress: (0, auth_1.getClientIP)(req),
            userAgent: (0, auth_1.getUserAgent)(req),
        });
        try {
            const refreshToken = req.body.refreshToken;
            const context = this.createRequestContext(req);
            const user = await this.authService.getProfile(userId);
            await this.authService.logout(refreshToken, user, context);
            const duration = Date.now() - startTime;
            this.logger.info('Logout completed successfully', {
                requestId,
                userId,
                duration,
            });
            res.json((0, auth_1.createApiResponse)(true, null, 'Logged out successfully'));
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Logout failed', error, {
                requestId,
                userId,
                duration,
            });
            throw error;
        }
    });
    logoutAllDevices = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const startTime = Date.now();
        const requestId = res.getHeader('X-Request-ID');
        const userId = req.userId;
        this.logger.info('Logout all devices request received', {
            requestId,
            userId,
            ipAddress: (0, auth_1.getClientIP)(req),
            userAgent: (0, auth_1.getUserAgent)(req),
        });
        try {
            const context = this.createRequestContext(req);
            const user = await this.authService.getProfile(userId);
            await this.authService.logoutAllDevices(user, context);
            const duration = Date.now() - startTime;
            this.logger.info('Logout all devices completed successfully', {
                requestId,
                userId,
                duration,
            });
            res.json((0, auth_1.createApiResponse)(true, null, 'Logged out from all devices'));
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Logout all devices failed', error, {
                requestId,
                userId,
                duration,
            });
            throw error;
        }
    });
    getProfile = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const startTime = Date.now();
        const requestId = res.getHeader('X-Request-ID');
        try {
            const userId = req.userId || req.user?.id;
            if (!userId) {
                this.logger.warn('Get profile failed: No user ID found', {
                    requestId,
                    ipAddress: (0, auth_1.getClientIP)(req),
                    userAgent: (0, auth_1.getUserAgent)(req),
                });
                throw new errorHandler_1.AuthenticationError('User ID not found');
            }
            this.logger.info('Get profile request received', {
                requestId,
                userId,
                ipAddress: (0, auth_1.getClientIP)(req),
                userAgent: (0, auth_1.getUserAgent)(req),
            });
            const user = await this.authService.getProfile(userId);
            const userProfile = AuthModels_1.UserProfile.fromAuthUser(user);
            const duration = Date.now() - startTime;
            this.logger.info('Get profile completed successfully', {
                requestId,
                userId,
                duration,
            });
            res.json((0, auth_1.createApiResponse)(true, { user: userProfile.toJSON() }));
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Get profile failed', error, {
                requestId,
                userId: req.userId,
                duration,
            });
            throw error;
        }
    });
    updateProfile = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const startTime = Date.now();
        const requestId = res.getHeader('X-Request-ID');
        const userId = req.userId;
        this.logger.info('Update profile request received', {
            requestId,
            userId,
            updatedFields: Object.keys(req.body),
            ipAddress: (0, auth_1.getClientIP)(req),
            userAgent: (0, auth_1.getUserAgent)(req),
        });
        try {
            const profileData = AuthModels_1.AuthenticationModel.createProfileUpdateData(req.body);
            const updatedUser = await this.authService.updateProfile(userId, req.body);
            const userProfile = AuthModels_1.UserProfile.fromAuthUser(updatedUser);
            const duration = Date.now() - startTime;
            this.logger.info('Update profile completed successfully', {
                requestId,
                userId,
                updatedFields: Object.keys(req.body),
                duration,
            });
            res.json((0, auth_1.createApiResponse)(true, {
                user: userProfile.toJSON(),
            }, 'Profile updated successfully'));
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Update profile failed', error, {
                requestId,
                userId,
                duration,
            });
            throw error;
        }
    });
    changePassword = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const startTime = Date.now();
        const requestId = res.getHeader('X-Request-ID');
        const userId = req.userId;
        this.logger.info('Change password request received', {
            requestId,
            userId,
            ipAddress: (0, auth_1.getClientIP)(req),
            userAgent: (0, auth_1.getUserAgent)(req),
        });
        try {
            const passwordData = AuthModels_1.AuthenticationModel.createPasswordChangeData(req.body);
            const context = this.createRequestContext(req);
            await this.authService.changePassword(userId, req.body, context);
            const duration = Date.now() - startTime;
            this.logger.info('Change password completed successfully', {
                requestId,
                userId,
                duration,
            });
            res.json((0, auth_1.createApiResponse)(true, null, 'Password changed successfully. Please log in again.'));
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Change password failed', error, {
                requestId,
                userId,
                duration,
            });
            throw error;
        }
    });
    getUserSessions = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const startTime = Date.now();
        const requestId = res.getHeader('X-Request-ID');
        const userId = req.userId;
        this.logger.info('Get user sessions request received', {
            requestId,
            userId,
            ipAddress: (0, auth_1.getClientIP)(req),
            userAgent: (0, auth_1.getUserAgent)(req),
        });
        try {
            const sessions = await this.authService.getUserSessions(userId);
            const context = this.createRequestContext(req);
            const sessionInfos = sessions.map((session) => {
                const sessionInfo = new AuthModels_1.SessionInfo(session.id, session.ipAddress, session.userAgent, session.createdAt, session.expiresAt, session.isActive);
                return {
                    ...sessionInfo.toJSON(),
                    isCurrent: sessionInfo.isCurrent(context.ipAddress, context.userAgent),
                };
            });
            const duration = Date.now() - startTime;
            this.logger.info('Get user sessions completed successfully', {
                requestId,
                userId,
                sessionCount: sessions.length,
                duration,
            });
            res.json((0, auth_1.createApiResponse)(true, { sessions: sessionInfos }));
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Get user sessions failed', error, {
                requestId,
                userId,
                duration,
            });
            throw error;
        }
    });
    revokeSession = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const startTime = Date.now();
        const requestId = res.getHeader('X-Request-ID');
        const userId = req.userId;
        const { sessionId } = req.params;
        this.logger.info('Revoke session request received', {
            requestId,
            userId,
            sessionId,
            ipAddress: (0, auth_1.getClientIP)(req),
            userAgent: (0, auth_1.getUserAgent)(req),
        });
        try {
            await this.authService.revokeSession(sessionId, userId);
            const duration = Date.now() - startTime;
            this.logger.info('Revoke session completed successfully', {
                requestId,
                userId,
                sessionId,
                duration,
            });
            res.json((0, auth_1.createApiResponse)(true, null, 'Session revoked successfully'));
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Revoke session failed', error, {
                requestId,
                userId,
                sessionId,
                duration,
            });
            throw error;
        }
    });
    requestPasswordReset = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const startTime = Date.now();
        const requestId = res.getHeader('X-Request-ID');
        this.logger.info('Password reset request received', {
            requestId,
            email: req.body.email,
            ipAddress: (0, auth_1.getClientIP)(req),
            userAgent: (0, auth_1.getUserAgent)(req),
        });
        try {
            const { email } = req.body;
            const context = this.createRequestContext(req);
            await this.authService.requestPasswordReset(email, context);
            const duration = Date.now() - startTime;
            this.logger.info('Password reset request completed', {
                requestId,
                email,
                duration,
            });
            res.json((0, auth_1.createApiResponse)(true, null, 'If an account with that email exists, a password reset link has been sent'));
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Password reset request failed', error, {
                requestId,
                email: req.body.email,
                duration,
            });
            throw error;
        }
    });
    resetPassword = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const startTime = Date.now();
        const requestId = res.getHeader('X-Request-ID');
        this.logger.info('Password reset confirmation received', {
            requestId,
            ipAddress: (0, auth_1.getClientIP)(req),
            userAgent: (0, auth_1.getUserAgent)(req),
        });
        try {
            const { token, newPassword } = req.body;
            const context = this.createRequestContext(req);
            await this.authService.resetPassword(token, newPassword, context);
            const duration = Date.now() - startTime;
            this.logger.info('Password reset completed successfully', {
                requestId,
                duration,
            });
            res.json((0, auth_1.createApiResponse)(true, null, 'Password has been reset successfully. Please log in with your new password.'));
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Password reset failed', error, {
                requestId,
                duration,
            });
            throw error;
        }
    });
    sendEmailVerification = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const startTime = Date.now();
        const requestId = res.getHeader('X-Request-ID');
        const userId = req.userId;
        this.logger.info('Email verification request received', {
            requestId,
            userId,
            ipAddress: (0, auth_1.getClientIP)(req),
            userAgent: (0, auth_1.getUserAgent)(req),
        });
        try {
            const context = this.createRequestContext(req);
            await this.authService.sendEmailVerification(userId, context);
            const duration = Date.now() - startTime;
            this.logger.info('Email verification sent successfully', {
                requestId,
                userId,
                duration,
            });
            res.json((0, auth_1.createApiResponse)(true, null, 'Verification email has been sent to your email address'));
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Email verification send failed', error, {
                requestId,
                userId,
                duration,
            });
            throw error;
        }
    });
    verifyEmail = (0, errorHandler_1.asyncHandler)(async (req, res) => {
        const startTime = Date.now();
        const requestId = res.getHeader('X-Request-ID');
        this.logger.info('Email verification attempt received', {
            requestId,
            ipAddress: (0, auth_1.getClientIP)(req),
            userAgent: (0, auth_1.getUserAgent)(req),
        });
        try {
            const { token } = req.body;
            const context = this.createRequestContext(req);
            await this.authService.verifyEmail(token, context);
            const duration = Date.now() - startTime;
            this.logger.info('Email verification completed successfully', {
                requestId,
                duration,
            });
            res.json((0, auth_1.createApiResponse)(true, null, 'Email has been verified successfully. Welcome to PrintWeditt!'));
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('Email verification failed', error, {
                requestId,
                duration,
            });
            throw error;
        }
    });
    createRequestContext(req) {
        return {
            ipAddress: (0, auth_1.getClientIP)(req),
            userAgent: (0, auth_1.getUserAgent)(req),
        };
    }
}
exports.AuthController = AuthController;
const createAuthController = (authService) => {
    return new AuthController(authService);
};
exports.createAuthController = createAuthController;
exports.default = AuthController;
//# sourceMappingURL=AuthController.js.map