import { Response } from 'express';
import { async<PERSON>and<PERSON>, AuthenticationError } from '../middleware/errorHandler';
import { AuthenticationModel, AuthenticationResponse, UserProfile } from '../models/AuthModels';
import { IAuthService, RequestContext } from '../services/AuthService';
import { AuthenticatedRequest } from '../types/auth';
import { getClientIP, getUserAgent } from '../utils/auth';
import { createLogger } from '../utils/logger';

// Controller interface for dependency inversion (using asyncHandler types)
export interface IAuthController {
  register: any;
  login: any;
  refreshTokens: any;
  logout: any;
  logoutAllDevices: any;
  getProfile: any;
  updateProfile: any;
  changePassword: any;
  requestPasswordReset: any;
  resetPassword: any;
  sendEmailVerification: any;
  verifyEmail: any;
  getUserSessions: any;
  revokeSession: any;
}

// HTTP Controller implementation - handles only HTTP concerns
export class Auth<PERSON>ontroller implements IAuthController {
  private logger = createLogger('AuthController');

  constructor(private authService: IAuthService) {
    this.logger.info('AuthController initialized');
  }

  // User registration
  register = asyncHandler(async (req: any, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Registration request received', {
      requestId,
      email: req.body.email,
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req),
    });

    try {
      const registrationData = AuthenticationModel.createRegistrationData(req.body);
      const context = this.createRequestContext(req);

      const result = await this.authService.register(req.body, context);
      const userProfile = UserProfile.fromAuthUser(result.user);
      const authResponse = new AuthenticationResponse(userProfile, result.tokens);

      const duration = Date.now() - startTime;
      this.logger.info('Registration completed successfully', {
        requestId,
        userId: result.user.id,
        email: result.user.email,
        duration,
      });

      // Set refresh token as HTTP-only cookie
      res.cookie('refreshToken', result.tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      res.created(
        {
          user: userProfile.toJSON(),
          tokens: result.tokens,
        },
        'Account created successfully'
      );
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Registration failed', error, {
        requestId,
        email: req.body.email,
        duration,
      });
      throw error;
    }
  });

  // User login
  login = asyncHandler(async (req: any, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Login request received', {
      requestId,
      email: req.body.email,
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req),
    });

    try {
      const loginData = AuthenticationModel.createLoginData(req.body);
      const context = this.createRequestContext(req);

      const result = await this.authService.login(req.body, context);
      const userProfile = UserProfile.fromAuthUser(result.user);

      const duration = Date.now() - startTime;
      this.logger.info('Login completed successfully', {
        requestId,
        userId: result.user.id,
        email: result.user.email,
        duration,
      });

      // Set refresh token as HTTP-only cookie
      res.cookie('refreshToken', result.tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      res.success(
        {
          user: userProfile.toJSON(),
          tokens: result.tokens,
        },
        'Login successful'
      );
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Login failed', error, {
        requestId,
        email: req.body.email,
        duration,
      });
      throw error;
    }
  });

  // Refresh authentication tokens
  refreshTokens = asyncHandler(async (req: any, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Token refresh request received', {
      requestId,
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req),
    });

    try {
      // 2025 Security Best Practice: Only accept refresh tokens from HttpOnly cookies
      const refreshToken = req.cookies?.refreshToken;

      if (!refreshToken) {
        throw new AuthenticationError('Refresh token is required');
      }

      const context = this.createRequestContext(req);

      const result = await this.authService.refreshTokens(refreshToken, context);

      // Set new refresh token as HTTP-only cookie
      res.cookie('refreshToken', result.tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      const duration = Date.now() - startTime;
      this.logger.info('Token refresh completed successfully', {
        requestId,
        duration,
      });

      // Return only access token in response body for security - refresh token is in HttpOnly cookie
      res.success(
        {
          tokens: {
            accessToken: result.tokens.accessToken,
            // refreshToken is intentionally omitted from response body for security
          },
        },
        'Tokens refreshed successfully'
      );
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Token refresh failed', error, {
        requestId,
        duration,
      });
      throw error;
    }
  });

  // User logout
  logout = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Logout request received', {
      requestId,
      userId: req.userId,
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req),
    });

    try {
      // Get user from userId (populated by authenticate middleware)
      if (!req.userId) {
        throw new AuthenticationError('User authentication required');
      }

      const user = await this.authService.getProfile(req.userId);
      if (!user) {
        throw new AuthenticationError('User not found');
      }

      // Only get refresh token from secure cookie for logout
      const refreshToken = req.cookies?.refreshToken;
      const context = this.createRequestContext(req);
      await this.authService.logout(refreshToken, user, context);

      // Clear the refresh token cookie
      res.clearCookie('refreshToken', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
      });

      const duration = Date.now() - startTime;
      this.logger.info('Logout completed successfully', {
        requestId,
        userId: req.userId,
        duration,
      });

      res.success({}, 'Logout successful');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Logout failed', error, {
        requestId,
        userId: req.userId,
        duration,
      });
      throw error;
    }
  });

  // Logout from all devices
  logoutAllDevices = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Logout all devices request received', {
      requestId,
      userId: req.userId,
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req),
    });

    try {
      // Get user from userId (populated by authenticate middleware)
      if (!req.userId) {
        throw new AuthenticationError('User authentication required');
      }

      const user = await this.authService.getProfile(req.userId);
      if (!user) {
        throw new AuthenticationError('User not found');
      }

      const context = this.createRequestContext(req);
      await this.authService.logoutAllDevices(user, context);

      const duration = Date.now() - startTime;
      this.logger.info('Logout all devices completed successfully', {
        requestId,
        userId: req.userId,
        duration,
      });

      res.success({}, 'Logged out from all devices successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Logout all devices failed', error, {
        requestId,
        userId: req.userId,
        duration,
      });
      throw error;
    }
  });

  // Get user profile
  getProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Get profile request received', {
      requestId,
      userId: req.userId,
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req),
    });

    try {
      // Get user from userId (populated by authenticate middleware)
      if (!req.userId) {
        throw new AuthenticationError('User authentication required');
      }
      const profile = await this.authService.getProfile(req.userId);

      const duration = Date.now() - startTime;
      this.logger.info('Get profile completed successfully', {
        requestId,
        userId: req.userId,
        duration,
      });

      res.success(profile);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get profile failed', error, {
        requestId,
        userId: req.userId,
        duration,
      });
      throw error;
    }
  });

  // Update user profile
  updateProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Update profile request received', {
      requestId,
      userId: req.userId,
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req),
    });

    try {
      // Get user from userId (populated by authenticate middleware)
      if (!req.userId) {
        throw new AuthenticationError('User authentication required');
      }
      const updatedProfile = await this.authService.updateProfile(req.userId, req.body);

      const duration = Date.now() - startTime;
      this.logger.info('Update profile completed successfully', {
        requestId,
        userId: req.userId,
        duration,
      });

      res.success(updatedProfile, 'Profile updated successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Update profile failed', error, {
        requestId,
        userId: req.userId,
        duration,
      });
      throw error;
    }
  });

  // Change password
  changePassword = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Change password request received', {
      requestId,
      userId: req.userId,
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req),
    });

    try {
      const context = this.createRequestContext(req);
      // Get user from userId (populated by authenticate middleware)
      if (!req.userId) {
        throw new AuthenticationError('User authentication required');
      }
      await this.authService.changePassword(req.userId, req.body, context);

      const duration = Date.now() - startTime;
      this.logger.info('Change password completed successfully', {
        requestId,
        userId: req.userId,
        duration,
      });

      res.success({}, 'Password changed successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Change password failed', error, {
        requestId,
        userId: req.userId,
        duration,
      });
      throw error;
    }
  });

  // Get user sessions
  getUserSessions = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Get user sessions request received', {
      requestId,
      userId: req.userId,
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req),
    });

    try {
      // Get user from userId (populated by authenticate middleware)
      if (!req.userId) {
        throw new AuthenticationError('User authentication required');
      }
      const sessions = await this.authService.getUserSessions(req.userId);

      const duration = Date.now() - startTime;
      this.logger.info('Get user sessions completed successfully', {
        requestId,
        userId: req.userId,
        duration,
      });

      res.success(sessions);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get user sessions failed', error, {
        requestId,
        userId: req.userId,
        duration,
      });
      throw error;
    }
  });

  // Revoke specific session
  revokeSession = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { sessionId } = req.params;

    this.logger.info('Revoke session request received', {
      requestId,
      userId: req.userId,
      sessionId,
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req),
    });

    try {
      // Get user from userId (populated by authenticate middleware)
      if (!req.userId) {
        throw new AuthenticationError('User authentication required');
      }
      await this.authService.revokeSession(sessionId, req.userId);

      const duration = Date.now() - startTime;
      this.logger.info('Revoke session completed successfully', {
        requestId,
        userId: req.userId,
        sessionId,
        duration,
      });

      res.success({}, 'Session revoked successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Revoke session failed', error, {
        requestId,
        userId: req.userId,
        sessionId,
        duration,
      });
      throw error;
    }
  });

  // Request password reset
  requestPasswordReset = asyncHandler(async (req: any, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Password reset request received', {
      requestId,
      email: req.body.email,
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req),
    });

    try {
      const context = this.createRequestContext(req);
      await this.authService.requestPasswordReset(req.body.email, context);

      const duration = Date.now() - startTime;
      this.logger.info('Password reset request completed successfully', {
        requestId,
        email: req.body.email,
        duration,
      });

      res.success({}, 'If the email exists, a password reset link has been sent');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Password reset request failed', error, {
        requestId,
        email: req.body.email,
        duration,
      });
      throw error;
    }
  });

  // Reset password
  resetPassword = asyncHandler(async (req: any, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Password reset confirmation received', {
      requestId,
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req),
    });

    try {
      const context = this.createRequestContext(req);
      await this.authService.resetPassword(req.body.token, req.body.newPassword, context);

      const duration = Date.now() - startTime;
      this.logger.info('Password reset completed successfully', {
        requestId,
        duration,
      });

      res.success({}, 'Password has been reset successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Password reset failed', error, {
        requestId,
        duration,
      });
      throw error;
    }
  });

  // Send email verification
  sendEmailVerification = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Send email verification request received', {
      requestId,
      userId: req.userId,
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req),
    });

    try {
      const context = this.createRequestContext(req);
      // Get user from userId (populated by authenticate middleware)
      if (!req.userId) {
        throw new AuthenticationError('User authentication required');
      }
      await this.authService.sendEmailVerification(req.userId, context);

      const duration = Date.now() - startTime;
      this.logger.info('Send email verification completed successfully', {
        requestId,
        userId: req.userId,
        duration,
      });

      res.success({}, 'Verification email sent');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Send email verification failed', error, {
        requestId,
        userId: req.userId,
        duration,
      });
      throw error;
    }
  });

  // Verify email
  verifyEmail = asyncHandler(async (req: any, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Email verification received', {
      requestId,
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req),
    });

    try {
      const context = this.createRequestContext(req);
      await this.authService.verifyEmail(req.body.token, context);

      const duration = Date.now() - startTime;
      this.logger.info('Email verification completed successfully', {
        requestId,
        duration,
      });

      res.success({}, 'Email verified successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Email verification failed', error, {
        requestId,
        duration,
      });
      throw error;
    }
  });

  private createRequestContext(req: any): RequestContext {
    return {
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req),
    };
  }
}

export const createAuthController = (authService: IAuthService): AuthController => {
  return new AuthController(authService);
};
