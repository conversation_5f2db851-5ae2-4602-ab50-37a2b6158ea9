import { Response } from 'express';
import { IAuthService, RequestContext } from '../services/AuthService';
import { AuthenticatedRequest } from '../types/auth';
import { 
  AuthenticationModel, 
  UserProfile, 
  AuthenticationResponse,
  SessionInfo 
} from '../models/AuthModels';
import { asyncHandler, AuthenticationError } from '../middleware/errorHandler';
import { getClientIP, getUserAgent, createApiResponse } from '../utils/auth';

// Controller interface for dependency inversion (using asyncHandler types)
export interface IAuthController {
  register: any;
  login: any;
  refreshTokens: any;
  logout: any;
  logoutAllDevices: any;
  getProfile: any;
  updateProfile: any;
  changePassword: any;
  getUserSessions: any;
  revokeSession: any;
}

// HTTP Controller implementation - handles only HTTP concerns
export class AuthController implements IAuthController {
  constructor(private authService: IAuthService) {}

  // User registration
  register = asyncHandler(async (req: any, res: Response): Promise<void> => {
    const registrationData = AuthenticationModel.createRegistrationData(req.body);
    const context = this.createRequestContext(req);
    
    const result = await this.authService.register(req.body, context);
    const userProfile = UserProfile.fromAuthUser(result.user);
    const authResponse = new AuthenticationResponse(userProfile, result.tokens);
    
    res.status(201).json(createApiResponse(true, {
      user: userProfile.toJSON(),
      tokens: result.tokens
    }, 'Account created successfully'));
  });

  // User login
  login = asyncHandler(async (req: any, res: Response): Promise<void> => {
    const loginData = AuthenticationModel.createLoginData(req.body);
    const context = this.createRequestContext(req);
    
    const result = await this.authService.login(req.body, context);
    const userProfile = UserProfile.fromAuthUser(result.user);
    
    res.json(createApiResponse(true, {
      user: userProfile.toJSON(),
      tokens: result.tokens
    }, 'Login successful'));
  });

  // Refresh authentication tokens
  refreshTokens = asyncHandler(async (req: any, res: Response): Promise<void> => {
    const { refreshToken } = req.body;
    const context = this.createRequestContext(req);
    
    const result = await this.authService.refreshTokens(refreshToken, context);
    
    res.json(createApiResponse(true, result, 'Tokens refreshed successfully'));
  });

  // User logout (single device)
  logout = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const refreshToken = req.body.refreshToken;
    const context = this.createRequestContext(req);
    const userId = req.userId!;
    
    // Get user for logout operation
    const user = await this.authService.getProfile(userId);
    await this.authService.logout(refreshToken, user, context);
    
    res.json(createApiResponse(true, null, 'Logged out successfully'));
  });

  // User logout (all devices)
  logoutAllDevices = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const context = this.createRequestContext(req);
    const userId = req.userId!;
    
    // Get user for logout operation
    const user = await this.authService.getProfile(userId);
    await this.authService.logoutAllDevices(user, context);
    
    res.json(createApiResponse(true, null, 'Logged out from all devices'));
  });

  // Get current user profile
  getProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    // Get user ID from token payload (set by middleware)
    const userId = req.userId || req.user?.id;
    if (!userId) {
      throw new AuthenticationError('User ID not found');
    }
    
    const user = await this.authService.getProfile(userId);
    const userProfile = UserProfile.fromAuthUser(user);
    
    res.json(createApiResponse(true, { user: userProfile.toJSON() }));
  });

  // Update user profile
  updateProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const profileData = AuthenticationModel.createProfileUpdateData(req.body);
    const userId = req.userId!;
    
    const updatedUser = await this.authService.updateProfile(userId, req.body);
    const userProfile = UserProfile.fromAuthUser(updatedUser);
    
    res.json(createApiResponse(true, { 
      user: userProfile.toJSON() 
    }, 'Profile updated successfully'));
  });

  // Change user password
  changePassword = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const passwordData = AuthenticationModel.createPasswordChangeData(req.body);
    const context = this.createRequestContext(req);
    const userId = req.userId!;
    
    await this.authService.changePassword(userId, req.body, context);
    
    res.json(createApiResponse(true, null, 'Password changed successfully. Please log in again.'));
  });

  // Get user sessions
  getUserSessions = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const userId = req.userId!;
    const sessions = await this.authService.getUserSessions(userId);
    const context = this.createRequestContext(req);
    
    // Convert to SessionInfo models with enhanced data
    const sessionInfos = sessions.map(session => {
      const sessionInfo = new SessionInfo(
        session.id,
        session.ipAddress,
        session.userAgent,
        session.createdAt,
        session.expiresAt,
        session.isActive
      );
      
      return {
        ...sessionInfo.toJSON(),
        isCurrent: sessionInfo.isCurrent(context.ipAddress, context.userAgent)
      };
    });
    
    res.json(createApiResponse(true, { sessions: sessionInfos }));
  });

  // Revoke specific session
  revokeSession = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { sessionId } = req.params;
    const userId = req.userId!;
    
    await this.authService.revokeSession(sessionId, userId);
    
    res.json(createApiResponse(true, null, 'Session revoked successfully'));
  });

  // Private helper methods for HTTP concerns
  private createRequestContext(req: any): RequestContext {
    return {
      ipAddress: getClientIP(req),
      userAgent: getUserAgent(req)
    };
  }
}

// Factory function to create controller with dependencies
export const createAuthController = (authService: IAuthService): AuthController => {
  return new AuthController(authService);
};

export default AuthController;