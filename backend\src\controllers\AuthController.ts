import {Response} from 'express';
import {IAuthService, RequestContext} from '../services/AuthService';
import {AuthenticatedRequest} from '../types/auth';
import {
	AuthenticationModel,
	UserProfile,
	AuthenticationResponse,
	SessionInfo,
} from '../models/AuthModels';
import {asyncHandler, AuthenticationError} from '../middleware/errorHandler';
import {getClientIP, getUserAgent, createApiResponse} from '../utils/auth';
import {authLogger, createLogger} from '../utils/logger';

// Controller interface for dependency inversion (using asyncHandler types)
export interface IAuthController {
	register: any;
	login: any;
	refreshTokens: any;
	logout: any;
	logoutAllDevices: any;
	getProfile: any;
	updateProfile: any;
	changePassword: any;
	getUserSessions: any;
	revokeSession: any;
}

// HTTP Controller implementation - handles only HTTP concerns
export class AuthController implements IAuthController {
	private logger = createLogger('AuthController');

	constructor(private authService: IAuthService) {
		this.logger.info('AuthController initialized');
	}

	// User registration
	register = asyncHandler(async (req: any, res: Response): Promise<void> => {
		const startTime = Date.now();
		const requestId = res.getHeader('X-Request-ID') as string;

		this.logger.info('Registration request received', {
			requestId,
			email: req.body.email,
			ipAddress: getClientIP(req),
			userAgent: getUserAgent(req),
		});

		try {
			const registrationData = AuthenticationModel.createRegistrationData(
				req.body
			);
			const context = this.createRequestContext(req);

			const result = await this.authService.register(req.body, context);
			const userProfile = UserProfile.fromAuthUser(result.user);
			const authResponse = new AuthenticationResponse(
				userProfile,
				result.tokens
			);

			const duration = Date.now() - startTime;
			this.logger.info('Registration completed successfully', {
				requestId,
				userId: result.user.id,
				email: result.user.email,
				duration,
			});

			res.status(201).json(
				createApiResponse(
					true,
					{
						user: userProfile.toJSON(),
						tokens: result.tokens,
					},
					'Account created successfully'
				)
			);
		} catch (error) {
			const duration = Date.now() - startTime;
			this.logger.error('Registration failed', error, {
				requestId,
				email: req.body.email,
				duration,
			});
			throw error;
		}
	});

	// User login
	login = asyncHandler(async (req: any, res: Response): Promise<void> => {
		const startTime = Date.now();
		const requestId = res.getHeader('X-Request-ID') as string;

		this.logger.info('Login request received', {
			requestId,
			email: req.body.email,
			ipAddress: getClientIP(req),
			userAgent: getUserAgent(req),
		});

		try {
			const loginData = AuthenticationModel.createLoginData(req.body);
			const context = this.createRequestContext(req);

			const result = await this.authService.login(req.body, context);
			const userProfile = UserProfile.fromAuthUser(result.user);

			const duration = Date.now() - startTime;
			this.logger.info('Login completed successfully', {
				requestId,
				userId: result.user.id,
				email: result.user.email,
				duration,
			});

			res.json(
				createApiResponse(
					true,
					{
						user: userProfile.toJSON(),
						tokens: result.tokens,
					},
					'Login successful'
				)
			);
		} catch (error) {
			const duration = Date.now() - startTime;
			this.logger.error('Login failed', error, {
				requestId,
				email: req.body.email,
				duration,
			});
			throw error;
		}
	});

	// Refresh authentication tokens
	refreshTokens = asyncHandler(
		async (req: any, res: Response): Promise<void> => {
			const startTime = Date.now();
			const requestId = res.getHeader('X-Request-ID') as string;

			this.logger.info('Token refresh request received', {
				requestId,
				ipAddress: getClientIP(req),
				userAgent: getUserAgent(req),
			});

			try {
				const {refreshToken} = req.body;
				const context = this.createRequestContext(req);

				const result = await this.authService.refreshTokens(
					refreshToken,
					context
				);

				const duration = Date.now() - startTime;
				this.logger.info('Token refresh completed successfully', {
					requestId,
					duration,
				});

				res.json(
					createApiResponse(true, result, 'Tokens refreshed successfully')
				);
			} catch (error) {
				const duration = Date.now() - startTime;
				this.logger.error('Token refresh failed', error, {
					requestId,
					duration,
				});
				throw error;
			}
		}
	);

	// User logout (single device)
	logout = asyncHandler(
		async (req: AuthenticatedRequest, res: Response): Promise<void> => {
			const startTime = Date.now();
			const requestId = res.getHeader('X-Request-ID') as string;
			const userId = req.userId!;

			this.logger.info('Logout request received', {
				requestId,
				userId,
				ipAddress: getClientIP(req),
				userAgent: getUserAgent(req),
			});

			try {
				const refreshToken = req.body.refreshToken;
				const context = this.createRequestContext(req);

				// Get user for logout operation
				const user = await this.authService.getProfile(userId);
				await this.authService.logout(refreshToken, user, context);

				const duration = Date.now() - startTime;
				this.logger.info('Logout completed successfully', {
					requestId,
					userId,
					duration,
				});

				res.json(createApiResponse(true, null, 'Logged out successfully'));
			} catch (error) {
				const duration = Date.now() - startTime;
				this.logger.error('Logout failed', error, {
					requestId,
					userId,
					duration,
				});
				throw error;
			}
		}
	);

	// User logout (all devices)
	logoutAllDevices = asyncHandler(
		async (req: AuthenticatedRequest, res: Response): Promise<void> => {
			const startTime = Date.now();
			const requestId = res.getHeader('X-Request-ID') as string;
			const userId = req.userId!;

			this.logger.info('Logout all devices request received', {
				requestId,
				userId,
				ipAddress: getClientIP(req),
				userAgent: getUserAgent(req),
			});

			try {
				const context = this.createRequestContext(req);

				// Get user for logout operation
				const user = await this.authService.getProfile(userId);
				await this.authService.logoutAllDevices(user, context);

				const duration = Date.now() - startTime;
				this.logger.info('Logout all devices completed successfully', {
					requestId,
					userId,
					duration,
				});

				res.json(createApiResponse(true, null, 'Logged out from all devices'));
			} catch (error) {
				const duration = Date.now() - startTime;
				this.logger.error('Logout all devices failed', error, {
					requestId,
					userId,
					duration,
				});
				throw error;
			}
		}
	);

	// Get current user profile
	getProfile = asyncHandler(
		async (req: AuthenticatedRequest, res: Response): Promise<void> => {
			const startTime = Date.now();
			const requestId = res.getHeader('X-Request-ID') as string;

			try {
				// Get user ID from token payload (set by middleware)
				const userId = req.userId || req.user?.id;
				if (!userId) {
					this.logger.warn('Get profile failed: No user ID found', {
						requestId,
						ipAddress: getClientIP(req),
						userAgent: getUserAgent(req),
					});
					throw new AuthenticationError('User ID not found');
				}

				this.logger.info('Get profile request received', {
					requestId,
					userId,
					ipAddress: getClientIP(req),
					userAgent: getUserAgent(req),
				});

				const user = await this.authService.getProfile(userId);
				const userProfile = UserProfile.fromAuthUser(user);

				const duration = Date.now() - startTime;
				this.logger.info('Get profile completed successfully', {
					requestId,
					userId,
					duration,
				});

				res.json(createApiResponse(true, {user: userProfile.toJSON()}));
			} catch (error) {
				const duration = Date.now() - startTime;
				this.logger.error('Get profile failed', error, {
					requestId,
					userId: req.userId,
					duration,
				});
				throw error;
			}
		}
	);

	// Update user profile
	updateProfile = asyncHandler(
		async (req: AuthenticatedRequest, res: Response): Promise<void> => {
			const startTime = Date.now();
			const requestId = res.getHeader('X-Request-ID') as string;
			const userId = req.userId!;

			this.logger.info('Update profile request received', {
				requestId,
				userId,
				updatedFields: Object.keys(req.body),
				ipAddress: getClientIP(req),
				userAgent: getUserAgent(req),
			});

			try {
				const profileData = AuthenticationModel.createProfileUpdateData(
					req.body
				);

				const updatedUser = await this.authService.updateProfile(
					userId,
					req.body
				);
				const userProfile = UserProfile.fromAuthUser(updatedUser);

				const duration = Date.now() - startTime;
				this.logger.info('Update profile completed successfully', {
					requestId,
					userId,
					updatedFields: Object.keys(req.body),
					duration,
				});

				res.json(
					createApiResponse(
						true,
						{
							user: userProfile.toJSON(),
						},
						'Profile updated successfully'
					)
				);
			} catch (error) {
				const duration = Date.now() - startTime;
				this.logger.error('Update profile failed', error, {
					requestId,
					userId,
					duration,
				});
				throw error;
			}
		}
	);

	// Change user password
	changePassword = asyncHandler(
		async (req: AuthenticatedRequest, res: Response): Promise<void> => {
			const startTime = Date.now();
			const requestId = res.getHeader('X-Request-ID') as string;
			const userId = req.userId!;

			this.logger.info('Change password request received', {
				requestId,
				userId,
				ipAddress: getClientIP(req),
				userAgent: getUserAgent(req),
			});

			try {
				const passwordData = AuthenticationModel.createPasswordChangeData(
					req.body
				);
				const context = this.createRequestContext(req);

				await this.authService.changePassword(userId, req.body, context);

				const duration = Date.now() - startTime;
				this.logger.info('Change password completed successfully', {
					requestId,
					userId,
					duration,
				});

				res.json(
					createApiResponse(
						true,
						null,
						'Password changed successfully. Please log in again.'
					)
				);
			} catch (error) {
				const duration = Date.now() - startTime;
				this.logger.error('Change password failed', error, {
					requestId,
					userId,
					duration,
				});
				throw error;
			}
		}
	);

	// Get user sessions
	getUserSessions = asyncHandler(
		async (req: AuthenticatedRequest, res: Response): Promise<void> => {
			const startTime = Date.now();
			const requestId = res.getHeader('X-Request-ID') as string;
			const userId = req.userId!;

			this.logger.info('Get user sessions request received', {
				requestId,
				userId,
				ipAddress: getClientIP(req),
				userAgent: getUserAgent(req),
			});

			try {
				const sessions = await this.authService.getUserSessions(userId);
				const context = this.createRequestContext(req);

				// Convert to SessionInfo models with enhanced data
				const sessionInfos = sessions.map((session) => {
					const sessionInfo = new SessionInfo(
						session.id,
						session.ipAddress,
						session.userAgent,
						session.createdAt,
						session.expiresAt,
						session.isActive
					);

					return {
						...sessionInfo.toJSON(),
						isCurrent: sessionInfo.isCurrent(
							context.ipAddress,
							context.userAgent
						),
					};
				});

				const duration = Date.now() - startTime;
				this.logger.info('Get user sessions completed successfully', {
					requestId,
					userId,
					sessionCount: sessions.length,
					duration,
				});

				res.json(createApiResponse(true, {sessions: sessionInfos}));
			} catch (error) {
				const duration = Date.now() - startTime;
				this.logger.error('Get user sessions failed', error, {
					requestId,
					userId,
					duration,
				});
				throw error;
			}
		}
	);

	// Revoke specific session
	revokeSession = asyncHandler(
		async (req: AuthenticatedRequest, res: Response): Promise<void> => {
			const startTime = Date.now();
			const requestId = res.getHeader('X-Request-ID') as string;
			const userId = req.userId!;
			const {sessionId} = req.params;

			this.logger.info('Revoke session request received', {
				requestId,
				userId,
				sessionId,
				ipAddress: getClientIP(req),
				userAgent: getUserAgent(req),
			});

			try {
				await this.authService.revokeSession(sessionId, userId);

				const duration = Date.now() - startTime;
				this.logger.info('Revoke session completed successfully', {
					requestId,
					userId,
					sessionId,
					duration,
				});

				res.json(createApiResponse(true, null, 'Session revoked successfully'));
			} catch (error) {
				const duration = Date.now() - startTime;
				this.logger.error('Revoke session failed', error, {
					requestId,
					userId,
					sessionId,
					duration,
				});
				throw error;
			}
		}
	);

	// Private helper methods for HTTP concerns
	private createRequestContext(req: any): RequestContext {
		return {
			ipAddress: getClientIP(req),
			userAgent: getUserAgent(req),
		};
	}
}

// Factory function to create controller with dependencies
export const createAuthController = (
	authService: IAuthService
): AuthController => {
	return new AuthController(authService);
};

export default AuthController;
