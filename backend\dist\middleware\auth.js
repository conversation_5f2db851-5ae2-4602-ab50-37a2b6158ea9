"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authRateLimit = exports.requireOwnershipOrAdmin = exports.requireEmailVerification = exports.authorize = exports.optionalAuthenticate = exports.authenticate = exports.verifyRefreshToken = exports.verifyAccessToken = exports.generateRefreshToken = exports.generateAccessToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const client_1 = require("@prisma/client");
const errorHandler_1 = require("./errorHandler");
const logger_1 = require("../utils/logger");
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';
const generateAccessToken = (payload) => {
    const jwtPayload = {
        userId: payload.userId,
        email: payload.email,
        role: payload.role,
    };
    return jsonwebtoken_1.default.sign(jwtPayload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
};
exports.generateAccessToken = generateAccessToken;
const generateRefreshToken = (payload) => {
    const jwtPayload = {
        userId: payload.userId,
        email: payload.email,
        role: payload.role,
    };
    return jsonwebtoken_1.default.sign(jwtPayload, JWT_REFRESH_SECRET, {
        expiresIn: JWT_REFRESH_EXPIRES_IN,
    });
};
exports.generateRefreshToken = generateRefreshToken;
const verifyAccessToken = (token) => {
    try {
        return jsonwebtoken_1.default.verify(token, JWT_SECRET);
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            throw new errorHandler_1.AuthenticationError('Access token expired');
        }
        else if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            throw new errorHandler_1.AuthenticationError('Invalid access token');
        }
        throw new errorHandler_1.AuthenticationError('Token verification failed');
    }
};
exports.verifyAccessToken = verifyAccessToken;
const verifyRefreshToken = (token) => {
    try {
        return jsonwebtoken_1.default.verify(token, JWT_REFRESH_SECRET);
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            throw new errorHandler_1.AuthenticationError('Refresh token expired');
        }
        else if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            throw new errorHandler_1.AuthenticationError('Invalid refresh token');
        }
        throw new errorHandler_1.AuthenticationError('Refresh token verification failed');
    }
};
exports.verifyRefreshToken = verifyRefreshToken;
const authenticate = async (req, res, next) => {
    const startTime = Date.now();
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            logger_1.authLogger.warn('Authentication failed: No Bearer token provided', {
                ip: req.ip || req.connection?.remoteAddress,
                userAgent: req.headers['user-agent'],
                path: req.path,
            });
            throw new errorHandler_1.AuthenticationError('Access token is required');
        }
        const token = authHeader.substring(7);
        const payload = (0, exports.verifyAccessToken)(token);
        req.userId = payload.userId;
        const duration = Date.now() - startTime;
        logger_1.authLogger.info('Authentication successful', {
            userId: payload.userId,
            email: payload.email,
            role: payload.role,
            ip: req.ip || req.connection?.remoteAddress,
            userAgent: req.headers['user-agent'],
            path: req.path,
            duration,
        });
        next();
    }
    catch (error) {
        const duration = Date.now() - startTime;
        if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            logger_1.authLogger.warn('Authentication failed: Token expired', {
                ip: req.ip || req.connection?.remoteAddress,
                userAgent: req.headers['user-agent'],
                path: req.path,
                duration,
            });
        }
        else if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            logger_1.authLogger.warn('Authentication failed: Invalid token', {
                ip: req.ip || req.connection?.remoteAddress,
                userAgent: req.headers['user-agent'],
                path: req.path,
                duration,
            });
        }
        else {
            logger_1.authLogger.error('Authentication failed: Unexpected error', error, {
                ip: req.ip || req.connection?.remoteAddress,
                userAgent: req.headers['user-agent'],
                path: req.path,
                duration,
            });
        }
        next(error);
    }
};
exports.authenticate = authenticate;
const optionalAuthenticate = async (req, res, next) => {
    const startTime = Date.now();
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            logger_1.authLogger.debug('Optional authentication: No token provided', {
                ip: req.ip || req.connection?.remoteAddress,
                userAgent: req.headers['user-agent'],
                path: req.path,
            });
            return next();
        }
        const token = authHeader.substring(7);
        const payload = (0, exports.verifyAccessToken)(token);
        req.userId = payload.userId;
        const duration = Date.now() - startTime;
        logger_1.authLogger.debug('Optional authentication successful', {
            userId: payload.userId,
            email: payload.email,
            role: payload.role,
            ip: req.ip || req.connection?.remoteAddress,
            userAgent: req.headers['user-agent'],
            path: req.path,
            duration,
        });
        next();
    }
    catch (error) {
        const duration = Date.now() - startTime;
        logger_1.authLogger.debug('Optional authentication failed, continuing without auth', {
            error: error instanceof Error ? error.message : 'Unknown error',
            ip: req.ip || req.connection?.remoteAddress,
            userAgent: req.headers['user-agent'],
            path: req.path,
            duration,
        });
        next();
    }
};
exports.optionalAuthenticate = optionalAuthenticate;
const authorize = (...roles) => {
    return (req, res, next) => {
        const startTime = Date.now();
        try {
            if (!req.user) {
                logger_1.authLogger.warn('Authorization failed: No user found', {
                    ip: req.ip || req.connection?.remoteAddress,
                    userAgent: req.headers['user-agent'],
                    path: req.path,
                    requiredRoles: roles,
                });
                throw new errorHandler_1.AuthenticationError('Authentication required');
            }
            if (!roles.includes(req.user.role)) {
                const duration = Date.now() - startTime;
                logger_1.authLogger.warn('Authorization failed: Insufficient permissions', {
                    userId: req.user.id,
                    userRole: req.user.role,
                    requiredRoles: roles,
                    ip: req.ip || req.connection?.remoteAddress,
                    userAgent: req.headers['user-agent'],
                    path: req.path,
                    duration,
                });
                throw new errorHandler_1.AuthorizationError('Insufficient permissions');
            }
            const duration = Date.now() - startTime;
            logger_1.authLogger.debug('Authorization successful', {
                userId: req.user.id,
                userRole: req.user.role,
                requiredRoles: roles,
                ip: req.ip || req.connection?.remoteAddress,
                userAgent: req.headers['user-agent'],
                path: req.path,
                duration,
            });
            next();
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger_1.authLogger.error('Authorization error', error, {
                ip: req.ip || req.connection?.remoteAddress,
                userAgent: req.headers['user-agent'],
                path: req.path,
                duration,
            });
            throw error;
        }
    };
};
exports.authorize = authorize;
const requireEmailVerification = (req, res, next) => {
    const startTime = Date.now();
    try {
        if (!req.user) {
            logger_1.authLogger.warn('Email verification check failed: No user found', {
                ip: req.ip || req.connection?.remoteAddress,
                userAgent: req.headers['user-agent'],
                path: req.path,
            });
            throw new errorHandler_1.AuthenticationError('Authentication required');
        }
        if (!req.user.isVerified) {
            const duration = Date.now() - startTime;
            logger_1.authLogger.warn('Email verification required', {
                userId: req.user.id,
                email: req.user.email,
                ip: req.ip || req.connection?.remoteAddress,
                userAgent: req.headers['user-agent'],
                path: req.path,
                duration,
            });
            throw new errorHandler_1.AuthorizationError('Email verification required');
        }
        const duration = Date.now() - startTime;
        logger_1.authLogger.debug('Email verification check passed', {
            userId: req.user.id,
            email: req.user.email,
            ip: req.ip || req.connection?.remoteAddress,
            userAgent: req.headers['user-agent'],
            path: req.path,
            duration,
        });
        next();
    }
    catch (error) {
        const duration = Date.now() - startTime;
        logger_1.authLogger.error('Email verification check error', error, {
            ip: req.ip || req.connection?.remoteAddress,
            userAgent: req.headers['user-agent'],
            path: req.path,
            duration,
        });
        throw error;
    }
};
exports.requireEmailVerification = requireEmailVerification;
const requireOwnershipOrAdmin = (userIdParam = 'userId') => {
    return (req, res, next) => {
        const startTime = Date.now();
        try {
            if (!req.user) {
                logger_1.authLogger.warn('Ownership check failed: No user found', {
                    ip: req.ip || req.connection?.remoteAddress,
                    userAgent: req.headers['user-agent'],
                    path: req.path,
                    userIdParam,
                });
                throw new errorHandler_1.AuthenticationError('Authentication required');
            }
            const targetUserId = req.params[userIdParam] || req.body[userIdParam];
            if (req.user.role === client_1.UserRole.ADMIN) {
                const duration = Date.now() - startTime;
                logger_1.authLogger.debug('Ownership check passed: Admin access', {
                    adminUserId: req.user.id,
                    targetUserId,
                    ip: req.ip || req.connection?.remoteAddress,
                    userAgent: req.headers['user-agent'],
                    path: req.path,
                    duration,
                });
                return next();
            }
            if (req.user.id === targetUserId) {
                const duration = Date.now() - startTime;
                logger_1.authLogger.debug('Ownership check passed: Owner access', {
                    userId: req.user.id,
                    targetUserId,
                    ip: req.ip || req.connection?.remoteAddress,
                    userAgent: req.headers['user-agent'],
                    path: req.path,
                    duration,
                });
                return next();
            }
            const duration = Date.now() - startTime;
            logger_1.authLogger.warn('Ownership check failed: Access denied', {
                userId: req.user.id,
                userRole: req.user.role,
                targetUserId,
                ip: req.ip || req.connection?.remoteAddress,
                userAgent: req.headers['user-agent'],
                path: req.path,
                duration,
            });
            throw new errorHandler_1.AuthorizationError('Access denied to this resource');
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger_1.authLogger.error('Ownership check error', error, {
                ip: req.ip || req.connection?.remoteAddress,
                userAgent: req.headers['user-agent'],
                path: req.path,
                duration,
            });
            throw error;
        }
    };
};
exports.requireOwnershipOrAdmin = requireOwnershipOrAdmin;
const authRateLimit = (maxAttempts = 5, windowMs = 15 * 60 * 1000) => {
    const attempts = new Map();
    return (req, res, next) => {
        const startTime = Date.now();
        const key = `${req.ip}:${req.path}`;
        const now = Date.now();
        const current = attempts.get(key);
        if (current && now > current.resetTime) {
            attempts.delete(key);
        }
        const entry = attempts.get(key) || { count: 0, resetTime: now + windowMs };
        if (entry.count >= maxAttempts) {
            const remainingTime = Math.ceil((entry.resetTime - now) / 1000 / 60);
            const duration = Date.now() - startTime;
            logger_1.securityLogger.warn('Rate limit exceeded', {
                ip: req.ip,
                path: req.path,
                attempts: entry.count,
                maxAttempts,
                remainingTime,
                userAgent: req.headers['user-agent'],
                duration,
            });
            throw new errorHandler_1.AuthenticationError(`Too many attempts. Try again in ${remainingTime} minutes.`);
        }
        entry.count++;
        attempts.set(key, entry);
        if (entry.count > 1) {
            const duration = Date.now() - startTime;
            logger_1.securityLogger.info('Rate limit attempt', {
                ip: req.ip,
                path: req.path,
                attempts: entry.count,
                maxAttempts,
                userAgent: req.headers['user-agent'],
                duration,
            });
        }
        if (Math.random() < 0.01) {
            for (const [k, v] of attempts.entries()) {
                if (now > v.resetTime) {
                    attempts.delete(k);
                }
            }
        }
        next();
    };
};
exports.authRateLimit = authRateLimit;
exports.default = {
    generateAccessToken: exports.generateAccessToken,
    generateRefreshToken: exports.generateRefreshToken,
    verifyAccessToken: exports.verifyAccessToken,
    verifyRefreshToken: exports.verifyRefreshToken,
    authenticate: exports.authenticate,
    optionalAuthenticate: exports.optionalAuthenticate,
    authorize: exports.authorize,
    requireEmailVerification: exports.requireEmailVerification,
    requireOwnershipOrAdmin: exports.requireOwnershipOrAdmin,
    authRateLimit: exports.authRateLimit,
};
//# sourceMappingURL=auth.js.map