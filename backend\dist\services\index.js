"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisService = exports.redisService = exports.DatabaseService = exports.databaseService = void 0;
var database_1 = require("./database");
Object.defineProperty(exports, "databaseService", { enumerable: true, get: function () { return database_1.databaseService; } });
Object.defineProperty(exports, "DatabaseService", { enumerable: true, get: function () { return database_1.DatabaseService; } });
var redis_1 = require("./redis");
Object.defineProperty(exports, "redisService", { enumerable: true, get: function () { return redis_1.redisService; } });
Object.defineProperty(exports, "RedisService", { enumerable: true, get: function () { return redis_1.RedisService; } });
//# sourceMappingURL=index.js.map