# SuperClaude Quick Reference Card

## 🚀 Essential Commands

### Development

| Command         | Description        | Example                          |
| --------------- | ------------------ | -------------------------------- |
| `/sc:implement` | Implement features | `/sc:implement user auth`        |
| `/sc:build`     | Build/compile code | `/sc:build --target production`  |
| `/sc:test`      | Run tests          | `/sc:test --type unit`           |
| `/sc:debug`     | Debug code         | `/sc:debug --breakpoint auth:45` |

### Code Quality

| Command        | Description              | Example                          |
| -------------- | ------------------------ | -------------------------------- |
| `/sc:review`   | Code review              | `/sc:review --focus security`    |
| `/sc:refactor` | Refactor code            | `/sc:refactor --optimize`        |
| `/sc:optimize` | Performance optimization | `/sc:optimize --target database` |

### Documentation

| Command        | Description   | Example                        |
| -------------- | ------------- | ------------------------------ |
| `/sc:document` | Generate docs | `/sc:document --type api`      |
| `/sc:explain`  | Explain code  | `/sc:explain --depth detailed` |

### Project Management

| Command        | Description              | Example                               |
| -------------- | ------------------------ | ------------------------------------- |
| `/sc:plan`     | Project planning         | `/sc:plan --scope sprint`             |
| `/sc:analyze`  | Code analysis            | `/sc:analyze --metrics complexity`    |
| `/sc:workflow` | Implementation workflows | `/sc:workflow feature --strategy mvp` |

## 🔧 PrintWedittV1 Workflow

### Backend Development

```bash
# 1. Plan feature
/sc:plan --scope "user authentication"

# 2. Implement
/sc:implement --priority high user auth system

# 3. Generate tests
/sc:generate --type test --coverage 90

# 4. Test
/sc:test --type unit auth service

# 5. Review
/sc:review --focus security
```

### Frontend Development

```bash
# 1. Generate component
/sc:generate --type component --framework react

# 2. Implement feature
/sc:implement --target "user dashboard"

# 3. Test component
/sc:test --type unit --target components

# 4. Optimize
/sc:optimize --target "bundle size"
```

### API Development

```bash
# 1. Document API
/sc:document --type api --format openapi

# 2. Implement endpoints
/sc:implement --target "user management API"

# 3. Test API
/sc:test --type integration --target api
```

## 🛠️ MCP Servers

SuperClaude integrates with external tools via MCP servers for enhanced capabilities. These auto-connect when needed and can be controlled with flags.

| Server         | Purpose                     | Use Case                           | Activation Flags |
| -------------- | --------------------------- | ---------------------------------- | ---------------- |
| **Context7**   | Official library docs       | Framework patterns, best practices | `--c7`           |
| **Sequential** | Complex multi-step analysis | Root cause analysis, architecture  | `--seq`          |
| **Magic**      | UI component generation     | React components, design systems   | `--magic`        |
| **Playwright** | E2E testing & monitoring    | Cross-browser testing, performance | `--play`         |

### Control Flags

- `--all-mcp` - Enable all servers (complex problems)
- `--no-mcp` - Disable all servers (40-60% faster)
- `--no-[server]` - Disable specific server

### Advanced Examples

```bash
# Multi-domain coordination
/sc:analyze fullstack-app/ --all-mcp --delegate auto

# Domain-specific improvements
/sc:improve frontend/ --persona-frontend --magic
/sc:improve backend/ --persona-backend --c7

# Performance optimization
/sc:analyze huge-project/ --uc --no-mcp --scope module
```

## 📋 Daily Workflow

### Morning

1. `/sc:plan` - Review daily tasks
2. `/sc:analyze` - Check codebase health

### Development

1. `/sc:implement` - Build features
2. `/sc:test` - Validate changes
3. `/sc:debug` - Fix issues

### Before Commit

1. `/sc:review` - Code review
2. `/sc:optimize` - Performance check
3. `/sc:document` - Update docs

## 🚨 Troubleshooting

### Common Issues

```bash
# Command not found
py -3.13 -m SuperClaude --version

# MCP server issues
py -3.13 -m SuperClaude status

# Reinstall
py -3.13 -m SuperClaude install --force
```

### Environment Variables

```bash
# Set Magic API key
set TWENTYFIRST_API_KEY=your_key_here
```

## 📚 Resources

- **Full Guide**: `docs/SUPERCLAUDE_GUIDE.md`
- **GitHub**: https://github.com/SuperClaude-Org/SuperClaude_Framework
- **Docs**: https://superclaude-org.github.io/

---

**Version**: SuperClaude v3.0.0.2 | **PrintWedittV1** | **Last Updated**: Aug 5, 2025
