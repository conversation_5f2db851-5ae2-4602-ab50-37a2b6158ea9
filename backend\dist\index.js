"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const compression_1 = __importDefault(require("compression"));
const morgan_1 = __importDefault(require("morgan"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const client_1 = require("@prisma/client");
const config_1 = require("./config");
const logger_1 = require("./utils/logger");
const auth_1 = __importDefault(require("./routes/auth"));
const errorHandler_1 = require("./middleware/errorHandler");
const app = (0, express_1.default)();
const prisma = new client_1.PrismaClient();
const logger = (0, logger_1.createLogger)('Server');
const serverConfig = config_1.config.getServerConfig();
const securityConfig = config_1.config.getSecurityConfig();
const loggingConfig = config_1.config.getLoggingConfig();
app.use((0, helmet_1.default)({
    crossOriginResourcePolicy: { policy: 'cross-origin' },
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", 'data:', 'https:'],
        },
    },
}));
const limiter = (0, express_rate_limit_1.default)({
    windowMs: securityConfig.rateLimitWindowMs,
    max: securityConfig.rateLimitMaxRequests,
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
});
app.use(limiter);
app.use((0, cors_1.default)({
    origin: serverConfig.corsOrigin,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));
app.use((0, compression_1.default)());
app.use(express_1.default.json({ limit: `${securityConfig.maxFileSize}mb` }));
app.use(express_1.default.urlencoded({ extended: true, limit: `${securityConfig.maxFileSize}mb` }));
app.use((0, cookie_parser_1.default)(securityConfig.cookieSecret));
if (loggingConfig.enableConsole) {
    if (serverConfig.nodeEnv !== 'production') {
        app.use((0, morgan_1.default)('dev'));
    }
    else {
        app.use((0, morgan_1.default)('combined'));
    }
}
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: serverConfig.nodeEnv,
        version: process.env.npm_package_version || '1.0.0',
    });
});
app.use('/api/auth', auth_1.default);
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`,
    });
});
app.use(errorHandler_1.errorHandler);
async function connectDB() {
    try {
        const dbConfig = config_1.config.getDatabaseConfig();
        await prisma.$connect();
        logger.info('Database connected successfully', {
            url: dbConfig.url.replace(/\/\/.*@/, '//***:***@'),
            maxConnections: dbConfig.maxConnections,
            idleTimeout: dbConfig.idleTimeout,
        });
    }
    catch (error) {
        logger.error('Database connection failed', error);
        process.exit(1);
    }
}
process.on('SIGINT', async () => {
    logger.info('Shutting down gracefully...');
    await prisma.$disconnect();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    logger.info('Shutting down gracefully...');
    await prisma.$disconnect();
    process.exit(0);
});
async function startServer() {
    try {
        await connectDB();
        app.listen(serverConfig.port, serverConfig.host, () => {
            logger.info('Server started successfully', {
                port: serverConfig.port,
                host: serverConfig.host,
                environment: serverConfig.nodeEnv,
                apiBaseUrl: serverConfig.apiBaseUrl,
                frontendUrl: serverConfig.frontendUrl,
                healthCheck: `http://${serverConfig.host}:${serverConfig.port}/health`,
            });
        });
    }
    catch (error) {
        logger.error('Failed to start server', error);
        process.exit(1);
    }
}
startServer().catch((error) => {
    logger.error('Failed to start server', error);
    process.exit(1);
});
exports.default = app;
//# sourceMappingURL=index.js.map