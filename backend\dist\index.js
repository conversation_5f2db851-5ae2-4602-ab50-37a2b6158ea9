"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.startServer = exports.DatabaseManager = exports.ServerManager = exports.app = void 0;
const server_1 = require("./server");
const logger_1 = require("./utils/logger");
const logger = (0, logger_1.createLogger)('Index');
(0, server_1.startServer)().catch((error) => {
    logger.error('Failed to start server from index.ts', error);
    process.exit(1);
});
var app_1 = require("./app");
Object.defineProperty(exports, "app", { enumerable: true, get: function () { return __importDefault(app_1).default; } });
var server_2 = require("./server");
Object.defineProperty(exports, "ServerManager", { enumerable: true, get: function () { return server_2.ServerManager; } });
Object.defineProperty(exports, "DatabaseManager", { enumerable: true, get: function () { return server_2.DatabaseManager; } });
Object.defineProperty(exports, "startServer", { enumerable: true, get: function () { return server_2.startServer; } });
//# sourceMappingURL=index.js.map