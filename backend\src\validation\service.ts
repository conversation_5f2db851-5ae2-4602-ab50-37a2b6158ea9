import { ServicePricingType, FormFieldType } from '@prisma/client';
import <PERSON><PERSON> from 'joi';

// Base schemas for reuse
export const serviceNameSchema = Joi.string()
  .trim()
  .min(1)
  .max(255)
  .required()
  .messages({
    'string.empty': 'Service name cannot be empty',
    'string.min': 'Service name must be at least 1 character long',
    'string.max': 'Service name must not exceed 255 characters',
    'any.required': 'Service name is required',
  });

export const serviceDescriptionSchema = Joi.string()
  .trim()
  .min(1)
  .max(1000)
  .required()
  .messages({
    'string.empty': 'Service description cannot be empty',
    'string.min': 'Service description must be at least 1 character long',
    'string.max': 'Service description must not exceed 1000 characters',
    'any.required': 'Service description is required',
  });

export const serviceImageSchema = Joi.string()
  .uri()
  .max(500)
  .required()
  .messages({
    'string.uri': 'Service image must be a valid URL',
    'string.max': 'Service image URL must not exceed 500 characters',
    'any.required': 'Service image is required',
  });

export const basePriceSchema = Joi.number()
  .min(0)
  .precision(2)
  .required()
  .messages({
    'number.min': 'Base price must be non-negative',
    'number.precision': 'Base price must have at most 2 decimal places',
    'any.required': 'Base price is required',
  });

export const serviceIdSchema = Joi.string()
  .required()
  .messages({
    'any.required': 'Service ID is required',
  });

export const categoryIdSchema = Joi.string()
  .required()
  .messages({
    'any.required': 'Category ID is required',
  });

// Service CRUD validation schemas
export const createServiceSchema = Joi.object({
  name: serviceNameSchema,
  description: serviceDescriptionSchema,
  detailedDesc: Joi.string().trim().max(5000).optional().allow('').messages({
    'string.max': 'Detailed description must not exceed 5000 characters',
  }),
  features: Joi.array().items(Joi.string().trim().max(255)).max(20).default([]).messages({
    'array.max': 'Maximum 20 features allowed',
    'string.max': 'Feature must not exceed 255 characters',
  }),
  notes: Joi.string().trim().max(1000).optional().allow('').messages({
    'string.max': 'Notes must not exceed 1000 characters',
  }),
  categoryId: categoryIdSchema,
  image: serviceImageSchema,
  basePrice: basePriceSchema,
  pricingType: Joi.string()
    .valid(...Object.values(ServicePricingType))
    .default(ServicePricingType.FIXED)
    .messages({
      'any.only': 'Pricing type must be one of: FIXED, VARIABLE, QUOTE',
    }),
  isActive: Joi.boolean().default(true),
  sortOrder: Joi.number().integer().min(0).default(0).messages({
    'number.integer': 'Sort order must be an integer',
    'number.min': 'Sort order must be non-negative',
  }),
});

export const updateServiceSchema = Joi.object({
  name: serviceNameSchema.optional(),
  description: serviceDescriptionSchema.optional(),
  detailedDesc: Joi.string().trim().max(5000).optional().allow('').messages({
    'string.max': 'Detailed description must not exceed 5000 characters',
  }),
  features: Joi.array().items(Joi.string().trim().max(255)).max(20).optional().messages({
    'array.max': 'Maximum 20 features allowed',
    'string.max': 'Feature must not exceed 255 characters',
  }),
  notes: Joi.string().trim().max(1000).optional().allow('').messages({
    'string.max': 'Notes must not exceed 1000 characters',
  }),
  categoryId: categoryIdSchema.optional(),
  image: serviceImageSchema.optional(),
  basePrice: basePriceSchema.optional(),
  pricingType: Joi.string()
    .valid(...Object.values(ServicePricingType))
    .optional()
    .messages({
      'any.only': 'Pricing type must be one of: FIXED, VARIABLE, QUOTE',
    }),
  isActive: Joi.boolean().optional(),
  sortOrder: Joi.number().integer().min(0).optional().messages({
    'number.integer': 'Sort order must be an integer',
    'number.min': 'Sort order must be non-negative',
  }),
}).min(1).messages({
  'object.min': 'At least one field must be provided for update',
});

// Service list query validation schema
export const serviceListQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1).messages({
    'number.base': 'Page must be a number',
    'number.integer': 'Page must be an integer',
    'number.min': 'Page must be at least 1',
  }),
  limit: Joi.number().integer().min(1).max(100).default(20).messages({
    'number.base': 'Limit must be a number',
    'number.integer': 'Limit must be an integer',
    'number.min': 'Limit must be at least 1',
    'number.max': 'Limit must not exceed 100',
  }),
  search: Joi.string().max(255).trim().optional().allow('').messages({
    'string.max': 'Search term must not exceed 255 characters',
  }),
  categoryId: Joi.string().optional().messages({
    'string.base': 'Category ID must be a string',
  }),
  pricingType: Joi.string()
    .valid(...Object.values(ServicePricingType))
    .optional()
    .messages({
      'any.only': 'Pricing type must be one of: FIXED, VARIABLE, QUOTE',
    }),
  isActive: Joi.boolean().optional(),
  sortBy: Joi.string()
    .valid('name', 'basePrice', 'createdAt', 'updatedAt', 'sortOrder')
    .default('sortOrder')
    .messages({
      'any.only': 'Sort by must be one of: name, basePrice, createdAt, updatedAt, sortOrder',
    }),
  sortOrder: Joi.string().valid('asc', 'desc').default('asc').messages({
    'any.only': 'Sort order must be either asc or desc',
  }),
  minPrice: Joi.number().min(0).precision(2).optional().messages({
    'number.min': 'Minimum price must be non-negative',
    'number.precision': 'Minimum price must have at most 2 decimal places',
  }),
  maxPrice: Joi.number().min(0).precision(2).optional().messages({
    'number.min': 'Maximum price must be non-negative',
    'number.precision': 'Maximum price must have at most 2 decimal places',
  }),
}).custom((value, helpers) => {
  if (value.minPrice !== undefined && value.maxPrice !== undefined && value.minPrice > value.maxPrice) {
    return helpers.error('custom.priceRange');
  }
  return value;
}).messages({
  'custom.priceRange': 'Minimum price cannot be greater than maximum price',
});

// Service search query validation schema
export const serviceSearchQuerySchema = Joi.object({
  q: Joi.string().max(255).trim().optional().allow('').messages({
    'string.max': 'Search query must not exceed 255 characters',
  }),
  category: Joi.string().optional().messages({
    'string.base': 'Category must be a string',
  }),
  minPrice: Joi.number().min(0).precision(2).optional().messages({
    'number.min': 'Minimum price must be non-negative',
    'number.precision': 'Minimum price must have at most 2 decimal places',
  }),
  maxPrice: Joi.number().min(0).precision(2).optional().messages({
    'number.min': 'Maximum price must be non-negative',
    'number.precision': 'Maximum price must have at most 2 decimal places',
  }),
  pricingType: Joi.string()
    .valid(...Object.values(ServicePricingType))
    .optional()
    .messages({
      'any.only': 'Pricing type must be one of: FIXED, VARIABLE, QUOTE',
    }),
  features: Joi.array().items(Joi.string().trim().max(255)).max(10).optional().messages({
    'array.max': 'Maximum 10 features allowed in search',
    'string.max': 'Feature must not exceed 255 characters',
  }),
  sortBy: Joi.string()
    .valid('name', 'price', 'popularity', 'rating')
    .default('name')
    .messages({
      'any.only': 'Sort by must be one of: name, price, popularity, rating',
    }),
  sortOrder: Joi.string().valid('asc', 'desc').default('asc').messages({
    'any.only': 'Sort order must be either asc or desc',
  }),
}).custom((value, helpers) => {
  if (value.minPrice !== undefined && value.maxPrice !== undefined && value.minPrice > value.maxPrice) {
    return helpers.error('custom.priceRange');
  }
  return value;
}).messages({
  'custom.priceRange': 'Minimum price cannot be greater than maximum price',
});

// Service category validation schemas
export const createServiceCategorySchema = Joi.object({
  name: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.empty': 'Category name cannot be empty',
      'string.min': 'Category name must be at least 1 character long',
      'string.max': 'Category name must not exceed 100 characters',
      'any.required': 'Category name is required',
    }),
  description: Joi.string().trim().max(1000).optional().allow('').messages({
    'string.max': 'Category description must not exceed 1000 characters',
  }),
  icon: Joi.string().trim().max(100).optional().allow('').messages({
    'string.max': 'Category icon must not exceed 100 characters',
  }),
  route: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .pattern(/^[a-z0-9-]+$/)
    .required()
    .messages({
      'string.empty': 'Category route cannot be empty',
      'string.min': 'Category route must be at least 1 character long',
      'string.max': 'Category route must not exceed 100 characters',
      'string.pattern.base': 'Category route must contain only lowercase letters, numbers, and hyphens',
      'any.required': 'Category route is required',
    }),
  isActive: Joi.boolean().default(true),
  sortOrder: Joi.number().integer().min(0).default(0).messages({
    'number.integer': 'Sort order must be an integer',
    'number.min': 'Sort order must be non-negative',
  }),
});

export const updateServiceCategorySchema = Joi.object({
  name: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .optional()
    .messages({
      'string.empty': 'Category name cannot be empty',
      'string.min': 'Category name must be at least 1 character long',
      'string.max': 'Category name must not exceed 100 characters',
    }),
  description: Joi.string().trim().max(1000).optional().allow('').messages({
    'string.max': 'Category description must not exceed 1000 characters',
  }),
  icon: Joi.string().trim().max(100).optional().allow('').messages({
    'string.max': 'Category icon must not exceed 100 characters',
  }),
  route: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .pattern(/^[a-z0-9-]+$/)
    .optional()
    .messages({
      'string.empty': 'Category route cannot be empty',
      'string.min': 'Category route must be at least 1 character long',
      'string.max': 'Category route must not exceed 100 characters',
      'string.pattern.base': 'Category route must contain only lowercase letters, numbers, and hyphens',
    }),
  isActive: Joi.boolean().optional(),
  sortOrder: Joi.number().integer().min(0).optional().messages({
    'number.integer': 'Sort order must be an integer',
    'number.min': 'Sort order must be non-negative',
  }),
}).min(1).messages({
  'object.min': 'At least one field must be provided for update',
});

// Service form field validation schemas
export const createServiceFormFieldSchema = Joi.object({
  serviceId: serviceIdSchema,
  name: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.empty': 'Field name cannot be empty',
      'string.min': 'Field name must be at least 1 character long',
      'string.max': 'Field name must not exceed 100 characters',
      'any.required': 'Field name is required',
    }),
  label: Joi.string()
    .trim()
    .min(1)
    .max(255)
    .required()
    .messages({
      'string.empty': 'Field label cannot be empty',
      'string.min': 'Field label must be at least 1 character long',
      'string.max': 'Field label must not exceed 255 characters',
      'any.required': 'Field label is required',
    }),
  type: Joi.string()
    .valid(...Object.values(FormFieldType))
    .required()
    .messages({
      'any.only': 'Field type must be one of: TEXT, NUMBER, SELECT, CHECKBOX, RADIO, FILE',
      'any.required': 'Field type is required',
    }),
  required: Joi.boolean().default(false),
  placeholder: Joi.string().trim().max(255).optional().allow('').messages({
    'string.max': 'Field placeholder must not exceed 255 characters',
  }),
  defaultValue: Joi.string().trim().max(500).optional().allow('').messages({
    'string.max': 'Field default value must not exceed 500 characters',
  }),
  validation: Joi.object().optional(),
  sortOrder: Joi.number().integer().min(0).default(0).messages({
    'number.integer': 'Sort order must be an integer',
    'number.min': 'Sort order must be non-negative',
  }),
  isActive: Joi.boolean().default(true),
  options: Joi.array()
    .items(
      Joi.object({
        value: Joi.string().trim().min(1).max(100).required().messages({
          'string.empty': 'Option value cannot be empty',
          'string.min': 'Option value must be at least 1 character long',
          'string.max': 'Option value must not exceed 100 characters',
          'any.required': 'Option value is required',
        }),
        label: Joi.string().trim().min(1).max(255).required().messages({
          'string.empty': 'Option label cannot be empty',
          'string.min': 'Option label must be at least 1 character long',
          'string.max': 'Option label must not exceed 255 characters',
          'any.required': 'Option label is required',
        }),
        priceModifier: Joi.number().precision(2).optional().messages({
          'number.precision': 'Price modifier must have at most 2 decimal places',
        }),
        sortOrder: Joi.number().integer().min(0).default(0).messages({
          'number.integer': 'Sort order must be an integer',
          'number.min': 'Sort order must be non-negative',
        }),
        isActive: Joi.boolean().default(true),
      })
    )
    .max(50)
    .optional()
    .messages({
      'array.max': 'Maximum 50 options allowed per field',
    }),
});

export const updateServiceFormFieldSchema = Joi.object({
  name: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .optional()
    .messages({
      'string.empty': 'Field name cannot be empty',
      'string.min': 'Field name must be at least 1 character long',
      'string.max': 'Field name must not exceed 100 characters',
    }),
  label: Joi.string()
    .trim()
    .min(1)
    .max(255)
    .optional()
    .messages({
      'string.empty': 'Field label cannot be empty',
      'string.min': 'Field label must be at least 1 character long',
      'string.max': 'Field label must not exceed 255 characters',
    }),
  type: Joi.string()
    .valid(...Object.values(FormFieldType))
    .optional()
    .messages({
      'any.only': 'Field type must be one of: TEXT, NUMBER, SELECT, CHECKBOX, RADIO, FILE',
    }),
  required: Joi.boolean().optional(),
  placeholder: Joi.string().trim().max(255).optional().allow('').messages({
    'string.max': 'Field placeholder must not exceed 255 characters',
  }),
  defaultValue: Joi.string().trim().max(500).optional().allow('').messages({
    'string.max': 'Field default value must not exceed 500 characters',
  }),
  validation: Joi.object().optional(),
  sortOrder: Joi.number().integer().min(0).optional().messages({
    'number.integer': 'Sort order must be an integer',
    'number.min': 'Sort order must be non-negative',
  }),
  isActive: Joi.boolean().optional(),
}).min(1).messages({
  'object.min': 'At least one field must be provided for update',
});

// Service form field option validation schemas
export const createServiceFormFieldOptionSchema = Joi.object({
  fieldId: Joi.string().required().messages({
    'any.required': 'Field ID is required',
  }),
  value: Joi.string().trim().min(1).max(100).required().messages({
    'string.empty': 'Option value cannot be empty',
    'string.min': 'Option value must be at least 1 character long',
    'string.max': 'Option value must not exceed 100 characters',
    'any.required': 'Option value is required',
  }),
  label: Joi.string().trim().min(1).max(255).required().messages({
    'string.empty': 'Option label cannot be empty',
    'string.min': 'Option label must be at least 1 character long',
    'string.max': 'Option label must not exceed 255 characters',
    'any.required': 'Option label is required',
  }),
  priceModifier: Joi.number().precision(2).optional().messages({
    'number.precision': 'Price modifier must have at most 2 decimal places',
  }),
  sortOrder: Joi.number().integer().min(0).default(0).messages({
    'number.integer': 'Sort order must be an integer',
    'number.min': 'Sort order must be non-negative',
  }),
  isActive: Joi.boolean().default(true),
});

export const updateServiceFormFieldOptionSchema = Joi.object({
  value: Joi.string().trim().min(1).max(100).optional().messages({
    'string.empty': 'Option value cannot be empty',
    'string.min': 'Option value must be at least 1 character long',
    'string.max': 'Option value must not exceed 100 characters',
  }),
  label: Joi.string().trim().min(1).max(255).optional().messages({
    'string.empty': 'Option label cannot be empty',
    'string.min': 'Option label must be at least 1 character long',
    'string.max': 'Option label must not exceed 255 characters',
  }),
  priceModifier: Joi.number().precision(2).optional().messages({
    'number.precision': 'Price modifier must have at most 2 decimal places',
  }),
  sortOrder: Joi.number().integer().min(0).optional().messages({
    'number.integer': 'Sort order must be an integer',
    'number.min': 'Sort order must be non-negative',
  }),
  isActive: Joi.boolean().optional(),
}).min(1).messages({
  'object.min': 'At least one field must be provided for update',
});

// Price calculation validation schema
export const priceCalculationSchema = Joi.object({
  serviceId: serviceIdSchema,
  formData: Joi.object().required().messages({
    'any.required': 'Form data is required for price calculation',
  }),
  quantity: Joi.number().integer().min(1).default(1).messages({
    'number.integer': 'Quantity must be an integer',
    'number.min': 'Quantity must be at least 1',
  }),
  providerId: Joi.string().optional(),
});

// Parameter validation schemas
export const serviceIdParamSchema = Joi.object({
  id: serviceIdSchema,
});

export const categoryIdParamSchema = Joi.object({
  categoryId: categoryIdSchema,
});

export const categoryRouteParamSchema = Joi.object({
  route: Joi.string().required().messages({
    'any.required': 'Category route is required',
  }),
});

export const fieldIdParamSchema = Joi.object({
  fieldId: Joi.string().required().messages({
    'any.required': 'Field ID is required',
  }),
});

export const pricingTypeParamSchema = Joi.object({
  pricingType: Joi.string()
    .valid(...Object.values(ServicePricingType))
    .required()
    .messages({
      'any.only': 'Pricing type must be one of: FIXED, VARIABLE, QUOTE',
      'any.required': 'Pricing type is required',
    }),
});

// Utility query schemas
export const popularServicesQuerySchema = Joi.object({
  limit: Joi.number().integer().min(1).max(50).default(10).messages({
    'number.integer': 'Limit must be an integer',
    'number.min': 'Limit must be at least 1',
    'number.max': 'Limit must not exceed 50',
  }),
});

// Export validation middleware (reuse from user.ts patterns)
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
      }));

      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: 'Invalid request data',
        validationErrors,
      });
    }

    req.body = value;
    next();
  };
};

// Query validation middleware
export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
      }));

      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: 'Invalid query parameters',
        validationErrors,
      });
    }

    req.query = value;
    next();
  };
};

// Parameter validation middleware
export const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.params, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
      }));

      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: 'Invalid path parameters',
        validationErrors,
      });
    }

    req.params = value;
    next();
  };
};