import { Request, Response, NextFunction } from 'express';
import { AuthenticationError } from './errorHandler';
import { createLogger } from '../utils/logger';

const logger = createLogger('RefreshTokenValidator');

/**
 * Middleware to enforce refresh token cookie presence
 * 2025 Security Best Practice: Only accept refresh tokens from HttpOnly cookies
 */
export const requireRefreshTokenCookie = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    const refreshToken = req.cookies?.refreshToken;
    const bodyToken = req.body?.refreshToken;
    
    // Check if refresh token is sent in request body (not allowed)
    if (bodyToken) {
      logger.warn('Refresh token request failed - body token not allowed', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      
      throw new AuthenticationError(
        'Refresh tokens in request body are no longer supported. Please ensure your client sends refresh tokens via HttpOnly cookies only.'
      );
    }
    
    if (!refreshToken) {
      logger.warn('Refresh token request failed - missing cookie token', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      
      throw new AuthenticationError(
        'Refresh token must be provided via secure HttpOnly cookie. Body-based refresh tokens are not supported for security reasons.'
      );
    }
    
    // Log successful cookie validation
    logger.debug('Refresh token validated', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });
    
    next();
  } catch (error) {
    next(error);
  }
};