import { Request, Response, NextFunction } from 'express';
import { AuthenticationError } from './errorHandler';
import { createLogger } from '../utils/logger';

const logger = createLogger('RefreshTokenValidator');

/**
 * Middleware to enforce refresh token cookie presence
 * 2025 Security Best Practice: Only accept refresh tokens from HttpOnly cookies
 */
export const requireRefreshTokenCookie = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    const refreshToken = req.cookies?.refreshToken;
    
    if (!refreshToken) {
      logger.warn('Refresh token request failed - missing token', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      
      throw new AuthenticationError('Refresh token is required');
    }
    
    // Log successful cookie validation
    logger.debug('Refresh token validated', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });
    
    next();
  } catch (error) {
    next(error);
  }
};