import { CoreServiceRepository } from '@/repositories/CoreServiceRepository';

const mockPrisma = {
  service: {
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
    findMany: jest.fn(),
  },
  service_categories: {
    findMany: jest.fn(),
  },
} as any;

describe('CoreServiceRepository', () => {
  let repo: CoreServiceRepository;

  beforeEach(() => {
    repo = new CoreServiceRepository(mockPrisma);
    jest.clearAllMocks();
  });

  it('findServiceById - includes associations and maps', async () => {
    mockPrisma.service.findUnique.mockResolvedValue({
      id: 'svc-1',
      name: 'A',
      description: 'D',
      basePrice: 10,
      service_categories: { name: 'Cat' },
      formFields: [],
      providers: [],
    });
    const result = await repo.findServiceById('svc-1', true);
    expect(mockPrisma.service.findUnique).toHaveBeenCalledWith({
      where: { id: 'svc-1' },
      include: expect.objectContaining({
        service_categories: true,
        providers: expect.any(Object),
      }),
    });
    expect(result?.id).toBe('svc-1');
  });

  it('create/update/delete - delegates to prisma', async () => {
    mockPrisma.service.create.mockResolvedValue({ id: 'svc-1' });
    await repo.createService({ id: 'svc-1' } as any);
    expect(mockPrisma.service.create).toHaveBeenCalledWith({ data: expect.objectContaining({ id: 'svc-1' }) });

    mockPrisma.service.update.mockResolvedValue({});
    await repo.updateService('svc-1', { name: 'N' } as any);
    expect(mockPrisma.service.update).toHaveBeenCalledWith({ where: { id: 'svc-1' }, data: { name: 'N' } });

    mockPrisma.service.delete.mockResolvedValue({});
    await expect(repo.deleteService('svc-1')).resolves.toBe(true);
    mockPrisma.service.delete.mockRejectedValue(new Error('fk'));
    await expect(repo.deleteService('svc-1')).resolves.toBe(false);
  });

  it('getServices - applies filters, pagination, and mapping', async () => {
    mockPrisma.service.count.mockResolvedValue(1);
    mockPrisma.service.findMany.mockResolvedValue([
      { id: 'svc-1', service_categories: { name: 'Cat' }, providers: [] },
    ]);
    const result = await repo.getServices({ page: 1, limit: 10 } as any);
    expect(mockPrisma.service.count).toHaveBeenCalled();
    expect(result.pagination.total).toBe(1);
    expect(result.services[0].id).toBe('svc-1');
  });

  it('searchServices - builds where and returns detail list', async () => {
    mockPrisma.service.findMany.mockResolvedValue([
      { id: 'svc-1', features: [], service_categories: {}, formFields: [], providers: [] },
    ]);
    mockPrisma.service_categories.findMany.mockResolvedValue([{ id: 'cat-1', name: 'Cat', _count: { services: 1 } }]);
    mockPrisma.service.aggregate = jest.fn().mockResolvedValue({ _min: { basePrice: 1 }, _max: { basePrice: 100 } });
    const result = await repo.searchServices({ q: 'a' } as any);
    expect(mockPrisma.service.findMany).toHaveBeenCalled();
    expect(result.services[0].id).toBe('svc-1');
  });

  it('getServicesByCategory - filters by categoryId', async () => {
    mockPrisma.service.findMany.mockResolvedValue([
      { id: 'svc-1', service_categories: { name: 'Cat' }, providers: [] },
    ]);
    const result = await repo.getServicesByCategory('cat-1');
    expect(mockPrisma.service.findMany).toHaveBeenCalledWith({
      where: { categoryId: 'cat-1', isActive: true },
      include: expect.any(Object),
      orderBy: { sortOrder: 'asc' },
    });
    expect(result[0].id).toBe('svc-1');
  });

  it('serviceExists - counts by id', async () => {
    mockPrisma.service.count.mockResolvedValue(1);
    await expect(repo.serviceExists('svc-1')).resolves.toBe(true);
  });
});
