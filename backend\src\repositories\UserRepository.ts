import { PrismaClient, User, UserRole } from '@prisma/client';
import { 
  CreateUserRequest, 
  UpdateUserRequest, 
  UserListQuery, 
  UserListResponse, 
  UserSummary, 
  UserDetail, 
  UserStats,
  CreateUserData,
  UpdateUserData
} from '../types/user';

// Repository interface for dependency inversion
export interface IUserRepository {
  // Basic CRUD operations
  findUserById(id: string): Promise<User | null>;
  findUserByEmail(email: string): Promise<User | null>;
  createUser(userData: CreateUserData): Promise<User>;
  updateUser(id: string, data: UpdateUserData): Promise<User>;
  deleteUser(id: string): Promise<boolean>;
  
  // List and search operations
  getUsers(query: UserListQuery): Promise<UserListResponse>;
  searchUsers(searchTerm: string, limit?: number): Promise<UserSummary[]>;
  
  // Statistics and analytics
  getUserStats(): Promise<UserStats>;
  getUsersByRole(role: UserRole): Promise<UserSummary[]>;
  getActiveUsers(): Promise<UserSummary[]>;
  getVerifiedUsers(): Promise<UserSummary[]>;
  
  // Bulk operations
  bulkUpdateUsers(userIds: string[], updates: Partial<UpdateUserRequest>): Promise<number>;
  bulkDeleteUsers(userIds: string[]): Promise<number>;
  
  // Utility operations
  userExists(email: string): Promise<boolean>;
  countUsers(): Promise<number>;
  countUsersByRole(role: UserRole): Promise<number>;
}

// Prisma implementation of the repository
export class UserRepository implements IUserRepository {
  constructor(private prisma: PrismaClient) {}

  async findUserById(id: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { id }
    });
  }

  async findUserByEmail(email: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    });
  }

  async createUser(userData: CreateUserData): Promise<User> {
    return this.prisma.user.create({
      data: {
        email: userData.email.toLowerCase(),
        password: userData.password, // Note: Should be hashed before calling this
        firstName: userData.firstName,
        lastName: userData.lastName,
        phone: userData.phone,
        role: userData.role,
        isActive: userData.isActive,
        isVerified: userData.isVerified,
      }
    });
  }

  async updateUser(id: string, data: UpdateUserData): Promise<User> {
    const updateData: Record<string, unknown> = { ...data };
    
    // Handle email case sensitivity
    if (data.email) {
      updateData.email = data.email.toLowerCase();
    }

    return this.prisma.user.update({
      where: { id },
      data: updateData
    });
  }

  async deleteUser(id: string): Promise<boolean> {
    try {
      await this.prisma.user.delete({
        where: { id }
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  async getUsers(query: UserListQuery): Promise<UserListResponse> {
    const {
      page = 1,
      limit = 20,
      search,
      role,
      isActive,
      isVerified,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = query;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } }
      ];
    }
    
    if (role) {
      where.role = role;
    }
    
    if (typeof isActive === 'boolean') {
      where.isActive = isActive;
    }
    
    if (typeof isVerified === 'boolean') {
      where.isVerified = isVerified;
    }

    // Get total count
    const total = await this.prisma.user.count({ where });

    // Get users with pagination
    const users = await this.prisma.user.findMany({
      where,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        isVerified: true,
        avatar: true,
        phone: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit
    });

    const totalPages = Math.ceil(total / limit);

    return {
      users: users as UserSummary[],
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  async searchUsers(searchTerm: string, limit: number = 10): Promise<UserSummary[]> {
    return this.prisma.user.findMany({
      where: {
        OR: [
          { firstName: { contains: searchTerm, mode: 'insensitive' } },
          { lastName: { contains: searchTerm, mode: 'insensitive' } },
          { email: { contains: searchTerm, mode: 'insensitive' } }
        ]
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        isVerified: true,
        avatar: true,
        phone: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
      },
      take: limit
    }) as Promise<UserSummary[]>;
  }

  async getUserStats(): Promise<UserStats> {
    const [
      totalUsers,
      activeUsers,
      verifiedUsers,
      usersByRole,
      recentRegistrations,
      newUsersThisMonth
    ] = await Promise.all([
      this.prisma.user.count(),
      this.prisma.user.count({ where: { isActive: true } }),
      this.prisma.user.count({ where: { isVerified: true } }),
      this.prisma.user.groupBy({
        by: ['role'],
        _count: { role: true }
      }),
      this.prisma.user.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        }
      }),
      this.prisma.user.count({
        where: {
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1) // This month
          }
        }
      })
    ]);

    // Convert role counts to Record
    const roleCounts: Record<UserRole, number> = {
      [UserRole.CUSTOMER]: 0,
      [UserRole.PROVIDER]: 0,
      [UserRole.ADMIN]: 0
    };

    usersByRole.forEach(({ role, _count }) => {
      roleCounts[role] = _count.role;
    });

    return {
      total: totalUsers,
      totalUsers,
      active: activeUsers,
      activeUsers,
      inactive: totalUsers - activeUsers,
      verified: verifiedUsers,
      verifiedUsers,
      unverified: totalUsers - verifiedUsers,
      byRole: roleCounts,
      usersByRole: roleCounts,
      recentRegistrations,
      averageRegistrationsPerDay: Math.round(recentRegistrations / 30),
      newUsersThisMonth
    };
  }

  async getUsersByRole(role: UserRole): Promise<UserSummary[]> {
    return this.prisma.user.findMany({
      where: { role },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        isVerified: true,
        avatar: true,
        phone: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
      }
    }) as Promise<UserSummary[]>;
  }

  async getActiveUsers(): Promise<UserSummary[]> {
    return this.prisma.user.findMany({
      where: { isActive: true },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        isVerified: true,
        avatar: true,
        phone: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
      }
    }) as Promise<UserSummary[]>;
  }

  async getVerifiedUsers(): Promise<UserSummary[]> {
    return this.prisma.user.findMany({
      where: { isVerified: true },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        isVerified: true,
        avatar: true,
        phone: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
      }
    }) as Promise<UserSummary[]>;
  }

  async bulkUpdateUsers(userIds: string[], updates: Partial<UpdateUserRequest>): Promise<number> {
    const result = await this.prisma.user.updateMany({
      where: { id: { in: userIds } },
      data: updates
    });
    return result.count;
  }

  async bulkDeleteUsers(userIds: string[]): Promise<number> {
    const result = await this.prisma.user.deleteMany({
      where: { id: { in: userIds } }
    });
    return result.count;
  }

  async userExists(email: string): Promise<boolean> {
    const count = await this.prisma.user.count({
      where: { email: email.toLowerCase() }
    });
    return count > 0;
  }

  async countUsers(): Promise<number> {
    return this.prisma.user.count();
  }

  async countUsersByRole(role: UserRole): Promise<number> {
    return this.prisma.user.count({
      where: { role }
    });
  }
} 