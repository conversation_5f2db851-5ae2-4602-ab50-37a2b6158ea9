2025-08-04 02:45:41 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:45:41 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:45:41 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:45:56 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:45:56 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:45:56 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:46:07 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:46:07 [INFO]: [AuthController] Auth<PERSON>ontroller initialized
{
  "service": "printco-backend"
}
2025-08-04 02:46:07 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:46:42 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:46:42 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:46:42 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:46:42 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:46:42 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:46:42 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:46:47 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:46:47 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:46:47 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:46:47 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:46:47 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:46:47 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:47:28 [INFO]: [Security] Rate limit attempt
{
  "service": "printco-backend",
  "ip": "::1",
  "path": "/register",
  "attempts": 2,
  "maxAttempts": 5,
  "userAgent": "axios/1.11.0",
  "duration": 0
}
2025-08-04 02:47:28 [INFO]: [Security] Rate limit attempt
{
  "service": "printco-backend",
  "ip": "::1",
  "path": "/register",
  "attempts": 3,
  "maxAttempts": 5,
  "userAgent": "axios/1.11.0",
  "duration": 0
}
2025-08-04 02:47:28 [INFO]: [Security] Rate limit attempt
{
  "service": "printco-backend",
  "ip": "::1",
  "path": "/register",
  "attempts": 4,
  "maxAttempts": 5,
  "userAgent": "axios/1.11.0",
  "duration": 0
}
2025-08-04 02:47:33 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:47:33 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:47:33 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:47:34 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:47:34 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:47:34 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:49:52 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:49:52 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:49:52 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:49:52 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:49:52 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:49:52 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:50:00 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:50:00 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:50:00 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:50:00 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:50:00 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:50:00 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:53:05 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:05 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:05 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:53:06 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:06 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:06 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:53:13 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:13 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:13 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:53:14 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:14 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:14 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:53:21 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:21 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:21 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:53:22 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:22 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:22 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:53:27 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:27 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:27 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:53:28 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:28 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:28 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:53:46 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:46 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:46 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:53:46 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:46 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:46 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:53:59 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:59 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:59 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
2025-08-04 02:53:59 [INFO]: [Authentication] AuthService initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:59 [INFO]: [AuthController] AuthController initialized
{
  "service": "printco-backend"
}
2025-08-04 02:53:59 [INFO]: [Authentication] Setting up authentication routes
{
  "service": "printco-backend"
}
