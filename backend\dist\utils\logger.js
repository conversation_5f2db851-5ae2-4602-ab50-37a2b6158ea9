"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearLoggerCache = exports.getLoggerConfig = exports.httpLogger = exports.fileLogger = exports.emailLogger = exports.securityLogger = exports.apiLogger = exports.dbLogger = exports.authLogger = exports.morganStream = void 0;
exports.createLogger = createLogger;
const winston_1 = __importDefault(require("winston"));
const winston_daily_rotate_file_1 = __importDefault(require("winston-daily-rotate-file"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const config_1 = require("../config");
const logsDir = path_1.default.join(process.cwd(), 'logs');
if (!fs_1.default.existsSync(logsDir)) {
    fs_1.default.mkdirSync(logsDir, { recursive: true });
}
const logLevels = {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4,
    verbose: 5,
};
const logColors = {
    error: 'bold red',
    warn: 'bold yellow',
    info: 'bold blue',
    http: 'bold magenta',
    debug: 'bold cyan',
    verbose: 'gray',
};
const serviceColors = {
    'Authentication': 'bgGreen bold white',
    'Database': 'bgBlue bold white',
    'API': 'bgMagenta bold white',
    'Security': 'bgRed bold white',
    'HTTP': 'bgCyan bold black',
    'Performance': 'bgYellow bold black',
    'Email': 'bgGreen bold black',
    'FileUpload': 'bgBlue bold black',
    'App': 'bgGray bold white',
    'Server': 'bgBlack bold white',
    'Routes': 'bgMagenta bold white',
    'Redis': 'bgRed bold white',
    'Event': 'bgBlue bold white',
    'AuthEvent': 'bgGreen bold white',
    'Error': 'bgRed bold white',
    'ErrorHandler': 'bgRed bold white',
};
const eventTypeColors = {
    'AUTH_EVENT': 'bold green',
    'SECURITY_EVENT': 'bold red',
    'PERFORMANCE': 'bold yellow',
    'DATABASE': 'bold blue',
    'ERROR': 'bold red',
};
winston_1.default.addColors({ ...logColors, ...serviceColors, ...eventTypeColors });
const supportsColor = () => {
    if (process.env.FORCE_COLOR)
        return true;
    if (process.env.NO_COLOR || process.env.NODE_DISABLE_COLORS)
        return false;
    if (process.stdout && !process.stdout.isTTY)
        return false;
    const { TERM, CI } = process.env;
    if (CI) {
        const ciEnvs = ['TRAVIS', 'CIRCLECI', 'APPVEYOR', 'GITLAB_CI', 'GITHUB_ACTIONS', 'BUILDKITE'];
        return ciEnvs.some(ci => ci in process.env);
    }
    if (TERM === 'dumb')
        return false;
    const colorTerms = ['xterm', 'xterm-256color', 'screen', 'screen-256color', 'linux', 'cygwin'];
    if (TERM && colorTerms.some(term => TERM.includes(term)))
        return true;
    if (process.platform === 'win32') {
        return true;
    }
    return true;
};
const BOX_CHARS = {
    horizontal: '─',
    vertical: '│',
    corner: '┌',
    junction: '├',
    end: '└',
};
const developmentFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'HH:mm:ss.SSS' }), winston_1.default.format.printf((info) => {
    const { timestamp, level, message, service, type, stack, ...meta } = info;
    const useColors = supportsColor();
    const formattedTime = useColors
        ? `\x1b[90m${timestamp}\x1b[0m`
        : timestamp;
    const levelText = level.toUpperCase().padEnd(7);
    const formattedLevel = useColors
        ? winston_1.default.format.colorize().colorize(level, levelText)
        : levelText;
    const serviceKey = service;
    let formattedService = '';
    if (useColors && serviceColors[serviceKey]) {
        formattedService = winston_1.default.format.colorize().colorize(serviceKey, `[${serviceKey}]`);
    }
    else {
        formattedService = useColors
            ? `\x1b[37m[${serviceKey}]\x1b[0m`
            : `[${serviceKey}]`;
    }
    let formattedMessage = message;
    if (useColors && type && eventTypeColors[type]) {
        formattedMessage = winston_1.default.format.colorize().colorize(type, message);
    }
    let metaStr = '';
    if (Object.keys(meta).length > 0) {
        const cleanMeta = { ...meta };
        delete cleanMeta.service;
        delete cleanMeta.timestamp;
        if (Object.keys(cleanMeta).length > 0) {
            const metaPairs = Object.entries(cleanMeta)
                .map(([key, value]) => {
                if (useColors) {
                    if (typeof value === 'object') {
                        return `\x1b[36m${key}\x1b[0m=${JSON.stringify(value)}`;
                    }
                    return `\x1b[36m${key}\x1b[0m=\x1b[33m${value}\x1b[0m`;
                }
                else {
                    return `${key}=${typeof value === 'object' ? JSON.stringify(value) : value}`;
                }
            })
                .join(' ');
            metaStr = useColors
                ? ` \x1b[90m│\x1b[0m ${metaPairs}`
                : ` | ${metaPairs}`;
        }
    }
    let stackStr = '';
    if (stack) {
        stackStr = useColors
            ? `\n\x1b[90m${stack}\x1b[0m`
            : `\n${stack}`;
    }
    return `${formattedTime} ${formattedLevel} ${formattedService} ${formattedMessage}${metaStr}${stackStr}`;
}));
const productionFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json({
    replacer: (key, value) => {
        if (key === '') {
            const logEntry = value;
            const { timestamp, level, service, message, type, ...rest } = logEntry;
            return { timestamp, level, service, type, message, ...rest };
        }
        return value;
    }
}));
const fileFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.printf((info) => {
    const { level, message, timestamp, service, type, stack, ...meta } = info;
    const cleanMeta = { ...meta };
    delete cleanMeta.service;
    delete cleanMeta.timestamp;
    const formattedLevel = level.toUpperCase().padEnd(7);
    const formattedService = service.padEnd(12);
    const eventIndicator = type ? `[${type}] ` : '';
    const metaString = Object.keys(cleanMeta).length > 0
        ? ` | ${Object.entries(cleanMeta).map(([k, v]) => `${k}=${typeof v === 'object' ? JSON.stringify(v) : v}`).join(' ')}`
        : '';
    const stackString = stack ? `\n${stack}` : '';
    return `${timestamp} [${formattedLevel}] [${formattedService}] ${eventIndicator}${message}${metaString}${stackString}`;
}));
const loggerCache = new Map();
class AppLogger {
    logger;
    service;
    constructor(logger, service) {
        this.logger = logger;
        this.service = service;
    }
    error(message, error, meta) {
        if (error instanceof Error) {
            this.logger.error(message, {
                service: this.service,
                error: error.message,
                stack: error.stack,
                ...meta,
            });
        }
        else if (error) {
            this.logger.error(message, { service: this.service, error, ...meta });
        }
        else {
            this.logger.error(message, { service: this.service, ...meta });
        }
    }
    warn(message, meta) {
        this.logger.warn(message, { service: this.service, ...meta });
    }
    info(message, meta) {
        this.logger.info(message, { service: this.service, ...meta });
    }
    http(message, meta) {
        this.logger.http(message, { service: this.service, ...meta });
    }
    debug(message, meta) {
        this.logger.debug(message, { service: this.service, ...meta });
    }
    verbose(message, meta) {
        this.logger.verbose(message, { service: this.service, ...meta });
    }
    silly(message, meta) {
        this.logger.silly(message, { service: this.service, ...meta });
    }
    security(event, details) {
        this.logger.info(`SECURITY: ${event}`, {
            service: this.service,
            type: 'SECURITY_EVENT',
            event,
            ...details,
            timestamp: new Date().toISOString(),
        });
    }
    auth(event, userId, details) {
        this.logger.info(`AUTH: ${event}`, {
            service: this.service,
            type: 'AUTH_EVENT',
            event,
            userId,
            ...details,
            timestamp: new Date().toISOString(),
        });
    }
    performance(operation, duration, meta) {
        this.logger.info(`PERFORMANCE: ${operation}`, {
            service: this.service,
            type: 'PERFORMANCE',
            operation,
            duration,
            ...meta,
            timestamp: new Date().toISOString(),
        });
    }
    database(operation, table, meta) {
        this.logger.debug(`DATABASE: ${operation}`, {
            service: this.service,
            type: 'DATABASE',
            operation,
            table,
            ...meta,
            timestamp: new Date().toISOString(),
        });
    }
}
function createLogger(service) {
    if (loggerCache.has(service)) {
        return loggerCache.get(service);
    }
    const loggingConfig = config_1.config.getLoggingConfig();
    const isProduction = process.env.NODE_ENV === 'production';
    const isDevelopment = process.env.NODE_ENV === 'development';
    const transports = [];
    if (loggingConfig.enableConsole) {
        const consoleFormat = isProduction ? productionFormat : developmentFormat;
        transports.push(new winston_1.default.transports.Console({
            level: loggingConfig.level,
            format: consoleFormat,
        }));
    }
    if (loggingConfig.enableFile || isProduction) {
        transports.push(new winston_daily_rotate_file_1.default({
            filename: path_1.default.join(logsDir, `${service}-%DATE%.log`),
            datePattern: 'YYYY-MM-DD',
            maxSize: '20m',
            maxFiles: '14d',
            format: fileFormat,
            level: 'info',
        }));
        transports.push(new winston_daily_rotate_file_1.default({
            filename: path_1.default.join(logsDir, `${service}-error-%DATE%.log`),
            datePattern: 'YYYY-MM-DD',
            maxSize: '20m',
            maxFiles: '30d',
            format: fileFormat,
            level: 'error',
        }));
        if (['HTTP', 'API', 'App'].includes(service)) {
            transports.push(new winston_daily_rotate_file_1.default({
                filename: path_1.default.join(logsDir, 'http-%DATE%.log'),
                datePattern: 'YYYY-MM-DD',
                maxSize: '20m',
                maxFiles: '7d',
                format: fileFormat,
                level: 'http',
            }));
        }
    }
    const logger = winston_1.default.createLogger({
        level: loggingConfig.level,
        levels: logLevels,
        format: fileFormat,
        defaultMeta: { service },
        transports,
        exceptionHandlers: isProduction
            ? [
                new winston_daily_rotate_file_1.default({
                    filename: path_1.default.join(logsDir, 'exceptions-%DATE%.log'),
                    datePattern: 'YYYY-MM-DD',
                    maxSize: '20m',
                    maxFiles: '30d',
                    format: fileFormat,
                }),
            ]
            : [],
        rejectionHandlers: isProduction
            ? [
                new winston_daily_rotate_file_1.default({
                    filename: path_1.default.join(logsDir, 'rejections-%DATE%.log'),
                    datePattern: 'YYYY-MM-DD',
                    maxSize: '20m',
                    maxFiles: '30d',
                    format: fileFormat,
                }),
            ]
            : [],
        exitOnError: false,
        silent: false,
    });
    const appLogger = new AppLogger(logger, service);
    loggerCache.set(service, appLogger);
    return appLogger;
}
exports.morganStream = {
    write: (message) => {
        const httpLogger = createLogger('HTTP');
        httpLogger.http(message.trim());
    },
};
exports.authLogger = createLogger('Authentication');
exports.dbLogger = createLogger('Database');
exports.apiLogger = createLogger('API');
exports.securityLogger = createLogger('Security');
exports.emailLogger = createLogger('Email');
exports.fileLogger = createLogger('FileUpload');
exports.httpLogger = createLogger('HTTP');
const getLoggerConfig = () => config_1.config.getLoggingConfig();
exports.getLoggerConfig = getLoggerConfig;
const clearLoggerCache = () => {
    loggerCache.clear();
};
exports.clearLoggerCache = clearLoggerCache;
exports.default = {
    createLogger,
    authLogger: exports.authLogger,
    dbLogger: exports.dbLogger,
    apiLogger: exports.apiLogger,
    securityLogger: exports.securityLogger,
    emailLogger: exports.emailLogger,
    fileLogger: exports.fileLogger,
    httpLogger: exports.httpLogger,
    morganStream: exports.morganStream,
    getLoggerConfig: exports.getLoggerConfig,
    clearLoggerCache: exports.clearLoggerCache,
};
//# sourceMappingURL=logger.js.map