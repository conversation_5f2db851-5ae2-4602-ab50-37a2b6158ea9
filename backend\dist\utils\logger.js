"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLoggerConfig = exports.fileLogger = exports.emailLogger = exports.securityLogger = exports.apiLogger = exports.dbLogger = exports.authLogger = void 0;
exports.createLogger = createLogger;
const winston_1 = __importDefault(require("winston"));
const path_1 = __importDefault(require("path"));
const config_1 = require("../config");
const developmentFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'HH:mm:ss' }), winston_1.default.format.colorize(), winston_1.default.format.printf(({ timestamp, level, message, service, ...meta }) => {
    const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
    return `${timestamp} ${level}: [${service}] ${message}${metaStr}`;
}));
const productionFormat = winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json());
function createLogger(service) {
    const loggingConfig = config_1.config.getLoggingConfig();
    const logLevel = loggingConfig.level;
    const transports = [];
    if (loggingConfig.enableConsole) {
        const consoleFormat = loggingConfig.format === 'json' ? productionFormat : developmentFormat;
        transports.push(new winston_1.default.transports.Console({
            level: logLevel,
            format: consoleFormat,
        }));
    }
    if (loggingConfig.enableFile && loggingConfig.file) {
        const logDir = path_1.default.dirname(loggingConfig.file);
        const logFile = path_1.default.basename(loggingConfig.file);
        try {
            const fs = require('fs');
            if (!fs.existsSync(logDir)) {
                fs.mkdirSync(logDir, { recursive: true });
            }
        }
        catch (error) {
            console.error('Failed to create log directory:', error);
        }
        transports.push(new winston_1.default.transports.File({
            filename: path_1.default.join(logDir, `${service}-${logFile}`),
            level: logLevel,
            format: productionFormat,
            maxsize: loggingConfig.maxFileSize,
            maxFiles: loggingConfig.maxFiles,
            tailable: true,
        }));
        transports.push(new winston_1.default.transports.File({
            filename: path_1.default.join(logDir, `${service}-error-${logFile}`),
            level: 'error',
            format: productionFormat,
            maxsize: loggingConfig.maxFileSize,
            maxFiles: loggingConfig.maxFiles,
            tailable: true,
        }));
    }
    const logger = winston_1.default.createLogger({
        level: logLevel,
        format: productionFormat,
        transports,
        exceptionHandlers: loggingConfig.enableFile && loggingConfig.file
            ? [
                new winston_1.default.transports.File({
                    filename: path_1.default.join(path_1.default.dirname(loggingConfig.file), 'exceptions.log'),
                    format: productionFormat,
                }),
            ]
            : [],
        rejectionHandlers: loggingConfig.enableFile && loggingConfig.file
            ? [
                new winston_1.default.transports.File({
                    filename: path_1.default.join(path_1.default.dirname(loggingConfig.file), 'rejections.log'),
                    format: productionFormat,
                }),
            ]
            : [],
    });
    const loggerWithService = {
        error: (message, meta) => logger.error(message, { service, ...meta }),
        warn: (message, meta) => logger.warn(message, { service, ...meta }),
        info: (message, meta) => logger.info(message, { service, ...meta }),
        debug: (message, meta) => logger.debug(message, { service, ...meta }),
        verbose: (message, meta) => logger.verbose(message, { service, ...meta }),
        silly: (message, meta) => logger.silly(message, { service, ...meta }),
    };
    return loggerWithService;
}
exports.authLogger = createLogger('Authentication');
exports.dbLogger = createLogger('Database');
exports.apiLogger = createLogger('API');
exports.securityLogger = createLogger('Security');
exports.emailLogger = createLogger('Email');
exports.fileLogger = createLogger('FileUpload');
const getLoggerConfig = () => config_1.config.getLoggingConfig();
exports.getLoggerConfig = getLoggerConfig;
//# sourceMappingURL=logger.js.map