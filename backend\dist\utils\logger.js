"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.winstonLogger = exports.createLogger = exports.morganStream = exports.securityLogger = exports.httpLogger = exports.dbLogger = exports.authLogger = exports.defaultLogger = exports.Logger = void 0;
const winston_1 = __importDefault(require("winston"));
const winston_daily_rotate_file_1 = __importDefault(require("winston-daily-rotate-file"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const logsDir = path_1.default.join(process.cwd(), 'logs');
if (!fs_1.default.existsSync(logsDir)) {
    fs_1.default.mkdirSync(logsDir, { recursive: true });
}
const logLevels = {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4,
};
const logColors = {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    http: 'magenta',
    debug: 'white',
};
winston_1.default.addColors(logColors);
const logFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.printf(({ level, message, timestamp, stack, ...meta }) => {
    const metaString = Object.keys(meta).length > 0 ? `\n${JSON.stringify(meta, null, 2)}` : '';
    const stackString = stack ? `\n${stack}` : '';
    return `${timestamp} [${level.toUpperCase()}]: ${message}${metaString}${stackString}`;
}));
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.colorize({ all: true }), winston_1.default.format.timestamp({ format: 'HH:mm:ss' }), winston_1.default.format.printf(({ level, message, timestamp, stack, ...meta }) => {
    const metaString = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
    const stackString = stack ? `\n${stack}` : '';
    return `${timestamp} ${level}: ${message}${metaString}${stackString}`;
}));
const fileRotateTransport = new winston_daily_rotate_file_1.default({
    filename: path_1.default.join(logsDir, 'application-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    maxSize: '20m',
    maxFiles: '30d',
    format: logFormat,
    level: 'info'
});
const errorFileRotateTransport = new winston_daily_rotate_file_1.default({
    filename: path_1.default.join(logsDir, 'error-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    maxSize: '20m',
    maxFiles: '30d',
    format: logFormat,
    level: 'error'
});
const httpFileRotateTransport = new winston_daily_rotate_file_1.default({
    filename: path_1.default.join(logsDir, 'http-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    maxSize: '20m',
    maxFiles: '14d',
    format: logFormat,
    level: 'http'
});
const logger = winston_1.default.createLogger({
    level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'info' : 'debug'),
    levels: logLevels,
    format: logFormat,
    defaultMeta: { service: 'printco-backend' },
    transports: [
        fileRotateTransport,
        errorFileRotateTransport,
        httpFileRotateTransport,
    ],
    exceptionHandlers: [
        new winston_daily_rotate_file_1.default({
            filename: path_1.default.join(logsDir, 'exceptions-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            maxSize: '20m',
            maxFiles: '30d',
            format: logFormat,
        })
    ],
    rejectionHandlers: [
        new winston_daily_rotate_file_1.default({
            filename: path_1.default.join(logsDir, 'rejections-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            maxSize: '20m',
            maxFiles: '30d',
            format: logFormat,
        })
    ]
});
exports.winstonLogger = logger;
if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston_1.default.transports.Console({
        format: consoleFormat,
        level: 'debug'
    }));
}
if (process.env.NODE_ENV === 'production') {
    logger.add(new winston_1.default.transports.Console({
        format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json()),
        level: 'error'
    }));
}
class Logger {
    context;
    constructor(context = 'Application') {
        this.context = context;
    }
    formatMessage(message) {
        return `[${this.context}] ${message}`;
    }
    error(message, error, meta) {
        if (error instanceof Error) {
            logger.error(this.formatMessage(message), { error: error.message, stack: error.stack, ...meta });
        }
        else if (error) {
            logger.error(this.formatMessage(message), { error, ...meta });
        }
        else {
            logger.error(this.formatMessage(message), meta);
        }
    }
    warn(message, meta) {
        logger.warn(this.formatMessage(message), meta);
    }
    info(message, meta) {
        logger.info(this.formatMessage(message), meta);
    }
    http(message, meta) {
        logger.http(this.formatMessage(message), meta);
    }
    debug(message, meta) {
        logger.debug(this.formatMessage(message), meta);
    }
    security(event, details) {
        logger.info(this.formatMessage(`SECURITY: ${event}`), {
            type: 'SECURITY_EVENT',
            event,
            ...details,
            timestamp: new Date().toISOString()
        });
    }
    auth(event, userId, details) {
        logger.info(this.formatMessage(`AUTH: ${event}`), {
            type: 'AUTH_EVENT',
            event,
            userId,
            ...details,
            timestamp: new Date().toISOString()
        });
    }
    performance(operation, duration, meta) {
        logger.info(this.formatMessage(`PERFORMANCE: ${operation}`), {
            type: 'PERFORMANCE',
            operation,
            duration,
            ...meta,
            timestamp: new Date().toISOString()
        });
    }
    database(operation, table, meta) {
        logger.debug(this.formatMessage(`DATABASE: ${operation}`), {
            type: 'DATABASE',
            operation,
            table,
            ...meta,
            timestamp: new Date().toISOString()
        });
    }
}
exports.Logger = Logger;
exports.defaultLogger = new Logger('Application');
exports.authLogger = new Logger('Authentication');
exports.dbLogger = new Logger('Database');
exports.httpLogger = new Logger('HTTP');
exports.securityLogger = new Logger('Security');
exports.morganStream = {
    write: (message) => {
        exports.httpLogger.http(message.trim());
    }
};
const createLogger = (context) => {
    return new Logger(context);
};
exports.createLogger = createLogger;
exports.default = exports.defaultLogger;
//# sourceMappingURL=logger.js.map