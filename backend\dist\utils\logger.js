"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearLoggerCache = exports.getLoggerConfig = exports.httpLogger = exports.fileLogger = exports.emailLogger = exports.securityLogger = exports.apiLogger = exports.dbLogger = exports.authLogger = exports.morganStream = void 0;
exports.createLogger = createLogger;
const winston_1 = __importDefault(require("winston"));
const winston_daily_rotate_file_1 = __importDefault(require("winston-daily-rotate-file"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const config_1 = require("../config");
const logsDir = path_1.default.join(process.cwd(), 'logs');
if (!fs_1.default.existsSync(logsDir)) {
    fs_1.default.mkdirSync(logsDir, { recursive: true });
}
const logLevels = {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4,
};
const logColors = {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    http: 'magenta',
    debug: 'white',
};
winston_1.default.addColors(logColors);
const developmentFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'HH:mm:ss' }), winston_1.default.format.colorize({ all: true }), winston_1.default.format.printf(({ timestamp, level, message, service, ...meta }) => {
    const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
    return `${timestamp} ${level}: [${service}] ${message}${metaStr}`;
}));
const productionFormat = winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json());
const fileFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.printf(({ level, message, timestamp, service, stack, ...meta }) => {
    const metaString = Object.keys(meta).length > 0 ? `\n${JSON.stringify(meta, null, 2)}` : '';
    const stackString = stack ? `\n${stack}` : '';
    return `${timestamp} [${level.toUpperCase()}]: [${service}] ${message}${metaString}${stackString}`;
}));
const loggerCache = new Map();
class AppLogger {
    logger;
    service;
    constructor(logger, service) {
        this.logger = logger;
        this.service = service;
    }
    error(message, error, meta) {
        if (error instanceof Error) {
            this.logger.error(message, { service: this.service, error: error.message, stack: error.stack, ...meta });
        }
        else if (error) {
            this.logger.error(message, { service: this.service, error, ...meta });
        }
        else {
            this.logger.error(message, { service: this.service, ...meta });
        }
    }
    warn(message, meta) {
        this.logger.warn(message, { service: this.service, ...meta });
    }
    info(message, meta) {
        this.logger.info(message, { service: this.service, ...meta });
    }
    http(message, meta) {
        this.logger.http(message, { service: this.service, ...meta });
    }
    debug(message, meta) {
        this.logger.debug(message, { service: this.service, ...meta });
    }
    verbose(message, meta) {
        this.logger.verbose(message, { service: this.service, ...meta });
    }
    silly(message, meta) {
        this.logger.silly(message, { service: this.service, ...meta });
    }
    security(event, details) {
        this.logger.info(`SECURITY: ${event}`, {
            service: this.service,
            type: 'SECURITY_EVENT',
            event,
            ...details,
            timestamp: new Date().toISOString()
        });
    }
    auth(event, userId, details) {
        this.logger.info(`AUTH: ${event}`, {
            service: this.service,
            type: 'AUTH_EVENT',
            event,
            userId,
            ...details,
            timestamp: new Date().toISOString()
        });
    }
    performance(operation, duration, meta) {
        this.logger.info(`PERFORMANCE: ${operation}`, {
            service: this.service,
            type: 'PERFORMANCE',
            operation,
            duration,
            ...meta,
            timestamp: new Date().toISOString()
        });
    }
    database(operation, table, meta) {
        this.logger.debug(`DATABASE: ${operation}`, {
            service: this.service,
            type: 'DATABASE',
            operation,
            table,
            ...meta,
            timestamp: new Date().toISOString()
        });
    }
}
function createLogger(service) {
    if (loggerCache.has(service)) {
        return loggerCache.get(service);
    }
    const loggingConfig = config_1.config.getLoggingConfig();
    const isProduction = process.env.NODE_ENV === 'production';
    const isDevelopment = process.env.NODE_ENV === 'development';
    const transports = [];
    if (loggingConfig.enableConsole) {
        const consoleFormat = isProduction ? productionFormat : developmentFormat;
        transports.push(new winston_1.default.transports.Console({
            level: loggingConfig.level,
            format: consoleFormat,
        }));
    }
    if (loggingConfig.enableFile || isProduction) {
        transports.push(new winston_daily_rotate_file_1.default({
            filename: path_1.default.join(logsDir, `${service}-%DATE%.log`),
            datePattern: 'YYYY-MM-DD',
            maxSize: '20m',
            maxFiles: '14d',
            format: fileFormat,
            level: 'info'
        }));
        transports.push(new winston_daily_rotate_file_1.default({
            filename: path_1.default.join(logsDir, `${service}-error-%DATE%.log`),
            datePattern: 'YYYY-MM-DD',
            maxSize: '20m',
            maxFiles: '30d',
            format: fileFormat,
            level: 'error'
        }));
        if (['HTTP', 'API', 'App'].includes(service)) {
            transports.push(new winston_daily_rotate_file_1.default({
                filename: path_1.default.join(logsDir, 'http-%DATE%.log'),
                datePattern: 'YYYY-MM-DD',
                maxSize: '20m',
                maxFiles: '7d',
                format: fileFormat,
                level: 'http'
            }));
        }
    }
    const logger = winston_1.default.createLogger({
        level: loggingConfig.level,
        levels: logLevels,
        format: fileFormat,
        defaultMeta: { service },
        transports,
        exceptionHandlers: isProduction ? [
            new winston_daily_rotate_file_1.default({
                filename: path_1.default.join(logsDir, 'exceptions-%DATE%.log'),
                datePattern: 'YYYY-MM-DD',
                maxSize: '20m',
                maxFiles: '30d',
                format: fileFormat,
            })
        ] : [],
        rejectionHandlers: isProduction ? [
            new winston_daily_rotate_file_1.default({
                filename: path_1.default.join(logsDir, 'rejections-%DATE%.log'),
                datePattern: 'YYYY-MM-DD',
                maxSize: '20m',
                maxFiles: '30d',
                format: fileFormat,
            })
        ] : [],
        exitOnError: false,
        silent: false,
    });
    const appLogger = new AppLogger(logger, service);
    loggerCache.set(service, appLogger);
    return appLogger;
}
exports.morganStream = {
    write: (message) => {
        const httpLogger = createLogger('HTTP');
        httpLogger.http(message.trim());
    }
};
exports.authLogger = createLogger('Authentication');
exports.dbLogger = createLogger('Database');
exports.apiLogger = createLogger('API');
exports.securityLogger = createLogger('Security');
exports.emailLogger = createLogger('Email');
exports.fileLogger = createLogger('FileUpload');
exports.httpLogger = createLogger('HTTP');
const getLoggerConfig = () => config_1.config.getLoggingConfig();
exports.getLoggerConfig = getLoggerConfig;
const clearLoggerCache = () => {
    loggerCache.clear();
};
exports.clearLoggerCache = clearLoggerCache;
exports.default = {
    createLogger,
    authLogger: exports.authLogger,
    dbLogger: exports.dbLogger,
    apiLogger: exports.apiLogger,
    securityLogger: exports.securityLogger,
    emailLogger: exports.emailLogger,
    fileLogger: exports.fileLogger,
    httpLogger: exports.httpLogger,
    morganStream: exports.morganStream,
    getLoggerConfig: exports.getLoggerConfig,
    clearLoggerCache: exports.clearLoggerCache,
};
//# sourceMappingURL=logger.js.map