/**
 * Application Helper Utilities
 * Centralized utilities to eliminate DRY violations
 */

import { randomBytes } from 'crypto';

/**
 * Get application version from package.json or default
 */
export const getAppVersion = (): string => 
  process.env.npm_package_version || '1.0.0';

/**
 * Get current ISO timestamp
 */
export const getCurrentTimestamp = (): string => 
  new Date().toISOString();

/**
 * Create standard error response format
 */
export const createErrorResponse = (error: string, message: string, path: string, method: string) => ({
  error,
  message,
  path,
  method,
  timestamp: getCurrentTimestamp(),
});

/**
 * Generate a unique ID using crypto.randomBytes
 */
export const generateId = (): string => {
  return randomBytes(12).toString('base64url');
};