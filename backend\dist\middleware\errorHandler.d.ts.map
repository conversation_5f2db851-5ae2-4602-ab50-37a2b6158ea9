{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAM1D,qBAAa,QAAS,SAAQ,KAAK;IAC1B,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,OAAO,CAAC;gBAElB,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM;CAMhD;AAGD,qBAAa,mBAAoB,SAAQ,QAAQ;gBACnC,OAAO,GAAE,MAAgC;CAGtD;AAED,qBAAa,kBAAmB,SAAQ,QAAQ;gBAClC,OAAO,GAAE,MAAwB;CAG9C;AAED,qBAAa,eAAgB,SAAQ,QAAQ;IACpC,gBAAgB,EAAE,GAAG,EAAE,CAAC;gBAEnB,OAAO,GAAE,MAA4B,EAAE,gBAAgB,GAAE,GAAG,EAAO;CAIhF;AAED,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,GAAE,MAA6B;CAGnD;AAED,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,GAAE,MAAkC;CAGxD;AAED,qBAAa,oBAAqB,SAAQ,QAAQ;gBACpC,OAAO,GAAE,MAA4B;CAGlD;AAGD,eAAO,MAAM,YAAY,GACvB,OAAO,KAAK,EACZ,KAAK,OAAO,EACZ,KAAK,QAAQ,EACb,MAAM,YAAY,KACjB,IA6FF,CAAC;AAGF,eAAO,MAAM,YAAY,GACvB,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAK,OAAO,CAAC,GAAG,CAAC,MAE7D,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAGxD,CAAC;AAGF,eAAO,MAAM,eAAe,GAAI,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAG9E,CAAC;;;;;;;;;0BAhHO,KAAK,OACP,OAAO,OACP,QAAQ,QACP,YAAY,KACjB,IAAI;uBAiGD,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAK,OAAO,CAAC,GAAG,CAAC,MAE7D,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY;2BAMpB,OAAO,OAAO,QAAQ,QAAQ,YAAY;;AAK/E,wBAWE"}