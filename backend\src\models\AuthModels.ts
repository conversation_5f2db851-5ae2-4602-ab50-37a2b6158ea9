import { UserR<PERSON> } from '@prisma/client';
import { 
  RegisterRequest, 
  LoginRequest, 
  RefreshTokenRequest,
  ChangePasswordRequest,
  UpdateProfileRequest,
  AuthUser,
  TokenPair,
  ApiResponse
} from '../types/auth';

// Authentication domain models with business logic
export class AuthenticationModel {
  // User registration model
  static createRegistrationData(request: RegisterRequest): RegistrationData {
    return new RegistrationData(
      request.email,
      request.password,
      request.firstName,
      request.lastName,
      request.phone
    );
  }

  // User login model
  static createLoginData(request: LoginRequest): LoginData {
    return new LoginData(request.email, request.password);
  }

  // Profile update model
  static createProfileUpdateData(request: UpdateProfileRequest): ProfileUpdateData {
    return new ProfileUpdateData(
      request.firstName,
      request.lastName,
      request.phone,
      request.avatar
    );
  }

  // Password change model
  static createPasswordChangeData(request: ChangePasswordRequest): PasswordChangeData {
    return new PasswordChangeData(request.currentPassword, request.newPassword);
  }
}

// Registration data model with validation
export class RegistrationData {
  constructor(
    public readonly email: string,
    public readonly password: string,
    public readonly firstName: string,
    public readonly lastName: string,
    public readonly phone?: string
  ) {
    this.validate();
  }

  private validate(): void {
    if (!this.email || !this.isValidEmail(this.email)) {
      throw new Error('Valid email is required');
    }
    
    if (!this.password || !this.isValidPassword(this.password)) {
      throw new Error('Password must meet security requirements');
    }
    
    if (!this.firstName?.trim()) {
      throw new Error('First name is required');
    }
    
    if (!this.lastName?.trim()) {
      throw new Error('Last name is required');
    }
    
    if (this.phone && !this.isValidPhone(this.phone)) {
      throw new Error('Invalid phone number format');
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 255;
  }

  private isValidPassword(password: string): boolean {
    // Password must be 8-128 chars with uppercase, lowercase, number, and special char
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,128}$/;
    return passwordRegex.test(password);
  }

  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone) && phone.length <= 20;
  }

  // Get sanitized data for storage
  getStorageData() {
    return {
      email: this.email.toLowerCase().trim(),
      firstName: this.firstName.trim(),
      lastName: this.lastName.trim(),
      phone: this.phone?.trim() || null
    };
  }
}

// Login data model with validation
export class LoginData {
  constructor(
    public readonly email: string,
    public readonly password: string
  ) {
    this.validate();
  }

  private validate(): void {
    if (!this.email?.trim()) {
      throw new Error('Email is required');
    }
    
    if (!this.password) {
      throw new Error('Password is required');
    }
    
    if (this.password.length > 128) {
      throw new Error('Password is too long');
    }
  }

  // Get normalized email for lookup
  getNormalizedEmail(): string {
    return this.email.toLowerCase().trim();
  }
}

// Profile update data model
export class ProfileUpdateData {
  constructor(
    public readonly firstName?: string,
    public readonly lastName?: string,
    public readonly phone?: string,
    public readonly avatar?: string
  ) {
    this.validate();
  }

  private validate(): void {
    if (this.firstName !== undefined && (!this.firstName || !this.isValidName(this.firstName))) {
      throw new Error('Invalid first name');
    }
    
    if (this.lastName !== undefined && (!this.lastName || !this.isValidName(this.lastName))) {
      throw new Error('Invalid last name');
    }
    
    if (this.phone !== undefined && this.phone !== null && !this.isValidPhone(this.phone)) {
      throw new Error('Invalid phone number format');
    }
    
    if (this.avatar !== undefined && this.avatar !== null && !this.isValidUrl(this.avatar)) {
      throw new Error('Invalid avatar URL');
    }
  }

  private isValidName(name: string): boolean {
    const nameRegex = /^[a-zA-Z\s'-]+$/;
    return nameRegex.test(name.trim()) && name.trim().length >= 1 && name.length <= 150;
  }

  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone) && phone.length <= 20;
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return url.length <= 500;
    } catch {
      return false;
    }
  }

  // Check if any fields are provided for update
  hasUpdates(): boolean {
    return this.firstName !== undefined || 
           this.lastName !== undefined || 
           this.phone !== undefined || 
           this.avatar !== undefined;
  }

  // Get sanitized data for storage
  getStorageData() {
    const data: any = {};
    
    if (this.firstName !== undefined) data.firstName = this.firstName.trim();
    if (this.lastName !== undefined) data.lastName = this.lastName.trim();
    if (this.phone !== undefined) data.phone = this.phone?.trim() || null;
    if (this.avatar !== undefined) data.avatar = this.avatar?.trim() || null;
    
    return data;
  }
}

// Password change data model
export class PasswordChangeData {
  constructor(
    public readonly currentPassword: string,
    public readonly newPassword: string
  ) {
    this.validate();
  }

  private validate(): void {
    if (!this.currentPassword) {
      throw new Error('Current password is required');
    }
    
    if (this.currentPassword.length > 128) {
      throw new Error('Current password is too long');
    }
    
    if (!this.newPassword || !this.isValidPassword(this.newPassword)) {
      throw new Error('New password must meet security requirements');
    }
    
    if (this.currentPassword === this.newPassword) {
      throw new Error('New password must be different from current password');
    }
  }

  private isValidPassword(password: string): boolean {
    // Password must be 8-128 chars with uppercase, lowercase, number, and special char
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,128}$/;
    return passwordRegex.test(password);
  }
}

// User profile model for responses
export class UserProfile {
  constructor(
    public readonly id: string,
    public readonly email: string,
    public readonly firstName: string,
    public readonly lastName: string,
    public readonly role: UserRole,
    public readonly isActive: boolean,
    public readonly isVerified: boolean,
    public readonly avatar: string | null,
    public readonly phone: string | null,
    public readonly createdAt: Date,
    public readonly updatedAt: Date,
    public readonly lastLoginAt: Date | null
  ) {}

  // Create from AuthUser
  static fromAuthUser(user: AuthUser): UserProfile {
    return new UserProfile(
      user.id,
      user.email,
      user.firstName,
      user.lastName,
      user.role,
      user.isActive,
      user.isVerified,
      user.avatar,
      user.phone,
      user.createdAt,
      user.updatedAt,
      user.lastLoginAt
    );
  }

  // Get display name
  getDisplayName(): string {
    return `${this.firstName} ${this.lastName}`.trim();
  }

  // Check if profile is complete
  isProfileComplete(): boolean {
    return !!(this.firstName && this.lastName && this.email);
  }

  // Get profile completion percentage
  getCompletionPercentage(): number {
    let completed = 0;
    const total = 5;

    if (this.firstName) completed++;
    if (this.lastName) completed++;
    if (this.email) completed++;
    if (this.phone) completed++;
    if (this.avatar) completed++;

    return Math.round((completed / total) * 100);
  }

  // Convert to plain object for API response
  toJSON() {
    return {
      id: this.id,
      email: this.email,
      firstName: this.firstName,
      lastName: this.lastName,
      role: this.role,
      isActive: this.isActive,
      isVerified: this.isVerified,
      avatar: this.avatar,
      phone: this.phone,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      lastLoginAt: this.lastLoginAt,
      displayName: this.getDisplayName(),
      isProfileComplete: this.isProfileComplete(),
      completionPercentage: this.getCompletionPercentage()
    };
  }
}

// Authentication response model
export class AuthenticationResponse {
  constructor(
    public readonly user: UserProfile,
    public readonly tokens: TokenPair
  ) {}

  // Create API response
  toApiResponse(): ApiResponse<{ user: UserProfile; tokens: TokenPair }> {
    return {
      success: true,
      data: {
        user: this.user,
        tokens: this.tokens
      },
      message: 'Authentication successful'
    };
  }
}

// Session information model
export class SessionInfo {
  constructor(
    public readonly id: string,
    public readonly ipAddress: string,
    public readonly userAgent: string,
    public readonly createdAt: Date,
    public readonly expiresAt: Date,
    public readonly isActive: boolean
  ) {}

  // Check if session is current (based on user agent and IP)
  isCurrent(currentIpAddress: string, currentUserAgent: string): boolean {
    return this.ipAddress === currentIpAddress && this.userAgent === currentUserAgent;
  }

  // Get device info from user agent
  getDeviceInfo(): { browser: string; os: string; device: string } {
    // Simple user agent parsing (could be enhanced with a library)
    const userAgent = this.userAgent.toLowerCase();
    
    let browser = 'Unknown';
    let os = 'Unknown';
    let device = 'Desktop';

    // Browser detection
    if (userAgent.includes('chrome')) browser = 'Chrome';
    else if (userAgent.includes('firefox')) browser = 'Firefox';
    else if (userAgent.includes('safari')) browser = 'Safari';
    else if (userAgent.includes('edge')) browser = 'Edge';

    // OS detection
    if (userAgent.includes('windows')) os = 'Windows';
    else if (userAgent.includes('mac')) os = 'macOS';
    else if (userAgent.includes('linux')) os = 'Linux';
    else if (userAgent.includes('android')) os = 'Android';
    else if (userAgent.includes('ios')) os = 'iOS';

    // Device type detection
    if (userAgent.includes('mobile') || userAgent.includes('android') || userAgent.includes('iphone')) {
      device = 'Mobile';
    } else if (userAgent.includes('tablet') || userAgent.includes('ipad')) {
      device = 'Tablet';
    }

    return { browser, os, device };
  }

  // Get time until expiration
  getTimeUntilExpiration(): number {
    return Math.max(0, this.expiresAt.getTime() - Date.now());
  }

  // Check if session is expired
  isExpired(): boolean {
    return this.expiresAt <= new Date();
  }

  // Convert to JSON for API response
  toJSON() {
    const deviceInfo = this.getDeviceInfo();
    
    return {
      id: this.id,
      ipAddress: this.ipAddress,
      createdAt: this.createdAt,
      expiresAt: this.expiresAt,
      isActive: this.isActive,
      isExpired: this.isExpired(),
      timeUntilExpiration: this.getTimeUntilExpiration(),
      deviceInfo
    };
  }
}

export default {
  AuthenticationModel,
  RegistrationData,
  LoginData,
  ProfileUpdateData,
  PasswordChangeData,
  UserProfile,
  AuthenticationResponse,
  SessionInfo
};