import type { ServiceCategory } from '../types';

// Service Categories Configuration
export const SERVICE_CATEGORIES: ServiceCategory[] = [
  {
    id: 'all-products',
    name: 'All Products',
    description: 'Browse all available products and services',
    route: '/services',
  },
  {
    id: 'business-cards',
    name: 'Business Cards',
    description: 'Professional business cards and networking materials',
    route: '/business-cards',
  },
  {
    id: 'marketing-materials',
    name: 'Marketing Materials',
    description: 'Brochures, flyers, and promotional materials',
    route: '/marketing-materials',
  },
  {
    id: 'signs-banners',
    name: 'Signs & Banners',
    description: 'Large format signage and banners',
    route: '/signs-banners',
  },
  {
    id: 'invitations-stationery',
    name: 'Invitations & Stationery',
    description: 'Wedding invitations and custom stationery',
    route: '/invitations-stationery',
  },
  {
    id: 'stickers-labels',
    name: 'Stickers & Labels',
    description: 'Custom stickers and product labels',
    route: '/stickers-labels',
  },
  {
    id: 'gifts-decor',
    name: 'Gifts & Decor',
    description: 'Personalized gifts and home decor',
    route: '/gifts-decor',
  },
  {
    id: 'apparel',
    name: 'Apparel',
    description: 'Custom clothing and wearables',
    route: '/apparel',
  },
];