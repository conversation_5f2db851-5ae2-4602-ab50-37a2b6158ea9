"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const compression_1 = __importDefault(require("compression"));
const morgan_1 = __importDefault(require("morgan"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const config_1 = require("./config");
const logger_1 = require("./utils/logger");
const routes_1 = __importDefault(require("./routes"));
const errorHandler_1 = require("./middleware/errorHandler");
const requestLogger_1 = require("./middleware/requestLogger");
const app = (0, express_1.default)();
const logger = (0, logger_1.createLogger)('App');
const serverConfig = config_1.config.getServerConfig();
const securityConfig = config_1.config.getSecurityConfig();
const loggingConfig = config_1.config.getLoggingConfig();
app.use((0, helmet_1.default)({
    crossOriginResourcePolicy: { policy: 'cross-origin' },
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", 'data:', 'https:'],
        },
    },
}));
const limiter = (0, express_rate_limit_1.default)({
    windowMs: securityConfig.rateLimitWindowMs,
    max: securityConfig.rateLimitMaxRequests,
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
});
app.use(limiter);
app.use((0, cors_1.default)({
    origin: serverConfig.corsOrigin,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));
app.use((0, compression_1.default)());
app.use(express_1.default.json({ limit: `${securityConfig.maxFileSize}mb` }));
app.use(express_1.default.urlencoded({ extended: true, limit: `${securityConfig.maxFileSize}mb` }));
app.use((0, cookie_parser_1.default)(securityConfig.cookieSecret));
if (loggingConfig.enableConsole) {
    if (serverConfig.nodeEnv !== 'production') {
        app.use((0, morgan_1.default)('dev'));
    }
    else {
        app.use((0, morgan_1.default)('combined'));
    }
}
app.use(requestLogger_1.requestLogger);
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: serverConfig.nodeEnv,
        version: process.env.npm_package_version || '1.0.0',
        apiBaseUrl: serverConfig.apiBaseUrl,
        frontendUrl: serverConfig.frontendUrl,
    });
});
app.use('/api', routes_1.default);
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`,
        path: req.originalUrl,
        method: req.method,
        timestamp: new Date().toISOString(),
    });
});
app.use(errorHandler_1.errorHandler);
logger.info('Express application configured successfully', {
    environment: serverConfig.nodeEnv,
    corsOrigin: serverConfig.corsOrigin,
    rateLimitMaxRequests: securityConfig.rateLimitMaxRequests,
    rateLimitWindowMs: securityConfig.rateLimitWindowMs,
    maxFileSize: securityConfig.maxFileSize,
    logLevel: loggingConfig.level,
    logFormat: loggingConfig.format,
});
exports.default = app;
//# sourceMappingURL=app.js.map