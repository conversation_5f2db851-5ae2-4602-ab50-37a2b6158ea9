"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const logger_1 = require("../utils/logger");
const auth_1 = __importDefault(require("./auth"));
const logger = (0, logger_1.createLogger)('Routes');
const router = (0, express_1.Router)();
router.use('/auth', auth_1.default);
logger.info('Routes registered successfully', {
    authRoutes: '/auth',
});
exports.default = router;
//# sourceMappingURL=index.js.map