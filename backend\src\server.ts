import {createServer, Server} from 'http';

// Import configuration system
import {config} from './config';
import {createLogger} from './utils/logger';

// Import the Express app
import app from './app';

// Import centralized services
import {databaseService, redisService} from './services';

const logger = createLogger('Server');

// Get configuration
const serverConfig = config.getServerConfig();

/**
 * Service Connection Management
 */
class ServiceManager {
	private static instance: ServiceManager;
	private isInitialized = false;

	private constructor() {}

	public static getInstance(): ServiceManager {
		if (!ServiceManager.instance) {
			ServiceManager.instance = new ServiceManager();
		}
		return ServiceManager.instance;
	}

	public async initialize(): Promise<void> {
		if (this.isInitialized) {
			logger.warn('Services already initialized');
			return;
		}

		try {
			// Initialize database service
			await databaseService.initialize();

			// Initialize Redis service
			await redisService.initialize();

			this.isInitialized = true;
			logger.info('All services initialized successfully');
		} catch (error) {
			logger.error('Service initialization failed', error);
			throw error;
		}
	}

	public async disconnect(): Promise<void> {
		try {
			// Disconnect Redis service
			await redisService.disconnect();

			// Disconnect database service
			await databaseService.disconnect();

			this.isInitialized = false;
			logger.info('All services disconnected successfully');
		} catch (error) {
			logger.error('Service disconnection failed', error);
			throw error;
		}
	}

	public getInitializedStatus(): boolean {
		return this.isInitialized;
	}

	public async healthCheck(): Promise<{
		database: boolean;
		redis: boolean;
	}> {
		const [dbHealth, redisHealth] = await Promise.all([
			databaseService.healthCheck(),
			redisService.healthCheck(),
		]);

		return {
			database: dbHealth,
			redis: redisHealth,
		};
	}
}

/**
 * HTTP Server Management
 */
class ServerManager {
	private server: Server | null = null;
	private serviceManager: ServiceManager;

	constructor() {
		this.serviceManager = ServiceManager.getInstance();
	}

	public async start(): Promise<void> {
		try {
			// Initialize all services first
			await this.serviceManager.initialize();

			// Create HTTP server
			this.server = createServer(app);

			// Start listening
			await new Promise<void>((resolve, reject) => {
				if (!this.server) {
					reject(new Error('Server not initialized'));
					return;
				}

				this.server!.listen(serverConfig.port, serverConfig.host, () => {
					logger.info('Server started successfully', {
						port: serverConfig.port,
						host: serverConfig.host,
						environment: serverConfig.nodeEnv,
						apiBaseUrl: serverConfig.apiBaseUrl,
						frontendUrl: serverConfig.frontendUrl,
						healthCheck: `http://${serverConfig.host}:${serverConfig.port}/health`,
						pid: process.pid,
					});

					// Log startup information
					this.logStartupInfo();
					resolve();
				});

				this.server!.on('error', (error) => {
					logger.error('Server startup failed', error);
					reject(error);
				});
			});

			// Set up graceful shutdown handlers
			this.setupGracefulShutdown();
		} catch (error) {
			logger.error('Failed to start server', error);
			throw error;
		}
	}

	public async stop(): Promise<void> {
		try {
			if (this.server) {
				await new Promise<void>((resolve) => {
					this.server!.close(() => {
						logger.info('HTTP server closed');
						resolve();
					});
				});
			}

			// Disconnect all services
			await this.serviceManager.disconnect();
		} catch (error) {
			logger.error('Error during server shutdown', error);
			throw error;
		}
	}

	private logStartupInfo(): void {
		logger.info('PrintWedittV1 Backend Server', {
			version: process.env.npm_package_version || '1.0.0',
			nodeVersion: process.version,
			platform: process.platform,
			architecture: process.arch,
			memoryUsage: process.memoryUsage(),
			uptime: process.uptime(),
		});

		// Log configuration summary
		logger.info('Configuration Summary', {
			environment: serverConfig.nodeEnv,
			port: serverConfig.port,
			host: serverConfig.host,
			corsOrigin: serverConfig.corsOrigin,
			database: {
				maxConnections: config.getDatabaseConfig().maxConnections,
				idleTimeout: config.getDatabaseConfig().idleTimeout,
			},
			redis: {
				url: redisService.getConfig().url,
				db: redisService.getConfig().db,
				keyPrefix: redisService.getConfig().keyPrefix,
			},
			security: {
				rateLimitMaxRequests: config.getSecurityConfig().rateLimitMaxRequests,
				rateLimitWindowMs: config.getSecurityConfig().rateLimitWindowMs,
				bcryptRounds: config.getSecurityConfig().bcryptRounds,
			},
			logging: {
				level: config.getLoggingConfig().level,
				format: config.getLoggingConfig().format,
				enableConsole: config.getLoggingConfig().enableConsole,
				enableFile: config.getLoggingConfig().enableFile,
			},
		});
	}

	private setupGracefulShutdown(): void {
		const shutdown = async (signal: string) => {
			logger.info(`Received ${signal}. Starting graceful shutdown...`);

			try {
				await this.stop();
				logger.info('Graceful shutdown completed');
				process.exit(0);
			} catch (error) {
				logger.error('Error during graceful shutdown', error);
				process.exit(1);
			}
		};

		// Handle different shutdown signals
		process.on('SIGTERM', () => shutdown('SIGTERM'));
		process.on('SIGINT', () => shutdown('SIGINT'));

		// Handle uncaught exceptions
		process.on('uncaughtException', (error) => {
			logger.error('Uncaught Exception', error);
			shutdown('uncaughtException');
		});

		// Handle unhandled promise rejections
		process.on('unhandledRejection', (reason, promise) => {
			logger.error('Unhandled Rejection', {reason, promise});
			shutdown('unhandledRejection');
		});
	}
}

/**
 * Main Server Startup Function
 */
async function startServer(): Promise<void> {
	const serverManager = new ServerManager();

	try {
		await serverManager.start();
		logger.info('PrintWedittV1 backend server is ready to handle requests');
	} catch (error) {
		logger.error('Failed to start server', error);
		process.exit(1);
	}
}

// Start the server if this file is run directly
if (require.main === module) {
	startServer().catch((error) => {
		logger.error('Server startup failed', error);
		process.exit(1);
	});
}

export {ServerManager, ServiceManager, startServer};
