import {createServer, Server} from 'http';
import {PrismaClient} from '@prisma/client';

// Import configuration system
import {config} from './config';
import {createLogger} from './utils/logger';

// Import the Express app
import app from './app';

const logger = createLogger('Server');
const prisma = new PrismaClient();

// Get configuration
const serverConfig = config.getServerConfig();
const dbConfig = config.getDatabaseConfig();

/**
 * Database Connection Management
 */
class DatabaseManager {
	private static instance: DatabaseManager;
	private isConnected = false;

	private constructor() {}

	public static getInstance(): DatabaseManager {
		if (!DatabaseManager.instance) {
			DatabaseManager.instance = new DatabaseManager();
		}
		return DatabaseManager.instance;
	}

	public async connect(): Promise<void> {
		try {
			await prisma.$connect();
			this.isConnected = true;
			logger.info('Database connected successfully', {
				url: dbConfig.url.replace(/\/\/.*@/, '//***:***@'), // Mask credentials
				maxConnections: dbConfig.maxConnections,
				idleTimeout: dbConfig.idleTimeout,
				connectionTimeout: dbConfig.connectionTimeout,
			});
		} catch (error) {
			logger.error('Database connection failed', error);
			throw error;
		}
	}

	public async disconnect(): Promise<void> {
		try {
			await prisma.$disconnect();
			this.isConnected = false;
			logger.info('Database disconnected successfully');
		} catch (error) {
			logger.error('Database disconnection failed', error);
			throw error;
		}
	}

	public isConnectedToDatabase(): boolean {
		return this.isConnected;
	}
}

/**
 * HTTP Server Management
 */
class ServerManager {
	private server: Server | null = null;
	private dbManager: DatabaseManager;

	constructor() {
		this.dbManager = DatabaseManager.getInstance();
	}

	public async start(): Promise<void> {
		try {
			// Connect to database first
			await this.dbManager.connect();

			// Create HTTP server
			this.server = createServer(app);

			// Start listening
			await new Promise<void>((resolve, reject) => {
				if (!this.server) {
					reject(new Error('Server not initialized'));
					return;
				}

				this.server!.listen(serverConfig.port, serverConfig.host, () => {
					logger.info('Server started successfully', {
						port: serverConfig.port,
						host: serverConfig.host,
						environment: serverConfig.nodeEnv,
						apiBaseUrl: serverConfig.apiBaseUrl,
						frontendUrl: serverConfig.frontendUrl,
						healthCheck: `http://${serverConfig.host}:${serverConfig.port}/health`,
						pid: process.pid,
					});

					// Log startup information
					this.logStartupInfo();
					resolve();
				});

				this.server!.on('error', (error) => {
					logger.error('Server startup failed', error);
					reject(error);
				});
			});

			// Set up graceful shutdown handlers
			this.setupGracefulShutdown();
		} catch (error) {
			logger.error('Failed to start server', error);
			throw error;
		}
	}

	public async stop(): Promise<void> {
		try {
			if (this.server) {
				await new Promise<void>((resolve) => {
					this.server!.close(() => {
						logger.info('HTTP server closed');
						resolve();
					});
				});
			}

			// Disconnect from database
			await this.dbManager.disconnect();
		} catch (error) {
			logger.error('Error during server shutdown', error);
			throw error;
		}
	}

	private logStartupInfo(): void {
		logger.info('PrintWedittV1 Backend Server', {
			version: process.env.npm_package_version || '1.0.0',
			nodeVersion: process.version,
			platform: process.platform,
			architecture: process.arch,
			memoryUsage: process.memoryUsage(),
			uptime: process.uptime(),
		});

		// Log configuration summary
		logger.info('Configuration Summary', {
			environment: serverConfig.nodeEnv,
			port: serverConfig.port,
			host: serverConfig.host,
			corsOrigin: serverConfig.corsOrigin,
			database: {
				maxConnections: dbConfig.maxConnections,
				idleTimeout: dbConfig.idleTimeout,
			},
			security: {
				rateLimitMaxRequests: config.getSecurityConfig().rateLimitMaxRequests,
				rateLimitWindowMs: config.getSecurityConfig().rateLimitWindowMs,
				bcryptRounds: config.getSecurityConfig().bcryptRounds,
			},
			logging: {
				level: config.getLoggingConfig().level,
				format: config.getLoggingConfig().format,
				enableConsole: config.getLoggingConfig().enableConsole,
				enableFile: config.getLoggingConfig().enableFile,
			},
		});
	}

	private setupGracefulShutdown(): void {
		const shutdown = async (signal: string) => {
			logger.info(`Received ${signal}. Starting graceful shutdown...`);

			try {
				await this.stop();
				logger.info('Graceful shutdown completed');
				process.exit(0);
			} catch (error) {
				logger.error('Error during graceful shutdown', error);
				process.exit(1);
			}
		};

		// Handle different shutdown signals
		process.on('SIGTERM', () => shutdown('SIGTERM'));
		process.on('SIGINT', () => shutdown('SIGINT'));

		// Handle uncaught exceptions
		process.on('uncaughtException', (error) => {
			logger.error('Uncaught Exception', error);
			shutdown('uncaughtException');
		});

		// Handle unhandled promise rejections
		process.on('unhandledRejection', (reason, promise) => {
			logger.error('Unhandled Rejection', {reason, promise});
			shutdown('unhandledRejection');
		});
	}
}

/**
 * Main Server Startup Function
 */
async function startServer(): Promise<void> {
	const serverManager = new ServerManager();

	try {
		await serverManager.start();
		logger.info('PrintWedittV1 backend server is ready to handle requests');
	} catch (error) {
		logger.error('Failed to start server', error);
		process.exit(1);
	}
}

// Start the server if this file is run directly
if (require.main === module) {
	startServer().catch((error) => {
		logger.error('Server startup failed', error);
		process.exit(1);
	});
}

export {ServerManager, DatabaseManager, startServer};
