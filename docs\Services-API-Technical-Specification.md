# Services API Technical Specification

## Overview

This document provides detailed technical specifications for implementing the Services API based on the frontend requirements analysis. It includes data models, API contracts, business logic specifications, and implementation guidelines.

## Data Models

### 1. Service Model

```typescript
interface Service {
  id: string;
  name: string;
  description: string;
  detailedDesc?: string;
  features: string[];
  notes?: string;
  categoryId: string;
  image: string;
  basePrice: number;
  pricingType: 'FIXED' | 'VARIABLE' | 'QUOTE';
  isActive: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;

  // Relations
  category: ServiceCategory;
  formFields: ServiceFormField[];
  providers: ProviderService[];
  orders: Order[];
}

interface ServiceCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  route: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;

  // Relations
  services: Service[];
}

interface ServiceFormField {
  id: string;
  serviceId: string;
  name: string;
  label: string;
  type: 'TEXT' | 'NUMBER' | 'SELECT' | 'CHECKBOX' | 'RADIO' | 'FILE';
  required: boolean;
  placeholder?: string;
  defaultValue?: string;
  validation?: Record<string, any>;
  sortOrder: number;
  isActive: boolean;
  createdAt: Date;

  // Relations
  service: Service;
  options: ServiceFormFieldOption[];
}

interface ServiceFormFieldOption {
  id: string;
  fieldId: string;
  value: string;
  label: string;
  priceModifier?: number;
  sortOrder: number;
  isActive: boolean;
  createdAt: Date;

  // Relations
  field: ServiceFormField;
}
```

### 2. Provider Model

```typescript
interface Provider {
  id: string;
  userId: string;
  businessName: string;
  description?: string;
  email?: string;
  website?: string;
  phone?: string;
  isVerified: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;

  // Relations
  user: User;
  services: ProviderService[];
  orders: Order[];
  operatingHours: ProviderOperatingHours[];
  serviceAreas: ProviderServiceArea[];
  ratingDisplay?: ProviderRatingDisplay;
}

interface ProviderService {
  id: string;
  providerId: string;
  serviceId: string;
  price: number;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;

  // Relations
  provider: Provider;
  service: Service;
}

interface ProviderOperatingHours {
  id: string;
  providerId: string;
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  openTime: string; // HH:MM format
  closeTime: string; // HH:MM format
  createdAt: Date;

  // Relations
  provider: Provider;
}

interface ProviderServiceArea {
  id: string;
  providerId: string;
  streetAddress: string;
  zipCode: string;
  city: string;
  state: string;
  isActive: boolean;
  createdAt: Date;

  // Relations
  provider: Provider;
}

interface ProviderRatingDisplay {
  id: string;
  providerId: string;
  averageRating: number;
  reviewCount: number;
  lastUpdated: Date;
  createdAt: Date;
  updatedAt: Date;

  // Relations
  provider: Provider;
}
```

### 3. Order Model

```typescript
interface Order {
  id: string;
  customerId: string;
  providerId?: string;
  serviceId: string;
  status: 'PENDING' | 'CONFIRMED' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'CANCELLED' | 'REFUNDED';
  totalAmount: number;
  description?: string;
  requirements?: Record<string, any>;
  dueDate?: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;

  // Relations
  customer: User;
  provider?: Provider;
  service: Service;
  items: OrderItem[];
  files: OrderFile[];
  statusHistory: OrderStatusHistory[];
}

interface OrderItem {
  id: string;
  orderId: string;
  name: string;
  description?: string;
  quantity: number;
  price: number;
  createdAt: Date;

  // Relations
  order: Order;
}

interface OrderFile {
  id: string;
  orderId: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  type: 'DESIGN' | 'REFERENCE' | 'PROOF' | 'FINAL' | 'OTHER';
  isPublic: boolean;
  virusScanned: boolean;
  accessToken?: string;
  uploadedBy?: string;
  createdAt: Date;

  // Relations
  order: Order;
}

interface OrderStatusHistory {
  id: string;
  orderId: string;
  status: OrderStatus;
  comment?: string;
  changedBy?: string;
  createdAt: Date;

  // Relations
  order: Order;
}
```

### 4. Gallery Model

```typescript
interface GalleryImage {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  title?: string;
  description?: string;
  category?: string;
  tags: string[];
  isActive: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}
```

## API Contracts

### 1. Service Management APIs

#### Get Services

```typescript
// Request
GET /api/services
Query Parameters:
- page?: number (default: 1)
- limit?: number (default: 20)
- category?: string
- search?: string
- isActive?: boolean
- sortBy?: 'name' | 'price' | 'createdAt' | 'sortOrder'
- sortOrder?: 'asc' | 'desc'

// Response
{
  success: boolean;
  data: {
    services: Service[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
  message?: string;
}
```

#### Get Service Details

```typescript
// Request
GET /api/services/:id

// Response
{
  success: boolean;
  data: {
    service: Service & {
      category: ServiceCategory;
      formFields: (ServiceFormField & {
        options: ServiceFormFieldOption[];
      })[];
    };
  };
  message?: string;
}
```

#### Create Service (Admin)

```typescript
// Request
POST /api/services
{
  name: string;
  description: string;
  detailedDesc?: string;
  features: string[];
  notes?: string;
  categoryId: string;
  image: string;
  basePrice: number;
  pricingType: 'FIXED' | 'VARIABLE' | 'QUOTE';
  sortOrder?: number;
  formFields?: {
    name: string;
    label: string;
    type: FormFieldType;
    required: boolean;
    placeholder?: string;
    defaultValue?: string;
    validation?: Record<string, any>;
    sortOrder?: number;
    options?: {
      value: string;
      label: string;
      priceModifier?: number;
      sortOrder?: number;
    }[];
  }[];
}

// Response
{
  success: boolean;
  data: {
    service: Service;
  };
  message: string;
}
```

### 2. Provider Management APIs

#### Get Providers

```typescript
// Request
GET /api/providers
Query Parameters:
- page?: number
- limit?: number
- serviceId?: string
- location?: string (ZIP code)
- radius?: number (miles, default: 10)
- isVerified?: boolean
- isActive?: boolean
- sortBy?: 'name' | 'rating' | 'distance'
- sortOrder?: 'asc' | 'desc'

// Response
{
  success: boolean;
  data: {
    providers: (Provider & {
      ratingDisplay?: ProviderRatingDisplay;
      serviceAreas: ProviderServiceArea[];
    })[];
    pagination: PaginationInfo;
  };
  message?: string;
}
```

#### Provider Registration

```typescript
// Request
POST /api/providers
{
  businessName: string;
  description?: string;
  email?: string;
  website?: string;
  phone?: string;
  serviceAreas: {
    streetAddress: string;
    zipCode: string;
    city: string;
    state: string;
  }[];
  operatingHours: {
    dayOfWeek: number;
    openTime: string;
    closeTime: string;
  }[];
  services: {
    serviceId: string;
    price: number;
    description?: string;
  }[];
}

// Response
{
  success: boolean;
  data: {
    provider: Provider;
  };
  message: string;
}
```

### 3. Order Management APIs

#### Create Order

```typescript
// Request
POST /api/orders
{
  serviceId: string;
  providerId?: string;
  description?: string;
  requirements: Record<string, any>;
  dueDate?: string;
  items: {
    name: string;
    description?: string;
    quantity: number;
    price: number;
  }[];
  customerLocation: {
    zipCode: string;
    city: string;
    state: string;
  };
}

// Response
{
  success: boolean;
  data: {
    order: Order & {
      service: Service;
      provider?: Provider;
      items: OrderItem[];
    };
    totalAmount: number;
    estimatedDelivery: string;
  };
  message: string;
}
```

#### Upload Order Files

```typescript
// Request
POST /api/orders/:id/files
Content-Type: multipart/form-data
{
  files: File[];
  type: 'DESIGN' | 'REFERENCE' | 'PROOF' | 'FINAL' | 'OTHER';
  isPublic?: boolean;
}

// Response
{
  success: boolean;
  data: {
    files: OrderFile[];
  };
  message: string;
}
```

### 4. Price Calculation API

#### Calculate Service Price

```typescript
// Request
POST /api/services/:id/calculate-price
{
  formData: Record<string, any>;
  quantity: number;
  providerId?: string;
  designService?: boolean;
}

// Response
{
  success: boolean;
  data: {
    basePrice: number;
    modifiers: {
      fieldName: string;
      optionLabel: string;
      priceModifier: number;
    }[];
    designServiceCost?: number;
    quantity: number;
    totalPrice: number;
    breakdown: {
      serviceCost: number;
      optionsCost: number;
      designCost?: number;
      quantity: number;
      total: number;
    };
  };
  message?: string;
}
```

## Business Logic Specifications

### 1. Service Management

#### Service Search and Filtering

```typescript
interface ServiceSearchParams {
  query?: string;
  categoryId?: string;
  minPrice?: number;
  maxPrice?: number;
  features?: string[];
  isActive?: boolean;
  sortBy?: 'name' | 'price' | 'popularity' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

interface ServiceSearchResult {
  services: Service[];
  totalCount: number;
  pagination: PaginationInfo;
  filters: {
    categories: ServiceCategory[];
    priceRange: { min: number; max: number };
    features: string[];
  };
}
```

#### Form Field Validation

```typescript
interface FormFieldValidation {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  min?: number;
  max?: number;
  fileTypes?: string[];
  maxFileSize?: number;
  options?: string[];
}

interface FormValidationResult {
  isValid: boolean;
  errors: {
    fieldName: string;
    message: string;
  }[];
}
```

### 2. Provider Management

#### Provider Matching Algorithm

```typescript
interface ProviderMatchCriteria {
  serviceId: string;
  location: {
    zipCode: string;
    city: string;
    state: string;
  };
  radius?: number;
  requirements?: Record<string, any>;
  budget?: {
    min: number;
    max: number;
  };
  timeline?: {
    dueDate: Date;
    isUrgent: boolean;
  };
}

interface ProviderMatch {
  provider: Provider;
  score: number;
  reasons: string[];
  estimatedPrice: number;
  estimatedDelivery: string;
  availability: boolean;
}

interface ProviderMatchingResult {
  matches: ProviderMatch[];
  totalFound: number;
  searchCriteria: ProviderMatchCriteria;
}
```

#### Provider Rating Calculation

```typescript
interface ProviderRating {
  averageRating: number;
  reviewCount: number;
  ratingBreakdown: {
    fiveStar: number;
    fourStar: number;
    threeStar: number;
    twoStar: number;
    oneStar: number;
  };
  recentReviews: Review[];
}

interface Review {
  id: string;
  orderId: string;
  customerId: string;
  providerId: string;
  rating: number;
  comment?: string;
  createdAt: Date;

  customer: {
    firstName: string;
    lastName: string;
  };
}
```

### 3. Order Management

#### Order Status Workflow

```typescript
enum OrderStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  IN_PROGRESS = 'IN_PROGRESS',
  REVIEW = 'REVIEW',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED',
}

interface OrderStatusTransition {
  from: OrderStatus;
  to: OrderStatus;
  allowedRoles: UserRole[];
  requiredFields?: string[];
  autoTransition?: boolean;
  notifications?: NotificationConfig[];
}

interface OrderStatusHistory {
  id: string;
  orderId: string;
  fromStatus: OrderStatus;
  toStatus: OrderStatus;
  changedBy: string;
  comment?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}
```

#### File Management

```typescript
interface FileUploadConfig {
  maxFileSize: number; // bytes
  allowedTypes: string[];
  maxFiles: number;
  virusScanning: boolean;
  imageOptimization: boolean;
  storageProvider: 'local' | 's3' | 'cloudfront';
}

interface FileValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  fileInfo: {
    originalName: string;
    mimeType: string;
    size: number;
    dimensions?: { width: number; height: number };
  };
}
```

### 4. Pricing Engine

#### Dynamic Pricing Calculation

```typescript
interface PricingContext {
  serviceId: string;
  providerId?: string;
  formData: Record<string, any>;
  quantity: number;
  customerId?: string;
  location?: {
    zipCode: string;
    state: string;
  };
  urgency?: 'standard' | 'rush' | 'express';
}

interface PricingResult {
  basePrice: number;
  modifiers: PriceModifier[];
  discounts: Discount[];
  taxes: Tax[];
  totalPrice: number;
  breakdown: {
    serviceCost: number;
    optionsCost: number;
    quantityCost: number;
    discountAmount: number;
    taxAmount: number;
    total: number;
  };
  currency: string;
  validUntil: Date;
}

interface PriceModifier {
  type: 'option' | 'quantity' | 'urgency' | 'location';
  description: string;
  amount: number;
  percentage?: number;
}

interface Discount {
  type: 'bulk' | 'customer' | 'promotional';
  code?: string;
  description: string;
  amount: number;
  percentage?: number;
}

interface Tax {
  type: 'sales' | 'service';
  rate: number;
  amount: number;
  description: string;
}
```

## Implementation Guidelines

### 1. Error Handling

#### Standard Error Response Format

```typescript
interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: Record<string, any>;
    timestamp: string;
    requestId: string;
  };
}

enum ErrorCodes {
  // Validation Errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',

  // Authentication & Authorization
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  INVALID_TOKEN = 'INVALID_TOKEN',

  // Resource Errors
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS = 'RESOURCE_ALREADY_EXISTS',
  RESOURCE_CONFLICT = 'RESOURCE_CONFLICT',

  // Business Logic Errors
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  INVALID_STATUS_TRANSITION = 'INVALID_STATUS_TRANSITION',
  PROVIDER_NOT_AVAILABLE = 'PROVIDER_NOT_AVAILABLE',

  // System Errors
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
}
```

### 2. Validation Schemas

#### Service Validation

```typescript
const createServiceSchema = {
  name: {
    type: 'string',
    required: true,
    minLength: 1,
    maxLength: 255,
    pattern: /^[a-zA-Z0-9\s\-_]+$/,
  },
  description: {
    type: 'string',
    required: true,
    minLength: 10,
    maxLength: 1000,
  },
  categoryId: {
    type: 'string',
    required: true,
    pattern: /^[a-zA-Z0-9-]+$/,
  },
  basePrice: {
    type: 'number',
    required: true,
    min: 0,
    max: 999999.99,
  },
  pricingType: {
    type: 'string',
    required: true,
    enum: ['FIXED', 'VARIABLE', 'QUOTE'],
  },
  formFields: {
    type: 'array',
    items: {
      type: 'object',
      properties: {
        name: { type: 'string', required: true },
        label: { type: 'string', required: true },
        type: { type: 'string', required: true, enum: ['TEXT', 'NUMBER', 'SELECT', 'CHECKBOX', 'RADIO', 'FILE'] },
        required: { type: 'boolean', default: false },
        options: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              value: { type: 'string', required: true },
              label: { type: 'string', required: true },
              priceModifier: { type: 'number', default: 0 },
            },
          },
        },
      },
    },
  },
};
```

### 3. Security Considerations

#### Input Sanitization

```typescript
interface SanitizationConfig {
  html: boolean;
  sql: boolean;
  xss: boolean;
  fileUpload: {
    allowedTypes: string[];
    maxSize: number;
    virusScanning: boolean;
  };
}

interface SanitizationResult {
  original: any;
  sanitized: any;
  warnings: string[];
  blocked: boolean;
  reason?: string;
}
```

#### Rate Limiting

```typescript
interface RateLimitConfig {
  windowMs: number; // 15 minutes
  maxRequests: number; // 100 requests per window
  message: string;
  headers: boolean;
  skipSuccessfulRequests: boolean;
  skipFailedRequests: boolean;
}

interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: Date;
  retryAfter?: number;
}
```

### 4. Performance Optimization

#### Caching Strategy

```typescript
interface CacheConfig {
  ttl: number; // seconds
  maxSize: number; // items
  strategy: 'lru' | 'lfu' | 'fifo';
  keys: {
    services: string;
    providers: string;
    categories: string;
    gallery: string;
  };
}

interface CacheKey {
  prefix: string;
  params: Record<string, any>;
  version: string;
}
```

#### Database Optimization

```typescript
interface DatabaseConfig {
  connectionPool: {
    min: number;
    max: number;
    acquireTimeout: number;
    idleTimeout: number;
  };
  queryOptimization: {
    enableQueryLogging: boolean;
    slowQueryThreshold: number;
    maxQueryTime: number;
  };
  indexing: {
    services: ['categoryId', 'isActive', 'sortOrder'];
    providers: ['isActive', 'isVerified', 'businessName'];
    orders: ['customerId', 'status', 'createdAt'];
  };
}
```

## Testing Specifications

### 1. Unit Tests

#### Service Controller Tests

```typescript
describe('ServiceController', () => {
  describe('GET /api/services', () => {
    it('should return paginated services', async () => {
      // Test implementation
    });

    it('should filter services by category', async () => {
      // Test implementation
    });

    it('should search services by name', async () => {
      // Test implementation
    });
  });

  describe('POST /api/services', () => {
    it('should create a new service', async () => {
      // Test implementation
    });

    it('should validate required fields', async () => {
      // Test implementation
    });

    it('should handle form field creation', async () => {
      // Test implementation
    });
  });
});
```

### 2. Integration Tests

#### Order Flow Tests

```typescript
describe('Order Flow Integration', () => {
  it('should complete full order lifecycle', async () => {
    // 1. Create order
    // 2. Upload files
    // 3. Assign provider
    // 4. Update status
    // 5. Complete order
  });

  it('should handle provider matching', async () => {
    // Test provider matching algorithm
  });

  it('should calculate pricing correctly', async () => {
    // Test pricing engine
  });
});
```

### 3. Performance Tests

#### Load Testing

```typescript
interface LoadTestConfig {
  concurrentUsers: number;
  duration: number; // seconds
  rampUpTime: number; // seconds
  endpoints: {
    path: string;
    method: string;
    weight: number; // percentage of requests
  }[];
}

interface PerformanceMetrics {
  responseTime: {
    p50: number;
    p95: number;
    p99: number;
  };
  throughput: number; // requests per second
  errorRate: number; // percentage
  resourceUsage: {
    cpu: number;
    memory: number;
    database: number;
  };
}
```

## Deployment Configuration

### 1. Environment Variables

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/printweditt
DATABASE_POOL_MIN=5
DATABASE_POOL_MAX=20

# Redis
REDIS_URL=redis://localhost:6379
REDIS_TTL=3600

# File Storage
STORAGE_PROVIDER=s3
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
S3_BUCKET=printweditt-files

# Security
JWT_SECRET=your_jwt_secret
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# External Services
VIRUS_SCAN_API_KEY=your_api_key
CDN_URL=https://cdn.printweditt.com
```

### 2. Docker Configuration

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

### 3. Health Checks

```typescript
interface HealthCheck {
  name: string;
  check: () => Promise<boolean>;
  timeout: number;
  interval: number;
}

const healthChecks: HealthCheck[] = [
  {
    name: 'database',
    check: async () => {
      // Test database connection
    },
    timeout: 5000,
    interval: 30000,
  },
  {
    name: 'redis',
    check: async () => {
      // Test Redis connection
    },
    timeout: 3000,
    interval: 30000,
  },
  {
    name: 'file-storage',
    check: async () => {
      // Test file storage access
    },
    timeout: 10000,
    interval: 60000,
  },
];
```

This technical specification provides a comprehensive foundation for implementing the Services API with all the necessary details for development, testing, and deployment.
