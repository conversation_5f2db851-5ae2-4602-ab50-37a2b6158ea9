import { createErrorResponse, createPaginationMeta, createSuccessResponse, HttpStatus } from '@/utils/responseWrapper';

describe('Response Wrapper Utils', () => {
  let mockRequest: any;
  let mockResponse: any;

  beforeEach(() => {
    mockRequest = {
      method: 'GET',
      originalUrl: '/api/test',
      headers: { 'user-agent': 'Jest Test' },
      ip: '127.0.0.1',
    };

    mockResponse = {
      getHeader: jest.fn().mockReturnValue('request-id-123'),
    };
  });

  describe('createSuccessResponse', () => {
    it('should create basic success response', () => {
      const result = createSuccessResponse({ id: 1, name: 'Test' }, 'Success message', mockRequest, mockResponse);

      expect(result).toMatchObject({
        success: true,
        data: { id: 1, name: 'Test' },
        message: 'Success message',
        meta: {
          timestamp: expect.any(String),
          requestId: 'request-id-123',
          version: expect.any(String),
          path: '/api/test',
          method: 'GET',
        },
      });
    });

    it('should create success response with pagination', () => {
      const pagination = {
        page: 1,
        limit: 10,
        total: 50,
        totalPages: 5,
        hasNext: true,
        hasPrev: false,
      };

      const result = createSuccessResponse(
        [{ id: 1 }, { id: 2 }],
        'Data retrieved',
        mockRequest,
        mockResponse,
        undefined,
        pagination
      );

      expect(result).toMatchObject({
        success: true,
        data: [{ id: 1 }, { id: 2 }],
        pagination,
      });
    });

    it('should create success response with metadata', () => {
      const result = createSuccessResponse({ results: [] }, 'Success', mockRequest, mockResponse);

      expect(result).toMatchObject({
        success: true,
        data: { results: [] },
        message: 'Success',
        meta: {
          timestamp: expect.any(String),
          version: expect.any(String),
        },
      });
    });

    it('should handle missing request and response', () => {
      const result = createSuccessResponse({ test: true }, 'Success');

      expect(result).toMatchObject({
        success: true,
        data: { test: true },
        message: 'Success',
        meta: {
          timestamp: expect.any(String),
          version: expect.any(String),
        },
      });

      expect(result.meta).not.toHaveProperty('requestId');
    });
  });

  describe('createErrorResponse', () => {
    it('should create basic error response', () => {
      const result = createErrorResponse(
        'Something went wrong',
        HttpStatus.INTERNAL_SERVER_ERROR,
        'InternalError',
        'ERR_INTERNAL',
        undefined,
        undefined,
        mockRequest,
        mockResponse
      );

      expect(result).toMatchObject({
        success: false,
        error: {
          type: 'InternalError',
          message: 'Something went wrong',
          code: 'ERR_INTERNAL',
        },
        meta: {
          timestamp: expect.any(String),
          requestId: 'request-id-123',
          version: expect.any(String),
          path: '/api/test',
          method: 'GET',
        },
      });
    });

    it('should create error response with details', () => {
      const errorDetails = [
        {
          field: 'email',
          code: 'INVALID_EMAIL',
          message: 'Email format is invalid',
          value: 'invalid-email',
        },
      ];

      const result = createErrorResponse(
        'Validation failed',
        HttpStatus.BAD_REQUEST,
        'ValidationError',
        'ERR_VALIDATION',
        errorDetails,
        undefined,
        mockRequest,
        mockResponse
      );

      expect(result).toMatchObject({
        success: false,
        error: {
          type: 'ValidationError',
          message: 'Validation failed',
          code: 'ERR_VALIDATION',
          details: errorDetails,
        },
      });
    });

    it('should create error response with stack trace', () => {
      const stackTrace = 'Error: Test error\n    at test.js:1:1';

      const result = createErrorResponse(
        'Error occurred',
        HttpStatus.INTERNAL_SERVER_ERROR,
        'Error',
        undefined,
        undefined,
        stackTrace,
        mockRequest,
        mockResponse
      );

      expect(result).toMatchObject({
        success: false,
        error: {
          type: 'Error',
          message: 'Error occurred',
          stack: stackTrace,
        },
      });
    });

    it('should handle missing optional parameters', () => {
      const result = createErrorResponse('Error', HttpStatus.BAD_REQUEST);

      expect(result).toMatchObject({
        success: false,
        error: {
          type: 'ValidationError',
          message: 'Error',
        },
        meta: {
          timestamp: expect.any(String),
          version: expect.any(String),
        },
      });

      expect(result.meta).not.toHaveProperty('requestId');
    });

    it('should include request context when available', () => {
      const result = createErrorResponse(
        'Context error',
        HttpStatus.BAD_REQUEST,
        'ContextError',
        undefined,
        undefined,
        undefined,
        mockRequest,
        mockResponse
      );

      expect(result.meta).toMatchObject({
        method: 'GET',
        path: '/api/test',
      });
    });
  });

  describe('createPaginationMeta', () => {
    it('should create pagination metadata', () => {
      const result = createPaginationMeta(2, 10, 55);

      expect(result).toEqual({
        page: 2,
        limit: 10,
        total: 55,
        totalPages: 6,
        hasNext: true,
        hasPrev: true,
        nextPage: 3,
        prevPage: 1,
      });
    });

    it('should handle first page', () => {
      const result = createPaginationMeta(1, 10, 25);

      expect(result).toEqual({
        page: 1,
        limit: 10,
        total: 25,
        totalPages: 3,
        hasNext: true,
        hasPrev: false,
        nextPage: 2,
        prevPage: undefined,
      });
    });

    it('should handle last page', () => {
      const result = createPaginationMeta(3, 10, 25);

      expect(result).toEqual({
        page: 3,
        limit: 10,
        total: 25,
        totalPages: 3,
        hasNext: false,
        hasPrev: true,
        nextPage: undefined,
        prevPage: 2,
      });
    });

    it('should handle single page', () => {
      const result = createPaginationMeta(1, 10, 5);

      expect(result).toEqual({
        page: 1,
        limit: 10,
        total: 5,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
        nextPage: undefined,
        prevPage: undefined,
      });
    });

    it('should handle zero total', () => {
      const result = createPaginationMeta(1, 10, 0);

      expect(result).toEqual({
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
        nextPage: undefined,
        prevPage: undefined,
      });
    });

    it('should handle exact division', () => {
      const result = createPaginationMeta(2, 10, 20);

      expect(result).toEqual({
        page: 2,
        limit: 10,
        total: 20,
        totalPages: 2,
        hasNext: false,
        hasPrev: true,
        nextPage: undefined,
        prevPage: 1,
      });
    });
  });

  describe('HttpStatus enum', () => {
    it('should have correct status codes', () => {
      expect(HttpStatus.OK).toBe(200);
      expect(HttpStatus.CREATED).toBe(201);
      expect(HttpStatus.NO_CONTENT).toBe(204);
      expect(HttpStatus.BAD_REQUEST).toBe(400);
      expect(HttpStatus.UNAUTHORIZED).toBe(401);
      expect(HttpStatus.FORBIDDEN).toBe(403);
      expect(HttpStatus.NOT_FOUND).toBe(404);
      expect(HttpStatus.CONFLICT).toBe(409);
      expect(HttpStatus.TOO_MANY_REQUESTS).toBe(429);
      expect(HttpStatus.INTERNAL_SERVER_ERROR).toBe(500);
    });
  });
});
