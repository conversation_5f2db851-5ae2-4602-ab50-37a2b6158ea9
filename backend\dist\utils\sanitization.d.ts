declare function isSensitiveField(fieldName: string): boolean;
export declare function sanitizeRequestBody(body: any): any;
export declare function sanitizeUser(user: any): any;
export declare function sanitizeHeaders(headers: any): any;
export declare function sanitizeQuery(query: any): any;
export declare function sanitizeObject(obj: any, options?: {
    maxDepth?: number;
    customSensitiveFields?: string[];
}): any;
declare const _default: {
    sanitizeRequestBody: typeof sanitizeRequestBody;
    sanitizeUser: typeof sanitizeUser;
    sanitizeHeaders: typeof sanitizeHeaders;
    sanitizeQuery: typeof sanitizeQuery;
    sanitizeObject: typeof sanitizeObject;
    isSensitiveField: typeof isSensitiveField;
};
export default _default;
//# sourceMappingURL=sanitization.d.ts.map