import {
  cleanupExpiredSessions,
  createApiResponse,
  generatePasswordResetToken,
  generateSecureToken,
  generateTokenPair,
  generateVerificationToken,
  getAccountLockInfo,
  getClientIP,
  getUserAgent,
  hashPassword,
  incrementLoginAttempts,
  isAccountLocked,
  isRefreshTokenValid,
  logSecurityEvent,
  resetLoginAttempts,
  revokeAllUserSessions,
  revokeRefreshToken,
  sanitizeUser,
  verifyPassword,
} from '@/utils/auth';
import { UserRole } from '@prisma/client';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';

// Mock dependencies
jest.mock('bcryptjs');
jest.mock('crypto');
jest.mock('@/middleware/auth');
jest.mock('@/services');
jest.mock('@/utils/sanitization');

const mockBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;
const mockCrypto = crypto as jest.Mocked<typeof crypto>;

// Mock database service
const mockPrismaClient = {
  user_sessions: {
    create: jest.fn(),
    findUnique: jest.fn(),
    updateMany: jest.fn(),
    deleteMany: jest.fn(),
  },
  user: {
    findUnique: jest.fn(),
    update: jest.fn(),
  },
};

jest.mock('@/services', () => ({
  databaseService: {
    getClient: jest.fn(() => mockPrismaClient),
  },
}));

jest.mock('@/middleware/auth', () => ({
  generateAccessToken: jest.fn().mockReturnValue('mock-access-token'),
  generateRefreshToken: jest.fn().mockReturnValue('mock-refresh-token'),
}));

jest.mock('@/utils/sanitization', () => ({
  sanitizeUser: jest.fn().mockImplementation((user: any) => ({ sanitized: true, ...user })),
}));

describe('Auth Utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockCrypto.randomBytes = jest.fn().mockReturnValue({
      toString: jest.fn().mockReturnValue('random-hex-string'),
    });

    // Ensure the database service mock is properly set up
    const { databaseService } = require('@/services');
    databaseService.getClient.mockReturnValue(mockPrismaClient);
  });

  describe('hashPassword', () => {
    it('should hash password with default rounds', async () => {
      (mockBcrypt.hash as jest.Mock).mockResolvedValue('hashed-password');

      const result = await hashPassword('plain-password');

      expect(mockBcrypt.hash).toHaveBeenCalledWith('plain-password', 12);
      expect(result).toBe('hashed-password');
    });

    it('should hash password with custom rounds', async () => {
      (mockBcrypt.hash as jest.Mock).mockResolvedValue('hashed-password-custom');

      const result = await hashPassword('plain-password', 10);

      expect(mockBcrypt.hash).toHaveBeenCalledWith('plain-password', 10);
      expect(result).toBe('hashed-password-custom');
    });
  });

  describe('verifyPassword', () => {
    it('should verify correct password', async () => {
      (mockBcrypt.compare as jest.Mock).mockResolvedValue(true);

      const result = await verifyPassword('plain-password', 'hashed-password');

      expect(mockBcrypt.compare).toHaveBeenCalledWith('plain-password', 'hashed-password');
      expect(result).toBe(true);
    });

    it('should reject incorrect password', async () => {
      (mockBcrypt.compare as jest.Mock).mockResolvedValue(false);

      const result = await verifyPassword('wrong-password', 'hashed-password');

      expect(mockBcrypt.compare).toHaveBeenCalledWith('wrong-password', 'hashed-password');
      expect(result).toBe(false);
    });
  });

  describe('generateSecureToken', () => {
    it('should generate token with default length', () => {
      const result = generateSecureToken();

      expect(mockCrypto.randomBytes).toHaveBeenCalledWith(32);
      expect(result).toBe('random-hex-string');
    });

    it('should generate token with custom length', () => {
      const result = generateSecureToken(16);

      expect(mockCrypto.randomBytes).toHaveBeenCalledWith(16);
      expect(result).toBe('random-hex-string');
    });
  });

  describe('generateVerificationToken', () => {
    it('should generate verification token with 24 hour expiration', () => {
      const mockDate = new Date('2023-01-01T12:00:00Z');
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate);

      const result = generateVerificationToken();

      expect(result.token).toBe('random-hex-string');
      expect(result.expires).toEqual(new Date('2023-01-02T12:00:00Z')); // 24 hours later
    });
  });

  describe('generatePasswordResetToken', () => {
    it('should generate password reset token with 1 hour expiration', () => {
      const mockDate = new Date('2023-01-01T12:00:00Z');
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate);

      const result = generatePasswordResetToken();

      expect(result.token).toBe('random-hex-string');
      expect(result.expires).toEqual(new Date('2023-01-01T13:00:00Z')); // 1 hour later
    });
  });

  describe('generateTokenPair', () => {
    it('should generate token pair and store session', async () => {
      const mockSession = { id: 'session-123' };
      mockPrismaClient.user_sessions.create.mockResolvedValue(mockSession);

      // Ensure the auth middleware mocks are properly set up
      const { generateAccessToken, generateRefreshToken } = require('@/middleware/auth');
      generateAccessToken.mockReturnValue('mock-access-token');
      generateRefreshToken.mockReturnValue('mock-refresh-token');

      const result = await generateTokenPair(
        'user-123',
        '<EMAIL>',
        'customer' as UserRole,
        '***********',
        'Mozilla/5.0'
      );

      expect(result).toEqual({
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
      });

      expect(mockPrismaClient.user_sessions.create).toHaveBeenCalledWith({
        data: {
          id: 'random-hex-string',
          userId: 'user-123',
          refreshToken: 'mock-refresh-token',
          expiresAt: expect.any(Date),
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0',
          isActive: true,
        },
      });
    });

    it('should truncate long IP address and user agent', async () => {
      const longIP = 'x'.repeat(50);
      const longUserAgent = 'x'.repeat(600);

      mockPrismaClient.user_sessions.create.mockResolvedValue({});

      await generateTokenPair('user-123', '<EMAIL>', 'customer' as UserRole, longIP, longUserAgent);

      expect(mockPrismaClient.user_sessions.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          ipAddress: 'x'.repeat(45), // Truncated to 45 chars
          userAgent: 'x'.repeat(500), // Truncated to 500 chars
        }),
      });
    });
  });

  describe('revokeRefreshToken', () => {
    it('should revoke refresh token', async () => {
      mockPrismaClient.user_sessions.updateMany.mockResolvedValue({ count: 1 });

      await revokeRefreshToken('refresh-token-123');

      expect(mockPrismaClient.user_sessions.updateMany).toHaveBeenCalledWith({
        where: { refreshToken: 'refresh-token-123' },
        data: {
          isActive: false,
          revokedAt: expect.any(Date),
        },
      });
    });
  });

  describe('revokeAllUserSessions', () => {
    it('should revoke all user sessions', async () => {
      mockPrismaClient.user_sessions.updateMany.mockResolvedValue({ count: 3 });

      await revokeAllUserSessions('user-123');

      expect(mockPrismaClient.user_sessions.updateMany).toHaveBeenCalledWith({
        where: { userId: 'user-123' },
        data: {
          isActive: false,
          revokedAt: expect.any(Date),
        },
      });
    });
  });

  describe('isRefreshTokenValid', () => {
    it('should return true for valid token', async () => {
      const mockSession = {
        refreshToken: 'valid-token',
        isActive: true,
        expiresAt: new Date(Date.now() + 60000), // 1 minute in future
      };
      mockPrismaClient.user_sessions.findUnique.mockResolvedValue(mockSession);

      const result = await isRefreshTokenValid('valid-token');

      expect(result).toBe(true);
    });

    it('should return false for non-existent token', async () => {
      mockPrismaClient.user_sessions.findUnique.mockResolvedValue(null);

      const result = await isRefreshTokenValid('non-existent-token');

      expect(result).toBe(false);
    });

    it('should return false for inactive token', async () => {
      const mockSession = {
        refreshToken: 'inactive-token',
        isActive: false,
        expiresAt: new Date(Date.now() + 60000),
      };
      mockPrismaClient.user_sessions.findUnique.mockResolvedValue(mockSession);

      const result = await isRefreshTokenValid('inactive-token');

      expect(result).toBe(false);
    });

    it('should return false for expired token', async () => {
      const mockSession = {
        refreshToken: 'expired-token',
        isActive: true,
        expiresAt: new Date(Date.now() - 60000), // 1 minute in past
      };
      mockPrismaClient.user_sessions.findUnique.mockResolvedValue(mockSession);

      const result = await isRefreshTokenValid('expired-token');

      expect(result).toBe(false);
    });
  });

  describe('cleanupExpiredSessions', () => {
    it('should delete expired and old revoked sessions', async () => {
      mockPrismaClient.user_sessions.deleteMany.mockResolvedValue({ count: 5 });

      const result = await cleanupExpiredSessions();

      expect(mockPrismaClient.user_sessions.deleteMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { expiresAt: { lt: expect.any(Date) } },
            {
              isActive: false,
              revokedAt: { lt: expect.any(Date) },
            },
          ],
        },
      });
      expect(result).toBe(5);
    });
  });

  describe('incrementLoginAttempts', () => {
    it('should increment login attempts', async () => {
      const mockUser = { loginAttempts: 2 };
      mockPrismaClient.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaClient.user.update.mockResolvedValue({});

      await incrementLoginAttempts('user-123');

      expect(mockPrismaClient.user.update).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        data: { loginAttempts: 3 },
      });
    });

    it('should lock account after max attempts', async () => {
      const mockUser = { loginAttempts: 4 }; // Will become 5, which is max
      mockPrismaClient.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaClient.user.update.mockResolvedValue({});

      await incrementLoginAttempts('user-123');

      expect(mockPrismaClient.user.update).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        data: {
          loginAttempts: 5,
          lockedUntil: expect.any(Date),
        },
      });
    });

    it('should handle non-existent user gracefully', async () => {
      mockPrismaClient.user.findUnique.mockResolvedValue(null);

      await expect(incrementLoginAttempts('non-existent')).resolves.not.toThrow();
      expect(mockPrismaClient.user.update).not.toHaveBeenCalled();
    });
  });

  describe('resetLoginAttempts', () => {
    it('should reset login attempts and update last login', async () => {
      mockPrismaClient.user.update.mockResolvedValue({});

      await resetLoginAttempts('user-123');

      expect(mockPrismaClient.user.update).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        data: {
          loginAttempts: 0,
          lockedUntil: null,
          lastLoginAt: expect.any(Date),
        },
      });
    });
  });

  describe('isAccountLocked', () => {
    it('should return true for locked account', async () => {
      const mockUser = {
        lockedUntil: new Date(Date.now() + 60000), // 1 minute in future
      };
      mockPrismaClient.user.findUnique.mockResolvedValue(mockUser);

      const result = await isAccountLocked('user-123');

      expect(result).toBe(true);
    });

    it('should return false for unlocked account', async () => {
      const mockUser = { lockedUntil: null };
      mockPrismaClient.user.findUnique.mockResolvedValue(mockUser);

      const result = await isAccountLocked('user-123');

      expect(result).toBe(false);
    });

    it('should auto-unlock expired locks', async () => {
      const mockUser = {
        lockedUntil: new Date(Date.now() - 60000), // 1 minute in past
      };
      mockPrismaClient.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaClient.user.update.mockResolvedValue({});

      const result = await isAccountLocked('user-123');

      expect(result).toBe(false);
      expect(mockPrismaClient.user.update).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        data: {
          lockedUntil: null,
          loginAttempts: 0,
        },
      });
    });
  });

  describe('getAccountLockInfo', () => {
    it('should return lock info for existing user', async () => {
      const lockTime = new Date(Date.now() + 60000);
      const mockUser = {
        loginAttempts: 3,
        lockedUntil: lockTime,
      };
      mockPrismaClient.user.findUnique.mockResolvedValue(mockUser);

      const result = await getAccountLockInfo('user-123');

      expect(result).toEqual({
        isLocked: true,
        lockExpires: lockTime,
        attempts: 3,
        maxAttempts: 5,
        remainingAttempts: 2,
      });
    });

    it('should return null for non-existent user', async () => {
      mockPrismaClient.user.findUnique.mockResolvedValue(null);

      const result = await getAccountLockInfo('non-existent');

      expect(result).toBeNull();
    });

    it('should return unlocked info for unlocked user', async () => {
      const mockUser = {
        loginAttempts: 2,
        lockedUntil: null,
      };
      mockPrismaClient.user.findUnique.mockResolvedValue(mockUser);

      const result = await getAccountLockInfo('user-123');

      expect(result).toEqual({
        isLocked: false,
        lockExpires: null,
        attempts: 2,
        maxAttempts: 5,
        remainingAttempts: 3,
      });
    });
  });

  describe('logSecurityEvent', () => {
    it('should log security event in development', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const event = {
        type: 'LOGIN_FAILURE' as any,
        userId: 'user-123',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        details: { reason: 'Invalid password' },
      };

      await logSecurityEvent(event);

      expect(consoleSpy).toHaveBeenCalledWith('Security Event:', {
        ...event,
        timestamp: expect.any(String),
      });

      consoleSpy.mockRestore();
      process.env.NODE_ENV = originalEnv;
    });

    it('should not log in production', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const event = {
        type: 'LOGIN_FAILURE' as any,
        userId: 'user-123',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        details: { reason: 'Invalid password' },
      };

      await logSecurityEvent(event);

      expect(consoleSpy).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('getClientIP', () => {
    it('should extract IP from x-forwarded-for header', () => {
      const req = {
        headers: {
          'x-forwarded-for': '***********, ********',
        },
      };

      const result = getClientIP(req);

      expect(result).toBe('***********');
    });

    it('should extract IP from x-real-ip header', () => {
      const req = {
        headers: {
          'x-real-ip': '***********',
        },
      };

      const result = getClientIP(req);

      expect(result).toBe('***********');
    });

    it('should extract IP from connection.remoteAddress', () => {
      const req = {
        headers: {},
        connection: {
          remoteAddress: '***********',
        },
      };

      const result = getClientIP(req);

      expect(result).toBe('***********');
    });

    it('should extract IP from socket.remoteAddress', () => {
      const req = {
        headers: {},
        socket: {
          remoteAddress: '***********',
        },
      };

      const result = getClientIP(req);

      expect(result).toBe('***********');
    });

    it('should extract IP from req.ip', () => {
      const req = {
        headers: {},
        ip: '***********',
      };

      const result = getClientIP(req);

      expect(result).toBe('***********');
    });

    it('should return unknown for missing IP', () => {
      const req = { headers: {} };

      const result = getClientIP(req);

      expect(result).toBe('unknown');
    });
  });

  describe('getUserAgent', () => {
    it('should extract user agent from headers', () => {
      const req = {
        headers: {
          'user-agent': 'Mozilla/5.0 (Test Browser)',
        },
      };

      const result = getUserAgent(req);

      expect(result).toBe('Mozilla/5.0 (Test Browser)');
    });

    it('should return unknown for missing user agent', () => {
      const req = { headers: {} };

      const result = getUserAgent(req);

      expect(result).toBe('unknown');
    });
  });

  describe('sanitizeUser', () => {
    it('should delegate to sanitization utility', () => {
      const user = { id: 'user-123', email: '<EMAIL>', password: 'secret' };
      const mockSanitizeUser = require('@/utils/sanitization').sanitizeUser;
      mockSanitizeUser.mockReturnValue({ sanitized: true, ...user });

      const result = sanitizeUser(user);

      expect(mockSanitizeUser).toHaveBeenCalledWith(user);
      expect(result).toEqual({ sanitized: true, ...user });
    });
  });

  describe('createApiResponse', () => {
    it('should create successful API response', () => {
      const result = createApiResponse(true, { id: 'user-123' }, 'Success');

      expect(result).toEqual({
        success: true,
        data: { id: 'user-123' },
        message: 'Success',
        timestamp: expect.any(String),
      });
    });

    it('should create error API response', () => {
      const result = createApiResponse(false, null, 'Error occurred');

      expect(result).toEqual({
        success: false,
        data: null,
        message: 'Error occurred',
        timestamp: expect.any(String),
      });
    });

    it('should create response without optional fields', () => {
      const result = createApiResponse(true);

      expect(result).toEqual({
        success: true,
        data: undefined,
        message: undefined,
        timestamp: expect.any(String),
      });
    });
  });
});
