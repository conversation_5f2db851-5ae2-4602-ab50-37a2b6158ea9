{
  "recommendations": [
    // TypeScript and JavaScript
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-json",

    // Database and ORM
    "Prisma.prisma",
    "cweijan.vscode-postgresql-client2",
    "ms-mssql.mssql",

    // React and Frontend
    "ms-vscode.vscode-react-native",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-css-peek",

    // Git and Version Control
    "eamodio.gitlens",
    "mhutchie.git-graph",
    "donjayamanne.githistory",

    // Testing
    "orta.vscode-jest",
    "ms-vscode.test-adapter-converter",

    // Docker and Containers
    "ms-azuretools.vscode-docker",
    "ms-vscode-remote.remote-containers",

    // API Development
    "humao.rest-client",
    "rangav.vscode-thunder-client",
    "ms-vscode.vscode-json",

    // Productivity
    "ms-vscode.vscode-npm-script",
    "christian-kohler.npm-intellisense",
    "ms-vscode.vscode-json",
    "streetsidesoftware.code-spell-checker",
    "ms-vscode.vscode-markdownlint",
    "DavidAnson.vscode-markdownlint",

    // Debugging and Performance
    "ms-vscode.vscode-js-debug",
    "ms-vscode.vscode-js-debug-companion",
    "ms-vscode.vscode-js-profile-flame",
    "ms-vscode.vscode-js-profile-table",

    // Database Tools
    "mtxr.sqltools",
    "mtxr.sqltools-driver-pg",
    "mtxr.sqltools-driver-mysql",
    "mtxr.sqltools-driver-sqlite",

    // Environment and Configuration
    "mikestead.dotenv",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",

    // Code Quality
    "ms-vscode.vscode-json",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",

    // Theme and Icons
    "PKief.material-icon-theme",
    "zhuangtongfa.Material-theme",
    "ms-vscode.Theme-TomorrowKit",

    // Utilities
    "ms-vscode.vscode-json",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",

    // Project Specific
    "ms-vscode.vscode-json",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",

    // Performance and Monitoring
    "ms-vscode.vscode-js-debug",
    "ms-vscode.vscode-js-debug-companion",
    "ms-vscode.vscode-js-profile-flame",
    "ms-vscode.vscode-js-profile-table",

    // Documentation
    "ms-vscode.vscode-markdownlint",
    "DavidAnson.vscode-markdownlint",
    "ms-vscode.vscode-json",

    // Security
    "ms-vscode.vscode-json",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",

    // Collaboration
    "ms-vscode.vscode-json",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint"
  ],
  "unwantedRecommendations": ["ms-vscode.vscode-typescript", "ms-vscode.vscode-javascript", "ms-vscode.vscode-json"]
}
