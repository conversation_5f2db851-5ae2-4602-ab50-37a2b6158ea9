{"version": "2.0.0", "tasks": [{"label": "npm: install", "type": "shell", "command": "npm", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: build", "type": "shell", "command": "npm", "args": ["run", "build"], "group": "build", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"label": "npm: dev", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": "build", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: test", "type": "shell", "command": "npm", "args": ["test"], "group": "test", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: test:watch", "type": "shell", "command": "npm", "args": ["run", "test:watch"], "group": "test", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: lint", "type": "shell", "command": "npm", "args": ["run", "lint"], "group": "build", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$eslint-stylish"]}, {"label": "npm: lint:fix", "type": "shell", "command": "npm", "args": ["run", "lint:fix"], "group": "build", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$eslint-stylish"]}, {"label": "npm: type-check", "type": "shell", "command": "npm", "args": ["run", "type-check"], "group": "build", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"label": "db: generate", "type": "shell", "command": "npm", "args": ["run", "db:generate"], "group": "build", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "db: push", "type": "shell", "command": "npm", "args": ["run", "db:push"], "group": "build", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "db: migrate", "type": "shell", "command": "npm", "args": ["run", "db:migrate"], "group": "build", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "db: migrate:dev", "type": "shell", "command": "npm", "args": ["run", "db:migrate:dev"], "group": "build", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "db: seed", "type": "shell", "command": "npm", "args": ["run", "db:seed"], "group": "build", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "db: studio", "type": "shell", "command": "npm", "args": ["run", "db:studio"], "group": "build", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "db: reset", "type": "shell", "command": "npm", "args": ["run", "db:reset"], "group": "build", "options": {"cwd": "${workspaceFolder}/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "docker: up", "type": "shell", "command": "docker-compose", "args": ["up", "-d"], "group": "build", "options": {"cwd": "${workspaceFolder}"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "docker: down", "type": "shell", "command": "docker-compose", "args": ["down"], "group": "build", "options": {"cwd": "${workspaceFolder}"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "docker: logs", "type": "shell", "command": "docker-compose", "args": ["logs", "-f"], "group": "build", "options": {"cwd": "${workspaceFolder}"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "frontend: install", "type": "shell", "command": "npm", "args": ["install"], "group": "build", "options": {"cwd": "${workspaceFolder}/frontend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "frontend: dev", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": "build", "options": {"cwd": "${workspaceFolder}/frontend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "frontend: build", "type": "shell", "command": "npm", "args": ["run", "build"], "group": "build", "options": {"cwd": "${workspaceFolder}/frontend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "frontend: preview", "type": "shell", "command": "npm", "args": ["run", "preview"], "group": "build", "options": {"cwd": "${workspaceFolder}/frontend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "full-stack: setup", "dependsOrder": "sequence", "dependsOn": ["npm: install", "frontend: install", "db: generate"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "full-stack: dev", "dependsOrder": "parallel", "dependsOn": ["npm: dev", "frontend: dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}