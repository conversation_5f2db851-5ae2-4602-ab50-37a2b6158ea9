{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../src/utils/auth.ts"], "names": [], "mappings": "AAEA,OAAO,EACN,SAAS,EAET,aAAa,EAEb,MAAM,eAAe,CAAC;AASvB,eAAO,MAAM,YAAY,GACxB,UAAU,MAAM,EAChB,eAAc,MAAW,KACvB,OAAO,CAAC,MAAM,CAEhB,CAAC;AAGF,eAAO,MAAM,cAAc,GAC1B,UAAU,MAAM,EAChB,MAAM,MAAM,KACV,OAAO,CAAC,OAAO,CAEjB,CAAC;AAGF,eAAO,MAAM,mBAAmB,GAAI,SAAQ,MAAW,KAAG,MAEzD,CAAC;AAGF,eAAO,MAAM,yBAAyB,QAAO;IAAC,KAAK,EAAE,MAAM,CAAC;IAAC,OAAO,EAAE,IAAI,CAAA;CAMzE,CAAC;AAGF,eAAO,MAAM,0BAA0B,QAAO;IAC7C,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,IAAI,CAAC;CAOd,CAAC;AAGF,eAAO,MAAM,iBAAiB,GAC7B,QAAQ,MAAM,EACd,OAAO,MAAM,EACb,MAAM,GAAG,EACT,WAAW,MAAM,EACjB,WAAW,MAAM,KACf,OAAO,CAAC,SAAS,CA2BnB,CAAC;AAGF,eAAO,MAAM,kBAAkB,GAC9B,cAAc,MAAM,KAClB,OAAO,CAAC,IAAI,CAQd,CAAC;AAGF,eAAO,MAAM,qBAAqB,GAAU,QAAQ,MAAM,KAAG,OAAO,CAAC,IAAI,CAQxE,CAAC;AAGF,eAAO,MAAM,mBAAmB,GAC/B,cAAc,MAAM,KAClB,OAAO,CAAC,OAAO,CAUjB,CAAC;AAGF,eAAO,MAAM,sBAAsB,QAAa,OAAO,CAAC,MAAM,CAc7D,CAAC;AAGF,eAAO,MAAM,sBAAsB,GAAU,QAAQ,MAAM,KAAG,OAAO,CAAC,IAAI,CA0BzE,CAAC;AAGF,eAAO,MAAM,kBAAkB,GAAU,QAAQ,MAAM,KAAG,OAAO,CAAC,IAAI,CASrE,CAAC;AAGF,eAAO,MAAM,eAAe,GAAU,QAAQ,MAAM,KAAG,OAAO,CAAC,OAAO,CAqBrE,CAAC;AAGF,eAAO,MAAM,kBAAkB,GAAU,QAAQ,MAAM;;;;;;SAqBtD,CAAC;AAGF,eAAO,MAAM,gBAAgB,GAC5B,OAAO,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,KACrC,OAAO,CAAC,IAAI,CAWd,CAAC;AAGF,eAAO,MAAM,WAAW,GAAI,KAAK,GAAG,KAAG,MAStC,CAAC;AAGF,eAAO,MAAM,YAAY,GAAI,KAAK,GAAG,KAAG,MAEvC,CAAC;AAGF,eAAO,MAAM,YAAY,GAAI,MAAM,GAAG,QAErC,CAAC;AAGF,eAAO,MAAM,iBAAiB,GAC7B,SAAS,OAAO,EAChB,OAAO,GAAG,EACV,UAAU,MAAM;;;;;CAQhB,CAAC;;6BA/QS,MAAM,iBACF,MAAM,KAClB,OAAO,CAAC,MAAM,CAAC;+BAMP,MAAM,QACV,MAAM,KACV,OAAO,CAAC,OAAO,CAAC;mCAKyB,MAAM,KAAQ,MAAM;qCAKnB;QAAC,KAAK,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,IAAI,CAAA;KAAC;sCAS7B;QAC7C,KAAK,EAAE,MAAM,CAAC;QACd,OAAO,EAAE,IAAI,CAAC;KACd;gCAUQ,MAAM,SACP,MAAM,QACP,GAAG,aACE,MAAM,aACN,MAAM,KACf,OAAO,CAAC,SAAS,CAAC;uCA+BN,MAAM,KAClB,OAAO,CAAC,IAAI,CAAC;oCAWoC,MAAM,KAAG,OAAO,CAAC,IAAI,CAAC;wCAY3D,MAAM,KAClB,OAAO,CAAC,OAAO,CAAC;kCAa6B,OAAO,CAAC,MAAM,CAAC;qCAiBV,MAAM,KAAG,OAAO,CAAC,IAAI,CAAC;iCA6B1B,MAAM,KAAG,OAAO,CAAC,IAAI,CAAC;8BAYzB,MAAM,KAAG,OAAO,CAAC,OAAO,CAAC;iCAwBtB,MAAM;;;;;;;8BAyB/C,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,KACrC,OAAO,CAAC,IAAI,CAAC;uBAciB,GAAG,KAAG,MAAM;wBAYX,GAAG,KAAG,MAAM;yBAKX,GAAG;iCAM5B,OAAO,SACT,GAAG,YACA,MAAM;;;;;;;AAUjB,wBAoBE"}