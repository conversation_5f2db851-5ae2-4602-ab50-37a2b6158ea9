import { NextFunction, Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { createLogger } from '../utils/logger';

const logger = createLogger('RequestCorrelation');

/**
 * Middleware to add correlation IDs and enhance request tracking
 * This middleware ensures every request has a unique correlation ID
 * for better debugging and monitoring across distributed systems
 */
export const requestCorrelationMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  // Generate or use existing correlation ID
  const correlationId = (req.headers['x-correlation-id'] as string) || uuidv4();

  // Generate request ID if not present
  const requestId = (req.headers['x-request-id'] as string) || uuidv4();

  // Add correlation and request IDs to request object
  (req as any).correlationId = correlationId;
  (req as any).requestId = requestId;

  // Set response headers
  res.setHeader('X-Correlation-ID', correlationId);
  res.setHeader('X-Request-ID', requestId);

  // Add request start time for duration calculation
  (req as any).startTime = Date.now();

  // Log request start
  logger.debug('Request started', {
    method: req.method,
    path: req.path,
    correlationId,
    requestId,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: (req as any).user?.id,
  });

  // Add response finish logging
  const originalEnd = res.end;
  res.end = function (chunk?: any, encoding?: any, cb?: () => void) {
    const duration = Date.now() - (req as any).startTime;

    // Log request completion
    logger.debug('Request completed', {
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      duration,
      correlationId,
      requestId,
      userId: (req as any).user?.id,
    });

    // Call original end method
    return originalEnd.call(this, chunk, encoding, cb);
  };

  next();
};

/**
 * Middleware to add request context for better error tracking
 */
export const requestContextMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  // Add request context to req object for use in controllers
  (req as any).context = {
    correlationId: (req as any).correlationId,
    requestId: (req as any).requestId,
    startTime: (req as any).startTime,
    method: req.method,
    path: req.path,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: (req as any).user?.id,
  };

  next();
};

export default {
  requestCorrelationMiddleware,
  requestContextMiddleware,
};
