import dotenv from 'dotenv';
import {z} from 'zod';

// Load environment variables
const result = dotenv.config();

if (result.error) {
	console.error('❌ Error loading .env file:', result.error.message);
	process.exit(1);
}

// Configuration schemas for validation
const DatabaseConfigSchema = z.object({
	url: z.string().url('Invalid database URL'),
	maxConnections: z.number().int().positive().default(10),
	idleTimeout: z.number().int().positive().default(30000),
	connectionTimeout: z.number().int().positive().default(2000),
});

const JWTConfigSchema = z.object({
	secret: z.string().min(32, 'JWT secret must be at least 32 characters'),
	refreshSecret: z
		.string()
		.min(32, 'JWT refresh secret must be at least 32 characters'),
	accessExpiresIn: z.string().default('15m'),
	refreshExpiresIn: z.string().default('7d'),
	issuer: z.string().default('printweditt'),
	audience: z.string().default('printweditt-users'),
});

const ServerConfigSchema = z.object({
	port: z.number().int().positive().default(3000),
	host: z.string().default('localhost'),
	nodeEnv: z.enum(['development', 'production', 'test']).default('development'),
	apiBaseUrl: z.string().url('Invalid API base URL'),
	frontendUrl: z.string().url('Invalid frontend URL'),
	corsOrigin: z.string().url('Invalid CORS origin'),
});

const RedisConfigSchema = z.object({
	url: z.string().url('Invalid Redis URL'),
	password: z.string().optional(),
	db: z.number().int().min(0).max(15).default(0),
	keyPrefix: z.string().default('printweditt:'),
	retryDelayOnFailover: z.number().int().positive().default(100),
	maxRetriesPerRequest: z.number().int().positive().default(3),
});

const AWSConfigSchema = z.object({
	accessKeyId: z.string().min(1, 'AWS access key ID is required'),
	secretAccessKey: z.string().min(1, 'AWS secret access key is required'),
	region: z.string().default('us-east-1'),
	s3Bucket: z.string().min(1, 'S3 bucket name is required'),
	cloudfrontUrl: z.string().url('Invalid CloudFront URL').optional(),
});

const EmailConfigSchema = z.object({
	service: z.string().default('gmail'),
	host: z.string().default('smtp.gmail.com'),
	port: z.number().int().positive().default(587),
	secure: z.boolean().default(false),
	user: z.string().email('Invalid email address'),
	password: z.string().min(1, 'Email password is required'),
	from: z.string().email('Invalid from email address'),
});

const LoggingConfigSchema = z.object({
	level: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
	file: z.string().optional(),
	enableConsole: z.boolean().default(true),
	enableFile: z.boolean().default(false),
	maxFileSize: z.number().int().positive().default(10485760), // 10MB
	maxFiles: z.number().int().positive().default(5),
	format: z.enum(['json', 'simple']).default('json'),
});

const SecurityConfigSchema = z.object({
	bcryptRounds: z.number().int().min(10).max(14).default(12),
	cookieSecret: z
		.string()
		.min(32, 'Cookie secret must be at least 32 characters'),
	rateLimitWindowMs: z.number().int().positive().default(900000), // 15 minutes
	rateLimitMaxRequests: z.number().int().positive().default(100),
	rateLimitAuthMax: z.number().int().positive().default(5),
	maxFileSize: z.number().int().positive().default(52428800), // 50MB
	allowedFileTypes: z
		.array(z.string())
		.default(['jpg', 'jpeg', 'png', 'webp', 'pdf', 'ai', 'psd']),
});

const GoogleOAuthConfigSchema = z.object({
	clientId: z.string().min(1, 'Google client ID is required'),
	clientSecret: z.string().min(1, 'Google client secret is required'),
	callbackUrl: z.string().url('Invalid Google callback URL'),
});

const AppConfigSchema = z.object({
	database: DatabaseConfigSchema,
	jwt: JWTConfigSchema,
	server: ServerConfigSchema,
	redis: RedisConfigSchema,
	aws: AWSConfigSchema,
	email: EmailConfigSchema,
	logging: LoggingConfigSchema,
	security: SecurityConfigSchema,
	googleOAuth: GoogleOAuthConfigSchema,
	enableSwagger: z.boolean().default(true),
	enableLogging: z.boolean().default(true),
	enableCors: z.boolean().default(true),
});

// Configuration types
export type AppConfig = z.infer<typeof AppConfigSchema>;
export type DatabaseConfig = z.infer<typeof DatabaseConfigSchema>;
export type JWTConfig = z.infer<typeof JWTConfigSchema>;
export type ServerConfig = z.infer<typeof ServerConfigSchema>;
export type RedisConfig = z.infer<typeof RedisConfigSchema>;
export type AWSConfig = z.infer<typeof AWSConfigSchema>;
export type EmailConfig = z.infer<typeof EmailConfigSchema>;
export type LoggingConfig = z.infer<typeof LoggingConfigSchema>;
export type SecurityConfig = z.infer<typeof SecurityConfigSchema>;
export type GoogleOAuthConfig = z.infer<typeof GoogleOAuthConfigSchema>;

// Configuration class
export class ConfigurationManager {
	private static instance: ConfigurationManager;
	private config: AppConfig;
	private isInitialized = false;

	private constructor() {
		this.config = this.loadConfiguration();
	}

	public static getInstance(): ConfigurationManager {
		if (!ConfigurationManager.instance) {
			ConfigurationManager.instance = new ConfigurationManager();
		}
		return ConfigurationManager.instance;
	}

	public static isInitialized(): boolean {
		return ConfigurationManager.instance !== undefined;
	}

	private loadConfiguration(): AppConfig {
		try {
			const rawConfig = {
				database: {
					url: process.env.DATABASE_URL!,
					maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '10'),
					idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
					connectionTimeout: parseInt(
						process.env.DB_CONNECTION_TIMEOUT || '2000'
					),
				},
				jwt: {
					secret: process.env.JWT_SECRET!,
					refreshSecret: process.env.JWT_REFRESH_SECRET!,
					accessExpiresIn: process.env.JWT_ACCESS_EXPIRES_IN || '15m',
					refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
					issuer: process.env.JWT_ISSUER || 'printweditt',
					audience: process.env.JWT_AUDIENCE || 'printweditt-users',
				},
				server: {
					port: parseInt(process.env.PORT || '3000'),
					host: process.env.HOST || 'localhost',
					nodeEnv:
						(process.env.NODE_ENV as 'development' | 'production' | 'test') ||
						'development',
					apiBaseUrl: process.env.API_BASE_URL!,
					frontendUrl: process.env.FRONTEND_URL!,
					corsOrigin: process.env.CORS_ORIGIN!,
				},
				redis: {
					url: process.env.REDIS_URL!,
					password: process.env.REDIS_PASSWORD,
					db: parseInt(process.env.REDIS_DB || '0'),
					keyPrefix: process.env.REDIS_KEY_PREFIX || 'printweditt:',
					retryDelayOnFailover: parseInt(
						process.env.REDIS_RETRY_DELAY || '100'
					),
					maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES || '3'),
				},
				aws: {
					accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
					secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
					region: process.env.AWS_REGION || 'us-east-1',
					s3Bucket: process.env.AWS_S3_BUCKET!,
					cloudfrontUrl: process.env.AWS_CLOUDFRONT_URL,
				},
				email: {
					service: process.env.EMAIL_SERVICE || 'gmail',
					host: process.env.EMAIL_HOST || 'smtp.gmail.com',
					port: parseInt(process.env.EMAIL_PORT || '587'),
					secure: process.env.EMAIL_SECURE === 'true',
					user: process.env.EMAIL_USER!,
					password: process.env.EMAIL_PASSWORD!,
					from: process.env.EMAIL_FROM!,
				},
				logging: {
					level:
						(process.env.LOG_LEVEL as 'error' | 'warn' | 'info' | 'debug') ||
						'info',
					file: process.env.LOG_FILE,
					enableConsole: process.env.LOG_ENABLE_CONSOLE !== 'false',
					enableFile: process.env.LOG_ENABLE_FILE === 'true',
					maxFileSize: parseInt(process.env.LOG_MAX_FILE_SIZE || '10485760'),
					maxFiles: parseInt(process.env.LOG_MAX_FILES || '5'),
					format: (process.env.LOG_FORMAT as 'json' | 'simple') || 'json',
				},
				security: {
					bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),
					cookieSecret: process.env.COOKIE_SECRET!,
					rateLimitWindowMs: parseInt(
						process.env.RATE_LIMIT_WINDOW_MS || '900000'
					),
					rateLimitMaxRequests: parseInt(
						process.env.RATE_LIMIT_MAX_REQUESTS || '100'
					),
					rateLimitAuthMax: parseInt(process.env.RATE_LIMIT_AUTH_MAX || '5'),
					maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '52428800'),
					allowedFileTypes: process.env.ALLOWED_FILE_TYPES?.split(',') || [
						'jpg',
						'jpeg',
						'png',
						'webp',
						'pdf',
						'ai',
						'psd',
					],
				},
				googleOAuth: {
					clientId: process.env.GOOGLE_CLIENT_ID!,
					clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
					callbackUrl: process.env.GOOGLE_CALLBACK_URL!,
				},
				enableSwagger: process.env.ENABLE_SWAGGER !== 'false',
				enableLogging: process.env.ENABLE_LOGGING !== 'false',
				enableCors: process.env.ENABLE_CORS !== 'false',
			};

			// Validate configuration
			const validatedConfig = AppConfigSchema.parse(rawConfig);

			console.log('✅ Configuration loaded and validated successfully');
			return validatedConfig;
		} catch (error) {
			if (error instanceof z.ZodError) {
				console.error('❌ Configuration validation failed:');
				console.error('Validation errors:', error.format());
			} else {
				console.error('❌ Failed to load configuration:', error);
			}
			process.exit(1);
		}
	}

	public getConfig(): AppConfig {
		if (!this.isInitialized) {
			this.isInitialized = true;
		}
		return this.config;
	}

	public getDatabaseConfig(): DatabaseConfig {
		return this.config.database;
	}

	public getJWTConfig(): JWTConfig {
		return this.config.jwt;
	}

	public getServerConfig(): ServerConfig {
		return this.config.server;
	}

	public getRedisConfig(): RedisConfig {
		return this.config.redis;
	}

	public getAWSConfig(): AWSConfig {
		return this.config.aws;
	}

	public getEmailConfig(): EmailConfig {
		return this.config.email;
	}

	public getLoggingConfig(): LoggingConfig {
		return this.config.logging;
	}

	public getSecurityConfig(): SecurityConfig {
		return this.config.security;
	}

	public getGoogleOAuthConfig(): GoogleOAuthConfig {
		return this.config.googleOAuth;
	}

	public isDevelopment(): boolean {
		return this.config.server.nodeEnv === 'development';
	}

	public isProduction(): boolean {
		return this.config.server.nodeEnv === 'production';
	}

	public isTest(): boolean {
		return this.config.server.nodeEnv === 'test';
	}

	public reload(): void {
		console.log('🔄 Reloading configuration...');
		this.config = this.loadConfiguration();
	}
}
