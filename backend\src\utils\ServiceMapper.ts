import { ServicePricingType } from '@prisma/client';
import {
  ServiceDetail,
  ServiceSummary,
  ServiceCategoryDetail,
  ServiceCategorySummary,
  ServiceFormFieldDetail,
  ServiceFormFieldOptionDetail,
  ProviderServiceDetail,
} from '../types/service';

/**
 * ServiceMapper utility class
 * Centralizes all data transformation logic for service-related entities
 * Eliminates duplication across repository methods
 */
export class ServiceMapper {
  /**
   * Maps Prisma service result to ServiceSummary
   */
  static toServiceSummary(service: any): ServiceSummary {
    return {
      id: service.id,
      name: service.name,
      description: service.description,
      categoryId: service.categoryId,
      categoryName: service.service_categories?.name || service.categoryName,
      image: service.image,
      basePrice: Number(service.basePrice),
      pricingType: service.pricingType as ServicePricingType,
      isActive: service.isActive,
      sortOrder: service.sortOrder,
      providerCount: service.providers?.length || service.providerCount || 0,
      createdAt: service.createdAt,
      updatedAt: service.updatedAt,
    };
  }

  /**
   * Maps Prisma service result to ServiceDetail
   */
  static toServiceDetail(service: any): ServiceDetail {
    return {
      id: service.id,
      name: service.name,
      description: service.description,
      detailedDesc: service.detailedDesc || null,
      features: service.features || [],
      notes: service.notes || null,
      categoryId: service.categoryId,
      categoryName: service.service_categories?.name || service.categoryName,
      image: service.image,
      basePrice: Number(service.basePrice),
      pricingType: service.pricingType as ServicePricingType,
      isActive: service.isActive,
      sortOrder: service.sortOrder,
      providerCount: service.providers?.length || service.providerCount || 0,
      createdAt: service.createdAt,
      updatedAt: service.updatedAt,
      formFields: service.formFields ? this.toFormFieldDetailArray(service.formFields) : [],
      category: service.service_categories ? this.toServiceCategoryDetail(service.service_categories) : {
        id: service.categoryId,
        name: service.categoryName || '',
        description: null,
        icon: null,
        route: '',
        isActive: true,
        sortOrder: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      providers: service.providers ? this.toProviderServiceDetailArray(service.providers) : [],
    };
  }

  /**
   * Maps Prisma category result to ServiceCategorySummary
   */
  static toServiceCategorySummary(category: any): ServiceCategorySummary {
    return {
      id: category.id,
      name: category.name,
      description: category.description || null,
      icon: category.icon || null,
      route: category.route,
      isActive: category.isActive,
      sortOrder: category.sortOrder,
      serviceCount: category._count?.services || category.services?.length || category.serviceCount || 0,
    };
  }

  /**
   * Maps Prisma category result to ServiceCategoryDetail
   */
  static toServiceCategoryDetail(category: any): ServiceCategoryDetail {
    return {
      id: category.id,
      name: category.name,
      description: category.description || null,
      icon: category.icon || null,
      route: category.route,
      isActive: category.isActive,
      sortOrder: category.sortOrder,
      serviceCount: category.services?.length || category.serviceCount || 0,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
      services: category.services ? this.toServiceSummaryArray(category.services) : [],
    };
  }

  /**
   * Maps Prisma form field result to ServiceFormFieldDetail
   */
  static toFormFieldDetail(field: any): ServiceFormFieldDetail {
    return {
      id: field.id,
      serviceId: field.serviceId,
      name: field.name,
      label: field.label,
      type: field.type,
      required: field.required,
      placeholder: field.placeholder || null,
      defaultValue: field.defaultValue || null,
      validation: field.validation,
      sortOrder: field.sortOrder,
      isActive: field.isActive,
      options: field.service_field_options ? this.toFormFieldOptionDetailArray(field.service_field_options) : [],
      createdAt: field.createdAt,
    };
  }

  /**
   * Maps Prisma form field option result to ServiceFormFieldOptionDetail
   */
  static toFormFieldOptionDetail(option: any): ServiceFormFieldOptionDetail {
    return {
      id: option.id,
      fieldId: option.fieldId,
      value: option.value,
      label: option.label,
      priceModifier: option.priceModifier ? Number(option.priceModifier) : null,
      sortOrder: option.sortOrder,
      isActive: option.isActive,
      createdAt: option.createdAt,
    };
  }

  /**
   * Maps Prisma provider service result to ProviderServiceDetail
   */
  static toProviderServiceDetail(providerService: any): ProviderServiceDetail {
    return {
      id: providerService.id,
      providerId: providerService.providerId,
      serviceId: providerService.serviceId,
      price: Number(providerService.price),
      description: providerService.description,
      isActive: providerService.isActive,
      provider: providerService.provider,
      createdAt: providerService.createdAt,
      updatedAt: providerService.updatedAt,
    };
  }

  /**
   * Array mapping utilities
   */
  static toServiceSummaryArray(services: any[]): ServiceSummary[] {
    return services.map(service => this.toServiceSummary(service));
  }

  static toServiceDetailArray(services: any[]): ServiceDetail[] {
    return services.map(service => this.toServiceDetail(service));
  }

  static toServiceCategorySummaryArray(categories: any[]): ServiceCategorySummary[] {
    return categories.map(category => this.toServiceCategorySummary(category));
  }

  static toServiceCategoryDetailArray(categories: any[]): ServiceCategoryDetail[] {
    return categories.map(category => this.toServiceCategoryDetail(category));
  }

  static toFormFieldDetailArray(fields: any[]): ServiceFormFieldDetail[] {
    return fields.map(field => this.toFormFieldDetail(field));
  }

  static toFormFieldOptionDetailArray(options: any[]): ServiceFormFieldOptionDetail[] {
    return options.map(option => this.toFormFieldOptionDetail(option));
  }

  static toProviderServiceDetailArray(providerServices: any[]): ProviderServiceDetail[] {
    return providerServices.map(ps => this.toProviderServiceDetail(ps));
  }

  /**
   * Filter mapping utilities for search results
   */
  static toCategoryFilter(category: any): { id: string; name: string; count: number } {
    return {
      id: category.id,
      name: category.name,
      count: category._count?.services || 0,
    };
  }

  static toFeatureFilter(feature: string, count: number): { feature: string; count: number } {
    return {
      feature,
      count,
    };
  }

  static toPriceRangeFilter(priceRange: any): { min: number; max: number } {
    return {
      min: Number(priceRange._min?.basePrice) || 0,
      max: Number(priceRange._max?.basePrice) || 0,
    };
  }
}