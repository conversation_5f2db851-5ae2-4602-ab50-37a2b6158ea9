"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const auth_2 = require("../validation/auth");
const auth_3 = require("../validation/auth");
const requestLogger_1 = require("../middleware/requestLogger");
const logger_1 = require("../utils/logger");
const config_1 = require("../config");
const AuthRepository_1 = require("../repositories/AuthRepository");
const AuthService_1 = require("../services/AuthService");
const AuthController_1 = require("../controllers/AuthController");
const router = express_1.default.Router();
let authController = null;
function getAuthController() {
    if (!authController) {
        const { databaseService } = require('../services');
        const { createEmailService } = require('../services/EmailService');
        const prisma = databaseService.getClient();
        const emailService = createEmailService(config_1.config.getEmailConfig());
        const authRepository = new AuthRepository_1.AuthRepository(prisma);
        const authService = new AuthService_1.AuthService(authRepository, emailService, config_1.config.getJWTConfig(), config_1.config.getSecurityConfig());
        authController = (0, AuthController_1.createAuthController)(authService);
        logger_1.authLogger.info('Authentication controller initialized', {
            jwtIssuer: config_1.config.getJWTConfig().issuer,
            jwtAudience: config_1.config.getJWTConfig().audience,
            accessTokenExpiry: config_1.config.getJWTConfig().accessExpiresIn,
            refreshTokenExpiry: config_1.config.getJWTConfig().refreshExpiresIn,
            bcryptRounds: config_1.config.getSecurityConfig().bcryptRounds,
            rateLimitAuthMax: config_1.config.getSecurityConfig().rateLimitAuthMax,
            rateLimitWindowMs: config_1.config.getSecurityConfig().rateLimitWindowMs,
        });
    }
    return authController;
}
router.post('/register', (0, auth_1.authRateLimit)(config_1.config.getSecurityConfig().rateLimitAuthMax, config_1.config.getSecurityConfig().rateLimitWindowMs), (0, auth_2.validate)(auth_3.registerSchema), (0, requestLogger_1.logAuthEvent)('REGISTER'), (req, res, next) => getAuthController().register(req, res, next));
router.post('/login', (0, auth_1.authRateLimit)(config_1.config.getSecurityConfig().rateLimitAuthMax, config_1.config.getSecurityConfig().rateLimitWindowMs), (0, auth_2.validate)(auth_3.loginSchema), (0, requestLogger_1.logAuthEvent)('LOGIN'), (req, res, next) => getAuthController().login(req, res, next));
router.post('/refresh', (0, auth_2.validate)(auth_3.refreshTokenSchema), (0, requestLogger_1.logAuthEvent)('TOKEN_REFRESH'), (req, res, next) => getAuthController().refreshTokens(req, res, next));
router.post('/logout', auth_1.authenticate, (0, requestLogger_1.logAuthEvent)('LOGOUT'), (req, res, next) => getAuthController().logout(req, res, next));
router.post('/logout-all', auth_1.authenticate, (0, requestLogger_1.logAuthEvent)('LOGOUT'), (req, res, next) => getAuthController().logoutAllDevices(req, res, next));
router.get('/me', auth_1.authenticate, (req, res, next) => getAuthController().getProfile(req, res, next));
router.patch('/profile', auth_1.authenticate, (0, auth_2.validate)(auth_3.updateProfileSchema), (req, res, next) => getAuthController().updateProfile(req, res, next));
router.post('/change-password', auth_1.authenticate, (0, auth_2.validate)(auth_3.changePasswordSchema), (0, requestLogger_1.logAuthEvent)('PASSWORD_CHANGE'), (req, res, next) => getAuthController().changePassword(req, res, next));
router.get('/sessions', auth_1.authenticate, (req, res, next) => getAuthController().getUserSessions(req, res, next));
router.delete('/sessions/:sessionId', auth_1.authenticate, (req, res, next) => getAuthController().revokeSession(req, res, next));
router.post('/password-reset/request', (0, auth_1.authRateLimit)(config_1.config.getSecurityConfig().rateLimitAuthMax, config_1.config.getSecurityConfig().rateLimitWindowMs), (0, auth_2.validate)(auth_3.passwordResetRequestSchema), (0, requestLogger_1.logAuthEvent)('PASSWORD_RESET_REQUEST'), (req, res, next) => getAuthController().requestPasswordReset(req, res, next));
router.post('/password-reset/confirm', (0, auth_1.authRateLimit)(config_1.config.getSecurityConfig().rateLimitAuthMax, config_1.config.getSecurityConfig().rateLimitWindowMs), (0, auth_2.validate)(auth_3.passwordResetConfirmSchema), (0, requestLogger_1.logAuthEvent)('PASSWORD_RESET_CONFIRM'), (req, res, next) => getAuthController().resetPassword(req, res, next));
router.post('/email/verify/send', auth_1.authenticate, (0, requestLogger_1.logAuthEvent)('EMAIL_VERIFICATION_SEND'), (req, res, next) => getAuthController().sendEmailVerification(req, res, next));
router.post('/email/verify/confirm', (0, auth_2.validate)(auth_3.emailVerificationSchema), (0, requestLogger_1.logAuthEvent)('EMAIL_VERIFICATION_CONFIRM'), (req, res, next) => getAuthController().verifyEmail(req, res, next));
exports.default = router;
//# sourceMappingURL=auth.js.map