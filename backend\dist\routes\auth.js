"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const auth_1 = require("../types/auth");
const auth_2 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_3 = require("../validation/auth");
const auth_4 = require("../utils/auth");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
router.post('/register', (0, auth_2.authRateLimit)(5, 15 * 60 * 1000), (0, auth_3.validate)(auth_3.registerSchema), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password, firstName, lastName, phone } = req.body;
    const ipAddress = (0, auth_4.getClientIP)(req);
    const userAgent = (0, auth_4.getUserAgent)(req);
    const existingUser = await prisma.user.findUnique({
        where: { email: email.toLowerCase() }
    });
    if (existingUser) {
        await (0, auth_4.logSecurityEvent)({
            type: auth_1.SecurityEventType.LOGIN_FAILURE,
            email: email.toLowerCase(),
            ipAddress,
            userAgent,
            metadata: { reason: 'Email already registered' }
        });
        throw new errorHandler_1.ConflictError('Email address is already registered');
    }
    const hashedPassword = await (0, auth_4.hashPassword)(password);
    const user = await prisma.user.create({
        data: {
            email: email.toLowerCase(),
            password: hashedPassword,
            firstName: firstName.trim(),
            lastName: lastName.trim(),
            phone: phone?.trim() || null,
            role: client_1.UserRole.CUSTOMER,
            isActive: true,
            isVerified: false
        }
    });
    const tokens = await (0, auth_4.generateTokenPair)(user.id, user.email, user.role, ipAddress, userAgent);
    await (0, auth_4.logSecurityEvent)({
        type: auth_1.SecurityEventType.LOGIN_SUCCESS,
        userId: user.id,
        email: user.email,
        ipAddress,
        userAgent,
        metadata: { action: 'registration' }
    });
    const sanitizedUser = (0, auth_4.sanitizeUser)(user);
    res.status(201).json((0, auth_4.createApiResponse)(true, {
        user: sanitizedUser,
        tokens
    }, 'Account created successfully'));
}));
router.post('/login', (0, auth_2.authRateLimit)(5, 15 * 60 * 1000), (0, auth_3.validate)(auth_3.loginSchema), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password } = req.body;
    const ipAddress = (0, auth_4.getClientIP)(req);
    const userAgent = (0, auth_4.getUserAgent)(req);
    const user = await prisma.user.findUnique({
        where: { email: email.toLowerCase() }
    });
    if (!user) {
        await (0, auth_4.logSecurityEvent)({
            type: auth_1.SecurityEventType.LOGIN_FAILURE,
            email: email.toLowerCase(),
            ipAddress,
            userAgent,
            metadata: { reason: 'User not found' }
        });
        throw new errorHandler_1.AuthenticationError('Invalid email or password');
    }
    const isLocked = await (0, auth_4.isAccountLocked)(user.id);
    if (isLocked) {
        const lockInfo = await (0, auth_4.getAccountLockInfo)(user.id);
        await (0, auth_4.logSecurityEvent)({
            type: auth_1.SecurityEventType.LOGIN_FAILURE,
            userId: user.id,
            email: user.email,
            ipAddress,
            userAgent,
            metadata: { reason: 'Account locked', lockInfo }
        });
        throw new errorHandler_1.AuthenticationError(`Account is temporarily locked. Try again after ${lockInfo?.lockExpires?.toLocaleTimeString()}`);
    }
    if (!user.isActive) {
        await (0, auth_4.logSecurityEvent)({
            type: auth_1.SecurityEventType.LOGIN_FAILURE,
            userId: user.id,
            email: user.email,
            ipAddress,
            userAgent,
            metadata: { reason: 'Account deactivated' }
        });
        throw new errorHandler_1.AuthenticationError('Account is deactivated');
    }
    if (!user.password) {
        throw new errorHandler_1.AuthenticationError('Password not set. Please use password reset.');
    }
    const isPasswordValid = await (0, auth_4.verifyPassword)(password, user.password);
    if (!isPasswordValid) {
        await (0, auth_4.incrementLoginAttempts)(user.id);
        await (0, auth_4.logSecurityEvent)({
            type: auth_1.SecurityEventType.LOGIN_FAILURE,
            userId: user.id,
            email: user.email,
            ipAddress,
            userAgent,
            metadata: { reason: 'Invalid password' }
        });
        throw new errorHandler_1.AuthenticationError('Invalid email or password');
    }
    await (0, auth_4.resetLoginAttempts)(user.id);
    const tokens = await (0, auth_4.generateTokenPair)(user.id, user.email, user.role, ipAddress, userAgent);
    await (0, auth_4.logSecurityEvent)({
        type: auth_1.SecurityEventType.LOGIN_SUCCESS,
        userId: user.id,
        email: user.email,
        ipAddress,
        userAgent
    });
    const sanitizedUser = (0, auth_4.sanitizeUser)(user);
    res.json((0, auth_4.createApiResponse)(true, {
        user: sanitizedUser,
        tokens
    }, 'Login successful'));
}));
router.post('/refresh', (0, auth_3.validate)(auth_3.refreshTokenSchema), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { refreshToken } = req.body;
    const ipAddress = (0, auth_4.getClientIP)(req);
    const userAgent = (0, auth_4.getUserAgent)(req);
    const payload = (0, auth_2.verifyRefreshToken)(refreshToken);
    const isValid = await (0, auth_4.isRefreshTokenValid)(refreshToken);
    if (!isValid) {
        throw new errorHandler_1.AuthenticationError('Invalid or expired refresh token');
    }
    const user = await prisma.user.findUnique({
        where: { id: payload.userId }
    });
    if (!user || !user.isActive) {
        throw new errorHandler_1.AuthenticationError('User not found or inactive');
    }
    await (0, auth_4.revokeRefreshToken)(refreshToken);
    const tokens = await (0, auth_4.generateTokenPair)(user.id, user.email, user.role, ipAddress, userAgent);
    await (0, auth_4.logSecurityEvent)({
        type: auth_1.SecurityEventType.TOKEN_REFRESH,
        userId: user.id,
        email: user.email,
        ipAddress,
        userAgent
    });
    res.json((0, auth_4.createApiResponse)(true, { tokens }, 'Tokens refreshed successfully'));
}));
router.post('/logout', auth_2.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const refreshToken = req.body.refreshToken;
    const ipAddress = (0, auth_4.getClientIP)(req);
    const userAgent = (0, auth_4.getUserAgent)(req);
    if (refreshToken) {
        await (0, auth_4.revokeRefreshToken)(refreshToken);
    }
    await (0, auth_4.logSecurityEvent)({
        type: auth_1.SecurityEventType.LOGOUT,
        userId: req.user.id,
        email: req.user.email,
        ipAddress,
        userAgent
    });
    res.json((0, auth_4.createApiResponse)(true, null, 'Logged out successfully'));
}));
router.post('/logout-all', auth_2.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const ipAddress = (0, auth_4.getClientIP)(req);
    const userAgent = (0, auth_4.getUserAgent)(req);
    await (0, auth_4.revokeAllUserSessions)(req.user.id);
    await (0, auth_4.logSecurityEvent)({
        type: auth_1.SecurityEventType.LOGOUT,
        userId: req.user.id,
        email: req.user.email,
        ipAddress,
        userAgent,
        metadata: { action: 'logout_all_devices' }
    });
    res.json((0, auth_4.createApiResponse)(true, null, 'Logged out from all devices'));
}));
router.get('/me', auth_2.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const user = await prisma.user.findUnique({
        where: { id: req.user.id },
        select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            role: true,
            isActive: true,
            isVerified: true,
            avatar: true,
            phone: true,
            createdAt: true,
            updatedAt: true,
            lastLoginAt: true
        }
    });
    if (!user) {
        throw new errorHandler_1.NotFoundError('User not found');
    }
    res.json((0, auth_4.createApiResponse)(true, { user }));
}));
router.patch('/profile', auth_2.authenticate, (0, auth_3.validate)(auth_3.updateProfileSchema), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { firstName, lastName, phone, avatar } = req.body;
    const updatedUser = await prisma.user.update({
        where: { id: req.user.id },
        data: {
            ...(firstName && { firstName: firstName.trim() }),
            ...(lastName && { lastName: lastName.trim() }),
            ...(phone !== undefined && { phone: phone?.trim() || null }),
            ...(avatar !== undefined && { avatar: avatar?.trim() || null }),
        },
        select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            role: true,
            isActive: true,
            isVerified: true,
            avatar: true,
            phone: true,
            createdAt: true,
            updatedAt: true,
            lastLoginAt: true
        }
    });
    res.json((0, auth_4.createApiResponse)(true, { user: updatedUser }, 'Profile updated successfully'));
}));
router.post('/change-password', auth_2.authenticate, (0, auth_3.validate)(auth_3.changePasswordSchema), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { currentPassword, newPassword } = req.body;
    const ipAddress = (0, auth_4.getClientIP)(req);
    const userAgent = (0, auth_4.getUserAgent)(req);
    const user = await prisma.user.findUnique({
        where: { id: req.user.id },
        select: { id: true, email: true, password: true }
    });
    if (!user || !user.password) {
        throw new errorHandler_1.AuthenticationError('Current password verification failed');
    }
    const isCurrentPasswordValid = await (0, auth_4.verifyPassword)(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
        throw new errorHandler_1.AuthenticationError('Current password is incorrect');
    }
    const hashedNewPassword = await (0, auth_4.hashPassword)(newPassword);
    await prisma.user.update({
        where: { id: user.id },
        data: { password: hashedNewPassword }
    });
    await (0, auth_4.revokeAllUserSessions)(user.id);
    await (0, auth_4.logSecurityEvent)({
        type: auth_1.SecurityEventType.PASSWORD_CHANGE,
        userId: user.id,
        email: user.email,
        ipAddress,
        userAgent
    });
    res.json((0, auth_4.createApiResponse)(true, null, 'Password changed successfully. Please log in again.'));
}));
router.get('/sessions', auth_2.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const sessions = await prisma.user_sessions.findMany({
        where: {
            userId: req.user.id,
            isActive: true,
            expiresAt: { gt: new Date() }
        },
        select: {
            id: true,
            ipAddress: true,
            userAgent: true,
            createdAt: true,
            expiresAt: true,
            isActive: true
        },
        orderBy: { createdAt: 'desc' }
    });
    res.json((0, auth_4.createApiResponse)(true, { sessions }));
}));
router.delete('/sessions/:sessionId', auth_2.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { sessionId } = req.params;
    const session = await prisma.user_sessions.findFirst({
        where: {
            id: sessionId,
            userId: req.user.id
        }
    });
    if (!session) {
        throw new errorHandler_1.NotFoundError('Session not found');
    }
    await prisma.user_sessions.update({
        where: { id: sessionId },
        data: {
            isActive: false,
            revokedAt: new Date()
        }
    });
    res.json((0, auth_4.createApiResponse)(true, null, 'Session revoked successfully'));
}));
exports.default = router;
//# sourceMappingURL=auth.js.map