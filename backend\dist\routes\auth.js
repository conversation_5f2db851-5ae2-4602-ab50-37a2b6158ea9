"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const auth_1 = require("../middleware/auth");
const auth_2 = require("../validation/auth");
const auth_3 = require("../validation/auth");
const requestLogger_1 = require("../middleware/requestLogger");
const logger_1 = require("../utils/logger");
const config_1 = require("../config");
const AuthRepository_1 = require("../repositories/AuthRepository");
const AuthService_1 = require("../services/AuthService");
const AuthController_1 = require("../controllers/AuthController");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
const authRepository = new AuthRepository_1.AuthRepository(prisma);
const authService = new AuthService_1.AuthService(authRepository, config_1.config.getJWTConfig(), config_1.config.getSecurityConfig());
const authController = (0, AuthController_1.createAuthController)(authService);
logger_1.authLogger.info('Setting up authentication routes', {
    jwtIssuer: config_1.config.getJWTConfig().issuer,
    jwtAudience: config_1.config.getJWTConfig().audience,
    accessTokenExpiry: config_1.config.getJWTConfig().accessExpiresIn,
    refreshTokenExpiry: config_1.config.getJWTConfig().refreshExpiresIn,
    bcryptRounds: config_1.config.getSecurityConfig().bcryptRounds,
    rateLimitAuthMax: config_1.config.getSecurityConfig().rateLimitAuthMax,
    rateLimitWindowMs: config_1.config.getSecurityConfig().rateLimitWindowMs,
});
router.post('/register', (0, auth_1.authRateLimit)(config_1.config.getSecurityConfig().rateLimitAuthMax, config_1.config.getSecurityConfig().rateLimitWindowMs), (0, auth_2.validate)(auth_3.registerSchema), (0, requestLogger_1.logAuthEvent)('REGISTER'), authController.register);
router.post('/login', (0, auth_1.authRateLimit)(config_1.config.getSecurityConfig().rateLimitAuthMax, config_1.config.getSecurityConfig().rateLimitWindowMs), (0, auth_2.validate)(auth_3.loginSchema), (0, requestLogger_1.logAuthEvent)('LOGIN'), authController.login);
router.post('/refresh', (0, auth_2.validate)(auth_3.refreshTokenSchema), (0, requestLogger_1.logAuthEvent)('TOKEN_REFRESH'), authController.refreshTokens);
router.post('/logout', auth_1.authenticate, (0, requestLogger_1.logAuthEvent)('LOGOUT'), authController.logout);
router.post('/logout-all', auth_1.authenticate, (0, requestLogger_1.logAuthEvent)('LOGOUT'), authController.logoutAllDevices);
router.get('/me', auth_1.authenticate, authController.getProfile);
router.patch('/profile', auth_1.authenticate, (0, auth_2.validate)(auth_3.updateProfileSchema), authController.updateProfile);
router.post('/change-password', auth_1.authenticate, (0, auth_2.validate)(auth_3.changePasswordSchema), (0, requestLogger_1.logAuthEvent)('PASSWORD_CHANGE'), authController.changePassword);
router.get('/sessions', auth_1.authenticate, authController.getUserSessions);
router.delete('/sessions/:sessionId', auth_1.authenticate, authController.revokeSession);
exports.default = router;
//# sourceMappingURL=auth.js.map