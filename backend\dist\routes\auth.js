"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const auth_1 = require("../middleware/auth");
const auth_2 = require("../validation/auth");
const auth_3 = require("../validation/auth");
const requestLogger_1 = require("../middleware/requestLogger");
const logger_1 = require("../utils/logger");
const AuthRepository_1 = require("../repositories/AuthRepository");
const AuthService_1 = require("../services/AuthService");
const AuthController_1 = require("../controllers/AuthController");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
const authRepository = new AuthRepository_1.AuthRepository(prisma);
const authService = new AuthService_1.AuthService(authRepository);
const authController = (0, AuthController_1.createAuthController)(authService);
logger_1.authLogger.info('Setting up authentication routes');
router.post('/register', (0, auth_1.authRateLimit)(5, 15 * 60 * 1000), (0, auth_2.validate)(auth_3.registerSchema), (0, requestLogger_1.logAuthEvent)('REGISTER'), authController.register);
router.post('/login', (0, auth_1.authRateLimit)(5, 15 * 60 * 1000), (0, auth_2.validate)(auth_3.loginSchema), (0, requestLogger_1.logAuthEvent)('LOGIN'), authController.login);
router.post('/refresh', (0, auth_2.validate)(auth_3.refreshTokenSchema), (0, requestLogger_1.logAuthEvent)('TOKEN_REFRESH'), authController.refreshTokens);
router.post('/logout', auth_1.authenticate, (0, requestLogger_1.logAuthEvent)('LOGOUT'), authController.logout);
router.post('/logout-all', auth_1.authenticate, (0, requestLogger_1.logAuthEvent)('LOGOUT'), authController.logoutAllDevices);
router.get('/me', auth_1.authenticate, authController.getProfile);
router.patch('/profile', auth_1.authenticate, (0, auth_2.validate)(auth_3.updateProfileSchema), authController.updateProfile);
router.post('/change-password', auth_1.authenticate, (0, auth_2.validate)(auth_3.changePasswordSchema), (0, requestLogger_1.logAuthEvent)('PASSWORD_CHANGE'), authController.changePassword);
router.get('/sessions', auth_1.authenticate, authController.getUserSessions);
router.delete('/sessions/:sessionId', auth_1.authenticate, authController.revokeSession);
exports.default = router;
//# sourceMappingURL=auth.js.map