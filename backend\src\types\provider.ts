// Provider core types
export interface CreateProviderRequest {
  userId: string;
  businessName: string;
  description?: string;
  email?: string;
  website?: string;
  phone?: string;
}

export interface UpdateProviderRequest {
  businessName?: string;
  description?: string;
  email?: string;
  website?: string;
  phone?: string;
  isActive?: boolean;
  isVerified?: boolean;
}

export interface ProviderSummary {
  id: string;
  userId: string;
  businessName: string;
  isActive: boolean;
  isVerified: boolean;
  averageRating?: number;
  reviewCount?: number;
  serviceAreaZipCodes?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ProviderDetail extends ProviderSummary {
  description?: string | null;
  email?: string | null;
  website?: string | null;
  phone?: string | null;
  services?: ProviderServiceSummary[];
  operatingHours?: ProviderOperatingHour[];
  serviceAreas?: ProviderServiceArea[];
}

// Provider service association
export interface ProviderServiceSummary {
  id: string;
  providerId: string;
  serviceId: string;
  price: number;
  description?: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateProviderServiceRequest {
  serviceId: string;
  price: number;
  description?: string;
  isActive?: boolean;
}

export interface UpdateProviderServiceRequest {
  price?: number;
  description?: string;
  isActive?: boolean;
}

// Operating hours
export interface ProviderOperatingHour {
  id: string;
  providerId: string;
  dayOfWeek: number; // 0-6 (Sun-Sat)
  openTime: string; // HH:mm
  closeTime: string; // HH:mm
  createdAt: Date;
}

export interface UpsertOperatingHoursRequest {
  hours: Array<{
    dayOfWeek: number;
    openTime: string;
    closeTime: string;
  }>;
}

// Service areas
export interface ProviderServiceArea {
  id: string;
  providerId: string;
  streetAddress: string;
  zipCode: string;
  city: string;
  state: string;
  isActive: boolean;
  createdAt: Date;
}

export interface CreateServiceAreaRequest {
  streetAddress: string;
  zipCode: string;
  city: string;
  state: string;
  isActive?: boolean;
}

// Ratings (aggregate focused)
export interface ProviderRatingDisplay {
  providerId: string;
  averageRating: number;
  reviewCount: number;
  lastUpdated: Date;
}

export interface AddRatingRequest {
  rating: number; // 1-5
  comment?: string; // stored only transiently for now
}

// Search and discovery
export interface ProviderListQuery {
  page?: number;
  limit?: number;
  search?: string; // business name
  isVerified?: boolean;
  isActive?: boolean;
  serviceId?: string;
  zipCode?: string;
  radiusMiles?: number; // reserved for future geo
}

export interface ProviderListResponse {
  providers: ProviderSummary[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
