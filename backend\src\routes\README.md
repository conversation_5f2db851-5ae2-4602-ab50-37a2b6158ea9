# User Routes Documentation

## Overview

The user routes provide comprehensive user management functionality for the PrintWedittV1 API. The implementation follows the established layered architecture pattern with proper separation of concerns, validation, and error handling.

## Architecture

The user routes follow the established pattern:
- **Routes** (`/routes/user.ts`) - HTTP routing and middleware
- **Controller** (`/controllers/UserController.ts`) - HTTP request/response handling
- **Service** (`/services/UserService.ts`) - Business logic
- **Repository** (`/repositories/UserRepository.ts`) - Data access
- **Validation** (`/validation/user.ts`) - Input validation
- **Types** (`/types/user.ts`) - TypeScript interfaces

## Authentication & Authorization

- All routes require authentication via JWT token
- Admin-only routes require `ADMIN` role
- Users can access and modify their own profiles
- <PERSON><PERSON> can access and modify any user profile

## API Endpoints

### Basic CRUD Operations

#### GET /api/users
- **Description**: Get paginated list of users
- **Access**: Admin only
- **Query Parameters**:
  - `page` (number, default: 1) - Page number
  - `limit` (number, default: 20, max: 100) - Items per page
  - `search` (string) - Search term for name/email
  - `role` (string) - Filter by user role
  - `isActive` (boolean) - Filter by active status
  - `isVerified` (boolean) - Filter by verification status
  - `sortBy` (string) - Sort field (createdAt, updatedAt, firstName, lastName, email)
  - `sortOrder` (string) - Sort direction (asc, desc)

#### POST /api/users
- **Description**: Create new user
- **Access**: Admin only
- **Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "firstName": "John",
    "lastName": "Doe",
    "phone": "+1234567890",
    "role": "CUSTOMER",
    "isActive": true,
    "isVerified": false
  }
  ```

#### GET /api/users/:id
- **Description**: Get user by ID
- **Access**: Admin or own profile
- **Response**: User details including sensitive fields

#### PUT /api/users/:id
- **Description**: Update user
- **Access**: Admin or own profile
- **Body**: Partial user data (all fields optional)

#### DELETE /api/users/:id
- **Description**: Delete user
- **Access**: Admin only

### Search & Analytics

#### GET /api/users/search
- **Description**: Search users by name or email
- **Access**: Admin only
- **Query Parameters**:
  - `search` (string) - Search term
  - `limit` (number, default: 10) - Maximum results

#### GET /api/users/stats
- **Description**: Get user statistics
- **Access**: Admin only
- **Response**:
  ```json
  {
    "totalUsers": 100,
    "activeUsers": 85,
    "verifiedUsers": 70,
    "usersByRole": {
      "CUSTOMER": 80,
      "PROVIDER": 15,
      "ADMIN": 5
    },
    "recentRegistrations": 10,
    "newUsersThisMonth": 25
  }
  ```

#### GET /api/users/role/:role
- **Description**: Get users by specific role
- **Access**: Admin only

#### GET /api/users/active
- **Description**: Get all active users
- **Access**: Admin only

#### GET /api/users/verified
- **Description**: Get all verified users
- **Access**: Admin only

### Bulk Operations

#### POST /api/users/bulk/update
- **Description**: Bulk update multiple users
- **Access**: Admin only
- **Body**:
  ```json
  {
    "userIds": ["id1", "id2", "id3"],
    "action": "activate",
    "updates": {
      "isActive": true,
      "isVerified": true
    }
  }
  ```

#### POST /api/users/bulk/delete
- **Description**: Bulk delete multiple users
- **Access**: Admin only
- **Body**:
  ```json
  {
    "userIds": ["id1", "id2", "id3"],
    "action": "delete"
  }
  ```

### Utility Endpoints

#### GET /api/users/exists/:email
- **Description**: Check if user exists by email
- **Access**: Admin only

#### GET /api/users/count
- **Description**: Get total user count
- **Access**: Admin only

#### GET /api/users/count/:role
- **Description**: Get user count by role
- **Access**: Admin only

## Validation

All endpoints include comprehensive validation:

- **Email**: Valid email format, max 255 characters
- **Password**: Minimum 8 characters, must contain uppercase, lowercase, number, and special character
- **Names**: Letters, spaces, hyphens, apostrophes only, max 150 characters
- **Phone**: Valid phone number format, max 20 characters
- **Role**: Must be one of: CUSTOMER, PROVIDER, ADMIN
- **Pagination**: Page numbers, limits, and sorting parameters validated

## Error Handling

The implementation includes proper error handling for:

- **400 Bad Request**: Validation errors
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: User not found
- **409 Conflict**: Duplicate email addresses
- **500 Internal Server Error**: Server errors

## Testing

Comprehensive test suite included in `/tests/user.test.ts` covering:

- Authentication and authorization
- CRUD operations
- Validation scenarios
- Error cases
- Bulk operations
- Search and analytics

## Security Features

- Password hashing using bcrypt
- JWT-based authentication
- Role-based access control
- Input sanitization and validation
- Rate limiting (inherited from app-level middleware)
- CORS protection
- Helmet security headers

## Database Schema

The implementation works with the existing Prisma schema:

```prisma
model User {
  id             String           @id @default(cuid())
  email          String           @unique @db.VarChar(255)
  password       String?          @db.VarChar(255)
  firstName      String           @db.VarChar(150)
  lastName       String           @db.VarChar(150)
  role           UserRole         @default(CUSTOMER)
  isActive       Boolean          @default(true)
  isVerified     Boolean          @default(false)
  emailVerified  DateTime?
  avatar         String?          @db.VarChar(500)
  phone          String?          @db.VarChar(20)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  // ... other fields
}
```

## Usage Examples

### Creating a User (Admin)
```bash
curl -X POST /api/users \
  -H "Authorization: Bearer <admin-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "firstName": "Jane",
    "lastName": "Smith",
    "role": "CUSTOMER"
  }'
```

### Getting User List (Admin)
```bash
curl -X GET "/api/users?page=1&limit=20&search=john&role=CUSTOMER" \
  -H "Authorization: Bearer <admin-token>"
```

### Updating Own Profile
```bash
curl -X PUT /api/users/<user-id> \
  -H "Authorization: Bearer <user-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "Updated",
    "lastName": "Name"
  }'
```

## Future Enhancements

Potential improvements for future iterations:

1. **Soft Deletes**: Implement soft delete functionality
2. **Audit Logging**: Track user changes and actions
3. **Email Verification**: Implement email verification workflow
4. **Password Reset**: Add password reset functionality
5. **User Import/Export**: Bulk user import/export features
6. **Advanced Search**: Full-text search with filters
7. **User Activity Tracking**: Track user login/logout activity
8. **Profile Picture Upload**: File upload for user avatars 