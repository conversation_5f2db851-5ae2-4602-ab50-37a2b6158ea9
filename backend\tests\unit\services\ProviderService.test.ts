import { IProviderRepository } from '@/repositories/ProviderRepository';
import { ProviderService } from '@/services/ProviderService';

jest.mock('@/utils/logger', () => ({
  createLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  }),
}));

describe('ProviderService - Phase 2', () => {
  let repo: jest.Mocked<IProviderRepository>;
  let service: ProviderService;

  beforeEach(() => {
    repo = {
      createProvider: jest.fn(),
      updateProvider: jest.fn(),
      deleteProvider: jest.fn(),
      findById: jest.fn(),
      listProviders: jest.fn(),
      findByUserId: jest.fn(),
      listProviderServices: jest.fn(),
      addProviderService: jest.fn(),
      updateProviderService: jest.fn(),
      removeProviderService: jest.fn(),
      getOperatingHours: jest.fn(),
      upsertOperatingHours: jest.fn(),
      listServiceAreas: jest.fn(),
      addServiceArea: jest.fn(),
      removeServiceArea: jest.fn(),
      getRatingDisplay: jest.fn(),
      incrementRating: jest.fn(),
    } as any;

    service = new ProviderService(repo);
  });

  it('createProvider - prevents duplicate per userId', async () => {
    (repo.findByUserId as jest.Mock).mockResolvedValue({ id: 'prov-1' } as any);
    await expect(service.createProvider({ userId: 'u1', businessName: 'A' } as any)).rejects.toThrow(
      'User already has a provider profile'
    );
  });

  it('getProviderById - throws when not found', async () => {
    (repo.findById as jest.Mock).mockResolvedValue(null);
    await expect(service.getProviderById('prov-1')).rejects.toThrow('Provider not found');
  });

  it('addRating - validates rating', async () => {
    (repo.findById as jest.Mock).mockResolvedValue({ id: 'prov-1' } as any);
    await expect(service.addRating('prov-1', { rating: 0 } as any)).rejects.toThrow('rating must be 1-5');
    await expect(service.addRating('prov-1', { rating: 6 } as any)).rejects.toThrow('rating must be 1-5');
  });
});
