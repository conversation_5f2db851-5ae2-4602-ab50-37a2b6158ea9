import { createUserSchema, updateUserSchema, userListQuerySchema } from '@/validation/user';

describe('User Validation Schemas', () => {
  describe('createUserSchema', () => {
    it('should validate valid user creation data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'New',
        lastName: 'User',
        role: 'CUSTOMER',
      };

      const { error, value } = createUserSchema.validate(validData);

      expect(error).toBeUndefined();
      expect(value).toEqual({
        ...validData,
        isActive: true,
        isVerified: false,
      });
    });

    it('should require email', () => {
      const invalidData = {
        password: 'SecurePass123!',
        firstName: 'New',
        lastName: 'User',
        role: 'CUSTOMER',
      };

      const { error } = createUserSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('email');
      expect(error?.details[0].message).toContain('required');
    });

    it('should validate email format', () => {
      const invalidData = {
        email: 'invalid-email-format',
        password: 'SecurePass123!',
        firstName: 'New',
        lastName: 'User',
        role: 'CUSTOMER',
      };

      const { error } = createUserSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('email');
      expect(error?.details[0].message).toContain('valid email');
    });

    it('should require password', () => {
      const invalidData = {
        email: '<EMAIL>',
        firstName: 'New',
        lastName: 'User',
        role: 'CUSTOMER',
      };

      const { error } = createUserSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('password');
    });

    it('should validate password strength', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'weak',
        firstName: 'New',
        lastName: 'User',
        role: 'CUSTOMER',
      };

      const { error } = createUserSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('password');
    });

    it('should require firstName', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        lastName: 'User',
        role: 'CUSTOMER',
      };

      const { error } = createUserSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('firstName');
    });

    it('should require lastName', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'New',
        role: 'CUSTOMER',
      };

      const { error } = createUserSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('lastName');
    });

    it('should require role', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'New',
        lastName: 'User',
      };

      const { error } = createUserSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('role');
    });

    it('should validate role enum values', () => {
      const validRoles = ['CUSTOMER', 'PROVIDER', 'ADMIN'];

      validRoles.forEach(role => {
        const validData = {
          email: '<EMAIL>',
          password: 'SecurePass123!',
          firstName: 'New',
          lastName: 'User',
          role,
        };

        const { error } = createUserSchema.validate(validData);
        expect(error).toBeUndefined();
      });
    });

    it('should reject invalid role values', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'New',
        lastName: 'User',
        role: 'invalid-role',
      };

      const { error } = createUserSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('role');
    });

    it('should allow optional phone field', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'New',
        lastName: 'User',
        role: 'CUSTOMER',
        phone: '+15551234567',
      };

      const { error } = createUserSchema.validate(validData);
      expect(error).toBeUndefined();
    });

    it('should allow optional isActive field', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'New',
        lastName: 'User',
        role: 'CUSTOMER',
        isActive: false,
      };

      const { error } = createUserSchema.validate(validData);
      expect(error).toBeUndefined();
    });

    it('should allow optional isVerified field', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'New',
        lastName: 'User',
        role: 'CUSTOMER',
        isVerified: true,
      };

      const { error } = createUserSchema.validate(validData);
      expect(error).toBeUndefined();
    });

    it('should trim whitespace from string fields', () => {
      const dataWithWhitespace = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: '  New  ',
        lastName: '  User  ',
        role: 'CUSTOMER',
      };

      const { error, value } = createUserSchema.validate(dataWithWhitespace);

      expect(error).toBeUndefined();
      expect(value.email).toBe('<EMAIL>');
      expect(value.firstName).toBe('New');
      expect(value.lastName).toBe('User');
    });
  });

  describe('updateUserSchema', () => {
    it('should validate valid user update data', () => {
      const validData = {
        firstName: 'Updated',
        lastName: 'Name',
        phone: '+15559876543',
        isActive: false,
      };

      const { error, value } = updateUserSchema.validate(validData);

      expect(error).toBeUndefined();
      expect(value).toEqual(validData);
    });

    it('should allow partial updates', () => {
      const partialData = {
        firstName: 'UpdatedFirstName',
      };

      const { error } = updateUserSchema.validate(partialData);

      expect(error).toBeUndefined();
    });

    it('should validate email format if provided', () => {
      const invalidData = {
        email: 'invalid-email-format',
      };

      const { error } = updateUserSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('email');
    });

    it('should allow valid email update', () => {
      const validData = {
        email: '<EMAIL>',
      };

      const { error } = updateUserSchema.validate(validData);

      expect(error).toBeUndefined();
    });

    it('should validate role if provided', () => {
      const validRoles = ['CUSTOMER', 'PROVIDER', 'ADMIN'];

      validRoles.forEach(role => {
        const data = { role };
        const { error } = updateUserSchema.validate(data);
        expect(error).toBeUndefined();
      });
    });

    it('should reject invalid role values', () => {
      const invalidData = {
        role: 'invalid-role',
      };

      const { error } = updateUserSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('role');
    });

    it('should validate boolean fields', () => {
      const validData = {
        isActive: true,
        isVerified: false,
      };

      const { error } = updateUserSchema.validate(validData);

      expect(error).toBeUndefined();
    });

    it('should reject non-boolean values for boolean fields', () => {
      const invalidData = {
        isActive: 'not-boolean',
      };

      const { error } = updateUserSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('isActive');
    });

    it('should validate phone format if provided', () => {
      const validPhoneFormats = ['+15551234567', '15551234567', '+12345678901'];

      validPhoneFormats.forEach(phone => {
        const data = { phone };
        const { error } = updateUserSchema.validate(data);
        expect(error).toBeUndefined();
      });
    });

    it('should reject empty string updates', () => {
      const invalidData = {
        firstName: '',
      };

      const { error } = updateUserSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('firstName');
    });

    it('should trim whitespace from string fields', () => {
      const dataWithWhitespace = {
        firstName: '  UpdatedFirst  ',
        lastName: '  UpdatedLast  ',
        email: '<EMAIL>',
      };

      const { error, value } = updateUserSchema.validate(dataWithWhitespace);

      expect(error).toBeUndefined();
      expect(value.firstName).toBe('UpdatedFirst');
      expect(value.lastName).toBe('UpdatedLast');
      expect(value.email).toBe('<EMAIL>');
    });

    it('should allow empty object (no updates)', () => {
      const emptyData = {};

      const { error } = updateUserSchema.validate(emptyData);

      expect(error).toBeUndefined();
    });

    it('should not allow password updates', () => {
      const invalidData = {
        password: 'NewPassword123!',
        firstName: 'Test',
      };

      const { error } = updateUserSchema.validate(invalidData);

      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('not allowed');
    });
  });

  describe('userListQuerySchema', () => {
    it('should validate valid query parameters', () => {
      const validQuery = {
        page: '1',
        limit: '20',
        search: 'john',
        role: 'CUSTOMER',
        isActive: 'true',
        isVerified: 'false',
        sortBy: 'email',
        sortOrder: 'asc',
      };

      const { error, value } = userListQuerySchema.validate(validQuery);

      expect(error).toBeUndefined();
      expect(value).toEqual({
        page: 1,
        limit: 20,
        search: 'john',
        role: 'CUSTOMER',
        isActive: true,
        isVerified: false,
        sortBy: 'email',
        sortOrder: 'asc',
      });
    });

    it('should use default values for optional fields', () => {
      const minimalQuery = {};

      const { error, value } = userListQuerySchema.validate(minimalQuery);

      expect(error).toBeUndefined();
      expect(value).toEqual({
        page: 1,
        limit: 20,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      });
    });

    it('should convert string numbers to integers', () => {
      const queryWithStrings = {
        page: '3',
        limit: '50',
      };

      const { error, value } = userListQuerySchema.validate(queryWithStrings);

      expect(error).toBeUndefined();
      expect(value.page).toBe(3);
      expect(value.limit).toBe(50);
      expect(typeof value.page).toBe('number');
      expect(typeof value.limit).toBe('number');
    });

    it('should convert string booleans to booleans', () => {
      const queryWithStringBooleans = {
        isActive: 'true',
        isVerified: 'false',
      };

      const { error, value } = userListQuerySchema.validate(queryWithStringBooleans);

      expect(error).toBeUndefined();
      expect(value.isActive).toBe(true);
      expect(value.isVerified).toBe(false);
      expect(typeof value.isActive).toBe('boolean');
      expect(typeof value.isVerified).toBe('boolean');
    });

    it('should validate page minimum value', () => {
      const invalidQuery = {
        page: '0',
      };

      const { error } = userListQuerySchema.validate(invalidQuery);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('page');
      expect(error?.details[0].message).toContain('at least 1');
    });

    it('should validate limit range', () => {
      const invalidQueries = [{ limit: '0' }, { limit: '101' }];

      invalidQueries.forEach(query => {
        const { error } = userListQuerySchema.validate(query);
        expect(error).toBeDefined();
        expect(error?.details[0].path).toContain('limit');
      });
    });

    it('should validate role enum values', () => {
      const validRoles = ['CUSTOMER', 'PROVIDER', 'ADMIN'];

      validRoles.forEach(role => {
        const query = { role };
        const { error } = userListQuerySchema.validate(query);
        expect(error).toBeUndefined();
      });
    });

    it('should reject invalid role values', () => {
      const invalidQuery = {
        role: 'invalid-role',
      };

      const { error } = userListQuerySchema.validate(invalidQuery);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('role');
    });

    it('should validate sortBy values', () => {
      const validSortFields = ['email', 'firstName', 'lastName', 'createdAt', 'updatedAt'];

      validSortFields.forEach(sortBy => {
        const query = { sortBy };
        const { error } = userListQuerySchema.validate(query);
        expect(error).toBeUndefined();
      });
    });

    it('should reject invalid sortBy values', () => {
      const invalidQuery = {
        sortBy: 'invalid-field',
      };

      const { error } = userListQuerySchema.validate(invalidQuery);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('sortBy');
    });

    it('should validate sortOrder values', () => {
      const validSortOrders = ['asc', 'desc'];

      validSortOrders.forEach(sortOrder => {
        const query = { sortOrder };
        const { error } = userListQuerySchema.validate(query);
        expect(error).toBeUndefined();
      });
    });

    it('should reject invalid sortOrder values', () => {
      const invalidQuery = {
        sortOrder: 'invalid-order',
      };

      const { error } = userListQuerySchema.validate(invalidQuery);

      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('sortOrder');
    });

    it('should trim search string', () => {
      const queryWithWhitespace = {
        search: '  john doe  ',
      };

      const { error, value } = userListQuerySchema.validate(queryWithWhitespace);

      expect(error).toBeUndefined();
      expect(value.search).toBe('john doe');
    });

    it('should allow empty search string', () => {
      const query = {
        search: '',
      };

      const { error } = userListQuerySchema.validate(query);

      expect(error).toBeUndefined();
    });

    it('should handle complex query combinations', () => {
      const complexQuery = {
        page: '2',
        limit: '15',
        search: 'test user',
        role: 'PROVIDER',
        isActive: 'true',
        isVerified: 'true',
        sortBy: 'firstName',
        sortOrder: 'asc',
      };

      const { error, value } = userListQuerySchema.validate(complexQuery);

      expect(error).toBeUndefined();
      expect(value).toEqual({
        page: 2,
        limit: 15,
        search: 'test user',
        role: 'PROVIDER',
        isActive: true,
        isVerified: true,
        sortBy: 'firstName',
        sortOrder: 'asc',
      });
    });
  });

  describe('common validation patterns', () => {
    it('should handle undefined values appropriately', () => {
      const schemas = [createUserSchema, updateUserSchema, userListQuerySchema];

      schemas.forEach(schema => {
        const { error } = schema.validate(undefined);
        // All Joi object schemas accept undefined by default
        expect(error).toBeUndefined();
      });
    });

    it('should handle null values appropriately', () => {
      const schemas = [createUserSchema, updateUserSchema, userListQuerySchema];

      schemas.forEach(schema => {
        const { error } = schema.validate(null);
        // All schemas should reject null values
        expect(error).toBeDefined();
      });
    });

    it('should reject unknown fields', () => {
      const dataWithUnknown = {
        email: '<EMAIL>',
        firstName: 'Test',
        unknownField: 'should be rejected',
      };

      const { error } = updateUserSchema.validate(dataWithUnknown);

      expect(error).toBeDefined();
      expect(error?.details[0].message).toContain('not allowed');
    });
  });
});
