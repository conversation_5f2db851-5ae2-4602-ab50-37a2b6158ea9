{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAC/B,2CAAwD;AAExD,iDAAyE;AAEzE,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAGlC,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,2BAA2B,CAAC;AACzE,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,KAAK,CAAC;AAC3D,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,+BAA+B,CAAC;AAC7F,MAAM,sBAAsB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,IAAI,CAAC;AAGnE,MAAM,mBAAmB,GAAG,CAAC,OAAmB,EAAU,EAAE;IACjE,MAAM,UAAU,GAAG;QACjB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,IAAI,EAAE,OAAO,CAAC,IAAI;KACnB,CAAC;IAEF,OAAO,sBAAG,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC,CAAC;AACzE,CAAC,CAAC;AARW,QAAA,mBAAmB,uBAQ9B;AAGK,MAAM,oBAAoB,GAAG,CAAC,OAAmB,EAAU,EAAE;IAClE,MAAM,UAAU,GAAG;QACjB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,IAAI,EAAE,OAAO,CAAC,IAAI;KACnB,CAAC;IAEF,OAAO,sBAAG,CAAC,IAAI,CAAC,UAAU,EAAE,kBAAkB,EAAE,EAAE,SAAS,EAAE,sBAAsB,EAAE,CAAC,CAAC;AACzF,CAAC,CAAC;AARW,QAAA,oBAAoB,wBAQ/B;AAGK,MAAM,iBAAiB,GAAG,CAAC,KAAa,EAAc,EAAE;IAC7D,IAAI,CAAC;QACH,OAAO,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,UAAoB,CAAe,CAAC;IAC/D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAC3C,MAAM,IAAI,kCAAmB,CAAC,sBAAsB,CAAC,CAAC;QACxD,CAAC;aAAM,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAClD,MAAM,IAAI,kCAAmB,CAAC,sBAAsB,CAAC,CAAC;QACxD,CAAC;QACD,MAAM,IAAI,kCAAmB,CAAC,2BAA2B,CAAC,CAAC;IAC7D,CAAC;AACH,CAAC,CAAC;AAXW,QAAA,iBAAiB,qBAW5B;AAGK,MAAM,kBAAkB,GAAG,CAAC,KAAa,EAAc,EAAE;IAC9D,IAAI,CAAC;QACH,OAAO,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,kBAA4B,CAAe,CAAC;IACvE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAC3C,MAAM,IAAI,kCAAmB,CAAC,uBAAuB,CAAC,CAAC;QACzD,CAAC;aAAM,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAClD,MAAM,IAAI,kCAAmB,CAAC,uBAAuB,CAAC,CAAC;QACzD,CAAC;QACD,MAAM,IAAI,kCAAmB,CAAC,mCAAmC,CAAC,CAAC;IACrE,CAAC;AACH,CAAC,CAAC;AAXW,QAAA,kBAAkB,sBAW7B;AAGK,MAAM,YAAY,GAAG,KAAK,EAC/B,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QAEH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,MAAM,IAAI,kCAAmB,CAAC,0BAA0B,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAGtC,MAAM,OAAO,GAAG,IAAA,yBAAiB,EAAC,KAAK,CAAC,CAAC;QAGzC,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE;YAC7B,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,KAAK,EAAE,IAAI;gBACX,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,IAAI;gBACd,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,IAAI;gBACX,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,IAAI;aAClB;SACF,CAAoB,CAAC;QAEtB,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,kCAAmB,CAAC,gBAAgB,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,kCAAmB,CAAC,wBAAwB,CAAC,CAAC;QAC1D,CAAC;QAGD,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;QAErB,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AApDW,QAAA,YAAY,gBAoDvB;AAGK,MAAM,oBAAoB,GAAG,KAAK,EACvC,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,OAAO,GAAG,IAAA,yBAAiB,EAAC,KAAK,CAAC,CAAC;QAEzC,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE;YAC7B,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,KAAK,EAAE,IAAI;gBACX,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,IAAI;gBACd,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,IAAI;gBACX,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,IAAI;aAClB;SACF,CAAoB,CAAC;QAEtB,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC1B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;YAChB,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;QACvB,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAEf,IAAI,EAAE,CAAC;IACT,CAAC;AACH,CAAC,CAAC;AA1CW,QAAA,oBAAoB,wBA0C/B;AAGK,MAAM,SAAS,GAAG,CAAC,GAAG,KAAiB,EAAE,EAAE;IAChD,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC5E,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,IAAI,kCAAmB,CAAC,yBAAyB,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,iCAAkB,CAAC,0BAA0B,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAZW,QAAA,SAAS,aAYpB;AAGK,MAAM,wBAAwB,GAAG,CACtC,GAAyB,EACzB,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,IAAI,kCAAmB,CAAC,yBAAyB,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;QACzB,MAAM,IAAI,iCAAkB,CAAC,6BAA6B,CAAC,CAAC;IAC9D,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAdW,QAAA,wBAAwB,4BAcnC;AAGK,MAAM,uBAAuB,GAAG,CAAC,cAAsB,QAAQ,EAAE,EAAE;IACxE,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC5E,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,IAAI,kCAAmB,CAAC,yBAAyB,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAGtE,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,iBAAQ,CAAC,KAAK,EAAE,CAAC;YACrC,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAGD,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,YAAY,EAAE,CAAC;YACjC,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,MAAM,IAAI,iCAAkB,CAAC,gCAAgC,CAAC,CAAC;IACjE,CAAC,CAAC;AACJ,CAAC,CAAC;AApBW,QAAA,uBAAuB,2BAoBlC;AAGK,MAAM,aAAa,GAAG,CAAC,cAAsB,CAAC,EAAE,WAAmB,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,EAAE;IAC1F,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAgD,CAAC;IAEzE,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC5E,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAGlC,IAAI,OAAO,IAAI,GAAG,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;YACvC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACvB,CAAC;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,GAAG,QAAQ,EAAE,CAAC;QAE3E,IAAI,KAAK,CAAC,KAAK,IAAI,WAAW,EAAE,CAAC;YAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;YACrE,MAAM,IAAI,kCAAmB,CAAC,mCAAmC,aAAa,WAAW,CAAC,CAAC;QAC7F,CAAC;QAED,KAAK,CAAC,KAAK,EAAE,CAAC;QACd,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAGzB,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE,CAAC;YACzB,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;gBACxC,IAAI,GAAG,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC;oBACtB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAlCW,QAAA,aAAa,iBAkCxB;AAEF,kBAAe;IACb,mBAAmB,EAAnB,2BAAmB;IACnB,oBAAoB,EAApB,4BAAoB;IACpB,iBAAiB,EAAjB,yBAAiB;IACjB,kBAAkB,EAAlB,0BAAkB;IAClB,YAAY,EAAZ,oBAAY;IACZ,oBAAoB,EAApB,4BAAoB;IACpB,SAAS,EAAT,iBAAS;IACT,wBAAwB,EAAxB,gCAAwB;IACxB,uBAAuB,EAAvB,+BAAuB;IACvB,aAAa,EAAb,qBAAa;CACd,CAAC"}