import express from 'express';
import { authenticate } from '../middleware/auth';
import { AuthenticatedRequest } from '../types/auth';
import { createLogger } from '../utils/logger';
import {
  bulkUserOperationSchema,
  createUserSchema,
  updateUserSchema,
  userIdParamSchema,
  userListQuerySchema,
  validate,
  validateParams,
  validateQuery,
} from '../validation/user';

// Import the layered architecture components
import { createUserController } from '../controllers/UserController';
import { UserRepository } from '../repositories/UserRepository';
import { UserService } from '../services/UserService';

const router = express.Router();
const logger = createLogger('UserRoutes');

// Lazy initialization of user components
let userController: any = null;

function getUserController() {
  if (!userController) {
    // Use test Prisma client in test environment, otherwise use database service
    let prisma;
    if (process.env.NODE_ENV === 'test' && (global as any).__PRISMA__) {
      prisma = (global as any).__PRISMA__;
    } else {
      const { databaseService } = require('../services');
      prisma = databaseService.getClient();
    }

    const userRepository = new UserRepository(prisma);
    const userService = new UserService(userRepository);
    userController = createUserController(userService);

    logger.info('User controller initialized');
  }
  return userController;
}

// Middleware to check if user is admin (for admin-only routes)
const requireAdmin = (req: AuthenticatedRequest, res: any, next: any) => {
  if (req.user?.role !== 'ADMIN') {
    return res.forbidden('Admin access required');
  }
  next();
};

// Basic CRUD operations
// GET /api/users - Get users list (admin only)
router.get('/', authenticate, requireAdmin, validateQuery(userListQuerySchema), (req, res, next) =>
  getUserController().getUsers(req, res, next)
);

// POST /api/users - Create new user (admin only)
router.post('/', authenticate, requireAdmin, validate(createUserSchema), (req, res, next) =>
  getUserController().createUser(req, res, next)
);

// Search and analytics endpoints (admin only) - MUST come before /:id routes
// GET /api/users/search - Search users
router.get('/search', authenticate, requireAdmin, (req, res, next) => getUserController().searchUsers(req, res, next));

// GET /api/users/stats - Get user statistics
router.get('/stats', authenticate, requireAdmin, (req, res, next) => getUserController().getUserStats(req, res, next));

// GET /api/users/role/:role - Get users by role
router.get('/role/:role', authenticate, requireAdmin, (req, res, next) =>
  getUserController().getUsersByRole(req, res, next)
);

// GET /api/users/active - Get active users
router.get('/active', authenticate, requireAdmin, (req, res, next) =>
  getUserController().getActiveUsers(req, res, next)
);

// GET /api/users/verified - Get verified users
router.get('/verified', authenticate, requireAdmin, (req, res, next) =>
  getUserController().getVerifiedUsers(req, res, next)
);

// Utility endpoints (admin only) - MUST come before /:id routes
// GET /api/users/exists/:email - Check if user exists
router.get('/exists/:email', authenticate, requireAdmin, (req, res, next) =>
  getUserController().userExists(req, res, next)
);

// GET /api/users/count - Count total users
router.get('/count', authenticate, requireAdmin, (req, res, next) => getUserController().countUsers(req, res, next));

// GET /api/users/count/:role - Count users by role
router.get('/count/:role', authenticate, requireAdmin, (req, res, next) =>
  getUserController().countUsersByRole(req, res, next)
);

// Bulk operations (admin only)
// POST /api/users/bulk/update - Bulk update users
router.post('/bulk/update', authenticate, requireAdmin, validate(bulkUserOperationSchema), (req, res, next) =>
  getUserController().bulkUpdateUsers(req, res, next)
);

// POST /api/users/bulk/delete - Bulk delete users
router.post('/bulk/delete', authenticate, requireAdmin, validate(bulkUserOperationSchema), (req, res, next) =>
  getUserController().bulkDeleteUsers(req, res, next)
);

// Parameterized routes - MUST come last to avoid conflicts
// GET /api/users/:id - Get user by ID (admin only, or own profile)
router.get('/:id', authenticate, validateParams(userIdParamSchema), (req: AuthenticatedRequest, res, next) => {
  // Allow users to access their own profile, or admins to access any profile
  if (req.user?.role !== 'ADMIN' && req.user?.id !== req.params.id) {
    return res.forbidden('You can only access your own profile');
  }
  return getUserController().getUserById(req, res, next);
});

// PUT /api/users/:id - Update user (admin only, or own profile)
router.put(
  '/:id',
  authenticate,
  validateParams(userIdParamSchema),
  validate(updateUserSchema),
  (req: AuthenticatedRequest, res, next) => {
    // Allow users to update their own profile, or admins to update any profile
    if (req.user?.role !== 'ADMIN' && req.user?.id !== req.params.id) {
      return res.forbidden('You can only update your own profile');
    }
    return getUserController().updateUser(req, res, next);
  }
);

// DELETE /api/users/:id - Delete user (admin only)
router.delete('/:id', authenticate, requireAdmin, validateParams(userIdParamSchema), (req, res, next) =>
  getUserController().deleteUser(req, res, next)
);

export default router;
