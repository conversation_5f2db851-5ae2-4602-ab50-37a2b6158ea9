import { Response } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { IUserService } from '../services/UserService';
import { AuthenticatedRequest } from '../types/auth';
import { CreateUserRequest, UpdateUserRequest, UserListQuery } from '../types/user';
import { createLogger } from '../utils/logger';
import { createPaginationMeta } from '../utils/responseWrapper';

// Controller interface for dependency inversion
export interface IUserController {
  // Basic CRUD operations
  getUserById: any;
  createUser: any;
  updateUser: any;
  deleteUser: any;

  // List and search operations
  getUsers: any;
  searchUsers: any;

  // Statistics and analytics
  getUserStats: any;
  getUsersByRole: any;
  getActiveUsers: any;
  getVerifiedUsers: any;

  // Bulk operations
  bulkUpdateUsers: any;
  bulkDeleteUsers: any;

  // Utility operations
  userExists: any;
  countUsers: any;
  countUsersByRole: any;
}

// HTTP Controller implementation - handles only HTTP concerns
export class UserController implements IUserController {
  private logger = createLogger('UserController');

  constructor(private userService: IUserService) {
    this.logger.info('UserController initialized');
  }

  // Get user by ID
  getUserById = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { id } = req.params;

    this.logger.info('Get user by ID request received', {
      requestId,
      userId: id,
      requestingUserId: req.userId,
    });

    try {
      const user = await this.userService.getUserById(id);

      const duration = Date.now() - startTime;
      this.logger.info('Get user by ID completed successfully', {
        requestId,
        userId: id,
        duration,
      });

      res.success({ user });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get user by ID failed', error, {
        requestId,
        userId: id,
        duration,
      });
      throw error;
    }
  });

  // Create new user
  createUser = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const userData: CreateUserRequest = req.body;

    this.logger.info('Create user request received', {
      requestId,
      email: userData.email,
      requestingUserId: req.userId,
    });

    try {
      const user = await this.userService.createUser(userData);

      const duration = Date.now() - startTime;
      this.logger.info('Create user completed successfully', {
        requestId,
        userId: user.id,
        email: user.email,
        duration,
      });

      res.created({ user }, 'User created successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Create user failed', error, {
        requestId,
        email: userData.email,
        duration,
      });
      throw error;
    }
  });

  // Update user
  updateUser = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { id } = req.params;
    const updateData: UpdateUserRequest = req.body;

    this.logger.info('Update user request received', {
      requestId,
      userId: id,
      updatedFields: Object.keys(updateData),
      requestingUserId: req.userId,
    });

    try {
      const user = await this.userService.updateUser(id, updateData);

      const duration = Date.now() - startTime;
      this.logger.info('Update user completed successfully', {
        requestId,
        userId: id,
        updatedFields: Object.keys(updateData),
        duration,
      });

      res.success({ user }, 'User updated successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Update user failed', error, {
        requestId,
        userId: id,
        duration,
      });
      throw error;
    }
  });

  // Delete user
  deleteUser = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { id } = req.params;

    this.logger.info('Delete user request received', {
      requestId,
      userId: id,
      requestingUserId: req.userId,
    });

    try {
      const deleted = await this.userService.deleteUser(id);

      const duration = Date.now() - startTime;
      this.logger.info('Delete user completed successfully', {
        requestId,
        userId: id,
        deleted,
        duration,
      });

      res.success({ deleted }, 'User deleted successfully');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Delete user failed', error, {
        requestId,
        userId: id,
        duration,
      });
      throw error;
    }
  });

  // Get users list
  getUsers = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const query: UserListQuery = req.query;

    this.logger.info('Get users list request received', {
      requestId,
      query,
      requestingUserId: req.userId,
    });

    try {
      const result = await this.userService.getUsers(query);

      const duration = Date.now() - startTime;
      this.logger.info('Get users list completed successfully', {
        requestId,
        totalUsers: result.pagination.total,
        page: result.pagination.page,
        limit: result.pagination.limit,
        duration,
      });

      // Extract pagination data if it exists
      const { users, pagination: paginationData, ...otherData } = result;
      const pagination = paginationData
        ? createPaginationMeta(paginationData.page, paginationData.limit, paginationData.total)
        : undefined;

      res.success({ users, ...otherData }, undefined, undefined, pagination);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get users list failed', error, {
        requestId,
        duration,
      });
      throw error;
    }
  });

  // Search users
  searchUsers = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { search, limit } = req.query;

    this.logger.info('Search users request received', {
      requestId,
      search,
      limit,
      requestingUserId: req.userId,
    });

    try {
      const users = await this.userService.searchUsers(search as string, limit ? parseInt(limit as string) : 10);

      const duration = Date.now() - startTime;
      this.logger.info('Search users completed successfully', {
        requestId,
        search,
        resultsCount: users.length,
        duration,
      });

      res.success({ users });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Search users failed', error, {
        requestId,
        search,
        duration,
      });
      throw error;
    }
  });

  // Get user statistics
  getUserStats = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Get user stats request received', {
      requestId,
      requestingUserId: req.userId,
    });

    try {
      const stats = await this.userService.getUserStats();

      const duration = Date.now() - startTime;
      this.logger.info('Get user stats completed successfully', {
        requestId,
        totalUsers: stats.totalUsers,
        duration,
      });

      res.success({ stats });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get user stats failed', error, {
        requestId,
        duration,
      });
      throw error;
    }
  });

  // Get users by role
  getUsersByRole = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { role } = req.params;

    this.logger.info('Get users by role request received', {
      requestId,
      role,
      requestingUserId: req.userId,
    });

    try {
      const users = await this.userService.getUsersByRole(role as any);

      const duration = Date.now() - startTime;
      this.logger.info('Get users by role completed successfully', {
        requestId,
        role,
        count: users.length,
        duration,
      });

      res.success({ users });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get users by role failed', error, {
        requestId,
        role,
        duration,
      });
      throw error;
    }
  });

  // Get active users
  getActiveUsers = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Get active users request received', {
      requestId,
      requestingUserId: req.userId,
    });

    try {
      const users = await this.userService.getActiveUsers();

      const duration = Date.now() - startTime;
      this.logger.info('Get active users completed successfully', {
        requestId,
        count: users.length,
        duration,
      });

      res.success({ users });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get active users failed', error, {
        requestId,
        duration,
      });
      throw error;
    }
  });

  // Get verified users
  getVerifiedUsers = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Get verified users request received', {
      requestId,
      requestingUserId: req.userId,
    });

    try {
      const users = await this.userService.getVerifiedUsers();

      const duration = Date.now() - startTime;
      this.logger.info('Get verified users completed successfully', {
        requestId,
        count: users.length,
        duration,
      });

      res.success({ users });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Get verified users failed', error, {
        requestId,
        duration,
      });
      throw error;
    }
  });

  // Bulk update users
  bulkUpdateUsers = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { userIds, updates } = req.body;

    this.logger.info('Bulk update users request received', {
      requestId,
      userIdsCount: userIds.length,
      updatedFields: Object.keys(updates),
      requestingUserId: req.userId,
    });

    try {
      const updatedCount = await this.userService.bulkUpdateUsers(userIds, updates);

      const duration = Date.now() - startTime;
      this.logger.info('Bulk update users completed successfully', {
        requestId,
        requestedCount: userIds.length,
        updatedCount,
        duration,
      });

      res.success({ updatedCount }, `Successfully updated ${updatedCount} users`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Bulk update users failed', error, {
        requestId,
        userIdsCount: userIds.length,
        duration,
      });
      throw error;
    }
  });

  // Bulk delete users
  bulkDeleteUsers = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { userIds } = req.body;

    this.logger.info('Bulk delete users request received', {
      requestId,
      userIdsCount: userIds.length,
      requestingUserId: req.userId,
    });

    try {
      const deletedCount = await this.userService.bulkDeleteUsers(userIds);

      const duration = Date.now() - startTime;
      this.logger.info('Bulk delete users completed successfully', {
        requestId,
        requestedCount: userIds.length,
        deletedCount,
        duration,
      });

      res.success({ deletedCount }, `Successfully deleted ${deletedCount} users`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Bulk delete users failed', error, {
        requestId,
        userIdsCount: userIds.length,
        duration,
      });
      throw error;
    }
  });

  // Check if user exists
  userExists = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { email } = req.params;

    this.logger.info('Check user exists request received', {
      requestId,
      email,
      requestingUserId: req.userId,
    });

    try {
      const exists = await this.userService.userExists(email);

      const duration = Date.now() - startTime;
      this.logger.info('Check user exists completed successfully', {
        requestId,
        email,
        exists,
        duration,
      });

      res.success({ exists });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Check user exists failed', error, {
        requestId,
        email,
        duration,
      });
      throw error;
    }
  });

  // Count users
  countUsers = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;

    this.logger.info('Count users request received', {
      requestId,
      requestingUserId: req.userId,
    });

    try {
      const count = await this.userService.countUsers();

      const duration = Date.now() - startTime;
      this.logger.info('Count users completed successfully', {
        requestId,
        count,
        duration,
      });

      res.success({ count });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Count users failed', error, {
        requestId,
        duration,
      });
      throw error;
    }
  });

  // Count users by role
  countUsersByRole = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID') as string;
    const { role } = req.params;

    this.logger.info('Count users by role request received', {
      requestId,
      role,
      requestingUserId: req.userId,
    });

    try {
      const count = await this.userService.countUsersByRole(role as any);

      const duration = Date.now() - startTime;
      this.logger.info('Count users by role completed successfully', {
        requestId,
        role,
        count,
        duration,
      });

      res.success({ count });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error('Count users by role failed', error, {
        requestId,
        role,
        duration,
      });
      throw error;
    }
  });
}

// Factory function to create controller with dependencies
export const createUserController = (userService: IUserService): UserController => {
  return new UserController(userService);
};

export default UserController;
