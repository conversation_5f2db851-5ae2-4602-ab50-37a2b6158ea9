"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateConfiguration = validateConfiguration;
const index_1 = require("./index");
function validateConfiguration() {
    console.log('🔍 Validating configuration...\n');
    try {
        const appConfig = index_1.config.getConfig();
        console.log('✅ Configuration loaded successfully');
        console.log(`📊 Environment: ${appConfig.server.nodeEnv}`);
        console.log(`🌐 Server: ${appConfig.server.host}:${appConfig.server.port}`);
        console.log(`🔗 API Base URL: ${appConfig.server.apiBaseUrl}`);
        console.log(`🎨 Frontend URL: ${appConfig.server.frontendUrl}`);
        console.log(`🔒 CORS Origin: ${appConfig.server.corsOrigin}`);
        console.log('\n📋 Configuration Summary:');
        console.log('├── Database:', {
            url: appConfig.database.url.replace(/\/\/.*@/, '//***:***@'),
            maxConnections: appConfig.database.maxConnections,
            idleTimeout: appConfig.database.idleTimeout,
        });
        console.log('├── JWT:', {
            issuer: appConfig.jwt.issuer,
            audience: appConfig.jwt.audience,
            accessExpiresIn: appConfig.jwt.accessExpiresIn,
            refreshExpiresIn: appConfig.jwt.refreshExpiresIn,
        });
        console.log('├── Security:', {
            bcryptRounds: appConfig.security.bcryptRounds,
            rateLimitMaxRequests: appConfig.security.rateLimitMaxRequests,
            rateLimitAuthMax: appConfig.security.rateLimitAuthMax,
            maxFileSize: appConfig.security.maxFileSize,
            allowedFileTypes: appConfig.security.allowedFileTypes,
        });
        console.log('├── Logging:', {
            level: appConfig.logging.level,
            format: appConfig.logging.format,
            enableConsole: appConfig.logging.enableConsole,
            enableFile: appConfig.logging.enableFile,
        });
        console.log('├── Redis:', {
            url: appConfig.redis.url,
            db: appConfig.redis.db,
            keyPrefix: appConfig.redis.keyPrefix,
        });
        console.log('├── AWS:', {
            region: appConfig.aws.region,
            s3Bucket: appConfig.aws.s3Bucket,
            cloudfrontUrl: appConfig.aws.cloudfrontUrl,
        });
        console.log('├── Email:', {
            service: appConfig.email.service,
            host: appConfig.email.host,
            port: appConfig.email.port,
            secure: appConfig.email.secure,
            from: appConfig.email.from,
        });
        console.log('└── Google OAuth:', {
            clientId: appConfig.googleOAuth.clientId,
            callbackUrl: appConfig.googleOAuth.callbackUrl,
        });
        console.log('\n✅ All configuration sections validated successfully!');
        console.log('\n🌍 Environment Checks:');
        console.log(`├── Is Development: ${index_1.config.isDevelopment()}`);
        console.log(`├── Is Production: ${index_1.config.isProduction()}`);
        console.log(`└── Is Test: ${index_1.config.isTest()}`);
        return true;
    }
    catch (error) {
        console.error('❌ Configuration validation failed:', error);
        return false;
    }
}
if (require.main === module) {
    const isValid = validateConfiguration();
    process.exit(isValid ? 0 : 1);
}
//# sourceMappingURL=validate.js.map