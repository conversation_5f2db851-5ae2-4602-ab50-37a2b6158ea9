import express from 'express';

// Import configuration system
import {config} from './config';
import {createLogger} from './utils/logger';
import {getAppVersion, getCurrentTimestamp, createErrorResponse} from './utils/appHelpers';
import {configureSecurityMiddleware, configureCorsMiddleware, configureBodyParsingMiddleware, configureLoggingMiddleware} from './config/middlewareConfig';

// Import routes
import apiRoutes from './routes';
import userRoutes from './routes/user';
import {errorHandler} from './middleware/errorHandler';

// Import middleware
import {requestLogger} from './middleware/requestLogger';

const app = express();
const logger = createLogger('App');

// Get configuration
const serverConfig = config.getServerConfig();
const securityConfig = config.getSecurityConfig();
const loggingConfig = config.getLoggingConfig();

/**
 * Configure All Middleware
 */
configureSecurityMiddleware(app);
configureCorsMiddleware(app);
configureBodyParsingMiddleware(app);
configureLoggingMiddleware(app);

/**
 * Root Endpoint - API Information
 */
app.get('/', (req, res) => {
	res.status(200).json({
		message: 'PrintWedittV1 API',
		version: getAppVersion(),
		environment: serverConfig.nodeEnv,
		timestamp: getCurrentTimestamp(),
		endpoints: {
			health: '/health',
			api: '/api',
			auth: '/api/auth',
		},
		documentation: 'API documentation coming soon',
		links: {
			health: `${serverConfig.apiBaseUrl}/health`,
			api: `${serverConfig.apiBaseUrl}/api`,
			auth: `${serverConfig.apiBaseUrl}/api/auth`,
		},
	});
});

/**
 * Health Check Endpoint
 */
app.get('/health', (req, res) => {
	res.status(200).json({
		status: 'OK',
		timestamp: getCurrentTimestamp(),
		uptime: process.uptime(),
		environment: serverConfig.nodeEnv,
		version: getAppVersion(),
		apiBaseUrl: serverConfig.apiBaseUrl,
		frontendUrl: serverConfig.frontendUrl,
	});
});

/**
 * API Base Endpoint
 */
app.get('/api', (req, res) => {
	res.status(200).json({
		message: 'PrintWedittV1 API Endpoints',
		version: getAppVersion(),
		availableEndpoints: {
			auth: '/api/auth',
			users: '/api/users',
			// Add more endpoints as they are implemented
			// services: '/api/services',
			// orders: '/api/orders',
			// files: '/api/files',
		},
		documentation: 'API documentation coming soon',
	});
});

/**
 * API Routes
 */
app.use('/api', apiRoutes);

// TODO: Add more route modules as they are developed
app.use('/api/users', userRoutes);
// app.use('/api/services', serviceRoutes);
// app.use('/api/orders', orderRoutes);
// app.use('/api/files', fileRoutes);

/**
 * 404 Handler
 */
app.use('*', (req, res) => {
	const errorResponse = createErrorResponse(
		'Not Found',
		`Route ${req.originalUrl} not found`,
		req.originalUrl,
		req.method
	);
	res.status(404).json(errorResponse);
});

/**
 * Error Handling Middleware (must be last)
 */
app.use(errorHandler);

// Log application setup
logger.info('Express application configured successfully', {
	environment: serverConfig.nodeEnv,
	corsOrigin: serverConfig.corsOrigin,
	rateLimitMaxRequests: securityConfig.rateLimitMaxRequests,
	rateLimitWindowMs: securityConfig.rateLimitWindowMs,
	maxFileSize: securityConfig.maxFileSize,
	logLevel: loggingConfig.level,
	logFormat: loggingConfig.format,
});

export default app;
