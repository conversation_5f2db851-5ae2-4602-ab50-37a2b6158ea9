import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import compression from 'compression';
import morgan from 'morgan';
import cookieParser from 'cookie-parser';

// Import configuration system
import {config} from './config';
import {createLogger} from './utils/logger';

// Import routes
import apiRoutes from './routes';
import {errorHandler} from './middleware/errorHandler';

// Import middleware
import {requestLogger} from './middleware/requestLogger';

const app = express();
const logger = createLogger('App');

// Get configuration
const serverConfig = config.getServerConfig();
const securityConfig = config.getSecurityConfig();
const loggingConfig = config.getLoggingConfig();

/**
 * Security Middleware Configuration
 */
app.use(
	helmet({
		crossOriginResourcePolicy: {policy: 'cross-origin'},
		contentSecurityPolicy: {
			directives: {
				defaultSrc: ["'self'"],
				styleSrc: ["'self'", "'unsafe-inline'"],
				scriptSrc: ["'self'"],
				imgSrc: ["'self'", 'data:', 'https:'],
			},
		},
	})
);

// Rate limiting with configuration
const limiter = rateLimit({
	windowMs: securityConfig.rateLimitWindowMs,
	max: securityConfig.rateLimitMaxRequests,
	message: 'Too many requests from this IP, please try again later.',
	standardHeaders: true,
	legacyHeaders: false,
});

app.use(limiter);

/**
 * CORS Configuration
 */
app.use(
	cors({
		origin: serverConfig.corsOrigin,
		credentials: true,
		methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
		allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
	})
);

/**
 * Body Parsing Middleware
 */
app.use(compression());
app.use(express.json({limit: `${securityConfig.maxFileSize}mb`}));
app.use(
	express.urlencoded({extended: true, limit: `${securityConfig.maxFileSize}mb`})
);
app.use(cookieParser(securityConfig.cookieSecret));

/**
 * Logging Middleware
 */
if (loggingConfig.enableConsole) {
	if (serverConfig.nodeEnv !== 'production') {
		app.use(morgan('dev'));
	} else {
		app.use(morgan('combined'));
	}
}

// Request logging middleware
app.use(requestLogger);

/**
 * Health Check Endpoint
 */
app.get('/health', (req, res) => {
	res.status(200).json({
		status: 'OK',
		timestamp: new Date().toISOString(),
		uptime: process.uptime(),
		environment: serverConfig.nodeEnv,
		version: process.env.npm_package_version || '1.0.0',
		apiBaseUrl: serverConfig.apiBaseUrl,
		frontendUrl: serverConfig.frontendUrl,
	});
});

/**
 * API Routes
 */
app.use('/api', apiRoutes);

// TODO: Add more route modules as they are developed
// app.use('/api/users', userRoutes);
// app.use('/api/services', serviceRoutes);
// app.use('/api/orders', orderRoutes);
// app.use('/api/files', fileRoutes);

/**
 * 404 Handler
 */
app.use('*', (req, res) => {
	res.status(404).json({
		error: 'Not Found',
		message: `Route ${req.originalUrl} not found`,
		path: req.originalUrl,
		method: req.method,
		timestamp: new Date().toISOString(),
	});
});

/**
 * Error Handling Middleware (must be last)
 */
app.use(errorHandler);

// Log application setup
logger.info('Express application configured successfully', {
	environment: serverConfig.nodeEnv,
	corsOrigin: serverConfig.corsOrigin,
	rateLimitMaxRequests: securityConfig.rateLimitMaxRequests,
	rateLimitWindowMs: securityConfig.rateLimitWindowMs,
	maxFileSize: securityConfig.maxFileSize,
	logLevel: loggingConfig.level,
	logFormat: loggingConfig.format,
});

export default app;
