import express from 'express';

// Import configuration system
import { config } from './config';
import {
  configureBodyParsingMiddleware,
  configureCorsMiddleware,
  configureLoggingMiddleware,
  configureSecurityMiddleware,
} from './config/middlewareConfig';
import { getAppVersion } from './utils/appHelpers';
import { createLogger } from './utils/logger';
import {
  createErrorResponse,
  createSuccessResponse,
  HttpStatus,
  responseWrapperMiddleware,
} from './utils/responseWrapper';

// Import routes
import { errorHandler } from './middleware/errorHandler';
import { requestContextMiddleware, requestCorrelationMiddleware } from './middleware/requestCorrelation';
import apiRoutes from './routes';
import userRoutes from './routes/user';

// Import middleware

const app = express();
const logger = createLogger('App');

// Get configuration
const serverConfig = config.getServerConfig();
const securityConfig = config.getSecurityConfig();
const loggingConfig = config.getLoggingConfig();

/**
 * Configure All Middleware
 */
configureSecurityMiddleware(app);
configureCorsMiddleware(app);
configureBodyParsingMiddleware(app);
configureLoggingMiddleware(app);

// Add request correlation middleware (must be before response wrapper)
app.use(requestCorrelationMiddleware);
app.use(requestContextMiddleware);

// Add response wrapper middleware
app.use(responseWrapperMiddleware);

/**
 * Root Endpoint - API Information
 */
app.get('/', (req, res) => {
  const response = createSuccessResponse(
    {
      message: 'PrintWedittV1 API',
      version: getAppVersion(),
      environment: serverConfig.nodeEnv,
      endpoints: {
        health: '/health',
        api: '/api',
        auth: '/api/auth',
      },
      documentation: 'API documentation coming soon',
      links: {
        health: `${serverConfig.apiBaseUrl}/health`,
        api: `${serverConfig.apiBaseUrl}/api`,
        auth: `${serverConfig.apiBaseUrl}/api/auth`,
        users: `${serverConfig.apiBaseUrl}/api/users`,
        docs: `${serverConfig.apiBaseUrl}/api/docs`,
      },
    },
    undefined,
    req,
    res
  );
  res.status(200).json(response);
});

/**
 * Health Check Endpoint
 */
app.get('/health', (req, res) => {
  const response = createSuccessResponse(
    {
      status: 'OK',
      uptime: process.uptime(),
      environment: serverConfig.nodeEnv,
      version: getAppVersion(),
      apiBaseUrl: serverConfig.apiBaseUrl,
      frontendUrl: serverConfig.frontendUrl,
    },
    undefined,
    req,
    res
  );
  res.status(200).json(response);
});

/**
 * API Base Endpoint
 */
app.get('/api', (req, res) => {
  const response = createSuccessResponse(
    {
      message: 'PrintWedittV1 API Endpoints',
      version: getAppVersion(),
      availableEndpoints: {
        auth: '/api/auth',
        users: '/api/users',
        docs: '/api/docs',
        // Add more endpoints as they are implemented
        // services: '/api/services',
        // orders: '/api/orders',
        // files: '/api/files',
      },
      documentation: 'API documentation available at /api/docs',
    },
    undefined,
    req,
    res
  );
  res.status(200).json(response);
});

/**
 * API Routes
 */
app.use('/api', apiRoutes);

// TODO: Add more route modules as they are developed
app.use('/api/users', userRoutes);
// app.use('/api/services', serviceRoutes);
// app.use('/api/orders', orderRoutes);
// app.use('/api/files', fileRoutes);

/**
 * 404 Handler
 */
app.use('*', (req, res) => {
  const errorResponse = createErrorResponse(
    `Route ${req.originalUrl} not found`,
    HttpStatus.NOT_FOUND,
    'NotFoundError',
    undefined,
    undefined,
    undefined,
    req,
    res
  );
  res.status(404).json(errorResponse);
});

/**
 * Error Handling Middleware (must be last)
 */
app.use(errorHandler);

// Log application setup
logger.info('Express application configured successfully', {
  environment: serverConfig.nodeEnv,
  corsOrigin: serverConfig.corsOrigin,
  rateLimitMaxRequests: securityConfig.rateLimitMaxRequests,
  rateLimitWindowMs: securityConfig.rateLimitWindowMs,
  maxFileSize: securityConfig.maxFileSize,
  logLevel: loggingConfig.level,
  logFormat: loggingConfig.format,
});

export default app;
