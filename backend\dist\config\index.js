"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getGoogleOAuthConfig = exports.getSecurityConfig = exports.getLoggingConfig = exports.getEmailConfig = exports.getAWSConfig = exports.getRedisConfig = exports.getServerConfig = exports.getJWTConfig = exports.getDatabaseConfig = exports.getConfig = exports.config = exports.ConfigurationManager = void 0;
var config_1 = require("./config");
Object.defineProperty(exports, "ConfigurationManager", { enumerable: true, get: function () { return config_1.ConfigurationManager; } });
const config_2 = require("./config");
exports.config = new config_2.ConfigurationManager();
const getConfig = () => exports.config.getConfig();
exports.getConfig = getConfig;
const getDatabaseConfig = () => exports.config.getDatabaseConfig();
exports.getDatabaseConfig = getDatabaseConfig;
const getJWTConfig = () => exports.config.getJWTConfig();
exports.getJWTConfig = getJWTConfig;
const getServerConfig = () => exports.config.getServerConfig();
exports.getServerConfig = getServerConfig;
const getRedisConfig = () => exports.config.getRedisConfig();
exports.getRedisConfig = getRedisConfig;
const getAWSConfig = () => exports.config.getAWSConfig();
exports.getAWSConfig = getAWSConfig;
const getEmailConfig = () => exports.config.getEmailConfig();
exports.getEmailConfig = getEmailConfig;
const getLoggingConfig = () => exports.config.getLoggingConfig();
exports.getLoggingConfig = getLoggingConfig;
const getSecurityConfig = () => exports.config.getSecurityConfig();
exports.getSecurityConfig = getSecurityConfig;
const getGoogleOAuthConfig = () => exports.config.getGoogleOAuthConfig();
exports.getGoogleOAuthConfig = getGoogleOAuthConfig;
//# sourceMappingURL=index.js.map