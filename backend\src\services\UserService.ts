import { User, UserRole } from '@prisma/client';
import { IUserRepository } from '../repositories/UserRepository';
import { 
  CreateUserRequest, 
  UpdateUserRequest, 
  UserListQuery, 
  UserListResponse, 
  UserSummary, 
  UserDetail, 
  UserStats,
  CreateUserData,
  UpdateUserData
} from '../types/user';
import { 
  ConflictError, 
  NotFoundError, 
  ValidationError 
} from '../middleware/errorHandler';
import { hashPassword } from '../utils/auth';
import { createLogger } from '../utils/logger';
import { 
  UserManagementModel, 
  UserProfileModel, 
  UserStatisticsModel 
} from '../models/UserModels';

// Service interface for dependency inversion
export interface IUserService {
  // Basic CRUD operations
  getUserById(id: string): Promise<UserDetail>;
  createUser(userData: CreateUserRequest): Promise<UserDetail>;
  updateUser(id: string, data: UpdateUserRequest): Promise<UserDetail>;
  deleteUser(id: string): Promise<boolean>;
  
  // List and search operations
  getUsers(query: UserListQuery): Promise<UserListResponse>;
  searchUsers(searchTerm: string, limit?: number): Promise<UserSummary[]>;
  
  // Statistics and analytics
  getUserStats(): Promise<UserStats>;
  getUsersByRole(role: UserRole): Promise<UserSummary[]>;
  getActiveUsers(): Promise<UserSummary[]>;
  getVerifiedUsers(): Promise<UserSummary[]>;
  
  // Bulk operations
  bulkUpdateUsers(userIds: string[], updates: Partial<UpdateUserRequest>): Promise<number>;
  bulkDeleteUsers(userIds: string[]): Promise<number>;
  
  // Utility operations
  userExists(email: string): Promise<boolean>;
  countUsers(): Promise<number>;
  countUsersByRole(role: UserRole): Promise<number>;
}

// Service implementation
export class UserService implements IUserService {
  private logger = createLogger('UserService');

  constructor(private userRepository: IUserRepository) {
    this.logger.info('UserService initialized');
  }

  async getUserById(id: string): Promise<UserDetail> {
    this.logger.info('Getting user by ID', { userId: id });

    const user = await this.userRepository.findUserById(id);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Convert to domain model and then to UserDetail
    const userProfile = UserProfileModel.fromUser(user);
    const userDetail = userProfile.toUserDetail();

    this.logger.info('User retrieved successfully', { userId: id });
    return userDetail;
  }

  async createUser(userData: CreateUserRequest): Promise<UserDetail> {
    this.logger.info('Creating new user', { email: userData.email });

    // Create domain model for validation and business logic
    const userCreationData = UserManagementModel.createUserData(userData);

    // Check if user already exists
    const existingUser = await this.userRepository.findUserByEmail(userCreationData.getNormalizedEmail());
    if (existingUser) {
      throw new ConflictError('User with this email already exists');
    }

    // Hash password and prepare data for storage
    const hashedPassword = await hashPassword(userCreationData.password);
    const storageData: CreateUserData = {
      ...userCreationData.getStorageData(),
      password: hashedPassword
    };

    // Create user
    const user = await this.userRepository.createUser(storageData);

    // Convert to domain model and then to UserDetail
    const userProfile = UserProfileModel.fromUser(user);
    const userDetail = userProfile.toUserDetail();

    this.logger.info('User created successfully', { userId: user.id, email: user.email });
    return userDetail;
  }

  async updateUser(id: string, data: UpdateUserRequest): Promise<UserDetail> {
    this.logger.info('Updating user', { userId: id, updatedFields: Object.keys(data) });

    // Check if user exists
    const existingUser = await this.userRepository.findUserById(id);
    if (!existingUser) {
      throw new NotFoundError('User not found');
    }

    // Create domain model for validation and business logic
    const userUpdateData = UserManagementModel.createUserUpdateData(data);

    // Check email uniqueness if email is being updated
    if (userUpdateData.email && userUpdateData.email !== existingUser.email) {
      const emailExists = await this.userRepository.userExists(userUpdateData.getNormalizedEmail()!);
      if (emailExists) {
        throw new ConflictError('User with this email already exists');
      }
    }

    // Update user with sanitized data
    const updatedUser = await this.userRepository.updateUser(id, userUpdateData.getStorageData());

    // Convert to domain model and then to UserDetail
    const userProfile = UserProfileModel.fromUser(updatedUser);
    const userDetail = userProfile.toUserDetail();

    this.logger.info('User updated successfully', { userId: id });
    return userDetail;
  }

  async deleteUser(id: string): Promise<boolean> {
    this.logger.info('Deleting user', { userId: id });

    // Check if user exists
    const existingUser = await this.userRepository.findUserById(id);
    if (!existingUser) {
      throw new NotFoundError('User not found');
    }

    const deleted = await this.userRepository.deleteUser(id);
    
    if (deleted) {
      this.logger.info('User deleted successfully', { userId: id });
    } else {
      this.logger.error('Failed to delete user', { userId: id });
    }

    return deleted;
  }

  async getUsers(query: UserListQuery): Promise<UserListResponse> {
    this.logger.info('Getting users list', { query });

    // Create domain model for validation and business logic
    const userListQueryData = UserManagementModel.createUserListQuery(query);

    const result = await this.userRepository.getUsers(userListQueryData.getQueryParams());

    this.logger.info('Users list retrieved successfully', { 
      totalUsers: result.pagination.total,
      page: result.pagination.page,
      limit: result.pagination.limit,
      hasFilters: userListQueryData.hasFilters()
    });

    return result;
  }

  async searchUsers(searchTerm: string, limit: number = 10): Promise<UserSummary[]> {
    this.logger.info('Searching users', { searchTerm, limit });

    const users = await this.userRepository.searchUsers(searchTerm, limit);

    this.logger.info('User search completed', { 
      searchTerm, 
      resultsCount: users.length 
    });

    return users;
  }

  async getUserStats(): Promise<UserStats> {
    this.logger.info('Getting user statistics');

    const stats = await this.userRepository.getUserStats();

    // Convert to domain model for business logic
    const userStatistics = new UserStatisticsModel(
      stats.total,
      stats.totalUsers,
      stats.active,
      stats.activeUsers,
      stats.inactive,
      stats.verified,
      stats.verifiedUsers,
      stats.unverified,
      stats.byRole,
      stats.usersByRole,
      stats.recentRegistrations,
      stats.averageRegistrationsPerDay,
      stats.newUsersThisMonth
    );

    this.logger.info('User statistics retrieved successfully', {
      totalUsers: stats.totalUsers,
      activeUsers: stats.activeUsers,
      verifiedUsers: stats.verifiedUsers,
      growthRate: userStatistics.getGrowthRate(),
      verificationRate: userStatistics.getVerificationRate()
    });

    return userStatistics.toUserStats();
  }

  async getUsersByRole(role: UserRole): Promise<UserSummary[]> {
    this.logger.info('Getting users by role', { role });

    const users = await this.userRepository.getUsersByRole(role);

    this.logger.info('Users by role retrieved successfully', { 
      role, 
      count: users.length 
    });

    return users;
  }

  async getActiveUsers(): Promise<UserSummary[]> {
    this.logger.info('Getting active users');

    const users = await this.userRepository.getActiveUsers();

    this.logger.info('Active users retrieved successfully', { 
      count: users.length 
    });

    return users;
  }

  async getVerifiedUsers(): Promise<UserSummary[]> {
    this.logger.info('Getting verified users');

    const users = await this.userRepository.getVerifiedUsers();

    this.logger.info('Verified users retrieved successfully', { 
      count: users.length 
    });

    return users;
  }

  async bulkUpdateUsers(userIds: string[], updates: Partial<UpdateUserRequest>): Promise<number> {
    this.logger.info('Bulk updating users', { 
      userIdsCount: userIds.length, 
      updatedFields: Object.keys(updates) 
    });

    // Create domain model for validation
    const bulkOperationData = UserManagementModel.createBulkOperationData({
      userIds,
      updates
    });

    // Check email uniqueness if email is being updated
    if (updates.email) {
      const emailExists = await this.userRepository.userExists(updates.email);
      if (emailExists) {
        throw new ConflictError('User with this email already exists');
      }
    }

    const updatedCount = await this.userRepository.bulkUpdateUsers(userIds, updates);

    this.logger.info('Bulk user update completed', { 
      requestedCount: bulkOperationData.getOperationSize(), 
      updatedCount 
    });

    return updatedCount;
  }

  async bulkDeleteUsers(userIds: string[]): Promise<number> {
    this.logger.info('Bulk deleting users', { userIdsCount: userIds.length });

    const deletedCount = await this.userRepository.bulkDeleteUsers(userIds);

    this.logger.info('Bulk user deletion completed', { 
      requestedCount: userIds.length, 
      deletedCount 
    });

    return deletedCount;
  }

  async userExists(email: string): Promise<boolean> {
    return this.userRepository.userExists(email);
  }

  async countUsers(): Promise<number> {
    return this.userRepository.countUsers();
  }

  async countUsersByRole(role: UserRole): Promise<number> {
    return this.userRepository.countUsersByRole(role);
  }
} 