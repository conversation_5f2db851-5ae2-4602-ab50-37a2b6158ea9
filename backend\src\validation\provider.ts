import Joi from 'joi';

// Common helpers
export const idParamSchema = Joi.object({ id: Joi.string().required() });
export const providerIdParamSchema = Joi.object({ id: Joi.string().required() });
export const serviceIdParamSchema = Joi.object({ serviceId: Joi.string().required() });
export const providerServiceIdParamSchema = Joi.object({ serviceId: Joi.string().required() });
export const areaIdParamSchema = Joi.object({ areaId: Joi.string().required() });
export const zipParamSchema = Joi.object({ zipCode: Joi.string().max(20).required() });

// Provider list/search
export const providerListQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  search: Joi.string().allow('', null),
  isVerified: Joi.boolean(),
  isActive: Joi.boolean(),
  serviceId: Joi.string(),
  zipCode: Joi.string().max(20),
  radiusMiles: Joi.number().integer().min(1).max(200),
});

// Provider CRUD
export const createProviderSchema = Joi.object({
  userId: Joi.string().required(),
  businessName: Joi.string().max(255).required(),
  description: Joi.string().allow('', null),
  email: Joi.string().email().max(255).allow(null, ''),
  website: Joi.string().uri().max(500).allow(null, ''),
  phone: Joi.string().max(20).allow(null, ''),
});

export const updateProviderSchema = Joi.object({
  businessName: Joi.string().max(255),
  description: Joi.string().allow('', null),
  email: Joi.string().email().max(255).allow(null, ''),
  website: Joi.string().uri().max(500).allow(null, ''),
  phone: Joi.string().max(20).allow(null, ''),
  isActive: Joi.boolean(),
  isVerified: Joi.boolean(),
}).min(1);

// Provider services
export const createProviderServiceSchema = Joi.object({
  serviceId: Joi.string().required(),
  price: Joi.number().min(0).required(),
  description: Joi.string().allow('', null),
  isActive: Joi.boolean(),
});

export const updateProviderServiceSchema = Joi.object({
  price: Joi.number().min(0),
  description: Joi.string().allow('', null),
  isActive: Joi.boolean(),
}).min(1);

// Operating hours
export const upsertOperatingHoursSchema = Joi.object({
  hours: Joi.array()
    .items(
      Joi.object({
        dayOfWeek: Joi.number().integer().min(0).max(6).required(),
        openTime: Joi.string()
          .pattern(/^\d{2}:\d{2}$/)
          .required(),
        closeTime: Joi.string()
          .pattern(/^\d{2}:\d{2}$/)
          .required(),
      })
    )
    .min(1)
    .required(),
});

// Service areas
export const createServiceAreaSchema = Joi.object({
  streetAddress: Joi.string().max(255).required(),
  zipCode: Joi.string().max(20).required(),
  city: Joi.string().max(100).required(),
  state: Joi.string().max(50).required(),
  isActive: Joi.boolean(),
});

// Ratings
export const addRatingSchema = Joi.object({
  rating: Joi.number().integer().min(1).max(5).required(),
  comment: Joi.string().max(1000).allow('', null),
});

// Shared wrappers to keep same pattern as service.ts
export const validate = (schema: Joi.ObjectSchema) => (req: any, res: any, next: any) => {
  const { error, value } = schema.validate(req.body, { abortEarly: false, stripUnknown: true });
  if (error)
    return res.validationError(
      'Validation failed',
      error.details.map(d => ({ code: 'VALIDATION_ERROR', message: d.message }))
    );
  req.body = value;
  next();
};

export const validateQuery = (schema: Joi.ObjectSchema) => (req: any, res: any, next: any) => {
  const { error, value } = schema.validate(req.query, { abortEarly: false, stripUnknown: true });
  if (error)
    return res.validationError(
      'Validation failed',
      error.details.map(d => ({ code: 'VALIDATION_ERROR', message: d.message }))
    );
  req.query = value;
  next();
};

export const validateParams = (schema: Joi.ObjectSchema) => (req: any, res: any, next: any) => {
  const { error, value } = schema.validate(req.params, { abortEarly: false, stripUnknown: true });
  if (error)
    return res.validationError(
      'Validation failed',
      error.details.map(d => ({ code: 'VALIDATION_ERROR', message: d.message }))
    );
  req.params = value;
  next();
};
