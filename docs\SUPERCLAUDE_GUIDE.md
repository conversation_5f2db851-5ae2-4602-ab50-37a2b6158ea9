# SuperClaude Framework Guide for PrintWedittV1

## Table of Contents

1. [Overview](#overview)
2. [Installation](#installation)
3. [Framework Components](#framework-components)
4. [Available Commands](#available-commands)
5. [MCP Servers](#mcp-servers)
6. [Integration with PrintWedittV1](#integration-with-printwedittv1)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)
9. [Team Workflow](#team-workflow)

## Overview

SuperClaude is a configuration framework that enhances Claude Code with specialized commands, cognitive personas, and development methodologies. It provides 16 specialized slash commands for different development tasks and integrates with various MCP (Model Context Protocol) servers for enhanced capabilities.

### Key Features

- **16 Specialized Commands**: Tailored for different development tasks
- **MCP Server Integration**: Context7, Sequential Thinking, Magic, Playwright
- **Smart Routing**: Automatically picks the right tools based on your task
- **Development Methodologies**: Built-in support for various development approaches

## Installation

### Prerequisites

- Python 3.7+ (Python 3.13 recommended)
- Claude Code IDE
- Windows 10/11 (for this setup)

### Step 1: Install SuperClaude Package

```bash
# Using Python 3.13 (recommended)
py -3.13 -m pip install SuperClaude

# Alternative methods
python3 -m pip install SuperClaude
pip install SuperClaude
```

### Step 2: Run the Installer

```bash
# Developer profile (recommended for PrintWedittV1 team)
py -3.13 -m SuperClaude install --profile developer

# Other options
py -3.13 -m SuperClaude install --interactive  # Choose components
py -3.13 -m SuperClaude install --minimal     # Core only
```

### Step 3: Restart Claude Code

After installation, restart your Claude Code session to activate the framework.

## Framework Components

### 1. Core Framework

- **Location**: `~/.claude/`
- **Purpose**: Documentation and behavior files that guide Claude's responses
- **Files**: `COMMANDS.md`, `FLAGS.md`, `PERSONAS.md`, etc.

### 2. Slash Commands

- **Location**: `~/.claude/Commands/`
- **Purpose**: 16 specialized commands for development tasks
- **Usage**: Type `/` followed by command name

### 3. MCP Servers

- **Location**: `~/.claude/mcp/`
- **Purpose**: External service integrations
- **Servers**: Context7, Sequential Thinking, Magic, Playwright

## Available Commands

### Development Commands

#### `/sc:implement` - Feature Implementation

```bash
/sc:implement user authentication system
/sc:implement --priority high --complexity medium user profile management
```

**Use Case**: Implementing new features in PrintWedittV1

#### `/sc:build` - Compilation/Packaging

```bash
/sc:build --target production
/sc:build --optimize --minify
```

**Use Case**: Building the backend or frontend for deployment

#### `/sc:test` - Testing

```bash
/sc:test --type unit auth service
/sc:test --type integration --coverage
```

**Use Case**: Running tests for PrintWedittV1 components

#### `/sc:debug` - Debugging

```bash
/sc:debug --breakpoint auth:45
/sc:debug --watch --hot-reload
```

**Use Case**: Debugging issues in the application

### Code Quality Commands

#### `/sc:review` - Code Review

```bash
/sc:review --focus security --depth thorough
/sc:review --compare main --format detailed
```

**Use Case**: Reviewing code changes before merging

#### `/sc:refactor` - Code Refactoring

```bash
/sc:refactor --pattern strategy --target auth service
/sc:refactor --optimize --performance
```

**Use Case**: Improving code quality and performance

#### `/sc:optimize` - Performance Optimization

```bash
/sc:optimize --target database queries
/sc:optimize --profile memory --suggest
```

**Use Case**: Optimizing PrintWedittV1 performance

### Documentation Commands

#### `/sc:document` - Documentation

```bash
/sc:document --type api --format openapi
/sc:document --type user-guide --audience developers
```

**Use Case**: Creating API documentation for PrintWedittV1

#### `/sc:explain` - Code Explanation

```bash
/sc:explain --depth detailed --include examples
/sc:explain --focus algorithm --visualize
```

**Use Case**: Understanding complex code sections

### Project Management Commands

#### `/sc:plan` - Project Planning

```bash
/sc:plan --scope sprint --format agile
/sc:plan --estimate --breakdown
```

**Use Case**: Planning development sprints

#### `/sc:analyze` - Code Analysis

```bash
/sc:analyze --metrics complexity --threshold high
/sc:analyze --dependencies --visualize
```

**Use Case**: Analyzing codebase health

### Advanced Commands

#### `/sc:generate` - Code Generation

```bash
/sc:generate --type component --framework react
/sc:generate --type test --coverage 90
```

**Use Case**: Generating boilerplate code

#### `/sc:migrate` - Migration

```bash
/sc:migrate --from express --to fastify
/sc:migrate --database --strategy incremental
```

**Use Case**: Migrating between technologies

#### `/sc:deploy` - Deployment

```bash
/sc:deploy --environment staging --strategy blue-green
/sc:deploy --rollback --version 1.2.3
```

**Use Case**: Deploying PrintWedittV1

#### `/sc:monitor` - Monitoring

```bash
/sc:monitor --metrics performance --alert threshold
/sc:monitor --logs --filter error
```

**Use Case**: Monitoring application health

#### `/sc:security` - Security

```bash
/sc:security --scan --vulnerabilities
/sc:security --audit --compliance
```

**Use Case**: Security auditing

#### `/sc:scale` - Scaling

```bash
/sc:scale --horizontal --auto
/sc:scale --database --sharding
```

**Use Case**: Scaling the application

#### `/sc:workflow` - Implementation Workflow Generation

```bash
/sc:workflow "user authentication system" --strategy systematic --c7 --sequential
/sc:workflow payment-api --strategy mvp --risks --dependencies
/sc:workflow docs/feature-prd.md --persona security --output detailed
```

**Use Case**: Creating comprehensive implementation workflows from PRDs and feature requirements
**MCP Integration**: Context7 (patterns), Sequential (complex analysis), Magic (UI planning)
**Strategies**: systematic, agile, mvp
**Output Formats**: roadmap, tasks, detailed

## MCP Servers

SuperClaude integrates with external tools and services via MCP (Model Context Protocol) servers, enabling enhanced capabilities for different development tasks. These integrations auto-connect when needed and can be manually controlled with flags.

### 1. Context7

- **Purpose**: Official library documentation lookup and pattern extraction
- **Use Case**: Getting latest documentation for dependencies, framework patterns, best practices
- **Auto-Activation**: External library imports detected, framework-specific questions, Scribe persona active
- **Manual Activation**: `--c7` or `--context7` flags
- **Commands**:
  - `mcp_context7_resolve-library-id` - Find Context7-compatible library IDs
  - `mcp_context7_get-library-docs` - Retrieve official documentation
- **Workflow**: Library detection → ID resolution → Documentation retrieval → Pattern extraction → Implementation → Validation → Caching

### 2. Sequential Thinking

- **Purpose**: Complex multi-step reasoning and systematic analysis
- **Use Case**: Root cause analysis, performance bottleneck identification, architecture review, security threat modeling, code quality assessment
- **Auto-Activation**: Complex debugging scenarios, system design questions, `--think` flags
- **Manual Activation**: `--seq` or `--sequential` flags
- **Thinking Modes**:
  - `--think` (4K): Module-level analysis with context awareness
  - `--think-hard` (10K): System-wide analysis with architectural focus
  - `--ultrathink` (32K): Critical system analysis with comprehensive coverage
- **Workflow**: Problem decomposition → Server coordination → Systematic analysis → Relationship mapping → Hypothesis generation → Evidence gathering → Multi-server synthesis → Recommendation generation → Validation

### 3. Magic (21st.dev)

- **Purpose**: Modern UI component generation and design system integration
- **Use Case**: Creating React components, design systems, UI improvements
- **Auto-Activation**: UI component requests, design system queries, frontend persona
- **Manual Activation**: `--magic` flag
- **Setup**: Requires `TWENTYFIRST_API_KEY` environment variable
- **Detection**: component/button/form keywords, JSX patterns, accessibility requirements

### 4. Playwright

- **Purpose**: Cross-browser automation, E2E testing, and performance monitoring
- **Use Case**: End-to-end testing, performance metrics, user experience measurement, visual testing
- **Auto-Activation**: Test workflows, QA persona, performance monitoring
- **Manual Activation**: `--play` or `--playwright` flags
- **Detection**: test/e2e keywords, performance monitoring, visual testing, cross-browser requirements

### MCP Server Control Flags

| Flag                      | Purpose                                    | Use Case                                    |
| ------------------------- | ------------------------------------------ | ------------------------------------------- |
| `--all-mcp`               | Enable all MCP servers simultaneously      | Complex multi-domain problems               |
| `--no-mcp`                | Disable all MCP servers, native tools only | Faster execution (40-60% faster)            |
| `--c7` / `--context7`     | Enable Context7 for documentation          | Working with frameworks, need official docs |
| `--seq` / `--sequential`  | Enable Sequential for complex analysis     | Complex debugging, system design            |
| `--magic`                 | Enable Magic for UI generation             | Creating UI components, design systems      |
| `--play` / `--playwright` | Enable Playwright for testing              | E2E testing, performance monitoring         |
| `--no-[server]`           | Disable specific MCP server                | Server-specific fallback strategies         |

### Performance Optimization

- **Fast Execution**: Use `--no-mcp` for simple tasks (40-60% faster)
- **Selective Activation**: Use specific flags like `--magic --no-seq` for targeted capabilities
- **Compression**: Combine with `--uc` for ultra-compressed output
- **Balanced Approach**: Use `--c7` for documentation lookup only

### Advanced MCP Usage Patterns

#### Multi-Domain Project Coordination

```bash
# Coordinate across domains with all MCP servers
/sc:analyze fullstack-app/ --all-mcp --delegate auto

# Domain-specific improvements with targeted MCPs
/sc:improve frontend/ --persona-frontend --magic
/sc:improve backend/ --persona-backend --c7
/sc:improve infrastructure/ --persona-devops --seq

# Integration validation
/sc:test --type integration --play
```

#### Complex Problem Solving

```bash
# Root cause analysis with Sequential thinking
/sc:troubleshoot "auth randomly fails" --seq

# Performance optimization with Playwright metrics
/sc:analyze api/ --persona-performance --play

# Security audit with comprehensive analysis
/sc:scan user-authentication/ --focus security --seq
```

#### UI Development Workflow

```bash
# Generate modern UI components
/sc:build dashboard --magic

# Test components with E2E validation
/sc:test dashboard/ --play

# Document components with Context7 patterns
/sc:document components/ --c7
```

#### Error Recovery Strategies

- **Library not found**: WebSearch fallback, manual implementation
- **Documentation timeout**: Use cached knowledge, note limitations
- **Server unavailable**: Activate backup instances, graceful degradation
- **Version mismatch**: Find compatible version, suggest upgrade path

## Integration with PrintWedittV1

### Backend Development Workflow

#### 1. Feature Implementation

```bash
# Start with planning
/sc:plan --scope "user authentication" --format agile

# Implement the feature
/sc:implement --priority high user authentication system

# Generate tests
/sc:generate --type test --coverage 90 auth service

# Run tests
/sc:test --type unit auth service
/sc:test --type integration --coverage

# Review code
/sc:review --focus security --depth thorough
```

#### 2. API Development

```bash
# Generate API documentation
/sc:document --type api --format openapi

# Implement API endpoints
/sc:implement --target "user management API"

# Test API endpoints
/sc:test --type integration --target api

# Optimize performance
/sc:optimize --target "database queries"
```

### Frontend Development Workflow

#### 1. Component Development

```bash
# Generate React components
/sc:generate --type component --framework react

# Implement UI features
/sc:implement --target "user dashboard"

# Test components
/sc:test --type unit --target components

# Optimize bundle size
/sc:optimize --target "bundle size"
```

#### 2. E2E Testing

```bash
# Set up Playwright tests
/sc:generate --type e2e --framework playwright

# Run E2E tests
/sc:test --type e2e --browser chrome
```

### Database Development

#### 1. Schema Management

```bash
# Analyze database schema
/sc:analyze --target "database schema"

# Generate migrations
/sc:generate --type migration --database postgresql

# Test migrations
/sc:test --type migration --rollback
```

#### 2. Performance Optimization

```bash
# Analyze query performance
/sc:analyze --target "database queries"

# Optimize slow queries
/sc:optimize --target "database performance"

# Monitor database health
/sc:monitor --target "database metrics"
```

## Best Practices

### 1. Command Usage

- **Start with planning**: Use `/sc:plan` before implementing features
- **Test early**: Use `/sc:test` throughout development
- **Review regularly**: Use `/sc:review` before merging code
- **Document as you go**: Use `/sc:document` for new features

### 2. Project Structure

```
PrintWedittV1/
├── backend/           # Backend application
├── frontend/          # Frontend application
├── docs/             # Documentation (including this guide)
├── docker/           # Docker configuration
└── .vscode/          # VS Code configuration
```

### 3. Development Workflow

1. **Plan**: Use `/sc:plan` to define requirements
2. **Implement**: Use `/sc:implement` for feature development
3. **Test**: Use `/sc:test` for validation
4. **Review**: Use `/sc:review` for quality assurance
5. **Document**: Use `/sc:document` for knowledge sharing
6. **Deploy**: Use `/sc:deploy` for releases

### 4. Code Quality

- **Regular reviews**: Use `/sc:review` for all changes
- **Performance monitoring**: Use `/sc:optimize` and `/sc:monitor`
- **Security audits**: Use `/sc:security` regularly
- **Refactoring**: Use `/sc:refactor` to improve code quality

## Troubleshooting

### Common Issues

#### 1. Command Not Found

```bash
# Solution: Restart Claude Code session
# Verify installation
py -3.13 -m SuperClaude --version
```

#### 2. MCP Server Issues

```bash
# Check MCP server status
py -3.13 -m SuperClaude status

# Reinstall MCP servers
py -3.13 -m SuperClaude install --component mcp

# Diagnose MCP activation issues
/sc:troubleshoot "MCP servers not working" --introspect

# Test specific servers
/sc:analyze react-app/ --c7     # Should use Context7
/sc:troubleshoot issue --seq    # Should use Sequential
/sc:build ui/ --magic           # Should use Magic
/sc:test app/ --play            # Should use Playwright

# Force MCP activation
/sc:analyze code/ --all-mcp

# Use fallback approaches
/sc:analyze react-app/ --no-mcp  # Use native tools if MCP unavailable
```

#### 3. Environment Variables

```bash
# Set required environment variables
set TWENTYFIRST_API_KEY=your_api_key_here
```

#### 4. MCP Server Performance Issues

```bash
# Disable MCP for speed
/sc:analyze large-project/ --no-mcp

# Use selective MCP activation
/sc:analyze react-code/ --magic --no-seq  # Only UI generation, skip analysis

# Optimize MCP usage
/sc:analyze code/ --uc --c7  # Compression + documentation only

# Performance-optimized patterns
/sc:analyze huge-project/ --uc --no-mcp --scope module
/sc:improve monorepo/ --delegate auto --uc --concurrency 5
```

#### 4. Python Version Issues

```bash
# Use specific Python version
py -3.13 -m SuperClaude install
```

### Debugging Commands

```bash
# Check SuperClaude status
py -3.13 -m SuperClaude status

# View logs
py -3.13 -m SuperClaude logs

# Reinstall framework
py -3.13 -m SuperClaude install --force
```

## Team Workflow

### 1. Onboarding New Team Members

1. Install SuperClaude Framework
2. Review this documentation
3. Practice with sample commands
4. Set up environment variables
5. Join team development workflow

### 2. Daily Development Workflow

1. **Morning Standup**: Use `/sc:plan` to review daily tasks
2. **Development**: Use `/sc:implement` for feature work
3. **Testing**: Use `/sc:test` for validation
4. **Code Review**: Use `/sc:review` before commits
5. **Documentation**: Use `/sc:document` for updates

### 3. Sprint Planning

1. **Sprint Planning**: Use `/sc:plan` for sprint scope
2. **Estimation**: Use `/sc:analyze` for complexity assessment
3. **Implementation**: Use `/sc:implement` for features
4. **Testing**: Use `/sc:test` for quality assurance
5. **Deployment**: Use `/sc:deploy` for releases

### 4. Code Review Process

1. **Automated Review**: Use `/sc:review` for initial assessment
2. **Security Review**: Use `/sc:security` for vulnerabilities
3. **Performance Review**: Use `/sc:optimize` for improvements
4. **Documentation Review**: Use `/sc:document` for completeness

### 5. Release Process

1. **Pre-release Testing**: Use `/sc:test` for comprehensive testing
2. **Performance Check**: Use `/sc:optimize` for optimization
3. **Security Audit**: Use `/sc:security` for security review
4. **Documentation Update**: Use `/sc:document` for release notes
5. **Deployment**: Use `/sc:deploy` for production release

## Resources

### Official Documentation

- [SuperClaude Framework GitHub](https://github.com/SuperClaude-Org/SuperClaude_Framework)
- [User Guide](https://superclaude-org.github.io/)
- [Commands Guide](https://superclaude-org.github.io/commands)
- [Installation Guide](https://superclaude-org.github.io/installation)

### PrintWedittV1 Specific

- [Backend API Documentation](./backend/docs/API.md)
- [Frontend Component Library](./frontend/src/components/)
- [Database Schema](./backend/prisma/schema.prisma)
- [Docker Configuration](./docker-compose.yml)

### Support

- **Team Lead**: Contact for framework questions
- **Documentation**: This guide and official docs
- **Issues**: GitHub issues for bug reports
- **Community**: SuperClaude community discussions

---

**Last Updated**: August 5, 2025
**Version**: SuperClaude v3.0.0.2
**PrintWedittV1 Version**: 1.0.0

---

_This guide is maintained by the PrintWedittV1 development team. For updates or questions, please contact the team lead._
