import {createClient, RedisClientType} from 'redis';
import {config} from '../config';
import {createLogger} from '../utils/logger';

const logger = createLogger('Redis');

/**
 * Redis Service - Centralized Redis Management
 *
 * This service provides a singleton Redis client instance with proper
 * connection management, error handling, and lifecycle management.
 */
class RedisService {
	private static instance: RedisService;
	private client: RedisClientType;
	private isConnected = false;
	private isInitialized = false;

	private constructor() {
		this.client = createClient();
	}

	/**
	 * Get singleton instance
	 */
	public static getInstance(): RedisService {
		if (!RedisService.instance) {
			RedisService.instance = new RedisService();
		}
		return RedisService.instance;
	}

	/**
	 * Initialize Redis connection
	 */
	public async initialize(): Promise<void> {
		if (this.isInitialized) {
			logger.warn('Redis service already initialized');
			return;
		}

		try {
			const redisConfig = config.getRedisConfig();

			logger.info('Initializing Redis connection', {
				url: redisConfig.url.replace(/\/\/.*@/, '//***:***@'), // Mask credentials
				db: redisConfig.db,
				keyPrefix: redisConfig.keyPrefix,
			});

			// Configure Redis client
			this.client = createClient({
				url: redisConfig.url,
				password: redisConfig.password,
				database: redisConfig.db,
			});

			// Set up event handlers
			this.client.on('connect', () => {
				logger.info('Redis client connecting...');
			});

			this.client.on('ready', () => {
				logger.info('Redis client ready');
				this.isConnected = true;
			});

			this.client.on('error', (error) => {
				logger.error('Redis client error', error);
				this.isConnected = false;
			});

			this.client.on('end', () => {
				logger.info('Redis client disconnected');
				this.isConnected = false;
			});

			// Connect to Redis
			await this.client.connect();
			this.isInitialized = true;

			logger.info('Redis connection established successfully');
		} catch (error) {
			logger.error('Failed to initialize Redis connection', error);
			throw error;
		}
	}

	/**
	 * Get Redis client instance
	 */
	public getClient(): RedisClientType {
		if (!this.isInitialized) {
			throw new Error(
				'Redis service not initialized. Call initialize() first.'
			);
		}
		return this.client;
	}

	/**
	 * Check if Redis is connected
	 */
	public isConnectedToRedis(): boolean {
		return this.isConnected;
	}

	/**
	 * Disconnect from Redis
	 */
	public async disconnect(): Promise<void> {
		if (!this.isConnected) {
			logger.warn('Redis already disconnected');
			return;
		}

		try {
			await this.client.quit();
			this.isConnected = false;
			this.isInitialized = false;
			logger.info('Redis disconnected successfully');
		} catch (error) {
			logger.error('Error disconnecting from Redis', error);
			throw error;
		}
	}

	/**
	 * Health check for Redis connection
	 */
	public async healthCheck(): Promise<boolean> {
		try {
			await this.client.ping();
			return true;
		} catch (error) {
			logger.error('Redis health check failed', error);
			return false;
		}
	}

	/**
	 * Get Redis configuration
	 */
	public getConfig() {
		return config.getRedisConfig();
	}
}

// Export singleton instance
export const redisService = RedisService.getInstance();

// Export the class for testing purposes
export {RedisService};
