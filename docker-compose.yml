services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: printweditt-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: printweditt_db
      POSTGRES_USER: printweditt_user
      POSTGRES_PASSWORD: printweditt_password
      POSTGRES_INITDB_ARGS: '--encoding=UTF-8 --lc-collate=C --lc-ctype=C'
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - printweditt-network
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U printweditt_user -d printweditt_db']
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: printweditt-redis
    restart: unless-stopped
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - printweditt-network
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO (S3-compatible object storage for file uploads)
  minio:
    image: minio/minio:latest
    container_name: printweditt-minio
    restart: unless-stopped
    ports:
      - '9000:9000'
      - '9001:9001'
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
      MINIO_CONSOLE_ADDRESS: ':9001'
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - printweditt-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9000/minio/health/live']
      interval: 30s
      timeout: 10s
      retries: 3

  # Mailhog (Email testing service)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: printweditt-mailhog
    restart: unless-stopped
    ports:
      - '1025:1025' # SMTP
      - '8025:8025' # Web UI
    networks:
      - printweditt-network

  # Elasticsearch (for search functionality)
  # Note: Requires at least 2GB RAM. For development, consider using 'core' mode instead.
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: printweditt-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - 'ES_JAVA_OPTS=-Xms256m -Xmx256m'
      - bootstrap.memory_lock=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - '9200:9200'
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - printweditt-network
    healthcheck:
      test:
        ['CMD-SHELL', 'curl -f http://localhost:9200/_cluster/health || exit 1']
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 120s

  # Kibana (for Elasticsearch visualization)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: printweditt-kibana
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - '5601:5601'
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - printweditt-network
    healthcheck:
      test: ['CMD-SHELL', 'curl -f http://localhost:5601/api/status || exit 1']
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 60s

  # Prometheus (for monitoring)
  prometheus:
    image: prom/prometheus:latest
    container_name: printweditt-prometheus
    restart: unless-stopped
    ports:
      - '9090:9090'
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - printweditt-network

  # Grafana (for monitoring dashboards)
  grafana:
    image: grafana/grafana:latest
    container_name: printweditt-grafana
    restart: unless-stopped
    ports:
      - '3001:3000'
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - printweditt-network

  # Nginx (reverse proxy)
  nginx:
    image: nginx:alpine
    container_name: printweditt-nginx
    restart: unless-stopped
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - postgres
      - redis
    networks:
      - printweditt-network

  # Backend API (your Node.js application)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: printweditt-backend
    restart: unless-stopped
    ports:
      - '5000:3001'
    environment:
      - NODE_ENV=development
      - PORT=3001
      - DATABASE_URL=****************************************************************/printweditt_db
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=super-secure-jwt-secret-key-that-is-at-least-32-characters-long-for-development-only
      - JWT_REFRESH_SECRET=super-secure-refresh-secret-key-that-is-at-least-32-characters-long-for-development-only
      - API_BASE_URL=http://localhost:5000
      - FRONTEND_URL=http://localhost:5173
      - CORS_ORIGIN=http://localhost:5173
      - COOKIE_SECRET=super-secure-cookie-secret-key-that-is-at-least-32-characters-long-for-development-only
      - AWS_ACCESS_KEY_ID=development-key
      - AWS_SECRET_ACCESS_KEY=development-secret
      - AWS_S3_BUCKET=printco-assets
      - GOOGLE_CLIENT_ID=development-google-client-id
      - GOOGLE_CLIENT_SECRET=development-google-client-secret
      - GOOGLE_CALLBACK_URL=http://localhost:5000/api/auth/google/callback
      - EMAIL_USER=<EMAIL>
      - EMAIL_PASSWORD=development-password
      - EMAIL_FROM=<EMAIL>
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - SMTP_HOST=mailhog
      - SMTP_PORT=1025
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - printweditt-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3001/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (React application) - Production
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: printweditt-frontend
    restart: unless-stopped
    ports:
      - '5173:5173'
    environment:
      - NODE_ENV=production
      - VITE_API_BASE_URL=http://localhost:5000
    depends_on:
      - backend
    networks:
      - printweditt-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5173']
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (React application) - Development
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: printweditt-frontend-dev
    restart: unless-stopped
    ports:
      - '5174:5173'
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:5000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - printweditt-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:5173']
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  printweditt-network:
    driver: bridge
