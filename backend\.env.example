# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/printco_db?schema=public"

# JWT Configuration
JWT_SECRET="your-secure-jwt-secret-key-here"
JWT_REFRESH_SECRET="your-secure-refresh-secret-key-here"
JWT_ACCESS_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Server Configuration
PORT=3001
NODE_ENV="development"
API_BASE_URL="http://localhost:3001"
FRONTEND_URL="http://localhost:5173"

# Redis Configuration (for caching and sessions)
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=""

# AWS S3 Configuration (for file uploads)
AWS_ACCESS_KEY_ID="development-key"
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="printco-assets"
AWS_CLOUDFRONT_URL="https://cdn.printco.com"

# Google OAuth Configuration
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_CALLBACK_URL="http://localhost:3001/api/auth/google/callback"

# Email Configuration (for notifications and auth)
EMAIL_SERVICE="gmail"
EMAIL_HOST="smtp.gmail.com"
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER="<EMAIL>"
EMAIL_PASSWORD="your-email-app-password"
EMAIL_FROM="PrintWeditt <<EMAIL>>"

# Password Reset Configuration
RESET_TOKEN_EXPIRES_IN="1h"
EMAIL_VERIFICATION_TOKEN_EXPIRES_IN="24h"

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_AUTH_MAX=5

# File Upload Configuration
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES="jpg,jpeg,png,webp,pdf,ai,psd"

# Logging Configuration
LOG_LEVEL="info"
LOG_FILE="logs/app.log"

# Security Configuration
BCRYPT_ROUNDS=12
CORS_ORIGIN="http://localhost:5173"
COOKIE_SECRET="your-cookie-secret-key"

# Development Configuration
ENABLE_SWAGGER=true
ENABLE_LOGGING=true
ENABLE_CORS=true