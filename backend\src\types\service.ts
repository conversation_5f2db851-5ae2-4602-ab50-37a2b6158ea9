import { FormFieldType, ServicePricingType } from '@prisma/client';

// Service creation request
export interface CreateServiceRequest {
  name: string;
  description: string;
  detailedDesc?: string;
  features?: string[];
  notes?: string;
  categoryId: string;
  image: string;
  basePrice: number;
  pricingType?: ServicePricingType;
  isActive?: boolean;
  sortOrder?: number;
}

// Service update request
export interface UpdateServiceRequest {
  name?: string;
  description?: string;
  detailedDesc?: string;
  features?: string[];
  notes?: string;
  categoryId?: string;
  image?: string;
  basePrice?: number;
  pricingType?: ServicePricingType;
  isActive?: boolean;
  sortOrder?: number;
}

// Service list query parameters
export interface ServiceListQuery {
  page?: number;
  limit?: number;
  search?: string;
  categoryId?: string;
  pricingType?: ServicePricingType;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  minPrice?: number;
  maxPrice?: number;
}

// Service list response
export interface ServiceListResponse {
  services: ServiceSummary[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Service summary (for list views)
export interface ServiceSummary {
  id: string;
  name: string;
  description: string;
  categoryId: string;
  categoryName?: string;
  image: string;
  basePrice: number;
  pricingType: ServicePricingType;
  isActive: boolean;
  sortOrder: number;
  providerCount?: number;
  createdAt: Date;
  updatedAt: Date;
}

// Service detail (for single service views)
export interface ServiceDetail extends ServiceSummary {
  detailedDesc?: string | null;
  features: string[];
  notes?: string | null;
  formFields: ServiceFormFieldDetail[];
  category: ServiceCategoryDetail;
  providers?: ProviderServiceDetail[];
}

// Service statistics
export interface ServiceStats {
  total: number;
  active: number;
  inactive: number;
  byCategory: Record<string, number>;
  byPricingType: Record<ServicePricingType, number>;
  averageBasePrice: number;
  totalProviders: number;
  averageProvidersPerService: number;
}

// Service search and filtering
export interface ServiceSearchQuery {
  q?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  pricingType?: ServicePricingType;
  features?: string[];
  location?: {
    zipCode: string;
    radius?: number;
  };
  sortBy?: 'name' | 'price' | 'popularity' | 'rating';
  sortOrder?: 'asc' | 'desc';
}

export interface ServiceSearchResult {
  services: ServiceDetail[];
  total: number;
  query: ServiceSearchQuery;
  filters: {
    categories: Array<{ id: string; name: string; count: number }>;
    priceRange: { min: number; max: number };
    features: Array<{ feature: string; count: number }>;
  };
}

// Service category types
export interface ServiceCategorySummary {
  id: string;
  name: string;
  description?: string | null;
  icon?: string | null;
  route: string;
  isActive: boolean;
  sortOrder: number;
  serviceCount?: number;
}

export interface ServiceCategoryDetail extends ServiceCategorySummary {
  services?: ServiceSummary[];
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateServiceCategoryRequest {
  name: string;
  description?: string;
  icon?: string;
  route: string;
  isActive?: boolean;
  sortOrder?: number;
}

export interface UpdateServiceCategoryRequest {
  name?: string;
  description?: string;
  icon?: string;
  route?: string;
  isActive?: boolean;
  sortOrder?: number;
}

// Service form field types
export interface ServiceFormFieldSummary {
  id: string;
  serviceId: string;
  name: string;
  label: string;
  type: FormFieldType;
  required: boolean;
  placeholder?: string | null;
  defaultValue?: string | null;
  validation?: any;
  sortOrder: number;
  isActive: boolean;
}

export interface ServiceFormFieldDetail extends ServiceFormFieldSummary {
  options: ServiceFormFieldOptionDetail[];
  createdAt: Date;
}

export interface CreateServiceFormFieldRequest {
  serviceId: string;
  name: string;
  label: string;
  type: FormFieldType;
  required?: boolean;
  placeholder?: string;
  defaultValue?: string;
  validation?: any;
  sortOrder?: number;
  isActive?: boolean;
  options?: CreateServiceFormFieldOptionRequest[];
}

export interface UpdateServiceFormFieldRequest {
  name?: string;
  label?: string;
  type?: FormFieldType;
  required?: boolean;
  placeholder?: string;
  defaultValue?: string;
  validation?: any;
  sortOrder?: number;
  isActive?: boolean;
}

// Service form field option types
export interface ServiceFormFieldOptionSummary {
  id: string;
  fieldId: string;
  value: string;
  label: string;
  priceModifier?: number | null;
  sortOrder: number;
  isActive: boolean;
}

export interface ServiceFormFieldOptionDetail extends ServiceFormFieldOptionSummary {
  priceModifier?: number | null;
  createdAt: Date;
}

export interface CreateServiceFormFieldOptionRequest {
  fieldId: string;
  value: string;
  label: string;
  priceModifier?: number;
  sortOrder?: number;
  isActive?: boolean;
}

export interface UpdateServiceFormFieldOptionRequest {
  value?: string;
  label?: string;
  priceModifier?: number;
  sortOrder?: number;
  isActive?: boolean;
}

// Provider service types (for service-provider relationships)
export interface ProviderServiceDetail {
  id: string;
  providerId: string;
  serviceId: string;
  price: number;
  description?: string | null;
  isActive: boolean;
  provider: {
    id: string;
    businessName: string;
    isVerified: boolean;
    isActive: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Price calculation types
export interface PriceCalculationRequest {
  serviceId: string;
  formData: Record<string, any>;
  quantity?: number;
  providerId?: string;
}

export interface PriceCalculationResponse {
  basePrice: number;
  modifiers: Array<{
    fieldName: string;
    optionValue: string;
    modifier: number;
  }>;
  subtotal: number;
  quantity: number;
  total: number;
  providerId?: string;
  providerPrice?: number;
}

// Bulk operations
export interface BulkServiceOperation {
  serviceIds: string[];
  updates?: Partial<UpdateServiceRequest>;
}

// Repository data types (sanitized for storage)
export interface CreateServiceData {
  id: string;
  name: string;
  description: string;
  detailedDesc?: string | null;
  features: string[];
  notes?: string | null;
  categoryId: string;
  image: string;
  basePrice: number;
  pricingType: ServicePricingType;
  isActive: boolean;
  sortOrder: number;
}

export interface UpdateServiceData {
  name?: string;
  description?: string;
  detailedDesc?: string | null;
  features?: string[];
  notes?: string | null;
  categoryId?: string;
  image?: string;
  basePrice?: number;
  pricingType?: ServicePricingType;
  isActive?: boolean;
  sortOrder?: number;
}

export interface CreateServiceCategoryData {
  id: string;
  name: string;
  description?: string | null;
  icon?: string | null;
  route: string;
  isActive: boolean;
  sortOrder: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface UpdateServiceCategoryData {
  name?: string;
  description?: string | null;
  icon?: string | null;
  route?: string;
  isActive?: boolean;
  sortOrder?: number;
}

export interface CreateServiceFormFieldData {
  id: string;
  serviceId: string;
  name: string;
  label: string;
  type: FormFieldType;
  required: boolean;
  placeholder?: string | null;
  defaultValue?: string | null;
  validation?: any;
  sortOrder: number;
  isActive: boolean;
}

export interface UpdateServiceFormFieldData {
  name?: string;
  label?: string;
  type?: FormFieldType;
  required?: boolean;
  placeholder?: string | null;
  defaultValue?: string | null;
  validation?: any;
  sortOrder?: number;
  isActive?: boolean;
}

export interface CreateServiceFormFieldOptionData {
  id: string;
  fieldId: string;
  value: string;
  label: string;
  priceModifier?: number | null;
  sortOrder: number;
  isActive: boolean;
}

export interface UpdateServiceFormFieldOptionData {
  value?: string;
  label?: string;
  priceModifier?: number | null;
  sortOrder?: number;
  isActive?: boolean;
}