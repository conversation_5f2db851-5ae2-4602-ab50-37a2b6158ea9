"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthRepository = void 0;
const client_1 = require("@prisma/client");
class AuthRepository {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findUserByEmail(email) {
        return this.prisma.user.findUnique({
            where: { email: email.toLowerCase() }
        });
    }
    async findUserById(id) {
        return this.prisma.user.findUnique({
            where: { id }
        });
    }
    async findUserByIdSafe(id) {
        const user = await this.prisma.user.findUnique({
            where: { id },
            select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
                role: true,
                isActive: true,
                isVerified: true,
                avatar: true,
                phone: true,
                createdAt: true,
                updatedAt: true,
                lastLoginAt: true
            }
        });
        return user;
    }
    async createUser(userData) {
        return this.prisma.user.create({
            data: {
                email: userData.email.toLowerCase(),
                password: userData.password,
                firstName: userData.firstName.trim(),
                lastName: userData.lastName.trim(),
                phone: userData.phone?.trim() || null,
                role: userData.role || client_1.UserRole.CUSTOMER,
                isActive: userData.isActive ?? true,
                isVerified: userData.isVerified ?? false
            }
        });
    }
    async updateUser(id, data) {
        return this.prisma.user.update({
            where: { id },
            data: {
                ...(data.firstName && { firstName: data.firstName.trim() }),
                ...(data.lastName && { lastName: data.lastName.trim() }),
                ...(data.phone !== undefined && { phone: data.phone?.trim() || null }),
                ...(data.avatar !== undefined && { avatar: data.avatar?.trim() || null }),
                ...(data.emailVerified !== undefined && { emailVerified: data.emailVerified }),
                ...(data.emailVerificationToken !== undefined && {
                    emailVerificationToken: data.emailVerificationToken
                }),
                ...(data.emailVerificationTokenExpires !== undefined && {
                    emailVerificationTokenExpires: data.emailVerificationTokenExpires
                }),
                ...(data.passwordResetToken !== undefined && {
                    passwordResetToken: data.passwordResetToken
                }),
                ...(data.passwordResetTokenExpires !== undefined && {
                    passwordResetTokenExpires: data.passwordResetTokenExpires
                }),
                ...(data.lastLoginAt && { lastLoginAt: data.lastLoginAt }),
                ...(data.loginAttempts !== undefined && { loginAttempts: data.loginAttempts }),
                ...(data.lockedUntil !== undefined && { lockedUntil: data.lockedUntil })
            }
        });
    }
    async updateUserPassword(id, hashedPassword) {
        await this.prisma.user.update({
            where: { id },
            data: { password: hashedPassword }
        });
    }
    async incrementLoginAttempts(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            select: { loginAttempts: true }
        });
        if (!user)
            return;
        const newAttempts = user.loginAttempts + 1;
        const maxAttempts = 5;
        const updateData = {
            loginAttempts: newAttempts
        };
        if (newAttempts >= maxAttempts) {
            const lockUntil = new Date();
            lockUntil.setMinutes(lockUntil.getMinutes() + 30);
            updateData.lockedUntil = lockUntil;
        }
        await this.prisma.user.update({
            where: { id: userId },
            data: updateData
        });
    }
    async resetLoginAttempts(userId) {
        await this.prisma.user.update({
            where: { id: userId },
            data: {
                loginAttempts: 0,
                lockedUntil: null,
                lastLoginAt: new Date()
            }
        });
    }
    async isAccountLocked(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            select: { lockedUntil: true }
        });
        if (!user?.lockedUntil)
            return false;
        if (user.lockedUntil > new Date()) {
            return true;
        }
        else {
            await this.prisma.user.update({
                where: { id: userId },
                data: {
                    lockedUntil: null,
                    loginAttempts: 0
                }
            });
            return false;
        }
    }
    async getAccountLockInfo(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            select: {
                loginAttempts: true,
                lockedUntil: true
            }
        });
        if (!user)
            return null;
        const isLocked = user.lockedUntil ? user.lockedUntil > new Date() : false;
        const maxAttempts = 5;
        return {
            isLocked,
            lockExpires: user.lockedUntil,
            attempts: user.loginAttempts,
            maxAttempts,
            remainingAttempts: Math.max(0, maxAttempts - user.loginAttempts)
        };
    }
    async findUserByPasswordResetToken(token) {
        return this.prisma.user.findFirst({
            where: {
                passwordResetToken: token,
                passwordResetTokenExpires: { gt: new Date() }
            }
        });
    }
    async setPasswordResetToken(userId, token, expiresAt) {
        await this.prisma.user.update({
            where: { id: userId },
            data: {
                passwordResetToken: token,
                passwordResetTokenExpires: expiresAt
            }
        });
    }
    async clearPasswordResetToken(userId) {
        await this.prisma.user.update({
            where: { id: userId },
            data: {
                passwordResetToken: null,
                passwordResetTokenExpires: null
            }
        });
    }
    async findUserByEmailVerificationToken(token) {
        return this.prisma.user.findFirst({
            where: {
                emailVerificationToken: token,
                emailVerificationTokenExpires: { gt: new Date() }
            }
        });
    }
    async setEmailVerificationToken(userId, token, expiresAt) {
        await this.prisma.user.update({
            where: { id: userId },
            data: {
                emailVerificationToken: token,
                emailVerificationTokenExpires: expiresAt
            }
        });
    }
    async markEmailAsVerified(userId) {
        await this.prisma.user.update({
            where: { id: userId },
            data: {
                isVerified: true,
                emailVerified: new Date(),
                emailVerificationToken: null,
                emailVerificationTokenExpires: null
            }
        });
    }
    async clearEmailVerificationToken(userId) {
        await this.prisma.user.update({
            where: { id: userId },
            data: {
                emailVerificationToken: null,
                emailVerificationTokenExpires: null
            }
        });
    }
    async createSession(sessionData) {
        return this.prisma.user_sessions.create({
            data: {
                id: sessionData.id,
                userId: sessionData.userId,
                refreshToken: sessionData.refreshToken,
                expiresAt: sessionData.expiresAt,
                ipAddress: sessionData.ipAddress.substring(0, 45),
                userAgent: sessionData.userAgent.substring(0, 500),
                isActive: true
            }
        });
    }
    async findSessionByRefreshToken(refreshToken) {
        return this.prisma.user_sessions.findUnique({
            where: { refreshToken }
        });
    }
    async revokeSession(refreshToken) {
        await this.prisma.user_sessions.updateMany({
            where: { refreshToken },
            data: {
                isActive: false,
                revokedAt: new Date()
            }
        });
    }
    async revokeAllUserSessions(userId) {
        await this.prisma.user_sessions.updateMany({
            where: { userId },
            data: {
                isActive: false,
                revokedAt: new Date()
            }
        });
    }
    async getUserSessions(userId) {
        const sessions = await this.prisma.user_sessions.findMany({
            where: {
                userId,
                isActive: true,
                expiresAt: { gt: new Date() }
            },
            select: {
                id: true,
                ipAddress: true,
                userAgent: true,
                createdAt: true,
                expiresAt: true,
                isActive: true
            },
            orderBy: { createdAt: 'desc' }
        });
        return sessions;
    }
    async revokeSpecificSession(sessionId, userId) {
        const session = await this.prisma.user_sessions.findFirst({
            where: {
                id: sessionId,
                userId
            }
        });
        if (!session) {
            return false;
        }
        await this.prisma.user_sessions.update({
            where: { id: sessionId },
            data: {
                isActive: false,
                revokedAt: new Date()
            }
        });
        return true;
    }
    async cleanupExpiredSessions() {
        const result = await this.prisma.user_sessions.deleteMany({
            where: {
                OR: [
                    { expiresAt: { lt: new Date() } },
                    {
                        isActive: false,
                        revokedAt: { lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
                    }
                ]
            }
        });
        return result.count;
    }
}
exports.AuthRepository = AuthRepository;
exports.default = AuthRepository;
//# sourceMappingURL=AuthRepository.js.map