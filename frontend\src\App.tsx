import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
// Context Providers
import { AuthProvider, ServiceProvider, ProviderProvider } from './contexts';

// Layout Components
import { Header, Footer, Breadcrumbs } from './components/layout';
import { ProtectedRoute } from './components/navigation';

// Page Components
import {
  // Auth Pages
  Login,
  Register,
  // Dashboard Pages
  Dashboard,
  CustomerProfile,
  ProviderDashboard,
  ProviderProfile,
  // Service Pages
  Services,
  ServiceDetail,
  ProviderServices,
  ProviderLocator,
  BusinessCards,
  MarketingMaterials,
  SignsBanners,
  InvitationsStationery,
  StickersLabels,
  GiftsDecor,
  Apparel,
  // Design Pages
  DesignServices,
  LogoDesign,
  WebsiteDesign,
  PrintingDesign,
  PackageDesign,
  IllustratorArt,
  VehicleWraps,
  // General Pages
  Home,
  Contact,
  OurWork,
  Admin,
} from './pages';

function App() {
  return (
    <AuthProvider>
      <ProviderProvider>
        <ServiceProvider>
          <Router>
            <div className="min-h-screen flex flex-col">
              <Header />
              <Breadcrumbs />
              <main className="flex-1">
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route path="/services" element={<Services />} />
                  <Route path="/business-cards" element={<BusinessCards />} />
                  <Route path="/marketing-materials" element={<MarketingMaterials />} />
                  <Route path="/signs-banners" element={<SignsBanners />} />
                  <Route path="/invitations-stationery" element={<InvitationsStationery />} />
                  <Route path="/stickers-labels" element={<StickersLabels />} />
                  <Route path="/gifts-decor" element={<GiftsDecor />} />
                  <Route path="/apparel" element={<Apparel />} />
                  <Route path="/design-services" element={<DesignServices />} />
                  <Route path="/provider-services" element={<ProviderServices />} />
                  <Route path="/find-providers" element={<ProviderLocator />} />
                  <Route path="/service/:id" element={<ServiceDetail />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/register" element={<Register />} />
                  <Route
                    path="/dashboard"
                    element={
                      <ProtectedRoute>
                        <Dashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin"
                    element={
                      <ProtectedRoute adminOnly>
                        <Admin />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/provider-dashboard"
                    element={
                      <ProtectedRoute>
                        <ProviderDashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/customer-profile"
                    element={
                      <ProtectedRoute>
                        <CustomerProfile />
                      </ProtectedRoute>
                    }
                  />
                  <Route path="/provider/:id" element={<ProviderProfile />} />
                  <Route path="/design-services/logo-design" element={<LogoDesign />} />
                  <Route path="/design-services/website-design" element={<WebsiteDesign />} />
                  <Route path="/design-services/printing-design" element={<PrintingDesign />} />
                  <Route path="/design-services/package-design" element={<PackageDesign />} />
                  <Route path="/design-services/illustrator-art" element={<IllustratorArt />} />
                  <Route path="/design-services/vehicle-wraps" element={<VehicleWraps />} />
                  <Route path="/contact" element={<Contact />} />
                  <Route path="/our-work" element={<OurWork />} />
                </Routes>
              </main>
              <Footer />
            </div>
          </Router>
        </ServiceProvider>
      </ProviderProvider>
    </AuthProvider>
  );
}

export default App;