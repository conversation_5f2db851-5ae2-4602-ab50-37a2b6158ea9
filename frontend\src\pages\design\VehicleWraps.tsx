import React from 'react';
import { Link } from 'react-router-dom';
import { 
  <PERSON><PERSON><PERSON>cle, 
  Clock, 
  Shield, 
  Award,
  ChevronDown,
  ChevronUp,
  Star,
  Car,
  Truck,
  Users,
  Zap,
  ArrowLeft,
  Palette,
  Target,
  Eye,
  MapPin,
  Lightbulb,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

const VehicleWraps: React.FC = () => {
  const [openFaq, setOpenFaq] = React.useState<number | null>(null);
  const [portfolioScrollPosition, setPortfolioScrollPosition] = React.useState(0);
  const portfolioScrollRef = React.useRef<HTMLDivElement>(null);

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  const scrollPortfolio = (direction: 'left' | 'right') => {
    if (portfolioScrollRef.current) {
      const scrollAmount = 280; // Width of card + gap
      const currentScroll = portfolioScrollRef.current.scrollLeft;
      const newScroll = direction === 'left' 
        ? currentScroll - scrollAmount 
        : currentScroll + scrollAmount;
      
      portfolioScrollRef.current.scrollTo({
        left: newScroll,
        behavior: 'smooth'
      });
      setPortfolioScrollPosition(newScroll);
    }
  };

  const handlePortfolioScroll = () => {
    if (portfolioScrollRef.current) {
      setPortfolioScrollPosition(portfolioScrollRef.current.scrollLeft);
    }
  };

  const faqs = [
    {
      question: "What types of vehicles can you wrap?",
      answer: "We design wraps for all types of vehicles including cars, trucks, vans, buses, trailers, boats, and motorcycles. Each design is customized to fit the specific vehicle dimensions and contours."
    },
    {
      question: "How long do vehicle wraps last?",
      answer: "High-quality vehicle wraps typically last 5-7 years with proper care. The lifespan depends on factors like sun exposure, weather conditions, and maintenance. We use premium materials for maximum durability."
    },
    {
      question: "Will a wrap damage my vehicle's paint?",
      answer: "No! Professional vehicle wraps actually protect your original paint. When properly installed and removed by professionals, wraps leave no residue and can even preserve your vehicle's resale value."
    },
    {
      question: "Can you design partial wraps or just graphics?",
      answer: "Absolutely! We design everything from full vehicle wraps to partial wraps, door graphics, window decals, and fleet graphics. We can work within any budget and coverage preference."
    },
    {
      question: "Do you provide installation services?",
      answer: "While we specialize in design, we work with certified installation partners nationwide. We can recommend trusted installers in your area and provide them with precise installation specifications."
    },
    {
      question: "How do you ensure the design fits my vehicle perfectly?",
      answer: "We use vehicle-specific templates and precise measurements to ensure perfect fit. Our designs account for door handles, mirrors, curves, and other vehicle features for seamless application."
    }
  ];

  const portfolioSamples = [
    {
      image: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789523124.jpg&w=1080&q=75",
      title: "Food Truck Wrap",
      description: "Vibrant full wrap design for mobile food business"
    },
    {
      image: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75",
      title: "Delivery Van Graphics",
      description: "Professional fleet graphics for logistics company"
    },
    {
      image: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75",
      title: "Sports Car Wrap",
      description: "Custom racing-inspired design with bold graphics"
    },
    {
      image: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75",
      title: "Service Vehicle Branding",
      description: "Professional branding for HVAC service trucks"
    }
  ];

  const designProcess = [
    {
      step: "01",
      title: "Vehicle Assessment",
      description: "We analyze your vehicle type, dimensions, and surface areas to create accurate design templates."
    },
    {
      step: "02",
      title: "Concept Development",
      description: "Our designers create initial wrap concepts that maximize visual impact while considering vehicle contours."
    },
    {
      step: "03",
      title: "3D Visualization",
      description: "We create realistic 3D renderings showing exactly how your wrap will look on your specific vehicle."
    },
    {
      step: "04",
      title: "Production Files",
      description: "You receive print-ready files with precise cut lines and installation guidelines for professional application."
    }
  ];

  const features = [
    {
      icon: Eye,
      title: "Maximum Visibility",
      description: "Turn your vehicle into a mobile billboard that advertises 24/7 wherever you drive"
    },
    {
      icon: Shield,
      title: "Paint Protection",
      description: "Protect your vehicle's original paint while creating stunning visual impact"
    },
    {
      icon: Target,
      title: "Brand Mobility",
      description: "Take your brand message directly to your customers on roads and in parking lots"
    },
    {
      icon: MapPin,
      title: "Local Marketing",
      description: "Increase local brand awareness and reach customers in your service area"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Back Button */}
      <div className="bg-warm-cream py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link
            to="/design-services"
            className="flex items-center text-blue-600 hover:text-blue-700 transition-colors"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Design Studio
          </Link>
        </div>
      </div>

      {/* Hero Section */}
      <section className="bg-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900">
                Professional Vehicle Wraps
              </h1>
              <p className="text-xl mb-8 text-gray-600">
                Turn your vehicle into a moving billboard with eye-catching wraps that increase visibility and promote your brand wherever you go. Professional design that drives results.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/contact"
                  className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center justify-center"
                >
                  Start Your Vehicle Wrap
                </Link>
                <Link
                  to="#portfolio"
                  className="border-2 border-brand-orange text-brand-orange px-8 py-3 rounded-lg font-semibold hover:bg-brand-orange hover:text-white transition-colors inline-flex items-center justify-center"
                >
                  View Portfolio
                </Link>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789523124.jpg&w=1080&q=75"
                alt="Professional Vehicle Wraps"
                className="rounded-lg shadow-2xl"
              />
              <div className="absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-xl">
                <div className="flex items-center space-x-3">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <Car className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Mobile Marketing</p>
                    <p className="text-sm text-gray-600">24/7 brand exposure</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-warm-cream">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Vehicle Wraps Work
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Vehicle wraps are one of the most cost-effective forms of advertising, providing continuous brand exposure and reaching thousands of potential customers daily.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Design Process */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Vehicle Wrap Design Process
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We follow a precise process to ensure your vehicle wrap design is both visually stunning and technically perfect for installation.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {designProcess.map((process, index) => (
              <div key={index} className="text-center">
                <div className="bg-blue-600 text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  {process.step}
                </div>
                <h3 className="font-semibold text-gray-900 mb-3">{process.title}</h3>
                <p className="text-gray-600 text-sm">{process.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Vehicle Types We Design For */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Vehicle Types We Design For
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We create custom wrap designs for all types of vehicles, ensuring each design maximizes the unique characteristics of your vehicle.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <Car className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-3">Personal Vehicles</h3>
              <ul className="text-gray-600 text-sm space-y-2">
                <li>• Cars & Sedans</li>
                <li>• SUVs & Crossovers</li>
                <li>• Sports Cars</li>
                <li>• Motorcycles</li>
              </ul>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="bg-green-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <Truck className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-3">Commercial Vehicles</h3>
              <ul className="text-gray-600 text-sm space-y-2">
                <li>• Delivery Vans</li>
                <li>• Service Trucks</li>
                <li>• Food Trucks</li>
                <li>• Fleet Vehicles</li>
              </ul>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-3">Specialty Vehicles</h3>
              <ul className="text-gray-600 text-sm space-y-2">
                <li>• Buses & Shuttles</li>
                <li>• Trailers & RVs</li>
                <li>• Boats & Watercraft</li>
                <li>• Emergency Vehicles</li>
              </ul>
            </div>
          </div>
        </div>
      </section>


      {/* Pricing Section */}
      <section className="py-20 bg-warm-cream">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Vehicle Wrap Design Packages
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Choose the package that best fits your vehicle wrap design needs and budget.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Basic Package */}
            <div className="bg-white border-2 border-gray-200 rounded-lg p-8 text-center">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Partial Wrap Design</h3>
              <div className="text-4xl font-bold text-blue-600 mb-6">$399</div>
              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Partial vehicle coverage</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">2 design concepts</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">3 rounds of revisions</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Print-ready files</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">5-7 day delivery</span>
                </li>
              </ul>
              <button className="w-full bg-brand-orange text-white py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors">
                Get Started
              </button>
            </div>

            {/* Professional Package */}
            <div className="bg-white border-2 border-blue-600 rounded-lg p-8 text-center relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Full Wrap Design</h3>
              <div className="text-4xl font-bold text-blue-600 mb-6">$799</div>
              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Complete vehicle coverage</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">4 design concepts</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">5 rounds of revisions</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">3D vehicle mockups</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Installation guidelines</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">7-10 day delivery</span>
                </li>
              </ul>
              <button className="w-full bg-brand-orange text-white py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors">
                Get Started
              </button>
            </div>

            {/* Premium Package */}
            <div className="bg-white border-2 border-gray-200 rounded-lg p-8 text-center">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Fleet Design Package</h3>
              <div className="text-4xl font-bold text-blue-600 mb-6">$1,299</div>
              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Multiple vehicle designs</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">6 design concepts</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Unlimited revisions</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Brand consistency guide</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Installation support</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">10-14 day delivery</span>
                </li>
              </ul>
              <button className="w-full bg-brand-orange text-white py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors">
                Get Started
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="w-full py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-start">
            {/* Left Column - Client Love Content */}
            <div className="lg:col-span-1">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
                What our clients say ...
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                We're a global team of skilled designers united by a passion for design and a commitment to excellence.
              </p>
              
              {/* Statistics */}
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <Star className="h-8 w-8 text-yellow-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Mobile Marketing</div>
                    <div className="text-gray-600">Vehicle wraps that turn your car into a 24/7 mobile billboard.</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Lightbulb className="h-8 w-8 text-purple-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">3D Visualization</div>
                    <div className="text-gray-600">Realistic mockups showing exactly how your wrap will look.</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Award className="h-8 w-8 text-orange-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Installation Ready</div>
                    <div className="text-gray-600">Precise templates and guidelines for professional installation.</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Scrollable Portfolio */}
            <div className="lg:col-span-2">
              <div className="relative">
                {/* Left Navigation Button */}
                <button
                  onClick={() => scrollPortfolio('left')}
                  className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 ${
                    portfolioScrollPosition <= 0
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:bg-gray-50 hover:shadow-xl'
                  }`}
                  disabled={portfolioScrollPosition <= 0}
                >
                  <ChevronLeft className="h-5 w-5 text-gray-600" />
                </button>

                {/* Right Navigation Button */}
                <button
                  onClick={() => scrollPortfolio('right')}
                  className="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 hover:bg-gray-50 hover:shadow-xl"
                >
                  <ChevronRight className="h-5 w-5 text-gray-600" />
                </button>

                {/* Scrollable Container */}
                <div 
                  ref={portfolioScrollRef}
                  onScroll={handlePortfolioScroll}
                  className="overflow-x-auto whitespace-nowrap py-4 px-12 scrollbar-hide"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                  }}
                >
                <div className="flex space-x-6">
                  {portfolioSamples.map((sample, index) => (
                    <div
                      key={index}
                      className="inline-block w-64 bg-white rounded-lg shadow-lg overflow-hidden flex-shrink-0 hover:shadow-xl transition-shadow duration-300"
                    >
                      <img
                        src={sample.image}
                        alt={sample.title}
                        className="w-full h-48 object-cover"
                      />
                      <div className="p-6">
                        <h3 className="font-semibold text-gray-900 mb-2 whitespace-normal">
                          {sample.title}
                        </h3>
                        <p className="text-gray-600 text-sm mb-4 whitespace-normal">{sample.description}</p>
                        
                        {/* Star Rating */}
                        <div className="flex items-center mb-4">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className="h-4 w-4 text-yellow-400 fill-current"
                            />
                          ))}
                          <span className="text-sm text-gray-500 ml-2">5.0</span>
                        </div>
                        
                        {/* Reviewer Info */}
                        <div className="flex items-center space-x-3">
                          <img
                            src={sample.image}
                            alt="Client"
                            className="w-8 h-8 rounded-full object-cover"
                          />
                          <p className="text-sm font-medium text-gray-900 whitespace-normal">
                            Happy Client
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600">
              Everything you need to know about our vehicle wrap design services.
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg">
                <button
                  onClick={() => toggleFaq(index)}
                  className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
                >
                  <span className="font-semibold text-gray-900">{faq.question}</span>
                  {openFaq === index ? (
                    <ChevronUp className="h-5 w-5 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-500" />
                  )}
                </button>
                {openFaq === index && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-600">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4">Ready to create your vehicle wrap design?</p>
            <Link
              to="/contact"
              className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center"
            >
              Start Your Vehicle Wrap Project
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default VehicleWraps;