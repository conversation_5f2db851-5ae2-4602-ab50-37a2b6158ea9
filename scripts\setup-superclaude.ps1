# SuperClaude Framework Setup Script for PrintWedittV1 Team
# This script installs and configures SuperClaude Framework

param(
    [string]$PythonVersion = "3.13",
    [string]$Profile = "developer",
    [switch]$Force = $false
)

Write-Host "🚀 SuperClaude Framework Setup for PrintWedittV1" -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Green

# Check if Python is available
Write-Host "Checking Python installation..." -ForegroundColor Yellow
try {
    $pythonVersion = & py -$PythonVersion --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Python $PythonVersion found: $pythonVersion" -ForegroundColor Green
    } else {
        Write-Host "✗ Python $PythonVersion not found" -ForegroundColor Red
        Write-Host "Please install Python $PythonVersion or update the PythonVersion parameter" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "✗ Error checking Python version" -ForegroundColor Red
    exit 1
}

# Check if SuperClaude is already installed
Write-Host "Checking existing SuperClaude installation..." -ForegroundColor Yellow
try {
    $superclaudeVersion = & py -$PythonVersion -m SuperClaude --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ SuperClaude found: $superclaudeVersion" -ForegroundColor Green
        if (-not $Force) {
            Write-Host "SuperClaude is already installed. Use -Force to reinstall." -ForegroundColor Yellow
            $reinstall = Read-Host "Do you want to reinstall? (y/N)"
            if ($reinstall -ne "y" -and $reinstall -ne "Y") {
                Write-Host "Setup cancelled." -ForegroundColor Yellow
                exit 0
            }
        }
    } else {
        Write-Host "SuperClaude not found. Installing..." -ForegroundColor Yellow
    }
} catch {
    Write-Host "SuperClaude not found. Installing..." -ForegroundColor Yellow
}

# Install SuperClaude
Write-Host "Installing SuperClaude Framework..." -ForegroundColor Yellow
try {
    & py -$PythonVersion -m pip install SuperClaude
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ SuperClaude package installed successfully" -ForegroundColor Green
    } else {
        Write-Host "✗ Failed to install SuperClaude package" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "✗ Error installing SuperClaude package" -ForegroundColor Red
    exit 1
}

# Run SuperClaude installer
Write-Host "Running SuperClaude installer with $Profile profile..." -ForegroundColor Yellow
try {
    $installArgs = @("install", "--profile", $Profile)
    if ($Force) {
        $installArgs += "--force"
    }

    & py -$PythonVersion -m SuperClaude @installArgs
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ SuperClaude Framework installed successfully" -ForegroundColor Green
    } else {
        Write-Host "✗ Failed to install SuperClaude Framework" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "✗ Error running SuperClaude installer" -ForegroundColor Red
    exit 1
}

# Check installation
Write-Host "Verifying installation..." -ForegroundColor Yellow
try {
    $status = & py -$PythonVersion -m SuperClaude status
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Installation verified successfully" -ForegroundColor Green
    } else {
        Write-Host "✗ Installation verification failed" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "✗ Error verifying installation" -ForegroundColor Red
    exit 1
}

# Display next steps
Write-Host ""
Write-Host "🎉 SuperClaude Framework Setup Complete!" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. Restart your Claude Code session" -ForegroundColor White
Write-Host "2. Review the documentation:" -ForegroundColor White
Write-Host "   - Full Guide: docs/SUPERCLAUDE_GUIDE.md" -ForegroundColor White
Write-Host "   - Quick Reference: docs/SUPERCLAUDE_QUICK_REFERENCE.md" -ForegroundColor White
Write-Host "3. Try your first command: /sc:plan --scope 'test feature'" -ForegroundColor White
Write-Host ""
Write-Host "Optional Setup:" -ForegroundColor Yellow
Write-Host "- Set TWENTYFIRST_API_KEY for Magic MCP server" -ForegroundColor White
Write-Host "- Configure environment variables for your development setup" -ForegroundColor White
Write-Host ""
Write-Host "For help, check the documentation or contact the team lead." -ForegroundColor Cyan
