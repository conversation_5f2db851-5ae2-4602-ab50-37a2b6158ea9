"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notFoundHandler = exports.asyncHandler = exports.errorHandler = exports.TooManyRequestsError = exports.ConflictError = exports.NotFoundError = exports.ValidationError = exports.AuthorizationError = exports.AuthenticationError = exports.AppError = void 0;
const library_1 = require("@prisma/client/runtime/library");
const logger_1 = require("../utils/logger");
class AppError extends Error {
    statusCode;
    isOperational;
    constructor(message, statusCode) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = true;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
class AuthenticationError extends AppError {
    constructor(message = 'Authentication failed') {
        super(message, 401);
    }
}
exports.AuthenticationError = AuthenticationError;
class AuthorizationError extends AppError {
    constructor(message = 'Access denied') {
        super(message, 403);
    }
}
exports.AuthorizationError = AuthorizationError;
class ValidationError extends AppError {
    validationErrors;
    constructor(message = 'Validation failed', validationErrors = []) {
        super(message, 400);
        this.validationErrors = validationErrors;
    }
}
exports.ValidationError = ValidationError;
class NotFoundError extends AppError {
    constructor(message = 'Resource not found') {
        super(message, 404);
    }
}
exports.NotFoundError = NotFoundError;
class ConflictError extends AppError {
    constructor(message = 'Resource already exists') {
        super(message, 409);
    }
}
exports.ConflictError = ConflictError;
class TooManyRequestsError extends AppError {
    constructor(message = 'Too many requests') {
        super(message, 429);
    }
}
exports.TooManyRequestsError = TooManyRequestsError;
const errorHandler = (error, req, res, next) => {
    const logger = (0, logger_1.createLogger)('ErrorHandler');
    let statusCode = 500;
    let message = 'Internal server error';
    let details = undefined;
    if (error instanceof AppError) {
        statusCode = error.statusCode;
        message = error.message;
        if (error instanceof ValidationError) {
            details = { validationErrors: error.validationErrors };
        }
    }
    else if (error instanceof library_1.PrismaClientKnownRequestError) {
        switch (error.code) {
            case 'P2002':
                statusCode = 409;
                message = 'Resource already exists';
                const target = error.meta?.target;
                if (target?.includes('email')) {
                    message = 'Email address is already registered';
                }
                break;
            case 'P2025':
                statusCode = 404;
                message = 'Record not found';
                break;
            case 'P2003':
                statusCode = 400;
                message = 'Invalid reference';
                break;
            default:
                statusCode = 500;
                message = 'Database error';
        }
    }
    else if (error.name === 'ValidationError') {
        const joiError = error;
        statusCode = 400;
        message = 'Validation failed';
        details = {
            validationErrors: joiError.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message,
                value: detail.context?.value
            }))
        };
    }
    else if (error.name === 'JsonWebTokenError') {
        statusCode = 401;
        message = 'Invalid token';
    }
    else if (error.name === 'TokenExpiredError') {
        statusCode = 401;
        message = 'Token expired';
    }
    else if (error.name === 'SyntaxError' && 'body' in error) {
        statusCode = 400;
        message = 'Invalid JSON format';
    }
    const errorContext = {
        name: error.name,
        statusCode,
        path: req.path,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        requestId: res.getHeader('X-Request-ID'),
        userId: req.userId,
        body: sanitizeRequestBody(req.body),
        query: req.query,
        params: req.params
    };
    if (statusCode >= 500) {
        logger.error(`Server error: ${message}`, error, errorContext);
    }
    else if (statusCode >= 400) {
        logger.warn(`Client error: ${message}`, {
            ...errorContext,
            errorMessage: error.message,
            stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined
        });
    }
    const errorResponse = {
        error: error.name || 'Error',
        message,
        statusCode,
        timestamp: new Date().toISOString(),
        path: req.path
    };
    if (details) {
        errorResponse.details = details;
    }
    if (process.env.NODE_ENV !== 'production' && error.stack) {
        errorResponse.stack = error.stack;
    }
    res.status(statusCode).json(errorResponse);
};
exports.errorHandler = errorHandler;
function sanitizeRequestBody(body) {
    if (!body || typeof body !== 'object') {
        return body;
    }
    const sensitiveFields = [
        'password',
        'currentPassword',
        'newPassword',
        'confirmPassword',
        'token',
        'refreshToken',
        'accessToken',
        'apiKey',
        'secret'
    ];
    const sanitized = { ...body };
    sensitiveFields.forEach(field => {
        if (field in sanitized) {
            sanitized[field] = '[REDACTED]';
        }
    });
    return sanitized;
}
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
const notFoundHandler = (req, res, next) => {
    const error = new NotFoundError(`Route ${req.originalUrl} not found`);
    next(error);
};
exports.notFoundHandler = notFoundHandler;
exports.default = {
    AppError,
    AuthenticationError,
    AuthorizationError,
    ValidationError,
    NotFoundError,
    ConflictError,
    TooManyRequestsError,
    errorHandler: exports.errorHandler,
    asyncHandler: exports.asyncHandler,
    notFoundHandler: exports.notFoundHandler
};
//# sourceMappingURL=errorHandler.js.map