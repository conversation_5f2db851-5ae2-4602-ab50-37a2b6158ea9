"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApiResponse = exports.sanitizeUser = exports.getUserAgent = exports.getClientIP = exports.logSecurityEvent = exports.getAccountLockInfo = exports.isAccountLocked = exports.resetLoginAttempts = exports.incrementLoginAttempts = exports.cleanupExpiredSessions = exports.isRefreshTokenValid = exports.revokeAllUserSessions = exports.revokeRefreshToken = exports.generateTokenPair = exports.generatePasswordResetToken = exports.generateVerificationToken = exports.generateSecureToken = exports.verifyPassword = exports.hashPassword = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const crypto_1 = __importDefault(require("crypto"));
const client_1 = require("@prisma/client");
const auth_1 = require("../middleware/auth");
const prisma = new client_1.PrismaClient();
const hashPassword = async (password) => {
    const saltRounds = 12;
    return bcryptjs_1.default.hash(password, saltRounds);
};
exports.hashPassword = hashPassword;
const verifyPassword = async (password, hash) => {
    return bcryptjs_1.default.compare(password, hash);
};
exports.verifyPassword = verifyPassword;
const generateSecureToken = (length = 32) => {
    return crypto_1.default.randomBytes(length).toString('hex');
};
exports.generateSecureToken = generateSecureToken;
const generateVerificationToken = () => {
    const token = (0, exports.generateSecureToken)(32);
    const expires = new Date();
    expires.setHours(expires.getHours() + 24);
    return { token, expires };
};
exports.generateVerificationToken = generateVerificationToken;
const generatePasswordResetToken = () => {
    const token = (0, exports.generateSecureToken)(32);
    const expires = new Date();
    expires.setHours(expires.getHours() + 1);
    return { token, expires };
};
exports.generatePasswordResetToken = generatePasswordResetToken;
const generateTokenPair = async (userId, email, role, ipAddress, userAgent) => {
    const payload = {
        userId,
        email,
        role
    };
    const accessToken = (0, auth_1.generateAccessToken)(payload);
    const refreshToken = (0, auth_1.generateRefreshToken)(payload);
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);
    await prisma.user_sessions.create({
        data: {
            id: (0, exports.generateSecureToken)(16),
            userId,
            refreshToken,
            expiresAt,
            ipAddress: ipAddress.substring(0, 45),
            userAgent: userAgent.substring(0, 500),
            isActive: true
        }
    });
    return { accessToken, refreshToken };
};
exports.generateTokenPair = generateTokenPair;
const revokeRefreshToken = async (refreshToken) => {
    await prisma.user_sessions.updateMany({
        where: { refreshToken },
        data: {
            isActive: false,
            revokedAt: new Date()
        }
    });
};
exports.revokeRefreshToken = revokeRefreshToken;
const revokeAllUserSessions = async (userId) => {
    await prisma.user_sessions.updateMany({
        where: { userId },
        data: {
            isActive: false,
            revokedAt: new Date()
        }
    });
};
exports.revokeAllUserSessions = revokeAllUserSessions;
const isRefreshTokenValid = async (refreshToken) => {
    const session = await prisma.user_sessions.findUnique({
        where: { refreshToken }
    });
    if (!session)
        return false;
    if (!session.isActive)
        return false;
    if (session.expiresAt < new Date())
        return false;
    return true;
};
exports.isRefreshTokenValid = isRefreshTokenValid;
const cleanupExpiredSessions = async () => {
    const result = await prisma.user_sessions.deleteMany({
        where: {
            OR: [
                { expiresAt: { lt: new Date() } },
                { isActive: false, revokedAt: { lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } }
            ]
        }
    });
    return result.count;
};
exports.cleanupExpiredSessions = cleanupExpiredSessions;
const incrementLoginAttempts = async (userId) => {
    const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { loginAttempts: true }
    });
    if (!user)
        return;
    const newAttempts = user.loginAttempts + 1;
    const maxAttempts = 5;
    const updateData = {
        loginAttempts: newAttempts
    };
    if (newAttempts >= maxAttempts) {
        const lockUntil = new Date();
        lockUntil.setMinutes(lockUntil.getMinutes() + 30);
        updateData.lockedUntil = lockUntil;
    }
    await prisma.user.update({
        where: { id: userId },
        data: updateData
    });
};
exports.incrementLoginAttempts = incrementLoginAttempts;
const resetLoginAttempts = async (userId) => {
    await prisma.user.update({
        where: { id: userId },
        data: {
            loginAttempts: 0,
            lockedUntil: null,
            lastLoginAt: new Date()
        }
    });
};
exports.resetLoginAttempts = resetLoginAttempts;
const isAccountLocked = async (userId) => {
    const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { lockedUntil: true }
    });
    if (!user?.lockedUntil)
        return false;
    if (user.lockedUntil > new Date()) {
        return true;
    }
    else {
        await prisma.user.update({
            where: { id: userId },
            data: {
                lockedUntil: null,
                loginAttempts: 0
            }
        });
        return false;
    }
};
exports.isAccountLocked = isAccountLocked;
const getAccountLockInfo = async (userId) => {
    const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
            loginAttempts: true,
            lockedUntil: true
        }
    });
    if (!user)
        return null;
    const isLocked = user.lockedUntil ? user.lockedUntil > new Date() : false;
    const maxAttempts = 5;
    return {
        isLocked,
        lockExpires: user.lockedUntil,
        attempts: user.loginAttempts,
        maxAttempts,
        remainingAttempts: Math.max(0, maxAttempts - user.loginAttempts)
    };
};
exports.getAccountLockInfo = getAccountLockInfo;
const logSecurityEvent = async (event) => {
    if (process.env.NODE_ENV !== 'production') {
        console.log('Security Event:', {
            ...event,
            timestamp: new Date().toISOString()
        });
    }
};
exports.logSecurityEvent = logSecurityEvent;
const getClientIP = (req) => {
    return (req.headers['x-forwarded-for']?.split(',')[0] ||
        req.headers['x-real-ip'] ||
        req.connection?.remoteAddress ||
        req.socket?.remoteAddress ||
        req.ip ||
        'unknown');
};
exports.getClientIP = getClientIP;
const getUserAgent = (req) => {
    return req.headers['user-agent'] || 'unknown';
};
exports.getUserAgent = getUserAgent;
const sanitizeUser = (user) => {
    const { password, passwordResetToken, passwordResetTokenExpires, emailVerificationToken, emailVerificationTokenExpires, loginAttempts, lockedUntil, ...sanitizedUser } = user;
    return sanitizedUser;
};
exports.sanitizeUser = sanitizeUser;
const createApiResponse = (success, data, message) => {
    return {
        success,
        data,
        message,
        timestamp: new Date().toISOString()
    };
};
exports.createApiResponse = createApiResponse;
exports.default = {
    hashPassword: exports.hashPassword,
    verifyPassword: exports.verifyPassword,
    generateSecureToken: exports.generateSecureToken,
    generateVerificationToken: exports.generateVerificationToken,
    generatePasswordResetToken: exports.generatePasswordResetToken,
    generateTokenPair: exports.generateTokenPair,
    revokeRefreshToken: exports.revokeRefreshToken,
    revokeAllUserSessions: exports.revokeAllUserSessions,
    isRefreshTokenValid: exports.isRefreshTokenValid,
    cleanupExpiredSessions: exports.cleanupExpiredSessions,
    incrementLoginAttempts: exports.incrementLoginAttempts,
    resetLoginAttempts: exports.resetLoginAttempts,
    isAccountLocked: exports.isAccountLocked,
    getAccountLockInfo: exports.getAccountLockInfo,
    logSecurityEvent: exports.logSecurityEvent,
    getClientIP: exports.getClientIP,
    getUserAgent: exports.getUserAgent,
    sanitizeUser: exports.sanitizeUser,
    createApiResponse: exports.createApiResponse
};
//# sourceMappingURL=auth.js.map