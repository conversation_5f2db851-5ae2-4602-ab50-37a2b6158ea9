import { Request, Response } from 'express';
import { createLogger } from './logger';

const logger = createLogger('ResponseWrapper');

// HTTP Status Codes enum
export enum HttpStatus {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
}

// Response metadata interface
export interface ResponseMeta {
  timestamp: string;
  requestId?: string;
  version: string;
  duration?: number;
  path?: string;
  method?: string;
  correlationId?: string;
  endpoint?: string;
}

// Pagination metadata
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  nextPage?: number;
  prevPage?: number;
}

// HATEOAS link interface
export interface ApiLink {
  href: string;
  rel: string;
  method?: string;
  title?: string;
}

// Error details for validation errors
export interface ErrorDetail {
  field?: string;
  code: string;
  message: string;
  value?: any;
  suggestion?: string;
}

// Base response interface
export interface BaseResponse {
  success: boolean;
  meta: ResponseMeta;
}

// Success response interface
export interface SuccessResponse<T = any> extends BaseResponse {
  success: true;
  data: T;
  message?: string;
  pagination?: PaginationMeta;
  links?: ApiLink[];
  warnings?: string[];
}

// Error response interface
export interface ErrorResponse extends BaseResponse {
  success: false;
  error: {
    type: string;
    message: string;
    code?: string;
    details?: ErrorDetail[];
    stack?: string;
    retryAfter?: number;
    helpUrl?: string;
  };
}

// Union type for all responses
export type ApiResponse<T = any> = SuccessResponse<T> | ErrorResponse;

/**
 * Creates a unified success response with enhanced metadata
 */
export const createSuccessResponse = <T>(
  data: T,
  message?: string,
  req?: Request,
  res?: Response,
  startTime?: number,
  pagination?: PaginationMeta,
  links?: ApiLink[],
  warnings?: string[]
): SuccessResponse<T> => {
  const meta: ResponseMeta = {
    timestamp: new Date().toISOString(),
    version: process.env.API_VERSION || '1.0.0',
    path: req?.originalUrl || req?.path,
    method: req?.method,
    correlationId: req?.headers['x-correlation-id'] as string,
    endpoint: req?.route?.path,
  };

  if (res?.getHeader('X-Request-ID')) {
    meta.requestId = res.getHeader('X-Request-ID') as string;
  }

  if (startTime) {
    meta.duration = Date.now() - startTime;
  }

  const response: SuccessResponse<T> = {
    success: true,
    data,
    meta,
  };

  if (message) {
    response.message = message;
  }

  if (pagination) {
    response.pagination = pagination;
  }

  if (links && links.length > 0) {
    response.links = links;
  }

  if (warnings && warnings.length > 0) {
    response.warnings = warnings;
  }

  // Log successful API calls for monitoring
  if (req && meta.duration) {
    logger.info('API Success Response', {
      method: req.method,
      path: req.path,
      statusCode: 200,
      duration: meta.duration,
      requestId: meta.requestId,
      correlationId: meta.correlationId,
      endpoint: meta.endpoint,
    });
  }

  return response;
};

/**
 * Creates a unified error response with enhanced error details
 */
export const createErrorResponse = (
  message: string,
  statusCode: HttpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
  errorType?: string,
  code?: string,
  details?: ErrorDetail[],
  stack?: string,
  req?: Request,
  res?: Response,
  startTime?: number,
  retryAfter?: number,
  helpUrl?: string
): ErrorResponse => {
  const meta: ResponseMeta = {
    timestamp: new Date().toISOString(),
    version: process.env.API_VERSION || '1.0.0',
    path: req?.originalUrl || req?.path,
    method: req?.method,
    correlationId: req?.headers['x-correlation-id'] as string,
    endpoint: req?.route?.path,
  };

  if (res?.getHeader('X-Request-ID')) {
    meta.requestId = res.getHeader('X-Request-ID') as string;
  }

  if (startTime) {
    meta.duration = Date.now() - startTime;
  }

  const response: ErrorResponse = {
    success: false,
    error: {
      type: errorType || getErrorTypeFromStatusCode(statusCode),
      message,
    },
    meta,
  };

  if (code) {
    response.error.code = code;
  }

  if (details && details.length > 0) {
    response.error.details = details;
  }

  if (retryAfter) {
    response.error.retryAfter = retryAfter;
  }

  if (helpUrl) {
    response.error.helpUrl = helpUrl;
  }

  // Only include stack in development
  if (process.env.NODE_ENV !== 'production' && stack) {
    response.error.stack = stack;
  }

  // Log error responses
  if (req) {
    logger.error('API Error Response', {
      method: req.method,
      path: req.path,
      statusCode,
      errorType: response.error.type,
      message: response.error.message,
      requestId: meta.requestId,
      correlationId: meta.correlationId,
      endpoint: meta.endpoint,
    });
  }

  return response;
};

/**
 * Helper function to get error type from HTTP status code
 */
const getErrorTypeFromStatusCode = (statusCode: HttpStatus): string => {
  const errorTypeMap: Record<number, string> = {
    [HttpStatus.BAD_REQUEST]: 'ValidationError',
    [HttpStatus.UNAUTHORIZED]: 'AuthenticationError',
    [HttpStatus.FORBIDDEN]: 'AuthorizationError',
    [HttpStatus.NOT_FOUND]: 'NotFoundError',
    [HttpStatus.CONFLICT]: 'ConflictError',
    [HttpStatus.UNPROCESSABLE_ENTITY]: 'ValidationError',
    [HttpStatus.TOO_MANY_REQUESTS]: 'RateLimitError',
    [HttpStatus.INTERNAL_SERVER_ERROR]: 'InternalServerError',
    [HttpStatus.BAD_GATEWAY]: 'ExternalServiceError',
    [HttpStatus.SERVICE_UNAVAILABLE]: 'ServiceUnavailableError',
  };

  return errorTypeMap[statusCode] || 'UnknownError';
};

/**
 * Helper function to create pagination metadata with enhanced information
 */
export const createPaginationMeta = (page: number, limit: number, total: number): PaginationMeta => {
  const totalPages = Math.ceil(total / limit) || 1;
  const currentPage = Math.max(1, page);

  return {
    page: currentPage,
    limit: Math.max(1, limit),
    total: Math.max(0, total),
    totalPages,
    hasNext: currentPage < totalPages,
    hasPrev: currentPage > 1,
    nextPage: currentPage < totalPages ? currentPage + 1 : undefined,
    prevPage: currentPage > 1 ? currentPage - 1 : undefined,
  };
};

/**
 * Helper function to create HATEOAS links
 */
export const createApiLinks = (baseUrl: string, resource: string, id?: string): ApiLink[] => {
  const links: ApiLink[] = [
    {
      href: `${baseUrl}/${resource}`,
      rel: 'collection',
      method: 'GET',
      title: `Get all ${resource}`,
    },
  ];

  if (id) {
    links.push(
      {
        href: `${baseUrl}/${resource}/${id}`,
        rel: 'self',
        method: 'GET',
        title: `Get ${resource} by ID`,
      },
      {
        href: `${baseUrl}/${resource}/${id}`,
        rel: 'update',
        method: 'PUT',
        title: `Update ${resource}`,
      },
      {
        href: `${baseUrl}/${resource}/${id}`,
        rel: 'delete',
        method: 'DELETE',
        title: `Delete ${resource}`,
      }
    );
  } else {
    links.push({
      href: `${baseUrl}/${resource}`,
      rel: 'create',
      method: 'POST',
      title: `Create new ${resource}`,
    });
  }

  return links;
};

/**
 * Helper function to create pagination links
 */
export const createPaginationLinks = (
  baseUrl: string,
  resource: string,
  page: number,
  limit: number,
  total: number,
  queryParams?: Record<string, any>
): ApiLink[] => {
  const totalPages = Math.ceil(total / limit) || 1;
  const links: ApiLink[] = [];

  // Build query string from existing params
  const queryString = queryParams
    ? '&' +
      Object.entries(queryParams)
        .filter(([key]) => key !== 'page' && key !== 'limit')
        .map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`)
        .join('&')
    : '';

  // Self link
  links.push({
    href: `${baseUrl}/${resource}?page=${page}&limit=${limit}${queryString}`,
    rel: 'self',
    method: 'GET',
    title: `Current page`,
  });

  // First page
  if (page > 1) {
    links.push({
      href: `${baseUrl}/${resource}?page=1&limit=${limit}${queryString}`,
      rel: 'first',
      method: 'GET',
      title: 'First page',
    });
  }

  // Previous page
  if (page > 1) {
    links.push({
      href: `${baseUrl}/${resource}?page=${page - 1}&limit=${limit}${queryString}`,
      rel: 'prev',
      method: 'GET',
      title: 'Previous page',
    });
  }

  // Next page
  if (page < totalPages) {
    links.push({
      href: `${baseUrl}/${resource}?page=${page + 1}&limit=${limit}${queryString}`,
      rel: 'next',
      method: 'GET',
      title: 'Next page',
    });
  }

  // Last page
  if (page < totalPages) {
    links.push({
      href: `${baseUrl}/${resource}?page=${totalPages}&limit=${limit}${queryString}`,
      rel: 'last',
      method: 'GET',
      title: 'Last page',
    });
  }

  return links;
};

/**
 * Express middleware to add response helper methods
 */
export const responseWrapperMiddleware = (req: Request, res: Response, next: Function) => {
  const startTime = Date.now();

  // Add success response helper
  res.success = function <T>(
    data: T,
    message?: string,
    statusCode: HttpStatus = HttpStatus.OK,
    pagination?: PaginationMeta,
    links?: ApiLink[],
    warnings?: string[]
  ) {
    const response = createSuccessResponse(data, message, req, res, startTime, pagination, links, warnings);
    return this.status(statusCode).json(response);
  };

  // Add created response helper
  res.created = function <T>(data: T, message?: string, links?: ApiLink[]) {
    const response = createSuccessResponse(data, message, req, res, startTime, undefined, links);
    return this.status(HttpStatus.CREATED).json(response);
  };

  // Add no content response helper
  res.noContent = function () {
    return this.status(HttpStatus.NO_CONTENT).send();
  };

  // Add error response helper
  res.error = function (
    message: string,
    statusCode: HttpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
    errorType?: string,
    code?: string,
    details?: ErrorDetail[],
    retryAfter?: number,
    helpUrl?: string
  ) {
    const response = createErrorResponse(
      message,
      statusCode,
      errorType,
      code,
      details,
      undefined, // stack will be handled by error handler
      req,
      res,
      startTime,
      retryAfter,
      helpUrl
    );
    return this.status(statusCode).json(response);
  };

  // Add validation error helper
  res.validationError = function (message: string, details: ErrorDetail[]) {
    return this.error(message, HttpStatus.BAD_REQUEST, 'ValidationError', 'VALIDATION_FAILED', details);
  };

  // Add not found error helper
  res.notFound = function (message: string = 'Resource not found') {
    return this.error(message, HttpStatus.NOT_FOUND, 'NotFoundError', 'RESOURCE_NOT_FOUND');
  };

  // Add unauthorized error helper
  res.unauthorized = function (message: string = 'Authentication required') {
    return this.error(message, HttpStatus.UNAUTHORIZED, 'AuthenticationError', 'AUTHENTICATION_REQUIRED');
  };

  // Add forbidden error helper
  res.forbidden = function (message: string = 'Access denied') {
    return this.error(message, HttpStatus.FORBIDDEN, 'AuthorizationError', 'ACCESS_DENIED');
  };

  // Add conflict error helper
  res.conflict = function (message: string = 'Resource already exists') {
    return this.error(message, HttpStatus.CONFLICT, 'ConflictError', 'RESOURCE_CONFLICT');
  };

  // Add rate limit error helper
  res.rateLimit = function (message: string = 'Too many requests', retryAfter?: number) {
    return this.error(
      message,
      HttpStatus.TOO_MANY_REQUESTS,
      'RateLimitError',
      'RATE_LIMIT_EXCEEDED',
      undefined,
      retryAfter
    );
  };

  next();
};

// Extend Express Response interface
declare global {
  namespace Express {
    interface Response {
      success<T>(
        data: T,
        message?: string,
        statusCode?: HttpStatus,
        pagination?: PaginationMeta,
        links?: ApiLink[],
        warnings?: string[]
      ): Response;

      created<T>(data: T, message?: string, links?: ApiLink[]): Response;

      noContent(): Response;

      error(
        message: string,
        statusCode?: HttpStatus,
        errorType?: string,
        code?: string,
        details?: ErrorDetail[],
        retryAfter?: number,
        helpUrl?: string
      ): Response;

      validationError(message: string, details: ErrorDetail[]): Response;

      notFound(message?: string): Response;

      unauthorized(message?: string): Response;

      forbidden(message?: string): Response;

      conflict(message?: string): Response;

      rateLimit(message?: string, retryAfter?: number): Response;
    }
  }
}

export default {
  HttpStatus,
  createSuccessResponse,
  createErrorResponse,
  createPaginationMeta,
  createApiLinks,
  createPaginationLinks,
  responseWrapperMiddleware,
};
