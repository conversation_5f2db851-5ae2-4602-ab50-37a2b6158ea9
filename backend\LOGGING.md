# Logging System Documentation

## Overview

The PrintCo backend implements a comprehensive logging system using Winston and
`winston-daily-rotate-file` for robust, rotating log files. The system provides
structured logging with different log levels, specialized loggers for different
contexts, and environment-specific configurations.

## Features

### ✅ **Core Logging Features**

- **Structured JSON Logging**: All logs are formatted as JSON with metadata
- **Log Level Management**: Custom log levels (error, warn, info, http, debug)
- **File Rotation**: Daily rotating log files with automatic cleanup
- **Environment-Specific Configurations**: Different behaviors for
  dev/staging/prod
- **Performance Monitoring**: Request duration tracking and slow request
  detection
- **Security Logging**: Dedicated security event logging
- **Sensitive Data Redaction**: Automatic redaction of passwords, tokens, etc.

### ✅ **Specialized Loggers**

- **`authLogger`**: Authentication and authorization events
- **`securityLogger`**: Security events, rate limiting, threats
- **`dbLogger`**: Database operations and queries
- **`httpLogger`**: HTTP request/response logging
- **`createLogger()`**: Custom logger instances for specific contexts

### ✅ **Middleware Integration**

- **Request Logging**: Incoming requests with metadata
- **Response Logging**: Completed requests with performance metrics
- **Error Logging**: Comprehensive error tracking
- **Authentication Logging**: Login, logout, registration events
- **Rate Limiting Logging**: Security threat detection

## Log Files

The system creates the following log files in the `logs/` directory:

```
logs/
├── application-YYYY-MM-DD.log    # Application-level logs
├── error-YYYY-MM-DD.log          # Error logs only
├── http-YYYY-MM-DD.log           # HTTP request/response logs
├── exceptions-YYYY-MM-DD.log     # Unhandled exceptions
└── rejections-YYYY-MM-DD.log     # Promise rejections
```

## Environment Configurations

### Development Environment

- **Log Level**: `debug`
- **Console Output**: Enabled (verbose)
- **File Logging**: Enabled
- **Log Rotation**: Daily

### Staging Environment

- **Log Level**: `info`
- **Console Output**: Minimal
- **File Logging**: Enabled
- **Log Rotation**: Daily

### Production Environment

- **Log Level**: `info`
- **Console Output**: Disabled
- **File Logging**: Enabled
- **Log Rotation**: Daily
- **Security**: Enhanced (no sensitive data in logs)

## Usage Examples

### Basic Logging

```typescript
import {authLogger, securityLogger, dbLogger} from '../utils/logger';

// Authentication logging
authLogger.info('User login successful', {
	userId: '123',
	email: '<EMAIL>',
	ipAddress: '***********',
	duration: 150,
});

// Security logging
securityLogger.warn('Rate limit exceeded', {
	ip: '***********',
	path: '/api/auth/login',
	attempts: 5,
});

// Database logging
dbLogger.database('User profile updated', {
	userId: '123',
	table: 'users',
	operation: 'UPDATE',
});
```

### Custom Logger

```typescript
import {createLogger} from '../utils/logger';

const paymentLogger = createLogger('Payment');

paymentLogger.info('Payment processed', {
	transactionId: 'txn_123',
	amount: 99.99,
	currency: 'USD',
});
```

### Middleware Usage

```typescript
import {requestLogger, logAuthEvent} from '../middleware/requestLogger';

// Request logging middleware
app.use(requestLogger);

// Authentication event logging
app.post('/login', logAuthEvent('LOGIN'), loginController);
```

## Testing the Logging System

### Available Test Commands

```bash
# Test all environments
npm run test:logging:all

# Test specific environment
npm run test:logging:dev      # Development
npm run test:logging:staging  # Staging
npm run test:logging:prod     # Production

# Run comprehensive test suite
npm run test:logging
```

### Test Coverage

The logging tests cover:

- ✅ Environment setup and configuration
- ✅ Authentication flow logging
- ✅ Rate limiting and security events
- ✅ Error handling and logging
- ✅ Performance monitoring
- ✅ Database operation logging
- ✅ Log file rotation and management

## Log Levels

| Level   | Description                  | Usage                             |
| ------- | ---------------------------- | --------------------------------- |
| `error` | System errors and exceptions | Critical failures, exceptions     |
| `warn`  | Warning conditions           | Rate limiting, security threats   |
| `info`  | General information          | Business operations, user actions |
| `http`  | HTTP requests/responses      | API calls, performance metrics    |
| `debug` | Detailed debugging info      | Development debugging             |

## Security Features

### Sensitive Data Redaction

The system automatically redacts sensitive information:

- Passwords (current, new, confirm)
- Tokens (access, refresh, API keys)
- Credit card information
- Social security numbers
- Other sensitive fields

### Security Event Logging

- Login attempts (success/failure)
- Rate limiting events
- Password changes
- Session management
- Suspicious activity

## Performance Monitoring

### Request Performance

- Request duration tracking
- Slow request detection (>1000ms)
- Response size monitoring
- Concurrent request handling

### Database Performance

- Query execution time
- Database connection status
- Transaction monitoring

## Configuration

### Environment Variables

```bash
NODE_ENV=development|staging|production
LOG_LEVEL=error|warn|info|http|debug
```

### Logger Configuration

```typescript
// Custom log level configuration
const customLevels = {
	error: 0,
	warn: 1,
	info: 2,
	http: 3,
	debug: 4,
};

// File rotation configuration
const fileRotationConfig = {
	datePattern: 'YYYY-MM-DD',
	maxSize: '20m',
	maxFiles: '14d',
};
```

## Best Practices

### ✅ **Do's**

- Use appropriate log levels
- Include relevant metadata
- Use structured logging (JSON)
- Log security events
- Monitor log file sizes
- Use custom loggers for specific contexts

### ❌ **Don'ts**

- Log sensitive information
- Use console.log in production
- Log excessive debug information
- Ignore error logs
- Use inconsistent log formats

## Monitoring and Maintenance

### Log File Management

- Daily rotation prevents large files
- Automatic cleanup of old logs
- Configurable retention periods
- Size-based rotation

### Health Checks

- Monitor log file creation
- Check log file sizes
- Verify log rotation
- Monitor error rates

## Troubleshooting

### Common Issues

1. **Log files not created**: Check directory permissions
2. **High log volume**: Adjust log levels
3. **Missing logs**: Verify logger configuration
4. **Performance impact**: Use appropriate log levels

### Debug Commands

```bash
# Check log files
ls -la logs/

# Monitor logs in real-time
tail -f logs/application-$(date +%Y-%m-%d).log

# Check log file sizes
du -h logs/
```

## Integration with Monitoring Tools

The structured JSON logging format makes it easy to integrate with:

- **ELK Stack** (Elasticsearch, Logstash, Kibana)
- **Splunk**
- **Datadog**
- **New Relic**
- **CloudWatch**

## Conclusion

The logging system provides comprehensive visibility into application behavior,
security events, and performance metrics. It's designed to be production-ready
with proper security measures, performance optimization, and easy integration
with monitoring tools.

For questions or issues, refer to the test suite or check the log files in the
`logs/` directory.
