import { PrismaClient } from '@prisma/client';
import {
  ServiceFormFieldDetail,
  CreateServiceFormFieldData,
  UpdateServiceFormFieldData,
  ServiceFormFieldOptionDetail,
  CreateServiceFormFieldOptionData,
  UpdateServiceFormFieldOptionData,
} from '../types/service';
import { ServiceMapper } from '../utils/ServiceMapper';

/**
 * ServiceFormFieldRepository
 * Handles all service form field and option database operations
 * Follows Single Responsibility Principle - only form field management
 */
export interface IServiceFormFieldRepository {
  // Form field operations
  getServiceFormFields(serviceId: string): Promise<ServiceFormFieldDetail[]>;
  findServiceFormFieldById(id: string): Promise<ServiceFormFieldDetail | null>;
  createServiceFormField(fieldData: CreateServiceFormFieldData): Promise<any>;
  updateServiceFormField(id: string, data: UpdateServiceFormFieldData): Promise<any>;
  deleteServiceForm<PERSON>ield(id: string): Promise<boolean>;

  // Form field option operations
  getServiceFormFieldOptions(fieldId: string): Promise<ServiceFormFieldOptionDetail[]>;
  createServiceFormFieldOption(optionData: CreateServiceFormFieldOptionData): Promise<any>;
  updateServiceFormFieldOption(id: string, data: UpdateServiceFormFieldOptionData): Promise<any>;
  deleteServiceFormFieldOption(id: string): Promise<boolean>;
}

export class ServiceFormFieldRepository implements IServiceFormFieldRepository {
  constructor(private prisma: PrismaClient) {}

  // Form field operations
  async getServiceFormFields(serviceId: string): Promise<ServiceFormFieldDetail[]> {
    const fields = await this.prisma.serviceFormField.findMany({
      where: { serviceId, isActive: true },
      include: {
        service_field_options: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
        },
      },
      orderBy: { sortOrder: 'asc' },
    });

    return ServiceMapper.toFormFieldDetailArray(fields);
  }

  async findServiceFormFieldById(id: string): Promise<ServiceFormFieldDetail | null> {
    const field = await this.prisma.serviceFormField.findUnique({
      where: { id },
      include: {
        service_field_options: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
        },
      },
    });

    if (!field) return null;
    return ServiceMapper.toFormFieldDetail(field);
  }

  async createServiceFormField(fieldData: CreateServiceFormFieldData): Promise<any> {
    return this.prisma.serviceFormField.create({
      data: fieldData,
    });
  }

  async updateServiceFormField(id: string, data: UpdateServiceFormFieldData): Promise<any> {
    return this.prisma.serviceFormField.update({
      where: { id },
      data,
    });
  }

  async deleteServiceFormField(id: string): Promise<boolean> {
    try {
      await this.prisma.serviceFormField.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  // Form field option operations
  async getServiceFormFieldOptions(fieldId: string): Promise<ServiceFormFieldOptionDetail[]> {
    const options = await this.prisma.service_field_options.findMany({
      where: { fieldId, isActive: true },
      orderBy: { sortOrder: 'asc' },
    });

    return ServiceMapper.toFormFieldOptionDetailArray(options);
  }

  async createServiceFormFieldOption(optionData: CreateServiceFormFieldOptionData): Promise<any> {
    return this.prisma.service_field_options.create({
      data: optionData,
    });
  }

  async updateServiceFormFieldOption(id: string, data: UpdateServiceFormFieldOptionData): Promise<any> {
    return this.prisma.service_field_options.update({
      where: { id },
      data,
    });
  }

  async deleteServiceFormFieldOption(id: string): Promise<boolean> {
    try {
      await this.prisma.service_field_options.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      return false;
    }
  }
}