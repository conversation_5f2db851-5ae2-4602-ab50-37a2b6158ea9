# 🔍 SuperClaude Prompt: Core Services API SRP & DRY Refactoring

## 🎯 **Mission Statement**

You are a Senior Software Architect specializing in SOLID principles and clean code practices. Your mission is to analyze and refactor the Core Services API to eliminate Single Responsibility Principle (SRP) violations and Don't Repeat Yourself (DRY) violations, transforming a monolithic codebase into a well-structured, maintainable architecture.

## 📊 **Current State Analysis**

### **Critical Issues Identified:**

#### 1. **Massive SRP Violations - God Classes**

- **ServiceRepository.ts** (1,068 lines): Handles 5 different domains

  - Service CRUD operations (200+ lines)
  - Category operations (150+ lines)
  - Form field operations (200+ lines)
  - Statistics operations (100+ lines)
  - Price calculations (80+ lines)

- **ServiceService.ts** (888 lines): Single service managing everything

  - 25+ methods across multiple domains
  - Mixed business logic and validation
  - No clear separation of concerns

- **ServiceController.ts** (1,123 lines): HTTP concerns mixed with business logic
  - 21 controller methods with duplicated logging patterns
  - Repeated error handling patterns
  - No abstraction for common HTTP operations

#### 2. **Extensive DRY Violations**

- **Request Logging Pattern**: Duplicated 21 times across controller methods
- **Data Mapping Pattern**: Repeated 8+ times for service transformations
- **Validation Logic**: Similar patterns repeated across multiple methods
- **Type Definition Duplication**: Nearly identical interfaces with minor differences

## 🏗️ **Target Architecture**

### **Phase 1: Repository Layer Refactoring**

Split the monolithic `ServiceRepository` into domain-specific repositories:

```typescript
// Core Service Repository
export class ServiceRepository {
  async findById(id: string): Promise<ServiceDetail | null>;
  async create(data: CreateServiceData): Promise<Service>;
  async update(id: string, data: UpdateServiceData): Promise<Service>;
  async delete(id: string): Promise<boolean>;
  async findMany(query: ServiceListQuery): Promise<ServiceListResponse>;
  async search(query: ServiceSearchQuery): Promise<ServiceSearchResult>;
}

// Category Repository
export class ServiceCategoryRepository {
  async findAll(): Promise<ServiceCategorySummary[]>;
  async findById(id: string): Promise<ServiceCategoryDetail | null>;
  async findByRoute(route: string): Promise<ServiceCategoryDetail | null>;
  async create(data: CreateServiceCategoryData): Promise<ServiceCategoryDetail>;
  async update(id: string, data: UpdateServiceCategoryData): Promise<ServiceCategoryDetail>;
  async delete(id: string): Promise<boolean>;
}

// Form Field Repository
export class ServiceFormFieldRepository {
  async findByServiceId(serviceId: string): Promise<ServiceFormFieldDetail[]>;
  async findById(id: string): Promise<ServiceFormFieldDetail | null>;
  async create(data: CreateServiceFormFieldData): Promise<ServiceFormFieldDetail>;
  async update(id: string, data: UpdateServiceFormFieldData): Promise<ServiceFormFieldDetail>;
  async delete(id: string): Promise<boolean>;
}

// Price Calculator Service
export class ServicePriceCalculator {
  async calculatePrice(request: PriceCalculationRequest): Promise<PriceCalculationResponse>;
}
```

### **Phase 2: Service Layer Refactoring**

Break down the monolithic `ServiceService` into focused business services:

```typescript
// Core Service Business Logic
export class ServiceBusinessService {
  constructor(
    private serviceRepository: ServiceRepository,
    private categoryRepository: ServiceCategoryRepository,
    private priceCalculator: ServicePriceCalculator
  ) {}

  async createService(data: CreateServiceRequest): Promise<ServiceDetail>;
  async updateService(id: string, data: UpdateServiceRequest): Promise<ServiceDetail>;
  async deleteService(id: string): Promise<boolean>;
}

// Category Business Logic
export class ServiceCategoryBusinessService {
  constructor(private categoryRepository: ServiceCategoryRepository) {}

  async createCategory(data: CreateServiceCategoryRequest): Promise<ServiceCategoryDetail>;
  async updateCategory(id: string, data: UpdateServiceCategoryRequest): Promise<ServiceCategoryDetail>;
}

// Form Field Business Logic
export class ServiceFormFieldBusinessService {
  constructor(private formFieldRepository: ServiceFormFieldRepository) {}

  async createFormField(data: CreateServiceFormFieldRequest): Promise<ServiceFormFieldDetail>;
  async updateFormField(id: string, data: UpdateServiceFormFieldRequest): Promise<ServiceFormFieldDetail>;
}
```

### **Phase 3: Controller Layer Refactoring**

Implement decorators and utilities to eliminate DRY violations:

```typescript
// Logging Decorator
const withRequestLogging = (operation: string) => (
  target: any,
  propertyKey: string,
  descriptor: PropertyDescriptor
) => {
  const original = descriptor.value;
  descriptor.value = async function(...args: any[]) {
    const [req, res] = args;
    const startTime = Date.now();
    const requestId = res.getHeader('X-Request-ID');

    this.logger.info(`${operation} started`, { requestId });

    try {
      const result = await original.apply(this, args);
      this.logger.info(`${operation} completed`, {
        requestId, duration: Date.now() - startTime
      });
      return result;
    } catch (error) {
      this.logger.error(`${operation} failed`, error, {
        requestId, duration: Date.now() - startTime
      });
      throw error;
    }
  };
};

// Usage in Controller
@withRequestLogging('Get service by ID')
getServiceById = asyncHandler(async (req, res) => {
  const service = await this.serviceService.getServiceById(req.params.id);
  res.success({ service });
});
```

### **Phase 4: Data Mapping Layer**

Create centralized mappers to eliminate transformation duplication:

```typescript
export class ServiceMapper {
  static toSummary(service: any): ServiceSummary {
    return {
      id: service.id,
      name: service.name,
      description: service.description,
      categoryId: service.categoryId,
      categoryName: service.service_categories?.name,
      image: service.image,
      basePrice: Number(service.basePrice),
      pricingType: service.pricingType,
      isActive: service.isActive,
      sortOrder: service.sortOrder,
      providerCount: service.providers?.length || 0,
      createdAt: service.createdAt,
      updatedAt: service.updatedAt,
    };
  }

  static toDetail(service: any): ServiceDetail {
    return {
      ...this.toSummary(service),
      detailedDesc: service.detailedDesc,
      features: service.features,
      notes: service.notes,
      formFields: service.formFields?.map(this.toFormFieldDetail) || [],
      category: this.toCategoryDetail(service.service_categories),
      providers: service.providers?.map(this.toProviderDetail) || [],
    };
  }
}
```

### **Phase 5: Validation Layer**

Centralize validation logic to eliminate duplication:

```typescript
export class ValidationUtils {
  static validateRequired(value: any, fieldName: string): void {
    if (!value || (typeof value === 'string' && value.trim().length === 0)) {
      throw new ValidationError(`${fieldName} is required`);
    }
  }

  static validateOptional(value: any, fieldName: string): void {
    if (value !== undefined && typeof value === 'string' && value.trim().length === 0) {
      throw new ValidationError(`${fieldName} cannot be empty`);
    }
  }

  static validatePositive(value: number, fieldName: string): void {
    if (value < 0) {
      throw new ValidationError(`${fieldName} must be non-negative`);
    }
  }
}
```

## 🎯 **Implementation Strategy**

### **Step 1: Create Utility Classes**

1. **ServiceMapper**: Centralize data transformation logic
2. **ValidationUtils**: Consolidate validation patterns
3. **LoggingDecorator**: Eliminate request logging duplication
4. **ResponseUtils**: Standardize HTTP response patterns

### **Step 2: Extract Domain Repositories**

1. **ServiceRepository**: Focus on core service CRUD operations
2. **ServiceCategoryRepository**: Handle category-specific operations
3. **ServiceFormFieldRepository**: Manage form field operations
4. **ServicePriceCalculator**: Dedicated price calculation logic

### **Step 3: Refactor Business Services**

1. **ServiceBusinessService**: Core service business logic
2. **ServiceCategoryBusinessService**: Category business logic
3. **ServiceFormFieldBusinessService**: Form field business logic
4. **ServiceAnalyticsService**: Statistics and analytics

### **Step 4: Implement Controller Decorators**

1. **@withRequestLogging**: Automatic request logging
2. **@withErrorHandling**: Standardized error handling
3. **@withValidation**: Request validation
4. **@withResponseMapping**: Response transformation

### **Step 5: Update Type Definitions**

1. **Consolidate duplicate interfaces**
2. **Create base types for common fields**
3. **Implement type composition patterns**
4. **Add strict typing for all operations**

## 📈 **Success Metrics**

### **Before Refactoring:**

- **SRP Compliance**: 40% ❌
- **DRY Compliance**: 35% ❌
- **Lines of Code**: 3,079 across 3 files
- **Methods per Class**: 25+ (God Classes)
- **Duplicated Patterns**: 21 instances

### **After Refactoring:**

- **SRP Compliance**: 90%+ ✅
- **DRY Compliance**: 85%+ ✅
- **Lines of Code**: Distributed across 12+ focused files
- **Methods per Class**: 5-8 (Single Responsibility)
- **Duplicated Patterns**: 0 instances

## 🚀 **Execution Commands**

### **Phase 1: Analysis and Refactoring Planning**

```bash
# Use SuperClaude's built-in refactoring analysis
/sc:refactor analyze backend/src/repositories/ServiceRepository.ts
/sc:refactor analyze backend/src/services/ServiceService.ts
/sc:refactor analyze backend/src/controllers/ServiceController.ts

# Custom SRP/DRY analysis for specific patterns
/srp_analyze backend/src/repositories/ServiceRepository.ts
/dry_analyze logging_patterns
/dry_analyze validation_logic
/dry_analyze data_mapping
```

### **Phase 2: Core Refactoring Operations**

```bash
# Main refactoring operations using SuperClaude
/sc:refactor extract-repository ServiceRepository --domain=service
/sc:refactor extract-repository ServiceRepository --domain=category
/sc:refactor extract-repository ServiceRepository --domain=form-field

/sc:refactor extract-service ServiceService --business-domain=service
/sc:refactor extract-service ServiceService --business-domain=category
/sc:refactor extract-service ServiceService --business-domain=form-field

/sc:refactor optimize-controller ServiceController --apply-decorators
```

### **Phase 3: Implementation of New Components**

```bash
# Create new utilities and components
/sc:implement ServiceMapper utility class
/sc:implement ValidationUtils utility class
/sc:implement LoggingDecorator utility class
/sc:implement ResponseUtils utility class

/sc:implement ServicePriceCalculator extraction
/sc:implement ServiceAnalyticsService extraction
```

### **Phase 4: Type System Refactoring**

```bash
# Refactor type definitions
/sc:refactor consolidate-types service.ts --eliminate-duplicates
/sc:refactor consolidate-types service.ts --create-base-types
/sc:implement TypeComposition patterns
/sc:implement StrictTyping implementation
```

### **Phase 5: Testing and Validation**

```bash
# Generate comprehensive tests
/sc:implement unit-tests repository
/sc:implement unit-tests service
/sc:implement unit-tests controller
/sc:implement integration-tests generation

# Validate refactoring results
/validate_refactoring srp_compliance
/validate_refactoring dry_compliance
/validate_refactoring performance
```

### **Alternative: One-Command Refactoring**

```bash
# Complete refactoring in one command
/sc:refactor complete --target=srp-dry-compliance --strategy=incremental
```

## 🔧 **Quality Assurance**

### **Testing Strategy**

1. **Unit Tests**: Each extracted class should have comprehensive unit tests
2. **Integration Tests**: Verify repository interactions work correctly
3. **Contract Tests**: Ensure interfaces are properly implemented
4. **Performance Tests**: Verify refactoring doesn't impact performance

### **Code Review Checklist**

- [ ] Each class has a single, well-defined responsibility
- [ ] No code duplication across the codebase
- [ ] All interfaces are properly implemented
- [ ] Decorators are working correctly
- [ ] Type safety is maintained throughout
- [ ] Performance benchmarks are met
- [ ] All existing functionality is preserved

## 📚 **References**

- **SOLID Principles**: https://en.wikipedia.org/wiki/SOLID
- **DRY Principle**: https://en.wikipedia.org/wiki/Don%27t_repeat_yourself
- **Repository Pattern**: https://martinfowler.com/eaaCatalog/repository.html
- **Decorator Pattern**: https://refactoring.guru/design-patterns/decorator
- **Domain-Driven Design**: https://martinfowler.com/bliki/DomainDrivenDesign.html

---

**🎯 Your mission is to transform this monolithic, violation-ridden codebase into a clean, maintainable, and scalable architecture that follows SOLID principles and eliminates all DRY violations. Focus on incremental improvements that maintain functionality while dramatically improving code quality.**
