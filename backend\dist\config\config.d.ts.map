{"version": 3, "file": "config.d.ts", "sourceRoot": "", "sources": ["../../src/config/config.ts"], "names": [], "mappings": "AACA,OAAO,EAAC,CAAC,EAAC,MAAM,KAAK,CAAC;AAWtB,QAAA,MAAM,oBAAoB;;;;;iBAKxB,CAAC;AAEH,QAAA,MAAM,eAAe;;;;;;;iBASnB,CAAC;AAEH,QAAA,MAAM,kBAAkB;;;;;;;;;;;iBAOtB,CAAC;AAEH,QAAA,MAAM,iBAAiB;;;;;;;iBAOrB,CAAC;AAEH,QAAA,MAAM,eAAe;;;;;;iBAMnB,CAAC;AAEH,QAAA,MAAM,iBAAiB;;;;;;;;iBAQrB,CAAC;AAEH,QAAA,MAAM,mBAAmB;;;;;;;;;;;;;;;;iBAQvB,CAAC;AAEH,QAAA,MAAM,oBAAoB;;;;;;;;iBAYxB,CAAC;AAEH,QAAA,MAAM,uBAAuB;;;;iBAI3B,CAAC;AAEH,QAAA,MAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iBAanB,CAAC;AAGH,MAAM,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,eAAe,CAAC,CAAC;AACxD,MAAM,MAAM,cAAc,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,oBAAoB,CAAC,CAAC;AAClE,MAAM,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,eAAe,CAAC,CAAC;AACxD,MAAM,MAAM,YAAY,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,kBAAkB,CAAC,CAAC;AAC9D,MAAM,MAAM,WAAW,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,iBAAiB,CAAC,CAAC;AAC5D,MAAM,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,eAAe,CAAC,CAAC;AACxD,MAAM,MAAM,WAAW,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,iBAAiB,CAAC,CAAC;AAC5D,MAAM,MAAM,aAAa,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,mBAAmB,CAAC,CAAC;AAChE,MAAM,MAAM,cAAc,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,oBAAoB,CAAC,CAAC;AAClE,MAAM,MAAM,iBAAiB,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,uBAAuB,CAAC,CAAC;AAGxE,qBAAa,oBAAoB;IAChC,OAAO,CAAC,MAAM,CAAY;IAC1B,OAAO,CAAC,aAAa,CAAS;;IAM9B,OAAO,CAAC,iBAAiB;IAiHlB,SAAS,IAAI,SAAS;IAOtB,iBAAiB,IAAI,cAAc;IAInC,YAAY,IAAI,SAAS;IAIzB,eAAe,IAAI,YAAY;IAI/B,cAAc,IAAI,WAAW;IAI7B,YAAY,IAAI,SAAS;IAIzB,cAAc,IAAI,WAAW;IAI7B,gBAAgB,IAAI,aAAa;IAIjC,iBAAiB,IAAI,cAAc;IAInC,oBAAoB,IAAI,iBAAiB;IAIzC,aAAa,IAAI,OAAO;IAIxB,YAAY,IAAI,OAAO;IAIvB,MAAM,IAAI,OAAO;IAIjB,MAAM,IAAI,IAAI;CAIrB"}