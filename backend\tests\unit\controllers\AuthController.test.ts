// Mock the logger before any imports
jest.mock('@/utils/logger');

import { AuthController } from '@/controllers/AuthController';
import { AuthenticationError, NotFoundError } from '@/middleware/errorHandler';
import { IAuthService } from '@/services/AuthService';
import { UserRole } from '@prisma/client';
import { Response } from 'express';

// Mock the middleware and models
jest.mock('@/middleware/errorHandler', () => ({
  asyncHandler: (fn: any) => fn, // Simplified mock for testing
  AuthenticationError: class AuthenticationError extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'AuthenticationError';
    }
  },
  NotFoundError: class NotFoundError extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'NotFoundError';
    }
  },
}));

jest.mock('@/models/AuthModels', () => ({
  AuthenticationModel: {
    createRegistrationData: jest.fn(),
    createLoginData: jest.fn(),
  },
  AuthenticationResponse: jest.fn().mockImplementation((user, tokens) => ({
    user,
    tokens,
  })),
  UserProfile: {
    fromAuthUser: jest.fn().mockReturnValue({
      toJSON: jest.fn().mockReturnValue({ id: '1', email: '<EMAIL>' }),
    }),
  },
}));

jest.mock('@/utils/auth', () => ({
  getClientIP: jest.fn().mockReturnValue('***********'),
  getUserAgent: jest.fn().mockReturnValue('test-agent'),
}));

describe('AuthController', () => {
  let authController: AuthController;
  let mockAuthService: jest.Mocked<IAuthService>;
  let mockResponse: Partial<Response>;
  let mockRequest: any;

  beforeEach(() => {
    // Mock the logger
    const { createLogger } = require('@/utils/logger');
    createLogger.mockReturnValue({
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    });

    // Mock the auth utilities
    const { getClientIP, getUserAgent } = require('@/utils/auth');
    getClientIP.mockReturnValue('***********');
    getUserAgent.mockReturnValue('test-agent');

    // Mock the AuthModels
    const { UserProfile } = require('@/models/AuthModels');
    UserProfile.fromAuthUser.mockReturnValue({
      toJSON: jest.fn().mockReturnValue({ id: '1', email: '<EMAIL>' }),
    });

    mockAuthService = {
      register: jest.fn(),
      login: jest.fn(),
      refreshTokens: jest.fn(),
      logout: jest.fn(),
      logoutAllDevices: jest.fn(),
      getProfile: jest.fn(),
      updateProfile: jest.fn(),
      changePassword: jest.fn(),
      requestPasswordReset: jest.fn(),
      resetPassword: jest.fn(),
      sendEmailVerification: jest.fn(),
      verifyEmail: jest.fn(),
      getUserSessions: jest.fn(),
      revokeSession: jest.fn(),
    } as any;

    mockResponse = {
      getHeader: jest.fn().mockReturnValue('request-id-123'),
      cookie: jest.fn(),
      clearCookie: jest.fn(),
      created: jest.fn(),
      success: jest.fn(),
    };

    mockRequest = {
      body: {},
      cookies: {},
      params: {},
      userId: 'user-123',
    };

    authController = new AuthController(mockAuthService);
    jest.clearAllMocks();
  });

  describe('register', () => {
    it('should successfully register a new user', async () => {
      const registrationData = { email: '<EMAIL>', password: 'password123' };
      const mockResult = {
        user: {
          id: '1',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          role: UserRole.CUSTOMER,
          isActive: true,
          isVerified: false,
          avatar: null,
          phone: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          lastLoginAt: null,
        },
        tokens: { accessToken: 'access-token', refreshToken: 'refresh-token' },
      };

      mockRequest.body = registrationData;
      mockAuthService.register.mockResolvedValue(mockResult);

      await authController.register(mockRequest as any, mockResponse as Response, jest.fn());

      expect(mockAuthService.register).toHaveBeenCalledWith(
        registrationData,
        expect.objectContaining({
          ipAddress: '***********',
          userAgent: 'test-agent',
        })
      );
      expect(mockResponse.cookie).toHaveBeenCalledWith(
        'refreshToken',
        'refresh-token',
        expect.objectContaining({
          httpOnly: true,
          secure: false, // NODE_ENV is not production in tests
          sameSite: 'strict',
          maxAge: 7 * 24 * 60 * 60 * 1000,
        })
      );
      expect(mockResponse.created).toHaveBeenCalled();
    });

    it('should handle registration errors', async () => {
      const registrationData = { email: '<EMAIL>', password: 'password123' };
      mockRequest.body = registrationData;

      const error = new Error('Registration failed');
      mockAuthService.register.mockRejectedValue(error);

      await expect(authController.register(mockRequest as any, mockResponse as Response, jest.fn())).rejects.toThrow(
        'Registration failed'
      );
    });
  });

  describe('login', () => {
    it('should successfully login a user', async () => {
      const loginData = { email: '<EMAIL>', password: 'password123' };
      const mockResult = {
        user: {
          id: '1',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          role: UserRole.CUSTOMER,
          isActive: true,
          isVerified: false,
          avatar: null,
          phone: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          lastLoginAt: null,
        },
        tokens: { accessToken: 'access-token', refreshToken: 'refresh-token' },
      };

      mockRequest.body = loginData;
      mockAuthService.login.mockResolvedValue(mockResult);

      await authController.login(mockRequest as any, mockResponse as Response, jest.fn());

      expect(mockAuthService.login).toHaveBeenCalledWith(
        loginData,
        expect.objectContaining({
          ipAddress: '***********',
          userAgent: 'test-agent',
        })
      );
      expect(mockResponse.cookie).toHaveBeenCalledWith(
        'refreshToken',
        'refresh-token',
        expect.objectContaining({
          httpOnly: true,
          secure: false,
          sameSite: 'strict',
        })
      );
      expect(mockResponse.success).toHaveBeenCalled();
    });

    it('should handle login errors', async () => {
      const loginData = { email: '<EMAIL>', password: 'wrongpassword' };
      mockRequest.body = loginData;

      const error = new Error('Invalid credentials');
      mockAuthService.login.mockRejectedValue(error);

      await expect(authController.login(mockRequest as any, mockResponse as Response, jest.fn())).rejects.toThrow(
        'Invalid credentials'
      );
    });
  });

  describe('refreshTokens', () => {
    it('should successfully refresh tokens', async () => {
      const mockResult = {
        tokens: { accessToken: 'new-access-token', refreshToken: 'new-refresh-token' },
      };

      mockRequest.cookies = { refreshToken: 'old-refresh-token' };
      mockAuthService.refreshTokens.mockResolvedValue(mockResult);

      await authController.refreshTokens(mockRequest as any, mockResponse as Response, jest.fn());

      expect(mockAuthService.refreshTokens).toHaveBeenCalledWith(
        'old-refresh-token',
        expect.objectContaining({
          ipAddress: '***********',
          userAgent: 'test-agent',
        })
      );
      expect(mockResponse.cookie).toHaveBeenCalledWith('refreshToken', 'new-refresh-token', expect.any(Object));
      expect(mockResponse.success).toHaveBeenCalledWith(
        {
          tokens: {
            accessToken: 'new-access-token',
          },
        },
        'Tokens refreshed successfully'
      );
    });

    it('should throw error when refresh token is missing', async () => {
      mockRequest.cookies = {};

      await expect(
        authController.refreshTokens(mockRequest as any, mockResponse as Response, jest.fn())
      ).rejects.toThrow(AuthenticationError);
    });
  });

  describe('logout', () => {
    it('should successfully logout a user', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: UserRole.CUSTOMER,
        isActive: true,
        isVerified: false,
        avatar: null,
        phone: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastLoginAt: null,
      };
      mockRequest.cookies = { refreshToken: 'refresh-token' };
      mockAuthService.getProfile.mockResolvedValue(mockUser);
      mockAuthService.logout.mockResolvedValue(undefined);

      await authController.logout(mockRequest as any, mockResponse as Response, jest.fn());

      expect(mockAuthService.getProfile).toHaveBeenCalledWith('user-123');
      expect(mockAuthService.logout).toHaveBeenCalledWith(
        'refresh-token',
        mockUser,
        expect.objectContaining({
          ipAddress: '***********',
          userAgent: 'test-agent',
        })
      );
      expect(mockResponse.clearCookie).toHaveBeenCalledWith(
        'refreshToken',
        expect.objectContaining({
          httpOnly: true,
          secure: false,
          sameSite: 'strict',
        })
      );
      expect(mockResponse.success).toHaveBeenCalledWith({}, 'Logout successful');
    });

    it('should throw error when user is not authenticated', async () => {
      mockRequest.userId = undefined;

      await expect(authController.logout(mockRequest as any, mockResponse as Response, jest.fn())).rejects.toThrow(
        AuthenticationError
      );
    });

    it('should throw error when user is not found', async () => {
      mockAuthService.getProfile.mockRejectedValue(new NotFoundError('User not found'));

      await expect(authController.logout(mockRequest as any, mockResponse as Response, jest.fn())).rejects.toThrow(
        NotFoundError
      );
    });
  });

  describe('getProfile', () => {
    it('should successfully get user profile', async () => {
      const mockProfile = {
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: UserRole.CUSTOMER,
        isActive: true,
        isVerified: false,
        avatar: null,
        phone: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastLoginAt: null,
      };
      mockAuthService.getProfile.mockResolvedValue(mockProfile);

      await authController.getProfile(mockRequest as any, mockResponse as Response, jest.fn());

      expect(mockAuthService.getProfile).toHaveBeenCalledWith('user-123');
      expect(mockResponse.success).toHaveBeenCalledWith(mockProfile);
    });

    it('should throw error when user is not authenticated', async () => {
      mockRequest.userId = undefined;

      await expect(authController.getProfile(mockRequest as any, mockResponse as Response, jest.fn())).rejects.toThrow(
        AuthenticationError
      );
    });
  });

  describe('updateProfile', () => {
    it('should successfully update user profile', async () => {
      const updateData = { firstName: 'John', lastName: 'Doe' };
      const mockUpdatedProfile = {
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: UserRole.CUSTOMER,
        isActive: true,
        isVerified: false,
        avatar: null,
        phone: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastLoginAt: null,
      };

      mockRequest.body = updateData;
      mockAuthService.updateProfile.mockResolvedValue(mockUpdatedProfile);

      await authController.updateProfile(mockRequest as any, mockResponse as Response, jest.fn());

      expect(mockAuthService.updateProfile).toHaveBeenCalledWith('user-123', updateData);
      expect(mockResponse.success).toHaveBeenCalledWith(mockUpdatedProfile, 'Profile updated successfully');
    });

    it('should throw error when user is not authenticated', async () => {
      mockRequest.userId = undefined;

      await expect(
        authController.updateProfile(mockRequest as any, mockResponse as Response, jest.fn())
      ).rejects.toThrow(AuthenticationError);
    });
  });

  describe('changePassword', () => {
    it('should successfully change password', async () => {
      const passwordData = { currentPassword: 'old123', newPassword: 'new123' };
      mockRequest.body = passwordData;
      mockAuthService.changePassword.mockResolvedValue(undefined);

      await authController.changePassword(mockRequest as any, mockResponse as Response, jest.fn());

      expect(mockAuthService.changePassword).toHaveBeenCalledWith(
        'user-123',
        passwordData,
        expect.objectContaining({
          ipAddress: '***********',
          userAgent: 'test-agent',
        })
      );
      expect(mockResponse.success).toHaveBeenCalledWith({}, 'Password changed successfully');
    });

    it('should throw error when user is not authenticated', async () => {
      mockRequest.userId = undefined;

      await expect(
        authController.changePassword(mockRequest as any, mockResponse as Response, jest.fn())
      ).rejects.toThrow(AuthenticationError);
    });
  });

  describe('requestPasswordReset', () => {
    it('should successfully request password reset', async () => {
      const emailData = { email: '<EMAIL>' };
      mockRequest.body = emailData;
      mockAuthService.requestPasswordReset.mockResolvedValue(undefined);

      await authController.requestPasswordReset(mockRequest as any, mockResponse as Response, jest.fn());

      expect(mockAuthService.requestPasswordReset).toHaveBeenCalledWith(
        '<EMAIL>',
        expect.objectContaining({
          ipAddress: '***********',
          userAgent: 'test-agent',
        })
      );
      expect(mockResponse.success).toHaveBeenCalledWith({}, 'If the email exists, a password reset link has been sent');
    });
  });

  describe('resetPassword', () => {
    it('should successfully reset password', async () => {
      const resetData = { token: 'reset-token', newPassword: 'newpassword123' };
      mockRequest.body = resetData;
      mockAuthService.resetPassword.mockResolvedValue(undefined);

      await authController.resetPassword(mockRequest as any, mockResponse as Response, jest.fn());

      expect(mockAuthService.resetPassword).toHaveBeenCalledWith(
        'reset-token',
        'newpassword123',
        expect.objectContaining({
          ipAddress: '***********',
          userAgent: 'test-agent',
        })
      );
      expect(mockResponse.success).toHaveBeenCalledWith({}, 'Password has been reset successfully');
    });
  });

  describe('sendEmailVerification', () => {
    it('should successfully send email verification', async () => {
      mockAuthService.sendEmailVerification.mockResolvedValue(undefined);

      await authController.sendEmailVerification(mockRequest as any, mockResponse as Response, jest.fn());

      expect(mockAuthService.sendEmailVerification).toHaveBeenCalledWith(
        'user-123',
        expect.objectContaining({
          ipAddress: '***********',
          userAgent: 'test-agent',
        })
      );
      expect(mockResponse.success).toHaveBeenCalledWith({}, 'Verification email sent');
    });

    it('should throw error when user is not authenticated', async () => {
      mockRequest.userId = undefined;

      await expect(
        authController.sendEmailVerification(mockRequest as any, mockResponse as Response, jest.fn())
      ).rejects.toThrow(AuthenticationError);
    });
  });

  describe('verifyEmail', () => {
    it('should successfully verify email', async () => {
      const tokenData = { token: 'verification-token' };
      mockRequest.body = tokenData;
      mockAuthService.verifyEmail.mockResolvedValue(undefined);

      await authController.verifyEmail(mockRequest as any, mockResponse as Response, jest.fn());

      expect(mockAuthService.verifyEmail).toHaveBeenCalledWith(
        'verification-token',
        expect.objectContaining({
          ipAddress: '***********',
          userAgent: 'test-agent',
        })
      );
      expect(mockResponse.success).toHaveBeenCalledWith({}, 'Email verified successfully');
    });
  });

  describe('getUserSessions', () => {
    it('should successfully get user sessions', async () => {
      const mockSessions = [
        {
          id: 'session-1',
          ipAddress: '***********',
          userAgent: 'test-agent',
          expiresAt: new Date(),
          isActive: true,
          createdAt: new Date(),
        },
      ];
      mockAuthService.getUserSessions.mockResolvedValue(mockSessions);

      await authController.getUserSessions(mockRequest as any, mockResponse as Response, jest.fn());

      expect(mockAuthService.getUserSessions).toHaveBeenCalledWith('user-123');
      expect(mockResponse.success).toHaveBeenCalledWith(mockSessions);
    });

    it('should throw error when user is not authenticated', async () => {
      mockRequest.userId = undefined;

      await expect(
        authController.getUserSessions(mockRequest as any, mockResponse as Response, jest.fn())
      ).rejects.toThrow(AuthenticationError);
    });
  });

  describe('revokeSession', () => {
    it('should successfully revoke session', async () => {
      mockRequest.params = { sessionId: 'session-123' };
      mockAuthService.revokeSession.mockResolvedValue(undefined);

      await authController.revokeSession(mockRequest as any, mockResponse as Response, jest.fn());

      expect(mockAuthService.revokeSession).toHaveBeenCalledWith('session-123', 'user-123');
      expect(mockResponse.success).toHaveBeenCalledWith({}, 'Session revoked successfully');
    });

    it('should throw error when user is not authenticated', async () => {
      mockRequest.userId = undefined;

      await expect(
        authController.revokeSession(mockRequest as any, mockResponse as Response, jest.fn())
      ).rejects.toThrow(AuthenticationError);
    });
  });

  describe('logoutAllDevices', () => {
    it('should successfully logout from all devices', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: UserRole.CUSTOMER,
        isActive: true,
        isVerified: false,
        avatar: null,
        phone: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastLoginAt: null,
      };
      mockAuthService.getProfile.mockResolvedValue(mockUser);
      mockAuthService.logoutAllDevices.mockResolvedValue(undefined);

      await authController.logoutAllDevices(mockRequest as any, mockResponse as Response, jest.fn());

      expect(mockAuthService.getProfile).toHaveBeenCalledWith('user-123');
      expect(mockAuthService.logoutAllDevices).toHaveBeenCalledWith(
        mockUser,
        expect.objectContaining({
          ipAddress: '***********',
          userAgent: 'test-agent',
        })
      );
      expect(mockResponse.success).toHaveBeenCalledWith({}, 'Logged out from all devices successfully');
    });

    it('should throw error when user is not authenticated', async () => {
      mockRequest.userId = undefined;

      await expect(
        authController.logoutAllDevices(mockRequest as any, mockResponse as Response, jest.fn())
      ).rejects.toThrow(AuthenticationError);
    });

    it('should throw error when user is not found', async () => {
      mockAuthService.getProfile.mockRejectedValue(new NotFoundError('User not found'));

      await expect(
        authController.logoutAllDevices(mockRequest as any, mockResponse as Response, jest.fn())
      ).rejects.toThrow(NotFoundError);
    });
  });
});
