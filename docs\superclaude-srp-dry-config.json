{"name": "SRP-DRY-Refactoring-Expert", "version": "3.0.0", "description": "Specialized configuration for Core Services API SRP & DRY refactoring", "persona": {"role": "Senior Software Architect", "expertise": ["SOLID Principles", "Clean Code Practices", "Domain-Driven Design", "Repository Pattern", "Decorator Pattern", "TypeScript Architecture", "Node.js/Express", "Prisma ORM"], "communication_style": "Analytical, systematic, and solution-focused", "approach": "Incremental refactoring with comprehensive testing"}, "commands": {"srp_analyze": {"description": "Analyze SRP violations in the codebase", "usage": "/srp_analyze [file_path]", "examples": ["/srp_analyze backend/src/repositories/ServiceRepository.ts", "/srp_analyze backend/src/services/ServiceService.ts"]}, "dry_analyze": {"description": "Identify DRY violations and code duplication", "usage": "/dry_analyze [pattern_type]", "examples": ["/dry_analyze logging_patterns", "/dry_analyze validation_logic", "/dry_analyze data_mapping"]}, "refactor_repository": {"description": "Extract domain-specific repositories from monolithic repository", "usage": "/refactor_repository [domain]", "examples": ["/refactor_repository service", "/refactor_repository category", "/refactor_repository form_field"]}, "extract_service": {"description": "Extract focused business services from monolithic service", "usage": "/extract_service [business_domain]", "examples": ["/extract_service service_business", "/extract_service category_business", "/extract_service form_field_business"]}, "implement_decorator": {"description": "Create decorators to eliminate DRY violations", "usage": "/implement_decorator [decorator_type]", "examples": ["/implement_decorator logging", "/implement_decorator validation", "/implement_decorator error_handling"]}, "create_mapper": {"description": "Create data mappers to eliminate transformation duplication", "usage": "/create_mapper [entity_type]", "examples": ["/create_mapper service", "/create_mapper category", "/create_mapper form_field"]}, "consolidate_types": {"description": "Consolidate duplicate type definitions", "usage": "/consolidate_types [type_category]", "examples": ["/consolidate_types service_types", "/consolidate_types request_types", "/consolidate_types response_types"]}, "generate_tests": {"description": "Generate comprehensive tests for refactored components", "usage": "/generate_tests [component_type]", "examples": ["/generate_tests repository", "/generate_tests service", "/generate_tests controller"]}, "validate_refactoring": {"description": "Validate that refactoring maintains functionality and improves quality", "usage": "/validate_refactoring [validation_type]", "examples": ["/validate_refactoring srp_compliance", "/validate_refactoring dry_compliance", "/validate_refactoring performance"]}}, "analysis_patterns": {"srp_violations": ["classes_with_multiple_responsibilities", "methods_doing_too_many_things", "mixed_abstraction_levels", "god_classes", "violation_of_single_reason_to_change"], "dry_violations": ["duplicated_code_blocks", "repeated_patterns", "similar_validation_logic", "identical_data_transformations", "redundant_type_definitions"], "code_smells": ["long_methods", "large_classes", "feature_envy", "data_clumps", "primitive_obsession"]}, "refactoring_strategies": {"repository_extraction": {"steps": ["identify_domain_boundaries", "extract_interface", "create_specialized_repository", "update_dependencies", "write_tests"], "principles": ["single_responsibility", "interface_segregation", "dependency_inversion"]}, "service_decomposition": {"steps": ["identify_business_domains", "extract_business_logic", "create_focused_services", "implement_dependency_injection", "maintain_contracts"], "principles": ["single_responsibility", "open_closed", "dependency_inversion"]}, "decorator_implementation": {"steps": ["identify_common_patterns", "design_decorator_interface", "implement_decorator_logic", "apply_to_targets", "verify_functionality"], "principles": ["don't_repeat_yourself", "open_closed", "single_responsibility"]}}, "quality_metrics": {"srp_compliance": {"target": "90%+", "measurement": "percentage_of_classes_with_single_responsibility", "current": "40%"}, "dry_compliance": {"target": "85%+", "measurement": "percentage_of_code_without_duplication", "current": "35%"}, "cyclomatic_complexity": {"target": "< 10 per method", "measurement": "average_complexity_per_method", "current": "15+"}, "lines_per_class": {"target": "< 200", "measurement": "average_lines_per_class", "current": "1000+"}, "methods_per_class": {"target": "< 10", "measurement": "average_methods_per_class", "current": "25+"}}, "file_structure": {"repositories": {"path": "backend/src/repositories/", "files": ["ServiceRepository.ts", "ServiceCategoryRepository.ts", "ServiceFormFieldRepository.ts", "ServicePriceCalculator.ts"]}, "services": {"path": "backend/src/services/", "files": ["ServiceBusinessService.ts", "ServiceCategoryBusinessService.ts", "ServiceFormFieldBusinessService.ts", "ServiceAnalyticsService.ts"]}, "controllers": {"path": "backend/src/controllers/", "files": ["ServiceController.ts", "ServiceCategoryController.ts", "ServiceFormFieldController.ts"]}, "utils": {"path": "backend/src/utils/", "files": ["ServiceMapper.ts", "ValidationUtils.ts", "LoggingDecorator.ts", "ResponseUtils.ts"]}, "types": {"path": "backend/src/types/", "files": ["service.ts", "service-category.ts", "service-form-field.ts", "common.ts"]}}, "testing_strategy": {"unit_tests": {"coverage_target": "90%+", "focus_areas": ["repository_methods", "service_business_logic", "utility_functions", "decorators"]}, "integration_tests": {"coverage_target": "80%+", "focus_areas": ["repository_interactions", "service_dependencies", "controller_endpoints"]}, "contract_tests": {"coverage_target": "100%", "focus_areas": ["interface_implementations", "type_safety", "api_contracts"]}}, "performance_benchmarks": {"response_time": {"target": "< 200ms", "measurement": "average_api_response_time"}, "memory_usage": {"target": "< 100MB", "measurement": "peak_memory_usage"}, "cpu_usage": {"target": "< 50%", "measurement": "average_cpu_utilization"}}, "documentation_requirements": {"code_documentation": {"required": true, "standards": ["JSDoc for all public methods", "TypeScript interfaces for all contracts", "README files for each module"]}, "architecture_documentation": {"required": true, "artifacts": ["architecture_diagram", "refactoring_plan", "migration_guide"]}}}