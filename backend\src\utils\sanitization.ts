/**
 * Data Sanitization Utilities
 *
 * Centralized utilities for sanitizing sensitive data before logging.
 * Prevents exposure of secrets, passwords, tokens, and PII in logs.
 */

// Comprehensive list of sensitive field patterns
const SENSITIVE_FIELD_PATTERNS = [
  // Authentication & Security
  /password/i,
  /secret/i,
  /token/i,
  /key/i,
  /auth/i,
  /credential/i,

  // Personal Identifiable Information
  /ssn/i,
  /social.*security/i,
  /credit.*card/i,
  /card.*number/i,
  /cvv/i,
  /cvc/i,

  // Financial
  /account.*number/i,
  /routing.*number/i,
  /iban/i,
  /swift/i,

  // Contact Information (optional based on requirements)
  /phone/i,
  /address/i,
];

// Direct field name matches (case-insensitive)
const SENSITIVE_FIELDS = [
  'password',
  'currentPassword',
  'newPassword',
  'confirmPassword',
  'token',
  'refreshToken',
  'accessToken',
  'apiKey',
  'secret',
  'privateKey',
  'publicKey',
  'jwt',
  'sessionId',
  'sessionToken',
  'authorization',
  'bearer',
  'ssn',
  'socialSecurityNumber',
  'creditCard',
  'cardNumber',
  'cvv',
  'cvc',
  'expiryDate',
  'accountNumber',
  'routingNumber',
  'iban',
  'swift',
].map(field => field.toLowerCase());

/**
 * Check if a field name is sensitive
 */
function isSensitiveField(fieldName: string): boolean {
  const lowerFieldName = fieldName.toLowerCase();

  // Check direct matches
  if (SENSITIVE_FIELDS.includes(lowerFieldName)) {
    return true;
  }

  // Check pattern matches
  return SENSITIVE_FIELD_PATTERNS.some(pattern => pattern.test(lowerFieldName));
}

/**
 * Deep sanitization of objects, arrays, and nested structures
 */
function deepSanitize(obj: any, maxDepth: number = 10, currentDepth: number = 0): any {
  // Prevent infinite recursion
  if (currentDepth >= maxDepth) {
    return '[MAX_DEPTH_REACHED]';
  }

  // Handle null/undefined
  if (obj === null || obj === undefined) {
    return obj;
  }

  // Handle primitive types
  if (typeof obj !== 'object') {
    return obj;
  }

  // Handle arrays
  if (Array.isArray(obj)) {
    return obj.map(item => deepSanitize(item, maxDepth, currentDepth + 1));
  }

  // Handle Date objects
  if (obj instanceof Date) {
    return obj;
  }

  // Handle regular objects
  const sanitized: any = {};

  for (const [key, value] of Object.entries(obj)) {
    if (isSensitiveField(key)) {
      // For sensitive field names, we still need to process the value
      // Arrays and objects should be processed, primitives should be redacted
      if (typeof value === 'object' && value !== null) {
        sanitized[key] = deepSanitize(value, maxDepth, currentDepth + 1);
      } else {
        sanitized[key] = '[REDACTED]';
      }
    } else if (typeof value === 'object' && value !== null) {
      sanitized[key] = deepSanitize(value, maxDepth, currentDepth + 1);
    } else {
      sanitized[key] = value;
    }
  }

  return sanitized;
}

/**
 * Sanitize request body for logging
 * Removes sensitive data while preserving structure
 */
export function sanitizeRequestBody(body: any): any {
  if (!body) {
    return body;
  }

  return deepSanitize(body);
}

/**
 * Sanitize user object for API responses
 * Removes authentication and sensitive fields
 */
export function sanitizeUser(user: any): any {
  if (!user || typeof user !== 'object') {
    return user;
  }

  const {
    password,
    passwordResetToken,
    passwordResetTokenExpires,
    emailVerificationToken,
    emailVerificationTokenExpires,
    loginAttempts,
    lockedUntil,
    sessions,
    ...sanitizedUser
  } = user;

  return sanitizedUser;
}

/**
 * Sanitize headers for logging
 * Removes authorization and sensitive headers
 */
export function sanitizeHeaders(headers: any): any {
  if (!headers || typeof headers !== 'object') {
    return headers;
  }

  const sensitiveHeaderPatterns = [/authorization/i, /cookie/i, /x-api-key/i, /x-auth/i, /bearer/i];

  const sanitized: any = {};

  for (const [key, value] of Object.entries(headers)) {
    const isSensitive = sensitiveHeaderPatterns.some(pattern => pattern.test(key));
    sanitized[key] = isSensitive ? '[REDACTED]' : value;
  }

  return sanitized;
}

/**
 * Sanitize query parameters for logging
 * Removes sensitive query params like tokens
 */
export function sanitizeQuery(query: any): any {
  if (!query || typeof query !== 'object') {
    return query;
  }

  return deepSanitize(query);
}

/**
 * General purpose sanitization for any object
 * Use this for miscellaneous data that might contain sensitive info
 */
export function sanitizeObject(
  obj: any,
  options: {
    maxDepth?: number;
    customSensitiveFields?: string[];
  } = {}
): any {
  const { maxDepth = 10, customSensitiveFields = [] } = options;

  // Temporarily add custom sensitive fields
  const originalFields = [...SENSITIVE_FIELDS];
  SENSITIVE_FIELDS.push(...customSensitiveFields.map(f => f.toLowerCase()));

  try {
    return deepSanitize(obj, maxDepth);
  } finally {
    // Restore original sensitive fields
    SENSITIVE_FIELDS.length = originalFields.length;
    SENSITIVE_FIELDS.push(...originalFields);
  }
}

export default {
  sanitizeRequestBody,
  sanitizeUser,
  sanitizeHeaders,
  sanitizeQuery,
  sanitizeObject,
  isSensitiveField,
};
