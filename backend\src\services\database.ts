import {PrismaClient} from '@prisma/client';
import {config} from '../config';
import {createLogger} from '../utils/logger';

const logger = createLogger('Database');

/**
 * Database Service - Centralized Database Management
 *
 * This service provides a singleton Prisma client instance with proper
 * connection management, error handling, and lifecycle management.
 */
class DatabaseService {
	private static instance: DatabaseService;
	private prisma: PrismaClient;
	private isConnected = false;
	private isInitialized = false;

	private constructor() {
		this.prisma = new PrismaClient({
			log: [
				{
					emit: 'stdout',
					level: 'query',
				},
				{
					emit: 'stdout',
					level: 'error',
				},
				{
					emit: 'stdout',
					level: 'info',
				},
				{
					emit: 'stdout',
					level: 'warn',
				},
			],
		});
	}

	/**
	 * Get singleton instance
	 */
	public static getInstance(): DatabaseService {
		if (!DatabaseService.instance) {
			DatabaseService.instance = new DatabaseService();
		}
		return DatabaseService.instance;
	}

	/**
	 * Initialize database connection
	 */
	public async initialize(): Promise<void> {
		if (this.isInitialized) {
			logger.warn('Database service already initialized');
			return;
		}

		try {
			const dbConfig = config.getDatabaseConfig();

			logger.info('Initializing database connection', {
				url: dbConfig.url.replace(/\/\/.*@/, '//***:***@'), // Mask credentials
				maxConnections: dbConfig.maxConnections,
				idleTimeout: dbConfig.idleTimeout,
				connectionTimeout: dbConfig.connectionTimeout,
			});

			await this.prisma.$connect();
			this.isConnected = true;
			this.isInitialized = true;

			logger.info('Database connection established successfully');
		} catch (error) {
			logger.error('Failed to initialize database connection', error);
			throw error;
		}
	}

	/**
	 * Get Prisma client instance
	 */
	public getClient(): PrismaClient {
		if (!this.isInitialized) {
			throw new Error(
				'Database service not initialized. Call initialize() first.'
			);
		}
		return this.prisma;
	}

	/**
	 * Check if database is connected
	 */
	public isConnectedToDatabase(): boolean {
		return this.isConnected;
	}

	/**
	 * Disconnect from database
	 */
	public async disconnect(): Promise<void> {
		if (!this.isConnected) {
			logger.warn('Database already disconnected');
			return;
		}

		try {
			await this.prisma.$disconnect();
			this.isConnected = false;
			this.isInitialized = false;
			logger.info('Database disconnected successfully');
		} catch (error) {
			logger.error('Error disconnecting from database', error);
			throw error;
		}
	}

	/**
	 * Health check for database connection
	 */
	public async healthCheck(): Promise<boolean> {
		try {
			await this.prisma.$queryRaw`SELECT 1`;
			return true;
		} catch (error) {
			logger.error('Database health check failed', error);
			return false;
		}
	}

	// Event handlers removed - using stdout logging instead
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance();

// Export the class for testing purposes
export {DatabaseService};
